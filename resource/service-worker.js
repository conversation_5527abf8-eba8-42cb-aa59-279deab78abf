/**
 * Created by <PERSON> on - 2020/10/19.
 */

const OFFLINE_INDEX = '/neterror.html'

const OFFLINE_URLS = [
  OFFLINE_INDEX
]

const RUNTIME_CACHE = 'cache-v1'

const PRECACHE_URLS = OFFLINE_URLS.concat([])

self.addEventListener('install', event => {
  event.waitUntil(
    caches
      .open(RUNTIME_CACHE)
      .then(cache => cache.addAll(PRECACHE_URLS))
      .then(self.skipWaiting())
  )
})

self.addEventListener('activate', event => {
  const currentCaches = [RUNTIME_CACHE]
  event.waitUntil(
    caches
      .keys()
      .then(cacheNames => {
        return cacheNames.filter(cacheName => !currentCaches.includes(cacheName))
      })
      .then(cachesToDelete => {
        return Promise.all(cachesToDelete.map(cacheToDelete => {
          return caches.delete(cacheToDelete)
        }))
      })
      .then(() => {
        if ('navigationPreload' in self.registration) {
          return self.registration.navigationPreload.enable()
        } else {
          return Promise.resolve()
        }
      })
      .then(() => self.clients.claim())
  )
})

self.addEventListener('fetch', event => {
  if (event.request.mode === 'navigate') {
    event.respondWith((async () => {
      try {
        const preloadResponse = await event.preloadResponse
        if (preloadResponse) {
          return preloadResponse
        }
        const networkResponse = await fetch(event.request)
        return networkResponse
      } catch (error) {
        const cache = await caches.open(RUNTIME_CACHE)
        const cachedResponse = await cache.match(OFFLINE_INDEX)
        return cachedResponse
      }
    })())
  }
})
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.register('service-worker.js')
}
