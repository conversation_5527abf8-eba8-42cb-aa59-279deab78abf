<template>
  <div class="app-dimension-title" :class="themeClass">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'app-dimension-title',
  props: {
    themeClass: { default: '', type: String }// danger
  },
  data() {
    return {}
  }
}
</script>

<style scoped lang="scss">
.app-dimension-title {
  height: 21px;
  font-size: 16px;
  display: flex;
  align-items: center;
  padding-left: 8px;
  position: relative;
  &:before{
    content: " ";
    display: inline-block;
    width: 3px;
    height: 100%;
    background: #128bed;
    position: absolute;
    left: 0;
    top: 0;
  }
}
</style>
<style lang="scss">
.app-dimension-title {
  &.danger {
    &:before{
      background: #F04040;
    }
  }
}

</style>
