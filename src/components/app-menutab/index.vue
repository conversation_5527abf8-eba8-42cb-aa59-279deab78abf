<template>
  <app-second-menu :list="list" :onlyName="onlyName"></app-second-menu>
</template>
<script>
  export default {
    name: 'app-menutab',
    props: {
      menus: {
        type: Array,
        default: function() { return [] }
      },
      onlyName: {
        type: String,
        default: ''
      }
    },
    computed: {
      list() {
        return (this.menus || []).map(v => ({ name: v.name, subpath: v.index }))
      }
    },
    methods: {

    }

  }

</script>
<style lang="scss" scoped>
</style>
