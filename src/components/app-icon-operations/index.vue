/**
* @Description:
* <AUTHOR>
* @date 2019/8/7 11:29
*/
<template>
  <el-dropdown @visible-change="visibleChange" :trigger="trigger" :placement="placement" :style="wrapperStyle" v-if="(list && list.length > 0) && !disabled">
    <span class="__dropdownBtn app-icon-operations" :class="[{flex: flexC}]">
      <i :style="{fontSize:fontSize}" :class="[icon, { 'icon-flex': iconFlex }]"></i>
      <slot><span class="title" v-if="title">{{title}}</span></slot>
      <i class="__arrowIcon" :class="arrowIcon"></i>
    </span>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item v-for="(item,index) in list" :key="index" @click.native.stop="clickMenu(item)">
        {{item.name}}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
  <span v-else class="app-icon-operations"
        :class="[(grayIcon?'__dropdownBtn iconGray hoverblue':'__dropdownBtn hoverblue'),defaultBlue ? 'default-blue' : '', {disabled: disabled},{flex: flexC}, {'icon-theme2':themeV2}]"
        @click="clickOneMenu">
        <slot name="icon">
          <i :class="[icon, { 'icon-flex': iconFlex }]" v-if="icon" :style="{fontSize:fontSize}"></i>
        </slot>
        <slot>
          <span class="title" v-if="title">{{title}}</span>
        </slot>
        <template v-if="suffixIcon">
          <i class="suffixIcon" :class="suffixIcon"></i>
        </template>
  </span>
</template>

<script>
  export default {
    name: 'app-icon-operations',
    props: {
      clickStopPropagation: { type: Boolean, default: false },
      list: { type: Array },
      title: { type: String },
      fontSize: { type: String },
      icon: { default: 'iconfont icon-daochu', type: String },
      suffixIcon: { default: '', type: String },
      wrapperStyle: {
        default: function() {
          return { 'line-height': '20px' }
        }
      },
      trigger: { default: 'click', type: String },
      placement: { default: 'bottom-start', type: String },
      disabled: { default: false, type: Boolean },
      grayIcon: { default: false, type: Boolean },
      zhugeText: {
        type: String,
        default: ''
      },
      notZhuge: { default: false, type: Boolean },
      flexC: { default: false, type: Boolean },
      iconFlex: { default: false, type: Boolean },
      defaultBlue: { default: false, type: Boolean },
      themeV2: { default: false, type: Boolean }
    },
    data: function() {
      return {
        isVisible: false
      }
    },
    computed: {
      arrowIcon: function() {
        return !this.isVisible ? 'el-icon-arrow-down' : 'el-icon-arrow-up'
      }
    },
    methods: {
      visibleChange: function(val) {
        this.isVisible = val
      },
      clickMenu(item) {
        if (this.$el.innerText) {
          $util.zhugeTrackThreeoLevel(this.$el.innerText.trim(), item.name.trim(), $util.getCurrentPageTitle(this))
        }
        if (item.onclick) {
          item.onclick(item)
        } else {
          this.$emit('clickbtn', item)
        }
      },
      clickOneMenu(e) {
        if (!this.notZhuge && (this.zhugeText || this.$el.innerText)) {
          $util.zhugeTrackTwoLevel((this.zhugeText || this.$el.innerText).trim(), $util.getCurrentPageTitle(this))
        }
        if (!this.disabled) {
          this.$emit('clickbtn')
        }
        if (this.clickStopPropagation) {
          e.stopPropagation()
        }
      }
    }
  }

</script>

<style scoped lang="scss">
  @import "../../styles/common";


  .app-icon-operations {
    &.icon-theme2 {
      height: 22px;
      line-height: 22px;
      padding: 0px 5px;
      ::v-deep {
        .iconfont{
          color: #bbb;
        }
      }
      &:hover{
        background: #F2F8FE;
        ::v-deep {
          .iconfont{
            color: #128bed;
          }
        }
      }
    }
  }
  .__dropdownBtn {
    font-size: 14px;
    color: #333;
    cursor: pointer;

    .suffixIcon {
      display: inline-block;
      vertical-align: middle;
    }

    .isNewsIcon{
      vertical-align:unset;
      margin-left: -2px;
      margin-right: -2px;
    }

    &.flex {
      display: flex;
      align-items: center;

      i + .title {
        margin-left: 5px;
      }

      .title + .suffixIcon {
        //margin-left: 2px;
      }
    }

    &.disabled {
      cursor: not-allowed;
      color: #C4C5C8;

      .iconfont {
        color: #C4C5C8;
      }

      &:hover {
        color: #C4C5C8;
      }
    }

    .iconfont {
      color: $color-primary
    }

    .iconGray .__arrowIcon {
      color: #C4C5C8;
    }

    &:hover {
      color: $color-primary;
    }

    .iconRight5 {
      margin-right: 5px;
    }
  }

  .size14 {
    font-size: 14px;
  }

  .size12 {
    font-size: 12px;
  }



  .iconGray {
    .iconfont {
      color: #999999;
    }


    &:hover {
      .iconfont {
        color: $color-primary;
      }
    }
  }

  .iconGray2 {
    .iconfont {
      color: #bbb;
    }


    &:hover {
      .iconfont {
        color: $color-primary;
      }
    }
  }

  .default-blue {
      color: #128BED;
    .iconfont {
      color: #128BED;
    }
    &:hover {
      color: $color-primary-hover;
      .iconfont {
        color: $color-primary-hover;
      }
    }
  }

  .icon-flex {
    display: flex;
    align-items: center;

    &.font-14 {
      width: 14px;
      height: 14px;
    }
  }
</style>
