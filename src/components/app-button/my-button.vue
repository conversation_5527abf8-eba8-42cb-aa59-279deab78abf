<template>
  <el-button :autofocus="autofocus" :size="size" ref="appBth" :type="type" :disabled="disabled" :loading="loading" :icon="icon"
             @click="clickBtn" :plain="plain" :class="btnClasses">
    <slot />
  </el-button>
</template>

<script>
  export default {
    name: 'app-button',
    props: {
      size: { default: 'small', type: String },
      type: { default: 'primary', type: String }, // 常用： primary:蓝色背景白色字 textblue：蓝色边框和字，白色背景 normal：白色背景，灰色边框
      disabled: { default: false, type: Boolean },
      loading: { default: false, type: Boolean },
      icon: { default: '', type: String },
      plain: { default: false, type: Boolean },
      plainHover: { default: false, type: Boolean },
      zhugeText: { default: '' },
      notZhuge: { default: false, type: Boolean },
      yellow: { default: false, type: Boolean },
      plainYellow: { default: false, type: Boolean },
      autofocus: { default: false, type: Boolean },
      notResetHeight: { default: false, type: Boolean }
    },
    data() {
      return {
        btnDebounceState: false
      }
    },
    computed: {
      btnClasses() {
        let cls = ''
        if (this.yellow) {
          cls += ' app-btn-yellow '
        }
        if (this.plainYellow) {
          cls += ' app-btn-plain-yellow '
        }
        if (this.plainHover && (this.type === 'primary' || !this.type) && this.plain) {
          cls += ' primary-plain-container '
        }
        if (this.notResetHeight) {
          cls += ' height-common'
        }

        return cls
      }
    },
    methods: {
      clickBtn(e) {
        if (this.btnDebounceState) {
          return
        }
        this.btnDebounceState = true
        setTimeout(() => {
          this.btnDebounceState = false
        }, 200)
        let pageTitle = $util.getCurrentPageTitle(window.AppRuntimeContext.instance)
        this.$emit('click', undefined, e)
        if (this.notZhuge) return
        if ((this.zhugeText || this.$el.innerText) && pageTitle) {
          try {
            $util.zhugeTrackTwoLevel((this.zhugeText || this.$el.innerText).trim(), pageTitle)
          } catch (e) {}
        }
      }
    }
  }

</script>

<style scoped lang="scss">
  @import "../../styles/common";

  @mixin buttonStyle($color, $hoverColor, $borderColor, $hoverBorderColor, $backgroundColor, $hoverBackgroundColor) {
    color: $color;
    border-color: $borderColor;
    background-color: $backgroundColor;

    &.button--hover:not(.is-disabled),&:hover:not(.is-disabled) {
      border-color: $hoverBorderColor;
      background-color: $hoverBackgroundColor;
      color: $hoverColor;
    }
  }

  @mixin buttonHeight($height) {
    padding-top: 0;
    padding-bottom: 0;
    height: $height;
    line-height: $height - 2px;
  }

  .el-button--small {
    @include buttonHeight(30px);
    &.height-common {
      @include buttonHeight(32px);
    }
  }

  .el-button--mini {
    @include buttonHeight(26px);
  }

  .el-button--normal,
  .el-button--normal:focus {
    @include buttonStyle(#333333, #128bed, #D8d8d8, #128bed, white, white);
  }

  .el-button--textblue,
  .el-button--textblue:focus {
    @include buttonStyle(#128bed, white, #128bed, #128bed, white, #128bed);
  }

  .el-button--text:hover,
  .el-button--text:focus {
    color: $global-color-priamry-hover;
  }

  .el-button--danger.is-plain {
    color: $global-color-danger;
    background: white;
    border-color: $global-color-danger;

    &:hover {
      color: $global-color-danger-hover;
      border-color: $global-color-danger-hover;
    }
  }

  .el-button--danger.fill:hover,
  .el-button--danger.fill:focus {
    background: $global-color-danger-hover;
    border-color: $global-color-danger-hover;
  }

  // .el-button--large {
  //   @include buttonHeight(50px);
  // }

  .primary-plain-container {
    background: white;
    color: $global-color-priamry-hover;
    border-color: $global-color-priamry-hover;
  }

  .app-btn-yellow {
    color: #fff;
    background: #ff722d;
    border-color: #ff722d;

    &:hover {
      background: #ff874c;
      border-color: #ff874c;
    }
  }

  .app-btn-plain-yellow {
    &:hover {
      color: #ff874c;
      border-color: #ff874c;
    }
  }
  .el-button.is-plain:focus:not(:hover),
  .el-button.is-plain:active:not(:hover),
  .el-button.is-plain:visited:not(:hover) {
    border-color: #D8d8d8;
    color: #666666;
  }

  .el-button.is-disabled.is-plain {
    border-color: #D8D8D8;
    background: #FAFAFA;
    color: #BBBBBB;
  }
</style>
