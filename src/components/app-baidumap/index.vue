<template>
  <app-popup :title="title" :options="popupOptions" :buttonOptions="[{ name: '关闭', type: 'primary' }]" @closed="closed" zIndex="9999999">
    <div id="popBaiduMapId" style="height: 500px;"></div>
  </app-popup>
</template>

<script>
import { loadOnlyBaiduMapApi } from '../../utils/load-static-files'
export default {
  name: 'app-baidumap',
  data() {
    return {
      popupOptions: {
        width: '1000px'
      }
    }
  },
  computed: {
    title() {
      return this.options.title || '完整地图'
    }
  },
  methods: {
    closed() {
      this.$modal.close()
    },
    setPoint (map, point, infoWindow) {
      map.addControl(new BMap.NavigationControl()) // 添加平移缩放控件
      map.addControl(new BMap.ScaleControl()) // 添加比例尺控件
      map.addControl(new BMap.OverviewMapControl()) // 添加缩略地图控件
      map.enableScrollWheelZoom() // 启用滚轮放大缩小
      map.enableDragging()
      map.addControl(new BMap.MapTypeControl())

      map.centerAndZoom(point, 16) // 百度地图API功能
      map.addOverlay(new BMap.Marker(point))
      map.openInfoWindow(infoWindow, point) // 开启信息窗口
      setTimeout(function() {
        map.panTo(point)
      }, 300)
    }
  },
  mounted() {
    loadOnlyBaiduMapApi().then(() => {
      this.$nextTick(() => {
        let city = this.options.city || '中国'
        if (city === '香港特别行政区') {
          city = ''
        }
        let map = new BMap.Map('popBaiduMapId') // 百度地图API功能
        let infoWindow = new BMap.InfoWindow(this.address) // 创建信息窗口对象
        if (this.options?.lng && this.options?.lat) {
          this.setPoint(map, new BMap.Point(this.options.lng, this.options.lat), infoWindow)
          return
        }
        let myGeo = new BMap.Geocoder() // 创建地址解析器实例
        myGeo.getPoint(this.address, (point) => { // 将地址解析结果显示在地图上,并调整地图视野
          // console.log('baiduMap-Api-result:', point)
          if (point) {
            this.setPoint(map, point, infoWindow)
          } else {
            this.$message.error('无法在地图上找到该企业地址')
          }
        }, city)
      })
    })
  }
}

</script>

<style scoped lang="scss">

</style>
<style lang="scss">
#popBaiduMapId {
  .BMap_bubble_content {
    padding-top: 20px;
  }
}

</style>
