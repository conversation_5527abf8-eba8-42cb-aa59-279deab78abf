<template>
  <el-popover
    placement="bottom-end"
    popper-class="header-user-info-pop no-effect-zIndex"
    width="250"
    v-model="visible"
    trigger="hover"
    content=""
    @show="handleFetchUserInfo"
  >
    <drop-menu ref="dropMenu"></drop-menu>
    <div slot="reference" class="header-userinfo-wrapper" :class="[{isOld}]">
      <div class="img-body-container" :class="[{visible: visible}]" @click="handleAvatarClick">
        <img :src="avatar" width="30px"/>
        <span class="hover-label el-icon-more"></span>
      </div>
    </div>
  </el-popover>
</template>

<script>
import localstorage from '@/utils/localstorage'
import { requestService } from '../../../services/broswer-service'
import moment from 'moment'

export default {
  components: {
    dropMenu: () => import('../app-drop')
  },
  props: {
    isOld: { default: false, type: <PERSON>ole<PERSON> }
  },
  data() {
    return {
      avatar: '/img/img-default-head.png',
      visible: false
    }
  },
  created() {
    this.getUserInfo()
    window.AppRuntimeContext.eventBus.$on(window.AppRuntimeContext.eventBusEvents.PERSONAL_MODIFY_AVATAR, (avatar) => {
      this.avatar = avatar
    })
  },
  methods: {
    handleFetchUserInfo () {
      const user = localstorage.fetchUser()
      return requestService.getUserHoverInfo({}).then(res => {
        if (res.status !== '200') {
          return
        }
        user.endDate = moment(res.result.endDate).format('YYYY-MM-DD')
        localstorage.saveUser(user)
        this.$refs?.dropMenu?.reset?.()
      })
    },
    handleAvatarClick() {
      if (this.$route.name === 'account-info') {
        return
      }
      this.isNewVersion && this.$store.dispatch('sidebar/setCollapse', false)
      this.$router.push({
        name: 'account-info'
      })
    },
    getUserInfo() {
      const user = localstorage.fetchUser()
      if (user.photo) {
        this.avatar = user.photo
      } else {
        this.avatar = '/img/img-default-head.png'
      }
      return user.accessToken || ''
    }
  }
}
</script>

<style scoped lang="scss">
.header-userinfo-wrapper {
  display: inline-flex;
  height: 50px;
  padding: 0 15px;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &.isOld {
    .img-body-container {
      .hover-label {
        font-size: 16px;
      }
    }
  }

  .img-body-container {
    position: relative;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    overflow: hidden;

    .hover-label {
      display: none;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      color: white;
      font-size: 18px;
      text-align: center;
      line-height: 30px;
      background: rgba(#000000, 0.4);
      border-radius: 50%;

      .icon-gengduo {
        width: auto;
      }
    }

    &.visible,&:hover {
      .hover-label {
        display: block;
      }
    }
  }
}
</style>
<style lang="scss">
.el-popover.header-user-info-pop {
  padding: 0;
  box-shadow: rgba(0, 0, 0, 0.3) 0px 5px 12px;
  //left: unset!important;
  //top: 50px!important;
  //right: 10px!important;
}
</style>
