import dropMenu from '../app-drop'
import newDropMenu from './app-drop'
import AppTaskList from '../app-task-list'
import AppBall from '../app-ball'
import searchSmall from '../app-search/search-small'
import mixin from './mixins'
import videoPop from '../../routes/welcome/components/video-pop'
import localstorage from '../../utils/localstorage'
import { mapState } from 'vuex'
import { refershMessage } from '../../utils/timer'
import { clearSessionWhenCutVersion } from '@/utils/storage-operate-util'
import { getCurrentUserMainPage } from '../../utils/router-utils'

export default {
  mixins: [mixin],
  components: {
    dropMenu,
      newDropMenu,
      AppTaskList,
      AppBall,
      searchSmall,
      cascaderMenu: () => import('../app-cascader'),
      videoPop,
      advanceEntry: () => import('./advance-entry'),
      headerUserInfo: () => import('./header-user-info')
  },
  data() {
    // let docurl = `https://pro-files.qichacha.com/open/docs/企业信息搜索系统操作手册.docx?v=${(Math.random() * 10000 + '').fixed(0)}`

    return {
      // docurl,
      notQCC: !__QCC__,
      isOnlyQCC: (__QCC__ && !__PLUGIN__),
      isYQWXB: __YQWXB__,
      avatar: '',
      accessToken: '',
      alreadyYouKnow: $util.isNotPermission4OverseaUser(),
      infoKey: Date.now(),
      isYqwxb: __YQWXB__
    }
  },
  created() {
    this.accessToken = this.getUserInfo()
    window && window.addEventListener('storage', this.onLocalStorageChangedListener)
    window.AppRuntimeContext.eventBus.$on(window.AppRuntimeContext.eventBusEvents.PERSONAL_MODIFY_AVATAR, (avatar) => {
      this.avatar = avatar
    })
  },
  beforeDestroy() {},
  mounted: function() {
    this.scroll()
  },
  methods: {
    onLocalStorageChangedListener(ev) {
      // removeItem同样触发storage事件，此时ev.newValue为空
      if (ev.key === 'userStorage' && ev.newValue) {
        let accessToken = this.getUserInfo()
        if (accessToken !== this.accessToken) { // 登录信息变化了,需要刷新
          this.$refs.userinfoRef && this.$refs.userinfoRef.reset()
          refershMessage()
          this.$store.dispatch('taskList/update')
          this.getUserInfo()
          this.infoKey = Date.now()
        }
      }
      try {
        let userInfo = JSON.parse(ev.newValue)
        if ((userInfo.menuVersion !== 'V2' && this.isNewVersion) || (userInfo.menuVersion === 'V2' && !this.isNewVersion)) {
          setTimeout(() => {
            clearSessionWhenCutVersion()
            window.location.reload()
          }, 2500)
        }
      } catch (e) {}
    },
    getUserInfo() {
      const user = localstorage.fetchUser()
      if (user.photo) {
        this.avatar = user.photo
      } else {
        this.avatar = require('./img/icon-defaulthead.png')
      }
      window.AppRuntimeContext.eventBus.$emit(window.AppRuntimeContext.eventBusEvents.PERSONAL_MODIFY_AVATAR, this.avatar)
      return user.accessToken || ''
    },
    gotoMain() {
      // let user = localstorage.fetchUser()
      /**
       * 1、老用户，未设置默认页的
       * 2、老用户，已设置默认页的
       * 3、新用户，未设置默认页的
       * 4、新用户，已设置默认页的
       */
      // if (user && user.menuHomeMode === 'NEW_HOME') {
      //   this.$router.push('/login')
      // } else {
      //   this.$router.push('/welcome')
      // }
      this.$router.push(getCurrentUserMainPage())
      $util.zhugeTrackThreeoLevel('logo点击', '顶部模块', '点击菜单')
    },
    triggerData() {
      $util.zhugeTrackOneLevel('接口API')
      this.$router.push('/inner-datainterface')
    },
    showMenu() {
      if (this.headerMenus && this.headerMenus.length) {
        this.$emit('update:show', true)
      }
      this.isShowMenu = true
    },
    scroll() {
      let that = this
      $(window).scroll(function() {
        if (that.headerMenus && that.headerMenus.length) {
          that.$emit('update:show', false)
        }
        that.isShowMenu = false
      })
    },
    showDrop() {
      this.hidePopper()
      this.isShowDrop = true
    },

    hideDrop(e) {
      if ($(e.toElement).closest('div.line').length) {
        return false
      }
      this.isShowDrop = false
      this.resetPopper()
    },

    triggerHelp(show = true) {
      const { href } = this.$router.resolve({
        path: '/help-center'
      })
      window.open(href, '_blank')
      $util.zhugeTrackOneLevel('帮助中心')
    },
    triggerBuy(show = true) {
      $util.enterBuyCombo()
      $util.zhugeTrackOneLevel('产品套餐')
    },

    triggerTask(show = true) {
      this.$refs.task.showTask(show)
      $util.zhugeTrackOneLevel('任务列表')
    },
    hidePopper() {
      let popList = document.getElementsByClassName('el-popper')
      for (const element of popList) {
        if (element.style['z-index']) {
          this.operateZIndexDom.push({ dom: element, zIndex: element.style['z-index'] })
          element.style['z-index'] = '0'
        }
      }
    },
    resetPopper() {
      this.operateZIndexDom.forEach(row => {
        $(row.dom).css('z-index', row.zIndex)
      })
      this.operateZIndexDom = []
    },

    showMessage(notZG) {
      if (!this.isShowMessage) {
        this.hidePopper()
        if (!notZG) { $util.zhugeTrackOneLevel('提醒') }
      }
      this.isShowMessage = true
    },

    triggerJwBuy(show = true) {
      this.$router.push({ name: 'buy-combo-login' })
      $util.zhugeTrackOneLevel('产品套餐')
    },
    handleAvatarClick() {
      this.$router.push({
        name: 'account-info'
      })
    }
  },
  computed: {
    ...mapState('appState', {
      backHomeTextVisable: state => state.showHomeButtonFlag
    }),
    backHomeVisable() {
      if (__YQWXB__) return this.backHomeTextVisable || this.$route.path.startsWith('/setting/')
      return this.backHomeTextVisable || ['data-interface'].includes(this.$route.name)
    },
    entryVisable() {
      return !['welcome'].includes(this.$route.name)
    },
    isUseHeaderBgColor: function() {
      return this.$route.name !== 'welcome' || this.alreadyYouKnow
    },
    showInterface: function() {
      return __QCC__ && !__PLUGIN__ && this.rediectOption.showDataInterface && !$util.getHasNoPermission('rvrs:api:disable_api_call')
    },
    showHelpCenter: function() {
      return __QCC__ && !__PLUGIN__ && this.rediectOption.showHelpCenter
    },
    shortUserName() {
      if (this.isJSJW) {
        return (this.loginId || '').split('-')[1]?.substr(0, 8)
      }
      return this.userName.length <= 11 ? this.userName : (this.userName.substr(0, 9) + '...')
    },
    showJwBuy() {
      const { name } = this.$route
      return this.isJSJW && (name === 'welcome')
    }
  }
}
