<template>
  <div id="appNormalheaderId">
    <div :class="['header-box',!isUseHeaderBgColor && 'without-bg-color',!isUseHeaderBgColor && top > 50 && 'hover-bg-color']">
      <div class="header-left">
        <div class="left-menu header-section" @mouseover="showMenu()">
          <div class="iconfont icon-caidan"></div>
        </div>
        <div class="left-line"></div>
        <cascader-menu v-if="rediectOption.showMenu" class="cascader-box" v-show="isShowMenu" :show.sync="isShowMenu"></cascader-menu>
        <img :src="logo" :height="logoHeight" @click="gotoMain" style="cursor: pointer">
        <search-small v-if='$route.name !== "welcome" && rediectOption.showSearch' :companyName="companyName"></search-small>
      </div>

      <div class="header-right">
<!--        <template v-if="['__HZJW__'].includes(ENV)">-->
<!--          <a style="font-size:14px;color:white;margin-right: 15px;" target="_blank" :href="docurl">操作手册</a>-->
<!--        </template>-->
        <video-pop class="header-task header-section" color="white" theme="smallIcon" v-if="isJSJW && !['__HZJW__'].includes(ENV)"
                   url='https://pro-files.qichacha.com/open/sec_video/ce6950f5e4174bfa12e55440a4040873/sub_system_app_intro_0dc652fbecb4452e8851a883d3d5cc8d.mp4?v=3.0'>
          <span style="font-size:14px;color:white;margin-left:5px">使用指南</span>
        </video-pop>

        <div v-if="showInterface" class="header-task header-section" @click.stop='triggerData'>
          <div class="iconfont icon-shujujiekou1"></div>
          <div class="header-task-title">数据接口</div>
        </div>
        <div v-if="showHelpCenter" class="header-task header-section" @click.stop='triggerHelp'>
          <div class="iconfont icon-bangzhuzhongxin1"></div>
          <div class="header-task-title">帮助中心</div>
        </div>
        <div class="header-task header-section" @click.stop='triggerTask' v-if="rediectOption.showTask && !$globalutil.getHasNoPermission('rvrs:api:disable_corp_detail_add_to')">
          <div class="iconfont icon-renwuliebiao"></div>
          <div class="header-task-title">任务列表</div>
          <span class="el-badge__content is-fixed" v-if="taskCount>0">{{taskCount > 99 ? '99+' : taskCount}}</span>
          <app-ball></app-ball>
        </div>
        <div class="header-message header-section" @mouseenter="showMessage()" @mouseleave="hideMessage()" v-if="rediectOption.showMessage">
          <div class="message-top" @click="messageClick">
            <div class="iconfont icon-xiaoxiicon"></div>
            <div class="header-message-title">提醒</div>
            <span class="el-badge__content is-fixed" v-if="messageTotalCount>0">{{messageTotalCount > 99 ? '99+' : messageTotalCount}}</span>
          </div>
          <app-message-list ref="appMessageList" class="message-box" v-show="isShowMessage"></app-message-list>
        </div>
        <div class="drop-user-box header-section" @mouseenter="showDrop()" @mouseleave="hideDrop">
          <div class="user-box">
            <div class="name-box">{{shortUserName}}</div>
            <i class="iconfont shouqi-box icon-xiala" :class="[{ 'icon-shouqi':isShowDrop === true }]"></i>
          </div>
          <drop-menu class="drop-menu-box" v-show="isShowDrop"></drop-menu>
        </div>
      </div>
    </div>
    <app-task-list ref="task"></app-task-list>
  </div>

</template>

<script src="./component.js"></script>
<style scoped lang="scss" src="./style.scss"></style>
