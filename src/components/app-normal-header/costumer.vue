<template>
  <div id="appNormalheaderId">
    <div :class="['header-box',!isUseHeaderBgColor && 'without-bg-color',!isUseHeaderBgColor && top > 50 && 'hover-bg-color']">
      <div class="header-left">
        <div class="left-menu header-section" @mouseover="showMenu()">
          <div class="iconfont icon-caidan"></div>
        </div>
        <div class="left-line"></div>
        <!-- <cascader-menu class="cascader-box" v-show="isShowMenu" :show.sync="isShowMenu"></cascader-menu> -->
        <cascader-menu v-if="rediectOption.showMenu" class="cascader-box" v-show="isShowMenu" :show.sync="isShowMenu"></cascader-menu>
        <img :src="logo" :height="logoHeight" @click="gotoMain" style="cursor: pointer">
        <!-- <app-company-search v-if='$route.name !== "welcome" && rediectOption.showSearch'></app-company-search> -->
        <search-small class="customer-header-search" v-if='$route.name !== "welcome"   :companyName="companyName" && rediectOption.showSearch'></search-small>
      </div>

      <div class="header-right">
        <!--        <div class="header-task header-section" @click.stop='triggerTask' v-if="rediectOption.showTask && !$globalutil.getHasNoPermission('rvrs:api:disable_corp_detail_add_to')">-->
        <!--          <div class="iconfont icon-renwuliebiao"></div>-->
        <!--          <div class="header-task-title">任务列表</div>-->
        <!--          <span class="el-badge__content is-fixed" v-if="taskCount>0">{{taskCount}}</span>-->
        <!--          <app-ball></app-ball>-->
        <!--        </div>-->
        <div class="header-message header-section" @mouseenter="showMessage()" @mouseleave="hideMessage()" v-if="rediectOption.showMessage">
          <div class="message-top" @click="messageClick">
            <div class="iconfont icon-xiaoxiicon"></div>
            <div class="header-message-title">提醒</div>
            <span class="el-badge__content is-fixed" v-if="messageTotalCount>0">{{messageTotalCount}}</span>
          </div>
          <app-message-list ref="appMessageList" class="message-box" v-show="isShowMessage"></app-message-list>
        </div>
        <div class="drop-user-box header-section" @mouseenter="showDrop()" @mouseleave="hideDrop">
          <div class="user-box">
            <div class="name-box">{{shortUserName}}</div>
            <i class="iconfont shouqi-box icon-xiala" :class="[{ 'icon-shouqi':isShowDrop === true }]"></i>
          </div>
          <drop-menu class="drop-menu-box" v-show="isShowDrop"></drop-menu>
        </div>
      </div>
    </div>
    <app-task-list ref="task"></app-task-list>
  </div>

</template>

<script src="./component.js"></script>
<style scoped lang="scss" src="./style.scss"></style>
