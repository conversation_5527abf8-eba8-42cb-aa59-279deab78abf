import localStorage from '../../utils/localstorage'
import { mapGetters, mapState } from 'vuex'
import config from '../../config/client'
import storageUtils from '../../utils/storage-operate-util'
import store from '../../store'
import appMessageList from '../app-message-list'
import { requestService } from '@/services/broswer-service'
import { clearSessionWhenCutVersion } from '@/utils/storage-operate-util'
import { notSupportOldVersionMenus } from '../../data/menu/switch-version-menusbehavior'
import { runtimeDynamicConfig } from '../../config/runtime-dynamic-config'

export default {
  data() {
    let user = localStorage.fetchUser()
    let isEnableMenuSwitchFlag = user.enableMenuSwitchFlag
    return {
      isEnableMenuSwitchFlag: isEnableMenuSwitchFlag && __QCC__ && !__PLUGIN__,
      logoHeight: config.customize.logoHeight || 30,
      isShowMenu: false,
      isShowDrop: false,
      userName: user?.userName,
      loginId: user?.loginId || '',
      isShowMessage: false,
      isClickMessage: false,
      operateZIndexDom: []
    }
  },
  props: {
    rediectOption: { // 标识该头部是否使用在新开的窗口
      type: Object,
      default: () => {
        return {
          showSearch: true,
          showDataInterface: true,
          showHelpCenter: true,
          showTask: true,
          showMessage: true,
          showMenu: false
        }
      }
    },
    companyName: { default: '', type: String },
    userFrame: { default: false, type: Boolean },
    trigger: { default: 'hover', type: String }
  },
  watch: {
    downloadMessageIds() {
      this.changeMessageStatus()
    },
    taskMessageCount() {
      if (this.isPush) {
        this.changeTaskMessageStatus()
      }
    },
    '$route.name'(val) {
      if (this.updateLogo) {
        this.$nextTick(() => {
          this.updateLogo()
        })
      }
    }
  },
  created() {
    this.updateLogo()
    window.AppRuntimeContext.eventBus.$on(window.AppRuntimeContext.eventBusEvents.OPEN_NOTIFICATION_POP, (is) => {
      if (is === undefined || is) {
        this.showMessage()
      }
    })
    window.AppRuntimeContext.eventBus.$on(window.AppRuntimeContext.eventBusEvents.CLOSE_NOTIFICATION_POP, (is) => {
      if (is === undefined || is) {
        this.hideMessage()
      }
    })
  },
  mounted: function() {
    this.scroll()
  },
  methods: {
    updateLogo() {
      if (this.$route.name === 'welcome') {
        if (__DEMOPLATFORM02__) {
          const { runtimeDynamicConfig } = require('../../config/runtime-dynamic-config')
          this.logo = runtimeDynamicConfig.logoAuthorized || config.customize.logoAuthorizedWelcome || config.customize.logo
        } else {
          this.logo = config.customize.logoAuthorizedWelcome || config.customize.logo
        }
      } else {
        if (__QCC__ && this.isNewVersion) {
          this.logo = require('@/assets/images/logo-pc-new2.png')
        } else {
          this.logo = __DEMOPLATFORM02__ ? runtimeDynamicConfig.logoAuthorized : config.customize.logo
        }
      }
    },
    gotoMain() {
      this.$router.push('/welcome')
    },
    showMenu() {
      if (this.headerMenus && this.headerMenus.length) {
        this.$emit('update:show', true)
      }
      this.isShowMenu = true
    },
    scroll() {
      let that = this
      $(window).scroll(function() {
        if (that.headerMenus && that.headerMenus.length) {
          that.$emit('update:show', false)
        }
        that.isShowMenu = false
      })
    },
    hidePopper() {
      let popList = document.getElementsByClassName('el-popper')
      for (const element of popList) {
        if (element.style['z-index']) {
          this.operateZIndexDom.push({ dom: element, zIndex: element.style['z-index'] })
          element.style['z-index'] = '0'
        }
      }
    },
    resetPopper() {
      this.operateZIndexDom.forEach(row => {
        $(row.dom).css('z-index', row.zIndex)
      })
      this.operateZIndexDom = []
    },

    showMessage(notZG) {
      if (!this.isShowMessage) {
        this.hidePopper()
        if (!notZG) { $util.zhugeTrackOneLevel('提醒') }
      }
      this.isShowMessage = true
    },

    hideMessage() {
      if (this.downloadMessageIds !== '') {
        const key = 'kDownloadMessageIds'
        storageUtils.setLocalStorageWidthCachetime(key, this.downloadMessageIds)
      }
      this.isShowMessage = false
      this.resetPopper()
    },
    messageClick() {
      if (this.$refs.appMessageList) {
        this.$refs.appMessageList.lookAllClick()
      }
    },

    changeMessageStatus() {
      const key = 'kDownloadMessageIds'
      let ids = storageUtils.getLocalStorageWidthCachetime(key)

      if (this.downloadMessageIds !== '' && this.downloadMessageIds !== ids) {
        this.isClickMessage = true
        this.isShowMessage = true
        this.hidePopper(true)
      } else {
        this.isClickMessage = false
        this.isShowMessage = false
        this.resetPopper()
      }
    },

    changeTaskMessageStatus() {
      if (this.isPush) {
        store.commit('messageList/setIsPush', false)
      }

      if (this.taskMessageCount !== 0) {
        this.isClickMessage = true
        this.isShowMessage = true
      } else {
        this.isClickMessage = false
        this.isShowMessage = false
      }
    },

    changeSysVersion(version) {
     const exeSwitch = () => {
       $util.zhugeTrackTwoLevel(version === 'V2' ? '切换新版' : '返回旧版', '新旧版本切换按钮')
       requestService.checkoutSysVersion({ menuVer: version || 'V2' })
         .then(() => {
           try {
             clearSessionWhenCutVersion()
             const user = localStorage.fetchUser()
             user.menuVersion = version || 'V2'
             localStorage.saveUser(user)
             this.$message.success('正在切换，请稍后...')
             setTimeout(() => {
               window.location.reload()
             }, 1000)
           } catch (e) {
             this.$message.warning('切换失败，请重新尝试')
           }
         })
     }
      if (version !== 'V2' && notSupportOldVersionMenus.includes(this.$route.name)) {
        this.$uiService.alertWarning(`旧版不支持${this.$route.meta.title}功能，确定切换为旧版吗？`)
          .then(exeSwitch)
      } else {
        exeSwitch()
      }
    }
  },
  computed: {
    ...mapGetters('taskList', ['taskCount']),
    ...mapGetters('messageList', ['messageCount', 'messageTotalCount', 'downloadMessageCount', 'downloadMessageIds', 'taskMessageCount', 'isPush']),
    ...mapState('appState', {
      headerMenus: state => state.headerMenus
    }),
    ...mapState('welcome', ['top']),
    showHelpCenter: function() {
      return __QCC__ && !__PLUGIN__ && this.rediectOption.showHelpCenter
    }
  },
  components: {
    appMessageList
  }
}
