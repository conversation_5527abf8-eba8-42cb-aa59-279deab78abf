<template>
  <div id="appNormalheaderId">
    <div :class="['header-box',!isUseHeaderBgColor && 'without-bg-color',!isUseHeaderBgColor && top > 50 && 'hover-bg-color']">
      <div class="header-left">
        <img :src="logo" :height="logoHeight" @click="gotoMain" style="cursor: pointer;margin-left: 8px">
        <search-small v-if='$route.name !== "welcome" && rediectOption.showSearch' :companyName="companyName"
                      :style="{'margin-left':isYQWXB?'50px':'8px'}"></search-small>
      </div>

      <div class="header-right fullHeight">
        <div class="advance-applications">
          <div class="header-task header-section nohover hover-icon" :class="[{'no-color':notQCC}]" @click.stop="changeSysVersion('V2')" v-if="isEnableMenuSwitchFlag">
            <span class="iconfont icon-xinjiubanben" style="font-size: 14px;margin-right: 5px;"></span> 体验新版
          </div>
          <div class="header-task header-section nohover hover-icon" :class="[{'no-color':notQCC}]" @click.stop="gotoMain" v-if="backHomeVisable">
            首页
          </div>
          <advance-entry v-if="entryVisable" style="margin-left: 15px;" />
        </div>
        <div v-if="showInterface" class="header-task header-section nohover hover-icon" :class="[{'no-color':notQCC}]" @click.stop='triggerData'>
          <el-popover placement="bottom" title="" width="78" popper-class="default-popover-tooltip tool-auto-width" trigger="hover" content="接口API">
            <div class="iconfont icon-shujujiekou1" slot="reference"></div>
          </el-popover>
        </div>
        <div v-if="showHelpCenter" class="header-task header-section nohover hover-icon" :class="[{'no-color':notQCC}]" @click.stop='triggerHelp'>
          <el-popover placement="bottom" title="" width="78" popper-class="default-popover-tooltip tool-auto-width" trigger="hover" content="帮助中心">
            <div class="iconfont icon-bangzhuzhongxin1" slot="reference"></div>
          </el-popover>
        </div>
        <div class="header-task header-section nohover hover-icon" :class="[{'no-color':notQCC}]"
             @click.stop='triggerTask' v-if="rediectOption.showTask && !$globalutil.getHasNoPermission('rvrs:api:disable_corp_detail_add_to') && !isYqwxb">
          <el-popover placement="bottom" title="" width="78" popper-class="default-popover-tooltip tool-auto-width" trigger="hover" content="任务列表">
            <div class="iconfont icon-renwuliebiao" slot="reference"></div>
          </el-popover>
          <span class="el-badge__content is-fixed" v-if="taskCount>0">{{taskCount > 99 ? '99+' : taskCount}}</span>
          <app-ball></app-ball>
        </div>
        <div class="header-message header-section nohover" @mouseenter="showMessage()" @mouseleave="hideMessage()" v-if="rediectOption.showMessage">
          <div class="message-top" :class="[{actived: isShowMessage},{'no-color':notQCC}]" @click="messageClick">
            <div class="iconfont icon-xiaoxiicon"></div>
            <span class="el-badge__content is-fixed" v-if="messageTotalCount>0">{{messageTotalCount > 99 ? '99+' : messageTotalCount}}</span>
          </div>
          <app-message-list ref="appMessageList" class="message-box" v-show="isShowMessage"></app-message-list>
        </div>
        <header-user-info is-old :key="infoKey"></header-user-info>
      </div>
    </div>
    <app-task-list ref="task"></app-task-list>
  </div>

</template>

<script src="./component.js"></script>
<style scoped lang="scss" src="./style.scss"></style>
<style lang="scss" scoped>
  .header-section {
    &.hover-icon {
      &:hover:not(.no-color) {
        .iconfont {
          color: #128bed;
        }
      }

      &.no-color:hover,
      .message-top:hover {
        .iconfont {
          opacity: 0.75;
        }
      }
    }

    .message-top:hover:not(.no-color),
    .message-top.actived:not(.no-color) {
      .iconfont {
        color: #128bed;
      }
    }

    .message-top.no-color:hover,
    .message-top.actived.no-color {
      .iconfont {
        opacity: 0.75;
      }
    }
  }

  .header-message .message-top .icon-xiaoxiicon,
  .header-task .iconfont {
    font-size: 16px;
  }

  .drop-user-box {
    .drop-menu-box {
      right: 10px;
    }

    img {
      border-radius: 50%;
      width: 30px;
      height: 30px;
    }
  }

</style>
