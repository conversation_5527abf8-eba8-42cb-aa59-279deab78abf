<template>
  <el-popover placement="bottom" v-if="menusList&&menusList.length" :width="menusList.length*100" trigger="hover" :visible-arrow="false"
              v-model="popoverVisable" popper-class="advance-detail-popover">
    <ul class="menu-wrapper">
      <li v-for="item in menusList" :key="item.title" :style="`width:${100/menusList.length}%`" class="menu-item" @click="openTarget(item)">
        <span :class="[item.icon, 'menu-icon', 'iconfont']"></span>
        <div class="text">{{item.title}}</div>
      </li>
    </ul>
    <div slot="reference" class="advance-entry-wrapper" :class="[{actived: popoverVisable},{'no-color':notQCC}]">
      应用
      <span class="hover-label el-icon-caret-bottom"></span>
    </div>
  </el-popover>
</template>

<script>
  import { mapState } from 'vuex'
  export default {
    data() {
      return {
        popoverVisable: false,
        notQCC: !__QCC__
      }
    },
    computed: {
      ...mapState('appState', {
        menusList: state => state.advancedEntryMenus
      })
    },
    methods: {
      openTarget(item) {
        $util.zhugeTrackOneLevel(`应用-${item.title}`)
        this.popoverVisable = false
        window.open(item.path)
      }
    }
  }

</script>

<style scoped lang="scss">
  .advance-entry-wrapper {
    font-size: 14px;
    color: white;
    cursor: pointer;
    transition: all linear 0.15s;
    line-height: 50px;

    &:hover:not(.no-color),
    &.actived:not(.no-color) {
      color: #128bed;

      .hover-label {
        transform: rotate(180deg);
      }
    }

    &.no-color:hover,
    &.no-color.actived {
      opacity: 0.75;

      .hover-label {
        transform: rotate(180deg);
      }
    }
  }

  .advance-detail-popover {
    padding: 0;

    .menu-wrapper {
      padding-top: 30px;
      color: #333333;

      .menu-item {
        width: 25%;
        display: inline-block;
        margin-bottom: 30px;
        text-align: center;
        cursor: pointer;

        .menu-icon {
          font-size: 24px;
          color: #128bed;
        }

        .text {
          margin-top: 10px;
        }

        &:hover {
          .text {
            color: #128bed;
          }
        }
      }
    }
  }

</style>

<style lang="scss">
  .el-popover.advance-detail-popover {
    padding: 0;
    margin-top: 0;
  }

</style>
