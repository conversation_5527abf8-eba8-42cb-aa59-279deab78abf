<template>
  <div class="drop-box">
    <div class="account">
      <div class="account__info">
        <a href="javascript:;" @click="hiddenSearchBody(personal)" class="account__name">{{ user.userName }}</a>
        <div class="account__type" v-if="user" :style="{ 'line-height': isQCCPRO_G ? '17px' : '' }">
          <template v-if="isQCCPRO_G">
            {{ user.mainUserFlag ? 'Primary' : 'Sub' }}
          </template>
          <template v-else>
            {{ user.mainUserFlag ? '主' : '子' }}账号
          </template>
        </div>
      </div>
      <div class="account__expire">
        <span v-if="isQCCPRO_G">Expiry Date：</span>
        <span v-else>到期日期：</span>
        <span class="account__expire-time" :class="{'expire': showExpireSign()}" v-if="user">{{ user.endDate }}</span>
        <a href="javascript:;" v-if="showExpireSign()" @click="handleAffairs">客户经理</a>
      </div>
    </div>
    <div class="menu">
      <div class="section-box" :class="[{isactive:currentIndex === index, 'exit': item.title === (isQCCPRO_G ? 'Log Out' : '安全退出')}]" v-for="(item,index) in dropList" :data="item" :key="item.id" @mouseenter="showItem(index)" @click="hiddenSearchBody(menuClick(item.title,item.func))">
        <div class="line" v-if="item.title === (isQCCPRO_G ? 'Log Out' : '安全退出')"></div>
        <div class="left-box"></div>
        <div class="iconfont" :class="[item.icon]"></div>
        <div class="section-title">{{item.title}}</div>
      </div>
    </div>
  </div>
</template>

<script src="./component.js"></script>
<style scoped lang="scss" src="./style.scss"></style>
