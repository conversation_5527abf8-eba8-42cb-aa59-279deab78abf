@import '../../../styles/common.scss';


.drop-box {
  display: flex;
  flex-direction: column;
  width: 250px;
  height: auto;
  background-color: #ffffff;
  color: #333333;
}

.menu {
  padding: 15px;

  .section-box:last-child {
    padding: 15px 0 0;
  }
}

.section-box {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  cursor: pointer;
  padding: 0 0 15px;
  /*background-color: red;*/

  &.exit {
    .iconfont {
      color: #999999;
    }

    .section-title {
      color: #999999;
    }

    &:hover {
      .iconfont {
        color: #128BED;
      }

      .section-title {
        color: #128BED;
      }
    }
  }

  .line {
    position: absolute;
    top: 0;
    height: 1px;
    width: 100%;
    background: #EEEEEE;
  }

  .left-box {
    //width: 3px;
    //height: 40px;
    // background-color: #128bed;

  }

  .section-icon {
    font-size: 14px;
  }

  .section-title {
    margin-left: 10px;
    font-size: 14px;
  }

  .iconfont {
    color: #128BED;
  }
}

.isactive {

  //background: rgba(255, 255, 255, 0.1);
  //background: $sidebar-item-hover-color;
  color: #128BED;

  .iconfont {
    color: #128BED;
  }

  .left-box {
    // background: $sidebar-item-hover-color;
    background-color: $base-main-color;
  }

}

.aq-line {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  // opacity: 0.1;
}

.account {
  background: #F3F9FD;
  padding: 15px;

  &__info {
    margin-bottom: 10px;
    font-size: 0;
  }

  &__type {
    width: 52px;
    height: 19px;
    border-radius: 10px;
    border: 1px solid #BBBBBB;
    font-size: 12px;
    color: #999999;
    text-align: center;
    display: inline-block;
    vertical-align: middle;
  }

  &__name {
    font-weight: bold;
    color: #333333;
    font-size: 16px;
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
    max-width: 158px;
    @include overrflow-with-ellipsis;
  }

  &__expire {
    color: #666666;
    font-size: 0;

    span {
      font-size: 12px;
    }

    a {
      font-size: 12px;
      margin-left: 10px;
      display: inline-block;
    }

    .expire {
      color: #F04040;
    }
  }
}
