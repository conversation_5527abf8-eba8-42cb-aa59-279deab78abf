import localStorage from '../../../utils/localstorage'
import userService from '../../../services/user'
import { mapActions, mapState } from 'vuex'
import { cookieStorage } from '../../../core/storage/cookie-storage'

export default {
  name: 'dropMenu',
  props: {
    data: Array
  },
  data() {
    return {
      isQCCPRO_G: __QCCPRO_G__,
      user: localStorage.fetchUser(),
      dropList: [
        ...($util.getIsMainuserAccount() ? [{
          func: 'sub/my_comp',
          icon: 'iconfont section-icon icon-shezhizhongxin',
          title: '设置中心'
        }] : []),
        {
          icon: 'iconfont section-icon icon-anquantuichu',
          title: '安全退出'
        }
      ],
      currentIndex: -1
    }
  },
  computed: {
    ...mapState('appState', {
      settingMenus: state => state.settingMenus
    })
  },
  created() {
    window.AppRuntimeContext.eventBus.$on(window.AppRuntimeContext.eventBusEvents.PERSONAL_MODIFY_USERNAME, (userName) => {
      this.user.userName = userName
    })
  },
  mounted() {
  },
  methods: {
    ...mapActions('functions', ['hiddenSearchBody']),
    reset() {
      this.user = localStorage.fetchUser()
    },
    showExpireSign () {
      return localStorage.showExpireSign(this.user)
    },
    showItem(index) {
      this.currentIndex = index
    },

    menuClick(title) {
      switch (title) {
        case '个人中心':
        case '设置中心':
        case 'Setting':
          if (__YQWXB__) {
            if (this.$route.name === 'user-management') {
              return
            }
            this.$router.push({ name: 'user-management' })
          } else {
            this.personal()
          }
          break
        case '我的下载':
          this.$router.push({ name: 'download-message' })
          break
        case '我的订单':
          this.$router.push({ name: 'order-manage-mine' })
          break
        case '安全退出':
        case 'Log Out':
          $util.clearCacheUserPerMissionList()
          this.logout()
          break
        default:
          break
      }
    },
    personal() {
      if (this.$route.name === 'account-info') {
        return
      }
      this.isNewVersion && this.$store.dispatch('sidebar/setCollapse', false)
      this.$router.push({ name: 'account-info' })
    },
    logout() {
      cookieStorage.clear('qccpro-client-id')
      cookieStorage.clear('qcc-guide-flag')
      cookieStorage.clear('qccpro-access-secret-key')
      cookieStorage.clear('qccpro-access-token')
      cookieStorage.clear('qccPhoneTrialApplyToken')
      cookieStorage.clear('qccpro-trial-account')
      cookieStorage.set('qccpro-logout-flag', 'Y')
      userService.logout({})
        .then(() => {
          localStorage.removeUser()
          sessionStorage.clear()
          window.location.href = $util.getLoginTargetUrl()
        })
        .catch(() => {
          localStorage.removeUser()
          sessionStorage.clear()
          window.location.href = $util.getLoginTargetUrl()
        })
    },
    handleAffairs () {
      $util.enterCustomerService()
    }
  },

  components: {}
}
