@import "../../styles/common.scss";
@import "../../styles/baseFlexAdapter.scss";

.header-box {
  width: 100%;
  min-width: 1250px;
  height: 50px;
  @include flex-def;
  @include flex-zBetween;
  @include flex-cCenter;
  background-color: $header-bg-color;
  color: $header-font-color;
  border-bottom: $navbar-border;
}

.welcome-header {
  .header-box {
    .header-section:not(.nohover):hover {
      background: rgba(255, 255, 255, 0.1);
    }

    border-bottom: transparent;
    color: #fff;
  }

  .header-box:not(.nohover):hover {
    background: rgba(0, 0, 0, 0.6);
  }
}

.without-bg-color {
  background: none;
}

.hover-bg-color {
  background: rgba(0, 0, 0, 0.6);
}

.header-left {
  @include flex-def;
  @include flex-cCenter;
  height: 50px;

  .left-menu {
    @include flex-def;
    @include flex-cCenter;
    height: 50px;
    width: 45px;

    .icon-caidan {
      font-size: 14px;
      margin-left: 15px;
      margin-right: 15px;
    }
  }

  .left-line {
    width: 1px;
    height: 50px;
    background-color: #fff;
    opacity: 0.15;
  }

  img {
    margin-left: 15px;
  }
}

.header-right {
  @include flex-def;
  @include flex-cCenter;
  height: 40px;
  font-size: 18px;
  &.fullHeight {
    height: 50px;
    .advance-applications {
      display: flex;
      padding-right: 30px;
      border-right: 1px solid rgba(255, 255, 255, 0.15);
      margin-right: 15px;
      font-size: 14px;
      .header-task {
        &:hover {
          color: #128bed;
        }
      }
    }
  }
}

.search-input {
  width: 200px;
  height: 49px;
  background-color: #fff;

  font-size: 14px;
  padding-left: 10px;
  outline-style: none; //取消外边框
  opacity: 0.15;
  border: 1px solid #ffffff;
}

input::-webkit-input-placeholder {
  color: red;
}

.header-task {
  @include flex-def;
  @include flex-cCenter;
  padding-left: 15px;
  padding-right: 15px;
  height: 48px;
  // min-width: 95px;
  // width: 90px;
  position: relative;
  cursor: pointer;

  .iconfont {
    font-size: 14px;
  }

  .icon-xiaoxiicon {
    font-size: 14px;
  }

  .header-task-title {
    margin-left: 3px;
    font-size: 14px;
    white-space: nowrap;
  }

  .icon-chanpintaocan {
    font-size: 13px;
  }

  .el-badge__content {
    position: absolute;
    top: 17px;
    right: 14px;
    background: #f04040;
    border-radius: 11px;
    border: none;
    display: inline-block;
    font-size: 12px;
    height: 18px;
    line-height: 18px;
    padding: 0 6px;
    text-align: center;
    white-space: nowrap;
    &.is-fixed {
      right: 14px;
    }
  }
}

.header-message {
  @include flex-def;
  flex-direction: column;
  @include flex-cCenter;
  padding-left: 15px;
  padding-right: 15px;
  height: 50px;
  position: relative;
  cursor: pointer;

  .message-top {
    @include flex-def;
    @include flex-cCenter;
    height: 50px;

    .icon-xiaoxiicon {
      font-size: 14px;
    }

    .header-message-title {
      margin-left: 3px;
      font-size: 14px;
      white-space: nowrap;
    }

    .el-badge__content {
      position: absolute;
      top: 17px;
      right: 17px;
      background: #f04040;
      border-radius: 11px;
      border: none;
      display: inline-block;
      font-size: 12px;
      height: 18px;
      line-height: 18px;
      padding: 0 6px;
      text-align: center;
      white-space: nowrap;
    }
  }

  .message-box {
    position: absolute;
    top: 49px;
    right: 0;
    z-index: 100000;
  }
}

.drop-user-box {
  position: relative;
  @include flex-def;
  flex-direction: column;
  padding-left: 15px;

  .user-box {
    @include flex-def;
    @include flex-cCenter;
    cursor: pointer;
    height: 50px;
    margin-right: 15px;

    .name-box {
      font-size: 14px;
      margin-right: 5px;
    }

    .icon-xiala {
      font-size: 12px;
    }

    .icon-shouqi {
      font-size: 12px;
    }
    &.head-wrapper {
      display: inline-block;
      overflow: hidden;
      cursor: pointer;
      position: relative;
      border: 1px solid transparent;
      line-height: 50px;
      > img {
        margin-top: 10px;
      }

      .hover-label {
        display: none;
        position: absolute;
        left: 0;
        top: 10px;
        width: 100%;
        color: white;
        font-size: 16px;
        text-align: center;
        line-height: 30px;
        background: rgba(#000000, 0.4);
        border-radius: 50%;
        .icon-gengduo {
          width: auto;
        }
      }

      &:hover {
        .hover-label {
          display: block;
        }
      }
    }
  }

  .drop-menu-box {
    position: absolute;
    top: 50px;
    right: 0;
    z-index: 100000;
    height: auto;
  }
}

.line {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 1px;
  background-color: white;
  opacity: 0.15;
}

.slide-fade-enter-active {
  transition: all 1s ease;
}

.slide-fade-enter,
.slide-fade-leave-to

/* .slide-fade-leave-active for below version 2.1.8 */ {
  transform: translateX(10px);
}

.drop-user-box .drop-menu-box {
  box-shadow: rgba(0, 0, 0, 0.3) 0px 5px 12px;
}
