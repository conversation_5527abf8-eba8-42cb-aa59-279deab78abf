<template>
  <div class='filterCollapse' ref='wrapperId'>
    <div style='z-index: 10'>
      <slot></slot>
    </div>
    <el-collapse-transition>
      <slot name='content'></slot>
    </el-collapse-transition>
    <div v-if='options.isOpen && !options.hideArrow' class='collapse-arrow content-border border-radius-bl-br-4' style='border-top:none;' @click='operateMenu()'>{{getText}} <i :class='getArrowClass' style='margin-right: 3px;font-weight: bold'></i>
    </div>
  </div>
</template>

<script>
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition'
import Vue from 'vue'
Vue.component(CollapseTransition.name, CollapseTransition)
export default {
  name: 'collapse-wrapper',
  props: { options: {} },
  data() {
    return {}
  },
  computed: {
    getText() {
      let down = '展开'
      let up = '收起'
      if (this.options.name) {
        down = this.options.name.down
        up = this.options.name.up
      }
      return this.options.show ? up : down
    },
    getArrowClass() {
      return this.options.show ? 'el-icon-arrow-up notreplace' : 'el-icon-arrow-down notreplace'
    }
  },
  methods: {
    operateMenu() {
      this.options.show = !this.options.show
      this.$emit('collapsestatechanged', this.options)
    },
    getHeight() {
      return this.$refs.wrapperId.offsetHeight
    }
  }
}

</script>

<style scoped lang="scss">
  @import "../../styles/common";

  .filterCollapse {
    width: 100%;
    position: relative;
    font-size: 12px;
    color: #128BED;

    .collapse-arrow {
      background: #FFFFFF;
      position: absolute;
      cursor: pointer;
      display: inline-block;
      padding-left: 10px;
      padding-right: 6px;
      height: 26px;
      line-height: 26px;
      bottom: -25px;
      z-index: 2;
      text-align: center;
      left: calc((100% - 63px) / 2);

      &:hover {
        color: $global-color-priamry-hover;
      }
    }
  }

  .content-border {
    box-sizing: border-box;
  }

</style>
