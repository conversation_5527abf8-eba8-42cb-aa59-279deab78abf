<template>
  <div class="adapter-item-wrapper">
    <div>
      <span style="line-height:32px" v-if="canTitle && !isMiniScreen">{{text}}：</span>
      <slot></slot>
    </div>
    <span class="hover-pop" v-if="isMiniScreen && !isDate && !selectValue">{{text}}</span>
  </div>
</template>

<script>
  export default {
    props: {
      text: {
        default: '不限',
        type: String
      },
      canTitle: {
        default: false,
        type: Boolean
      },
      isDate: {
        default: false,
        type: Boolean
      },
      selectValue: {
        default: '',
        type: [String, Object, Number]
      }
    },
    computed: {
      isMiniScreen() {
        if (this.$parent && this.$parent.isMiniScreen) {
          return true
        } else if (this.$parent && this.$parent.$parent && this.$parent.$parent.isMiniScreen) {
          return true
        } else if (this.$parent && this.$parent.$parent.$parent && this.$parent.$parent.$parent.isMiniScreen) {
          return true
        } else if (this.$parent && this.$parent.$parent.$parent.$parent && this.$parent.$parent.$parent.$parent.isMiniScreen) {
          return true
        } else {
          return $util.isMiniScreen()
        }
      }

    },
    data() {
      return {}
    }
  }

</script>

<style scoped lang="scss">
  .adapter-item-wrapper {
    position: relative;
    display: inline-block;
    width: auto;
    height: 32px;
    // padding: 8px 0px;
    line-height: normal;
    // vertical-align: top;

    .hover-pop {
      position: absolute;
      background: white;
      display: inline-block;
      width: auto;
      height: 28px;
      line-height: 28px;
      left: 9px;
      top: 1px;
      font-size: 12px;
      cursor: pointer;
      pointer-events: none;
    }
  }

</style>
