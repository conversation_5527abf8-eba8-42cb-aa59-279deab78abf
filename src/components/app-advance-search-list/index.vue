<template>
  <div class="app-advance-search-list" ref="advanceSearchList">
    <div v-for="(item, index) in advanceList" class="adv-search-item" :class="item.hide ? 'adv-search-item-hide' : ''"
      :key="'advance-search-' + index">
      <template v-if="!item.hide">
        <app-advance-search v-model="advanceList[index]" :ref="'adv' + index" :list="corpAndPersonList"
          :popWidth="popWidth" :placeholder="`目标${getIndex(index)}：请输入企业/人员名称`" @select="selectItem(index)"
          @clear="clearItem(index)"></app-advance-search>
      </template>
    </div>
    <div class="adv-search-item" v-if="advanceList.length < limit" ref="advbtnc">
      <div class="adv-btn" @click="addItem" ref="adv-btn"><i class="iconfont icon-xinzeng2"></i>&nbsp;新增关系目标</div>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'
export default {
  name: 'app-advance-search-list',
  props: {
    limit: { type: Number, default: 100 },
    defaultLength: { type: Number, default: 3 },
    popWidth: { type: Number, default: 370 },
    scrollBody: { type: String, default: 'body' },
    // 绑定的值
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      advanceList: []
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  watch: {
    // 监听用户的值
    value(newValue) {
      this.setAdvanceList(newValue)
    }
  },
  created() {
    this.setAdvanceList(this.value)
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs && this.$refs['adv0'] && this.$refs['adv0'][0]) { this.$refs['adv0'][0].showMokF() }
    })
  },
  computed: {
    showAdvanceList() {
      if (this.advanceList) { return this.advanceList.filter(e => !e.hide) }
      return []
    },
    corpAndPersonList() {
      let li = []
      if (this.showAdvanceList && this.showAdvanceList.length) {
        this.showAdvanceList.forEach((adv, index) => {
          if (adv && adv.corpKeyNo) {
            li.push(adv)
          }
        })
      }
      return li
    }
  },
  methods: {
    setAdvanceList(taskList) {
      if (taskList && taskList.length) {
        this.advanceList = taskList.map(e => ({ corpName: e.corpName, corpKeyNo: e.corpKeyNo, isSelected: true }))
      }
      this.init()
    },
    init() {
      if (this.defaultLength) {
        for (let index = 0; index < this.defaultLength; index++) {
          if (!this.advanceList[index]) {
            this.advanceList.push({})
          }
        }
      }
    },
    addItem() {
      this.advanceList.push({})
      this.$nextTick(() => {
        $(this.scrollBody).scrollTop(10000)
      })
    },
    clear() {
      this.advanceList = []
      this.init()
      this.$emit('change', this.advanceList)
      this.$emit('select')
    },
    selectItem(index) {
      if (this.showAdvanceList[index] && this.showAdvanceList[index].corpKeyNo) {
        let filters = this.corpAndPersonList.filter(e => e.corpKeyNo === this.showAdvanceList[index].corpKeyNo)
        if (filters && filters.length > 1) {
          this.$message.warning('列表中已存在，请勿重复添加')
          this.$refs['adv' + index][0].reset()
        } else {
          this.$emit('change', this.corpAndPersonList)
          this.$emit('select')
        }
      } else {
        this.$emit('change', this.corpAndPersonList)
        this.$emit('select')
      }
    },
    clearItem(index) {
      this.advanceList[index].hide = true
      this.$set(this.advanceList, index, this.advanceList[index])
      if (this.showAdvanceList.length < this.defaultLength) {
        this.advanceList.push({})
      }
      this.$emit('change', this.corpAndPersonList)
      this.$emit('select')
    },
    getIndex(index) {
      let lf = _.slice(this.advanceList, 0, index + 1)
      let hidCount = lf.filter(e => e.hide).length
      return index + 1 - hidCount
    }
  }
}

</script>
<style lang="scss" scoped>
.app-advance-search-list {
  .adv-search-item-hide {
    margin-bottom: 0px !important;
  }

  .adv-search-item {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0px;
    }

    &:last-child:not(:first-child) {
      margin-bottom: 0px;
    }

    // &:last-child:not(.adv-search-item-hide) {
    //   margin-bottom: 0px;
    // }
    // &:not(.adv-search-item-hide):last-child {
    //   margin-bottom: 0px;
    // }

    .adv-btn {
      height: 32px;
      width: 100%;
      font-size: 12px;
      color: #128BED;

      background: #FFFFFF;
      box-sizing: border-box;
      border: 1px dashed #128BED;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      border-radius: 2px;

      .iconfont {
        font-size: 12px;
        color: #128BED;
      }

      &:hover {
        background-color: #F2F8FE;
      }
    }
  }
}
</style>
