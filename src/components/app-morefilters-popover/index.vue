<template>
  <el-popover :placement="placement" :width="width" v-model="visible" :trigger="action" popper-class="more-filter-wrapper-pop">
    <ul class="more-filter-wrapper">
      <template v-for="(item,index) in filterList">
        <li :key="`filter-item-${index}`" class="_filter-item">
          <div class="_title">{{item.name}}</div>
          <span v-for="(tmp,cIndex) in item.children" :key="`${cIndex}-${tmp.value}`" :class="['_filter-item-select', {selected: selectModel[item.key] === tmp.value}]" @click="selectItem(tmp, item)">
            {{tmp.desc}}<template v-if="!!tmp.value && !hideCount">(<span class="count">{{tmp.count || 0}}</span>)</template>
          </span>
        </li>
      </template>

    </ul>
    <app-button slot="reference" type="small" :plain="true" style="padding-left: 10px;padding-right: 10px;">{{text}} {{selectModelLength > 0 ? selectModelLength : ''}}<span class="el-icon-caret-bottom"></span></app-button>
  </el-popover>
</template>

<script>
  export default {
    name: 'app-morefilters-popover',
    props: {
      width: {
        default: '500',
        type: [String, Number]
      },
      placement: {
        default: 'bottom-end',
        type: String
      },
      typesMap: {
        default: {},
        type: Object
      },
      list: {
        default: undefined,
        type: Array
      },
      text: {
        default: '',
        type: String
      },
      action: {
        default: 'click',
        type: String
      },
      hideCount: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        selectModel: {},
        filterList: [],
        visible: false,
        selectModelLength: 0
      }
    },
    watch: {
      list(val) {
        if (val && val.length) {
          this.detailWithFilterData(val)
        }
      }
    },
    created() {
      this.detailWithFilterData(this.list)
    },
    methods: {
      selectItem(row, pRow) {
        this.selectModel[pRow.key] = row.value
        this.$emit('change', this.selectModel)
        this.visible = false
        this.selectModelLength = Object.values(this.selectModel).reduce((acc, cur) => {
          return acc + (cur !== '' ? 1 : 0)
        }, 0)
      },
      detailWithFilterData(list) {
        // console.log(22222, list)
        let arr = []
        let tmpModel = {}
        if (list && list.length) {
          for (let row of list) {
            if (this.typesMap[row.key]) {
              if (this.typesMap[row.key].sortDesc) {
                row.items.sort((t1, t2) => {
                  return t2.count - t1.count
                })
              }
              arr.push({
                name: this.typesMap[row.key].value,
                key: row.key,
                children: [{ desc: '不限', value: '' }].concat(row.items)
              })
              tmpModel[row.key] = this.selectModel[row.key] || ''
            }
          }
        }
        arr.sort((t1, t2) => {
          return this.typesMap[t1.key].no - this.typesMap[t2.key].no
        })
        this.filterList = arr
        let len = 0
        for (let key in tmpModel) {
          if (tmpModel[key]) {
            len++
          }
        }
        this.selectModelLength = len
        this.$set(this, 'selectModel', tmpModel)
      }
    }
  }

</script>

<style scoped lang="scss">
  .more-filter-wrapper {
    padding: 10px;
    max-height: 400px;
    overflow: auto;

    ._filter-item {
      border-top: solid 1px #eee;
      padding-top: 10px;
      margin-top: 10px;

      &:first-child {
        border-top: none;
        margin-top: 0px;
      }

      ._title {
        color: #999;
        margin-bottom: 5px;
      }

      ._filter-item-select {
        cursor: pointer;
        padding: 2px 10px;
        margin: 3px 3px;
        display: inline-block;
        font-size: 13px;
        color: #333;
        border-radius: 2px;

        .count {
          color: #999;
        }

        &:hover,
        &.selected {
          background: #128bed;
          color: white;

          .count {
            color: white;
          }
        }
      }
    }

  }

  @media screen and (max-height: 640px) {
    .more-filter-wrapper {
      max-height: 350px;
    }
  }

</style>
<style lang="scss">
  .more-filter-wrapper-pop {
    padding: 0;
  }

</style>
