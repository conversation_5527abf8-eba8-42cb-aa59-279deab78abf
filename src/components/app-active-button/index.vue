<template>
  <div class="app-active-button" :class="{ '__app-btn-actived': actived }" :style="styleRadio" type="activebutton"
    @click="activeButton" :checked="actived">
    <slot>{{ text }}</slot>
  </div>
</template>

<script>
export default {
  name: 'app-active-button',
  props: {
    color: {
      type: String,
      default: '#333'
    },
    backgroundColor: {
      type: String,
      default: '#fff'
    },
    text: {
      type: String,
      default: '---'
    },
    actived: {
      type: Boolean,
      default: false
    },
    height: {
      type: String,
      default: '24px'
    }
  },
  computed: {
    styleRadio() {
      return {
        '--color': this.color,
        '--backgroundColor': this.backgroundColor,
        '--height': this.height,
        '--lineHeight': (+(this.height.replace('px', '')) - 3) + 'px'
      }
    }
  },
  methods: {
    activeButton() {
      this.$emit('click')
    }
  }
}
</script>

<style lang="scss" scoped>
.app-active-button {
  font-size: 12px;
  display: inline-block;
  position: relative;
  cursor: pointer;
  padding: 0px 20px;
  height: var(--height);
  margin: 0 auto;
  text-align: center;
  line-height: var(--lineHeight);
  border-radius: 2px;

  color: var(--color);
  background-color: var(--backgroundColor);
  border: 1px solid var(--color);
}


.__app-btn-actived {
  &:before {
    content: '';
    position: absolute;
    right: 0;
    bottom: 0;
    border: 6px solid var(--color);
    border-top-color: transparent;
    border-left-color: transparent;
  }

  &:after {
    content: '';
    width: 3px;
    height: 5px;
    position: absolute;
    right: 2px;
    bottom: 2px;
    border: 1px solid #fff;
    border-top-color: transparent;
    border-left-color: transparent;
    transform: rotate(45deg);
  }
}
</style>
