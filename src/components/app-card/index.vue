<template>
  <el-card
    class="app-card"
    :shadow="shadow"
    :class="{
      'full-height': fullHeight,
      'border': border,
      'hide-title': hideTitle,
      'app-card--rendering': rendering
    }"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <div
      slot="header"
      class="app-card__header"
      v-if="!hideTitle"
      :id="headerId"
      :class="[disableFlow ? 'disable-flow' : '']"
    >
      <div class="app-card__title">
        <template v-if="realLayoutType === 'default'">
          <slot v-if="$slots.title" name="title"></slot>
          <template v-else>
            <div v-html="realTitle"></div>
            <div class="app-card__title-sub">{{ subTitle }}</div>
          </template>
        </template>
        <template v-if="realLayoutType === 'tabs'">
          <el-tabs @tab-click="handleTabClick" v-model="tabValue">
            <el-tab-pane
              :label="item.name"
              :key="item.index || getSingleRouteName(item.routeName)"
              :name="item.index || getSingleRouteName(item.routeName)"
              v-for="item in menus"
            >
              {{item.index || getSingleRouteName(item.routeName)}}
              <template slot="label" v-if="item.labelSlotName">
                <slot :name="item.labelSlotName"></slot>
              </template>
<!--              <slot :name="item.slot"></slot>-->
            </el-tab-pane>
          </el-tabs>
        </template>
      </div>
      <div class="app-card__extra">
        <slot name="extra"></slot>
      </div>
      <slot name="fillContent"></slot>
    </div>
    <app-scrollbar
      ref="app-scrollbar"
      :height="scrollbarHeight"
      :max-height="scrollbarMaxHeight"
      v-if="scrollbar"
      resize
    >
      <slot></slot>
    </app-scrollbar>
    <slot v-else></slot>
  </el-card>
</template>

<script>
import _ from 'lodash'
export default {
  name: 'app-card',
  props: {
    headerId: {
      type: String,
      default: ''
    },
    layout: {
      type: String,
      default: 'default'
    },
    hideTitle: {
      type: Boolean,
      default: false
    },
    disableFlow: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    subTitle: {
      type: String,
      default: ''
    },
    shadow: {
      type: String,
      default: 'never'
    },
    fullHeight: {
      type: Boolean,
      default: false
    },
    border: {
      type: Boolean,
      default: false
    },
    menus: {
      type: Array,
      default: () => ([])
    },
    value: {
      type: String
    },
    /**
     * 是否开启自定义滚动条
     */
    scrollbar: {
      type: Boolean,
      default: false
    },
    /**
     * 自定义滚动条高度
     */
    scrollbarHeight: {
      type: String
    },
    /**
     * 自定义滚动条最大高度
     */
    scrollbarMaxHeight: {
      type: String
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  provide() {
    return {
      handleUpdateCardScrollbar: this.handleUpdateCardScrollbar
    }
  },
  data () {
    return {
      tabValue: this.value,
      rendering: true
    }
  },
  computed: {
      realLayoutType() {
        switch (true) {
          case __PLUGIN__ && this.$route.query.hideOtherMenuFlag === 'Y':
            return 'default'
          default:
            return this.layout
        }
      },
    realTitle() {
        switch (true) {
          case __PLUGIN__ && this.$route.query.hideOtherMenuFlag === 'Y' && this.layout === 'tabs':
            let find = this.menus.find(item => (item.index || this.getSingleRouteName(item.routeName)) === this.tabValue)
            return find?.name
          default:
            return this.title
        }
    }
  },
  watch: {
    value (val) {
      this.tabValue = val
      // this.handleTabClick()
    }
  },
  created() {
    this.tabValue = this.value
  },
  activated() {
    this.tabValue = this.value
  },
  mounted() {
    // this.rendering = false
  },
  methods: {
    handleTabClick () {
      this.$emit('change', this.tabValue)
      try {
        let find = this.menus.find(item => (item.index || this.getSingleRouteName(item.routeName)) === this.tabValue)
        if (find?.name) {
          $util.zhugeTrackThreeoLevel(find.name, '横向菜单', '点击菜单')
        }
      } catch (e) {}
    },
    getSingleRouteName (value) {
      if (_.isArray(value)) {
        return value[0]
      }
      return value
    },
    handleUpdateCardScrollbar () {
      this.$refs['app-scrollbar']?.update()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../styles/common';

.app-card {
  border-radius: 4px;
  line-height: 1.5;
  border: none;
  background: #fff;
  position: relative;
  transition: none;

  &.card-body-nopadding ::v-deep .el-card__body {
    padding: 0;
  }

  // 解决页面渲染时Tabs底部横线复位问题
  &--rendering {
    ::v-deep {
      .el-tabs__nav {
        transition: none;
      }

      .el-tabs__active-bar {
        transition: none;
      }
    }
  }

  &.hide-title {
    ::v-deep {
      .el-card__body {
        height: 100%;
      }
    }
  }

  ::v-deep {
    .el-card__header {
      padding: 0;
      margin: 0 15px;
      height: 50px;
      line-height: 50px;
      border-bottom: 1px solid #eee;
    }

    .el-card__body {
      background: #fff;
      padding: 15px;
      overflow: visible;
      border-radius: 4px;
      height: calc(100% - 50px);

      &:empty {
        display: none;
      }

      .el-scrollbar__bar.is-vertical {
        right: -10px;
      }
    }
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;

    &.disable-flow {
      position: relative !important;
      left: unset !important;
      top: unset !important;
      width: unset !important;
    }
  }

  &__title {
    font-size: 15px;
    font-weight: bold;
    @include flex-center();

    ::v-deep {
      em {
        color: #F04040;
      }

      em.primary {
        color: #128BED;
      }
    }
  }

  &__title-sub {
    color: #999999;
    font-size: 14px;
    margin-left: 10px;
  }

  &__extra {
    @include flex-center();
    font-size: 14px;

    ::v-deep {
      .app-card__extra-item {
        margin-right: 10px;
        display: flex;

        &.horizontal-center {
          display: flex;
          align-items: center;
        }

        &:last-child {
          margin-right: 0;
        }
      }

      .app-card__extra-item-label {
        margin-right: 8px;
      }

      .app-card__extra-icon {
        color: #d8d8d8;
        cursor: pointer;
        font-size: 14px;
        display: inline-flex;
        align-items: center;
        height: 14px;
        //&:hover {
        //  color: #128bed;
        //}
      }

      .el-dropdown {
        line-height: 32px;
        height: 32px;

        .el-button {
          border-color: #D8D8D8;

          &:hover:not(.is-disabled) , &.active:not(.is-disabled) {
            border-color: #128bed;
          }
        }
      }

      .el-button--normal {
        color: #333333;
      }

      .el-button {
        font-size: 14px;
        height: 32px;
        line-height: 30px;
        padding: 0 12px;
        border-radius: 2px;
        font-weight: normal;

        &:hover:not(.is-disabled)  {
          color: #128bed;
        }

        //&:focus {
        //  color: #128bed;
        //}

        &--primary:hover:not(.is-disabled) , &--primary:focus:not(.is-disabled)  {
          background: $color-primary-hover;
          border-color: $color-primary-hover;
          color: #fff;
        }
      }

      .el-switch {
        height: 16px;
        line-height: 16px;

        .el-switch__core {
          height: 16px;

          &:after {
            width: 12px;
            height: 12px;
          }
        }

        &.is-checked .el-switch__core::after {
          margin-left: -13px;
        }
      }

      .el-popover__reference-wrapper {
        display: inline-flex;
      }

      .app-icon-operations {
        padding: 0 5px;
        height: 22px;
        line-height: 22px;

        .iconfont {
          font-size: 14px;
        }

        &:hover {
          background: #F2F8FE;
          .iconfont {
            color: #128bed;
          }
        }
      }

      .app-tooltip {
        display: inline-flex;
      }

      .app-card__extra-desc {
        display: flex;
        color: #999999;

        .app-card__extra-icon {
          margin-left: 5px;
        }
      }
    }
  }
}

.full-height {
  flex: 1;
  display: flex;
  flex-direction: column;

  ::v-deep {
    .el-card__body {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}

.border {
  border: 1px solid #EEEEEE;
}

.el-tabs {
  ::v-deep {
    .el-tabs__header {
      margin: 0;
    }

    .el-tabs__item {
      height: 50px;
      line-height: 50px;
      font-size: 15px;
      font-weight: normal;
      color: #999999;
      padding: 0 15px;

      &.is-active {
        color: #333333;
        font-weight: bold;
      }
    }

    .el-tabs__active-bar {
      bottom: 2px;
    }

    .el-tabs__nav-wrap::after {
      height: 0 !important;
    }

    .el-tabs__content {
      display: none;
    }
  }
}
</style>
