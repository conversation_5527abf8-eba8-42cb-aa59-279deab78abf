import { mount } from '@vue/test-utils'
import myPopup from '../my-popup.vue'
import AppButton from '../../app-button/index'

describe('AppAbout', () => {
  it('配置button的参数和button个数', async () => {
    const clickHandler = vi.fn()
    const wrapper = mount(myPopup, {
      propsData: {
        buttonOptions: [
          { name: '取消', type: 'primary', disabled: false, click: clickHandler },
          { name: '确定', type: 'success', disabled: false }
        ]
      }
    })
    await wrapper.vm.$nextTick()
    // 查找所有 app-button 组件
    const buttons = wrapper.findAllComponents(AppButton)
    // 验证 app-button 组件的数量
    expect(buttons).toHaveLength(2)

    expect(buttons.at(0).text()).toBe('取消')

    buttons.at(0).vm.$emit('click')
    expect(clickHandler).toHaveBeenCalled()
  })
})
