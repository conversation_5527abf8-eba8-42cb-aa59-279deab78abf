import { mount } from '@vue/test-utils'
import BasePopup from '../base-popup.vue'
import { vi } from 'vitest'

describe('BasePopup Component', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('renders with default props', async () => {
    const wrapper = mount(BasePopup)
    await vi.runAllTimers() // Wait for all async operations
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.props('bgClose')).toBe(true)
    expect(wrapper.props('zIndex')).toBe(999)
    expect(wrapper.props('isoutpop')).toBe(false)
  })

  it('emits outerclick event when background is clicked', async () => {
    const wrapper = mount(BasePopup, {
      props: {
        bgClose: true
      }
    })
    await vi.runAllTimers() // Fast-forward through the setTimeout
    wrapper.vm.canClose = true
    await wrapper.find('.blackground').trigger('click')
    expect(wrapper.emitted('outerclick')).toBeTruthy()
  })

  it('does not emit outerclick when bgClose is false', async () => {
    const wrapper = mount(BasePopup, {
      props: {
        bgClose: false
      }
    })
    await wrapper.find('.blackground').trigger('click')
    expect(wrapper.emitted('outerclick')).toBeFalsy()
  })

  it('handles close method correctly', async () => {
    const wrapper = mount(BasePopup)
    const mockCallback = vi.fn()
    wrapper.vm.close(mockCallback)
    expect(wrapper.vm.show).toBe(false)
    await vi.advanceTimersByTime(300) // Fast-forward 300ms
    expect(mockCallback).toHaveBeenCalled()
  })

  it('tests delayed show state', async () => {
    const wrapper = mount(BasePopup)
    expect(wrapper.vm.show).toBe(true) // Initially false before mount completes
  })

  it('tests transbgFlag state change', async () => {
    const wrapper = mount(BasePopup)
    wrapper.vm.transbgFlag = true
    await wrapper.vm.$nextTick()
    expect(wrapper.find('.blackground').attributes('style')).toContain('background: transparent')
  })
})
