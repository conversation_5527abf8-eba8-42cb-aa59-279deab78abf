import _ from 'lodash'

let popList = {}

export const addNewPopup = (id, that) => {
  if (!that.transbg) {
    _.mapKeys(popList, (value, key) => {
      if (value && value.transbgFlag !== undefined) {
        value.transbgFlag = true
      }
    })
  }
  popList[id] = that
}

export const removePopup = (id) => {
  _.mapKeys(popList, (value, key) => {
    if (id !== key && value && value.transbgFlag !== undefined) {
      value.transbgFlag = false
    }
  })
  delete popList[id]
}

export const closeAllPopup = () => {
  window.AppRuntimeContext.eventBus.$emit(window.AppRuntimeContext.eventBusEvents.CLOSE_ALL_POPUP)
}

export const getAlreadyPopCount = () => {
  let count = 0
  _.mapKeys(popList, (value, key) => {
    if (value && value.$parent && value.$parent.close) {
      count++
    }
  })
  return count
}


export default {
  addNewPopup,
  removePopup,
  closeAllPopup,
  getAlreadyPopCount
}

