@import "../../styles/baseFlexAdapter";

@mixin commonCloseStyle() {
  position: absolute;
  padding: 0px;
  right: 0px;
  top: 0px;
  font-size: 18px;
  color: #bbb;
  cursor: pointer;

  &:hover {
    color: #128BED;
  }
}

@mixin commonBackStyle() {
  padding: 10px 0px 10px 0px;
  color: #999999;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

@mixin commonTitleWrapper($titleHeight) {
  height: $titleHeight;
  line-height: $titleHeight;
  position: relative;
  box-sizing: border-box;
  margin-left: 15px;
  margin-right: 15px;
  font-size: 15px;
  background: #fff;
  color: #333;
  font-weight: bold;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  border-bottom: 1px solid #eee;
}

._popup_contentWrapper {
  position: absolute;
  border-radius: 3px;
  box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.20);
  background: white;

  hr,
  p {
    margin: 0;
  }

  .titleWrapper {
    @include commonTitleWrapper(50px);

    .closeicon {
      @include commonCloseStyle;
    }


    .backicon {
      @include commonBackStyle;
    }
  }

  .dialogTitleWhiteWrapper {
    color: #333 !important;
    background-color: #fff !important;
    border-bottom: 1px solid #eee;
    padding: 0 15px !important;
  }

  .dialogTitleWrapper {
    @include commonTitleWrapper(50px);

    .closeicon {
      @include commonCloseStyle;
    }


    .filter-btn {
      right: 52px;
      position: absolute;
    }

    .backicon {
      @include commonBackStyle;

    }
  }

  ._contentDefault {
    box-sizing: border-box;
    height: auto;
    padding: 15px;
    overflow: auto;
    &.scrollbar {
      overflow: hidden;
    }
    &.noPadding {
      padding: 0;
      .popup__scrollbar {
        ::v-deep {
          .el-scrollbar__bar.is-vertical {
            right: 3px;
          }
        }
      }
    }

    .popup__body {
      overflow: visible;
      height: calc(100vh - 180px - 64px);
    }
    .popup__scrollbar {
      height: 100%;
      overflow: visible;
      ::-webkit-scrollbar {
        background: transparent;
      }
      ::-webkit-scrollbar-thumb {
        box-shadow: none;
        background: transparent;
      }
      ::v-deep {
        .el-scrollbar__wrap {
          //overflow: auto;
          overflow-x: hidden;
        }
        .el-scrollbar__bar.is-vertical {
          opacity: 1;
          width: 4px;
          right: -12px;
        }
      }
    }
  }

  ._contentOverFlow {
    overflow: auto;
    padding: 20px 15px;
    max-height: 50px;
  }

  .footerWrapper {
    @include flex-def;
    @include flex-cCenter;
    min-height: 64px;
    height: 64px;
    padding: 15px 0;
    margin: 0 15px;
    position: relative;
    box-sizing: border-box;
    border-top: 1px solid #eee;

    ._footer-left {
    }

    ._footer-right {
      position: absolute;
      height: 32px;
      right: 0;
      top: 16px;
    }

    ._footerbtn {
      margin-right: 15px;
      line-height: 100%;
      margin-left: 0;
      //min-width: 80px;
      padding: 0 26px!important;
      vertical-align: top;
      ::v-deep &.el-button--small {
        height: 32px;
        font-size: 14px;
        border-radius: 2px;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .sub-popup-title {
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    color: #999999;
  }
}

.popupSlideDown-enter-active {
  animation: popupSlideDown 0.3s;
}

.popupSlideDown-leave-active {
  animation: popupSlideDownclose 0.3s;
}

@-webkit-keyframes popupSlideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }

  to {
    transform: translateY(0%);
    opacity: 1;
  }
}

@keyframes popupSlideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }

  to {
    transform: translateY(0%);
    opacity: 1;
  }
}

@-webkit-keyframes popupSlideDownclose {
  from {
    transform: translateY(0%);
    opacity: 1;
  }

  25% {
    transform: translateY(-50%);
    opacity: 0.5;
  }

  50% {
    transform: translateY(-75%);
    opacity: 0.3;
  }

  75% {
    transform: translateY(-90%);
    opacity: 0.1;
  }

  to {
    transform: translateY(-100%);
    opacity: 0;
  }
}

@keyframes popupSlideDownclose {
  from {
    transform: translateY(0%);
    opacity: 1;
  }

  25% {
    transform: translateY(-50%);
    opacity: 0.5;
  }

  50% {
    transform: translateY(-75%);
    opacity: 0.3;
  }

  75% {
    transform: translateY(-90%);
    opacity: 0.1;
  }

  to {
    transform: translateY(-100%);
    opacity: 0;
  }
}
