._popup_out {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  z-index: 999;
  &.throughBg {
    pointer-events: none;
  }

  .blackground {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    transition: all 0.3s linear;
  }
}
.sort-app-popup {
  z-index: 1000001 !important; // 内容页面的loading层级是999999 顶部下拉菜单是1000000
  ::v-deep {
    .dialogTitleWrapper {
      background: #fff !important;
      border-bottom: 1px solid #EEEEEE;
      height: 54px !important;
      line-height: 54px !important;
    }
    .el-icon-close {
      font-size: 20px;
      top: 8px !important;
    }
  }
}
