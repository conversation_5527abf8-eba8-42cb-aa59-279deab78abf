<template>
  <li class="app-menu-item" :class="[{avtived}]">
    <span :class="icon" class="icon"></span>
    <slot></slot>
    <span class="collapse-btn" :class="[iconOperation]" v-if="iconOperation"></span>
  </li>
</template>

<script>
  export default {
    name: 'menu-item',
    props: {
      icon: { default: '', type: String },
      iconOperation: { default: '', type: String },
      avtived: { default: false, type: Boolean }
    },
    data() {
      return {}
    }
  }
</script>

<style scoped lang="scss">
  @import "../../../styles/common";
  $menu-item-bg: $sidebar-bg-color;
  .app-menu-item {
    position: relative;
    @include flex-def;
    @include flex-cCenter;
    color: $sidebar-link-color;
    font-size: 14px;
    cursor: pointer;
    height: 40px;
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;
    &:nth-child(2){
      border-color: transparent;
    }
    &.menuitem-count1:hover{
      border-color: $sidebar-item-hover-color;
    }
    &:not(.active):hover{
      border-color: $menu-item-bg;
      background: $sidebar-item-hover-color;
    }
    .icon {
      font-size: 14px;
      display: inline-block;
      width: 14px;
      margin-left: 15px;
      margin-right: 10px;
    }
    &.menu-group-item{
      height: 49px;
      font-weight: bold;
      .icon {
        font-weight: normal;
      }
    }
    &.active {
      color: $sidebar-link-active-color;
      background: $sidebar-item-hover-color;
    }
    .collapse-btn{
      position: absolute;
      right: 15px;
      top: 0;
      font-size: 14px;
      height: 100%;
      display: inline-block;
      color: $sidebar-link-color;
      line-height: 49px;
      transition: all linear 0.15s;
    }

    &.avtived {
      .collapse-btn {
        transform: rotate(-180deg);
      }
    }
  }

</style>
