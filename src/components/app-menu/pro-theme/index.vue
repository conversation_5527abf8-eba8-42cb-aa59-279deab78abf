<!--
  1、如果一级菜单的icon显示不出来，在菜单配置里面复制一份相同的菜单，更改func，一般是main 和 sub的区别
-->
<template>
  <ul class="app-menu-list" :class="[{'not-expand': isCollapse}]">
    <template v-for="(group,i) in list" v-if="!isCollapse">
      <menu-item  :key="i" :icon="group.icon" class="menu-group-item" :avtived="groupMenuActive(group, i)"
                  :iconOperation="list.length > 1 ? 'el-icon-caret-bottom' : ''"
                  @click.native="operateGroupTitle(group,i)">{{group.title}}</menu-item>
      <el-collapse-transition>
        <ul v-if="group && groupMenuActive(group, i)" class="menu-group-list-section">
          <menu-item  v-for="(item,index) in group.subs" :key="`child-${index}`" :class="[{active: getIsActived(item, index)}]"
                      @click.native="openTarget(item, group)">
            <span :class="[{'menu-new-icon right': hotModulesAndMenus.menus.includes(item.func)}]">
              {{item.title}}
            </span>
          </menu-item>
        </ul>
      </el-collapse-transition>
    </template>
<!--    <span class="expand-menu-icon iconfont" @click="$emit('collapseClick')" v-if="list.length === 1 && isJSJW"-->
<!--          :class="[{'icon-caidanshouqi1' : !isCollapse},{'icon-caidanzhankai1' : isCollapse}]"></span>-->
  </ul>
</template>

<script>
  import menuItem from './menu-item'
  import { myModelMixins } from '../../../utils/mixinsUtils'
  import _ from 'lodash'
  import CollapseTransition from 'element-ui/lib/transitions/collapse-transition'
  import Vue from 'vue'
  import { getHotModulesAndMenus, setClickedModulesAndMenus } from '../../../config/jw-config'
  Vue.component(CollapseTransition.name, CollapseTransition)

  const defaultActiveMenus = ['main/setting_ctr', 'main/person_info', 'main/wxb_srch_anls_yuntu', 'main/wxb_zoom']
  export default {
    components: { menuItem },
    mixins: [myModelMixins],
    props: {
      list: { type: Array, default() { return [] } },
      activedKey: { default: '', type: String },
      isCollapse: { default: true, type: Boolean }
    },
    data() {
      return {
        expandGroupIndex: $util.isJSJW() ? 0 : -1,
        hotModulesAndMenus: getHotModulesAndMenus()
      }
    },
    computed: {
      hasDirectActivedLink() {
        // console.log(this.list)
        if (this.list && this.list.length) {
          for (let item of this.list[0].subs) {
            if (this.$route.fullPath.indexOf(item.path) > -1) {
              // console.log(this.$route)
              return true
            }
          }
        }
        return false
      }
    },
    watch: {
      '$route.path'(val) {
        this.$nextTick(() => {
          this.changeGroupLevel()
        })
      },
      list() {
        this.$nextTick(() => {
          this.changeGroupLevel()
        })
        this.list.forEach((item, index) => {
          if (defaultActiveMenus.includes(item.func)) {
            this.$set(item, 'active', true)
          }
        })
      }
    },
    created() {
      this.openMenuPage = _.throttle((item, group) => {
        this.$router.push({ path: item.path, query: { pagev: new Date().getTime() } })
        if (this.hotModulesAndMenus.menus.includes(item.func)) {
          setClickedModulesAndMenus(item.func)
          this.hotModulesAndMenus = getHotModulesAndMenus()
        }
        $util.zhugeTrackThreeoLevel(item.title, group.title, '点击菜单')
      }, 1000)
    },
    mounted() {
      this.$nextTick(() => {
        this.changeGroupLevel()
      })
    },
    methods: {
      groupMenuActive (group, i) {
        return Object.hasOwnProperty.call(group, 'active') ? group.active : this.expandGroupIndex === i
      },
      operateGroupTitle(group, i) {
        if (this.list.length > 1 && Object.hasOwnProperty.call(group, 'active')) {
          group.active = !group.active
        }
        if (this.isJSJW) {
          this.expandGroupIndex = 0
          return
        }
        this.expandGroupIndex = i
      },
      changeGroupLevel() {
        if (this.isJSJW) {
          this.expandGroupIndex = 0
          return
        }
        for (let i = 0; i < this.list.length; i++) {
            if (this.list[i].subs.find(v => (v.path === this.$route.path) ||
              !!(v.menuMappingUrl || []).includes(this.$route.path))) {
              this.expandGroupIndex = i
              break
            }
        }
      },
      getIsActived(item, index) {
        if (this.$route.fullPath.indexOf(item.path) > -1) {
          if ($util.isJSJW() && this.hotModulesAndMenus.menus.includes(item.func)) {
            setClickedModulesAndMenus(item.func)
            this.hotModulesAndMenus = getHotModulesAndMenus()
          }
          return true
        }
        // console.log(this.$route, this.hasDirectActivedLink)
        if (item.menuMappingUrl && !this.hasDirectActivedLink) {
          for (let str of item.menuMappingUrl) {
            if (this.$route.fullPath.indexOf(str) === 0) {
              return true
            }
          }
        }
        // console.log(this.$route)
        return false
      },
      openTarget(item, group) {
        this.openMenuPage(item, group)
      }
    }
  }
</script>

<style scoped lang="scss">
@import "../../../styles/common";
.app-menu-list {
  background: $navbar-bg-color;
  position: relative;
  height: 100%;
  /*height: 100%;*/
  &.not-expand{
    .expand-menu-icon{
      width: 46px;
      right: unset;
      left: 15px;
    }
  }
  .expand-menu-icon{
    position: absolute;
    right: 15px;
    font-size: 12px;
    color: #fff;
    cursor: pointer;
    line-height: 50px;
    top: 0;
  }

  .menu-group-list-section {
    overflow-y: auto;
    max-height: calc(100% - 100px);
    background: $sidebar-bg-color;

    .new-menu {
      position: absolute;
      width: 23px;
    }
  }
}
</style>
<style lang="scss">
  .__IE__ .app-menu-list .menu-group-list-section {
    max-height: calc(100vh - 150px);
  }
</style>
