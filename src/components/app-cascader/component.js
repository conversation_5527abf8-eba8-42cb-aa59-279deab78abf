import { mapState } from 'vuex'
import usedupTips from '../usedup-tips'
import { getHotModulesAndMenus } from '../../config/jw-config'

export default {
  name: 'cascader',
  props: {
    data: Array,
    show: { default: false, type: Boolean }
  },
  data() {
    return {
      currentIndex: -1,
      hotModulesAndMenus: getHotModulesAndMenus()
    }
  },
  watch: {
    show(val) {
      if ($util.isJSJW() && val) {
        this.hotModulesAndMenus = getHotModulesAndMenus()
      }
    }
  },
  computed: {
    ...mapState('appState', {
      headerMenus: state => state.headerMenus
    })
  },
  mounted() {
    // $(this.$refs.headerMenuBox).css('height', window.innerHeight - 500)
  },
  methods: {
    pathClickMaidian(val) {
      $util.zhugeTrackOneLevel(val.title + '--from-左上角')
    },
    leaveMenu() {
      this.$emit('update:show', false)
    },
    pathClick(path, item) {
      this.$emit('update:show', false)

      if (item.sign === 'NA') {
        if (!__QCC__) {
          this.$uiService.alertWarning('暂无权限', { hidecancel: true })
        } else {
          usedupTips.showCommonBusinessTip(this, '您当前未开通该模块，请联系商务')
        }
        return
      }
      this.$globalutil.getIsCheckSMS(item, () => {
        this.$router.push(path)
      })
    },

    showChild(index) {
      this.currentIndex = index
    },
    getTilte(title) {
      var list = []
      var s = ''
      if (title.indexOf('·') > -1) {
       list = title.split('·')
        if (list.length) {
          s = list[0]
        }
        return s
      } else if (title.indexOf('（') > -1) {
        list = title.split('（')
        if (list.length) {
           s = list[0]
        }
        return s
       } else {
         return title
       }
    }
  }
}
