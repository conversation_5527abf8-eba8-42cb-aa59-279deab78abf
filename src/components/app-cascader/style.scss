@import './../../styles/common.scss';
@import '../../styles/baseFlexAdapter.scss';


.header-menu-box {
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 50px;
  height: calc(100vh - 50px);
  width: 100%;
  z-index: 999999;
  left: 0;

  .cascader-m-box {
    width: 100%;
    height: 130px;
    background-color: white;
    @include flex-def;
  }

  .section-box {
    position: relative;
    width: 130px;
    height: 130px;
    @include flex-def;
    flex-direction: column;
    @include flex-zCenter;
    @include flex-cCenter;
    cursor: pointer;

    .r-box {
      text-align: center;
    }

    .section-icon {
      // width: 36px;
      // height: 36px;
      font-size: 30px;
      color: $header-drop-color;

    }

    .section-title {
      width: 100%;
      margin-top: 10px;
      color: $header-drop-color;
      font-size: 14px;
      text-align: center;
    }

    .new-img {
      width:47px;
      height:47px;
      position:absolute;
      top:0;
      right:0;
    }

    .new-module {
      position: absolute;
      top: 25px;
      right: 20px;
      width: 23px;
    }
  }

  .isactive {

    background-color: $header-drop-color;

    .section-icon {
      color: white;
    }

    .section-title {
      color: white;
    }

  }
}


.border-right {
  border-right: 1px solid #eee;
}
