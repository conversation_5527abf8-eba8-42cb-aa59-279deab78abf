<template>
  <div class="header-menu-box" ref="headerMenuBox">
    <div class="cascader-m-box" v-if="headerMenus && headerMenus.length" @mouseleave="leaveMenu">
      <div class="section-box border-right"
           :class="[{isactive:currentIndex === index}]"
           v-for="(item,index) in headerMenus" :data="item" :key="item.id"
           @click="pathClick(item.path, item);pathClickMaidian(item)"
           @mouseenter="showChild(index)" :to="item.path">
        <i class="iconfont section-icon" :class="item.icon"></i>
        <div class="section-title">{{getTilte(item.title)}}</div>
        <img class="new-img" v-if="item.sign == 'N'" src="../../assets/images/icon_new_label.png">
        <img class="new-img" v-if="item.sign =='H'" src="../../assets/images/icon_hot_label.png">
        <img class="new-img" v-if="item.sign =='NA' && !isJSJW" src="../../assets/images/icon-not-open.png">
        <img v-if="hotModulesAndMenus.modules.includes(item.func)"
             class="new-module" src="/img/icon/icon-new.svg"
             >
      </div>
    </div>
  </div>
</template>

<script src="./component.js"></script>
<style scoped lang="scss" src="./style.scss"></style>
