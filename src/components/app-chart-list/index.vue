<template>
  <div class="tchart-list clearfix">
    <div v-for="(item, index) in list"
         :key="`tchart-list_${index}`"
         class="col" :class="`col-${item.col}`" v-show="shouldHide(item)">
      <div class="item" :style="{ height: height }">
        <div class="name">{{item.name}}</div>
        <slot name="tchart-btns" :item="item"></slot>
        <template v-if="item.option&&isNewEcharts">
          <slot
            v-if="item.customView"
            :name="item.customView"
            :chart="item"
            :option="item.option"></slot>
          <app-echarts-new v-else :height="height" :option="mergeOption(item.option)" :type="item.option&&item.option.series[0].type" :chart-obj="item" v-bind="$attrs" v-on="$listeners"></app-echarts-new>
        </template>
        <app-echarts v-else-if="item.option" :height="height" v-on="$listeners" v-bind="$attrs" :option="mergeOption(item.option)"></app-echarts>
        <app-nodata v-if="!item.option && item.isLoaded" :old="true" :padding="30" :absolute="true"></app-nodata>
      </div>
    </div>
  </div>
</template>
<script src="./component.js"></script>
