@import "../../styles/common";

i.jt {
  width: 0;
  height: 0;

  display: inline-block;
  vertical-align: middle;
  margin-left: 5px;

  &.jtsx {
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
  }

  &.jtzy {
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
  }

  &.jtshang {
    border-bottom: 4px dashed #666666;
  }

  &.jtxia {
    border-top: 4px dashed #666666;
  }

  &.jtz<PERSON> {
    border-right: 4px dashed #666666;
  }

  &.jtyou {
    border-left: 4px dashed #BBBBBB;
  }
}

.custom-item-radio {
  width: 100%;
  align-items: center;
  display: flex;
  ::v-deep{

    .el-radio__label {
      font-weight: normal;
      display: flex;
      align-items: center;
    }
  }
}
.custom-item-checkbox {
  width: 100%;
  align-items: center;
  display: flex;
  border-radius: 2px;

  ::v-deep {
    .el-checkbox__input {
      display: flex;
      align-items: center;
    }

    .el-checkbox__label {
      flex: 1;
      color: #333333;
      // padding-left: 5px;
      padding-left: 0;
      line-height: 22px !important;
      font-weight: normal;
      display: inline-flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  &.has-value {
    color: $color-primary;

    ::v-deep {
      .el-checkbox__label {
        color: $color-primary;
      }
    }
  }

  &.disableCheckBox {
    ::v-deep {
      .el-checkbox__input {
        display: none;
      }
    }
  }


  &.has-value,
  &:hover {
    .jtshang {
      border-bottom: 4px dashed $color-primary;
    }

    .jtxia {
      border-top: 4px dashed $color-primary;
    }
  }

  &:hover {
    ::v-deep {
      .el-checkbox__label {
        color: $color-primary;
      }
    }

    .jtzuo {
      border-right: 4px dashed $color-primary;
    }

    .jtyou {
      border-left: 4px dashed $color-primary;
    }
  }

  &.button-type {
    ::v-deep {
      .el-checkbox__label {
        padding-right: 10px;
      }
    }
  }

  &.has-value.button-type {
    background: #128BED;

    ::v-deep {
      .el-checkbox__label {
        color: #FFFFFF;
      }
    }

    .jtshang {
      border-bottom: 4px dashed #FFFFFF;
    }

    .jtxia {
      border-top: 4px dashed #FFFFFF;
    }
  }

  &.pop-show-checkbox {

    //background: #F2F8FE;
    ::v-deep {
      .el-checkbox__label {
        color: $color-primary;
      }
    }

    .jtzuo {
      border-right: 4px dashed $color-primary;
    }

    .jtyou {
      border-left: 4px dashed $color-primary;
    }
  }
}

.icon-button-reset {
  color: #999999;
  font-size: 14px;

  .icon-heimingdanchexiao {
    font-size: 14px;
    color: #999999;
    padding-right: 5px;
  }

  &:hover {
    color: $color-primary;

    .icon-heimingdanchexiao {
      color: $color-primary;
    }
  }
}

.icon-button-sure {
  color: $color-primary;
  font-size: 14px;

  &:hover {
    color: $color-primary-hover;
  }
}

.import-btn {
  margin-left: 10px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FAFAFA;
  padding: 9px 10px;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  cursor: pointer;

  >span {
    display: block;
  }

  .iconfont {
    color: #128bed;
    font-size: 12px;
    margin-right: 8px;
  }

  .num {
    color: #F04040;
    margin-left: 5px;
    margin-right: 5px;
  }

  .reset {
    margin-left: 10px;

    &:hover {
      color: $color-primary-hover;
    }
  }

  &.has-import-btn {
    color: #128bed;

    .import-btn-text {
      color: #333;
    }
  }

  &:hover {
    .import-btn-text {
      color: #128BED;
    }

    &.has-import-btn {

      .import-btn-text,
      em {
        color: #128BED;
      }
    }
  }

  &.disable {
    cursor: not-allowed;

    .iconfont {
      color: #bbbbbb;
    }

    .import-btn-text {
      color: #999999;
    }
  }

}
