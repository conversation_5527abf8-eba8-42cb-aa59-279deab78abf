<template>
  <el-checkbox-group class="group-wrap" v-model="checkboxGroup" @change="onChange" :disabled="disabled">
    <template v-if="checkBoxType === 'button'">
      <el-checkbox-button
        v-show="!option.hidden"
        class="bt-wrap"
        v-for="(option,index) in options"
        :key="option.name + option.code + index"
        :label="option.code"
      >
        {{ option.name }}
        <template v-if="enableCount">(<span class="count">{{ option.count }}</span>)</template>
      </el-checkbox-button>
    </template>
    <template v-else>
      <el-checkbox
        v-show="!option.hidden"
        class="checkbox-wrap"
        :style="{width:handleLabelWidth(option.name,labelWidth)}"
        v-for="(option,index) in options"
        :key="option.name + option.code + index"
        :label="option.code"
        :indeterminate="!!isIndeterminates.find(code=>code === option.code)"
      >
        {{ option.name }}
        <template v-if="enableCount">(<span class="count">{{ option.count }}</span>)</template>
      </el-checkbox>
    </template>
  </el-checkbox-group>
</template>

<script>
import { handleLabelWidth } from './tools'

export default {
  name: 'check-box-button',
  props: {
    checkBoxType: { type: String, default: 'button' },
    options: { type: Array, default: () => [] },
    enableCount: { type: Boolean, default: false },
    bindComponents: { default: null },
    labelWidth: { default: null },
    codeIsNumber: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
    includeIndeterminate: { type: Boolean, default: false }
  },
  data() {
    return {
      checkboxGroup: [],
      isIndeterminates: [],
      handleLabelWidth: handleLabelWidth
    }
  },
  mounted() {
    this.$emit('update:bindComponents', this)
  },
  methods: {
    setVmodel(newValue) {
      this.isIndeterminates = []
      let result = []
      this.options.forEach(i => {
        let match = newValue.find(j => {
          if (this.codeIsNumber) {
            return parseInt(j.code) === parseInt(i.code)
          }
          return j.code === i.code
        })
        if (match) {
          if (this.includeIndeterminate && match.isIndeterminate) {
            this.isIndeterminates.push(i.code)
          } else {
            result.push(i.code)
          }
        }
      })
      this.checkboxGroup = result
    },
    onChange(value) {
      this.options.forEach(i => {
        let match = this.isIndeterminates.find(m => m === value.find(z => z === i.code))
        if (match) {
          this.checkboxGroup = this.checkboxGroup.filter(m => m !== match)
          this.isIndeterminates = this.isIndeterminates.filter(m => m !== match)
        }
      })

      let result = this.options.map(i => {
        let item = { ...i }
        // 配合commodity-server-pop组件的非全选状态使用
        if (this.includeIndeterminate) {
          if (this.isIndeterminates.includes(i.code)) {
            item.specialParentItem = true
            item.isIndeterminate = true
            item.isAll = false
          }
          if (this.checkboxGroup.includes(i.code)) {
            item.specialParentItem = true
            item.isIndeterminate = false
            item.isAll = true
          }
        }
        if (value.includes(i.code)) { // 代表是从此组件返回的数据，防止commodity-server-pop设置的值影响
          item.isInner = true
        }
        return item
      })
      result = result.filter(i => {
        return this.checkboxGroup.find(m => m === i.code) || i.specialParentItem
      })

      this.$emit('change', result)
    }
  }
}
</script>

<style scoped lang="scss">
.group-wrap {
  display: flex;
  flex-wrap: wrap;

  ::v-deep {
    .el-checkbox__label {
      line-height: 22px;
      padding-left: 5px;
      font-weight: normal;
    }
  }
}

.bt-wrap {
  border: 0;
  padding: 1px 8px;
  line-height: 22px;
  font-size: 14px;
  box-sizing: border-box;
  border-radius: 2px;
  display: block;
  margin-right: 30px;
  margin-bottom: 15px;

  .count {
    color: #999999;
  }

  ::v-deep {
    .el-checkbox-button__inner {
      background: none;
      border: 0;
      border-radius: 2px;
      padding: 0;
      transition: none;
      display: block;
      line-height: 22px;
      box-shadow: inherit;
      color: #333333;
      font-weight: normal;

      &:hover {
        color: #128BED;

        .count {
          color: #128BED;
        }
      }
    }
  }

  &.is-checked, &:hover {
    background: #128BED;

    .count {
      color: #FFFFFF;
    }

    ::v-deep {
      .el-checkbox-button__inner {
        color: #FFFFFF;

        &:hover .count {
          color: #FFFFFF;
        }
      }
    }
  }
}

.checkbox-wrap {
  margin-right: 30px;
  margin-bottom: 15px;
  color: #333333;
}
</style>
