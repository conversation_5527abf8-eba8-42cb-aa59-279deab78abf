<template>
  <el-checkbox-group class="group-wrap" v-model="checkboxGroup" @change="onChange">
    <el-checkbox-button
      class="bt-wrap"
      v-for="(option,index) in options"
      :key="index"
      :label="option.code"
    >{{option.name }}(<span class="count">{{ option.count }}</span>)
    </el-checkbox-button>
  </el-checkbox-group>
</template>

<script>
export default {
  name: 'check-box-button',
  props: {
    options: { type: Array, default: () => [] },
    bindComponents: { default: null }
  },
  data() {
    return {
      checkboxGroup: []
    }
  },
  mounted() {
    this.$emit('update:bindComponents', this)
  },
  methods: {
    setVmodel(newValue) {
      this.checkboxGroup = newValue.map(i => i.code)
    },
    onChange(value) {
      let result = this.options.filter(i => {
        return value.find(code => { return code === i.code })
      })
      this.$emit('change', result)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .el-checkbox__label {
    color: #333333;
  }
}
</style>
