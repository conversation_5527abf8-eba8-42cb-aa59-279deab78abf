<template>
  <div class="system-conditions border-radius-4">
    <div
      ref="content"
      class="filter-conditions"
      :class="[isTheme2 ? 'theme2' : '']"
      :style="{height:shrinkHeight + 'px'}"
    >
      <div
        class="mask"
        v-if="canShrink && shrink"
        @click="shrink = !shrink;getShrinkHeight()"
      >
      </div>
      <div class="top-control" data-appear="1" v-if="appearTop">
        <p>{{ title }}</p>
        <div class="control-item-wrap">
          <div class="control-item" v-if="editConditionOptions.enable" @click="editCondition">
            <span class="iconfont icon-xinzeng1 size14"></span>
            <p>{{ editConditionOptions.buttonName }}</p>
          </div>
          <div class="control-item" style="margin-left: 10px" v-if="saveConditionStatus && saveConditionDialog.position === 2" @click="saveCondition">
            <span class="iconfont icon-baocunshaixuan1 size14"></span>
            <p>{{ saveConditionDialog.buttonName }}</p>
          </div>
          <div class="control-line" v-if="savedConditionDialog.enable && savedConditionDialog.position === 1 && savedConditionsList.length > 0"></div>
          <div class="control-item" v-if="savedConditionDialog.enable && savedConditionDialog.position === 1 && savedConditionsList.length > 0" @click="savedCondition">
            <span class="iconfont icon-baocunshaixuan size14"></span>
            <p>{{ savedConditionDialog.buttonName }}</p>
            <p>{{ savedConditionsList.length }}</p>
          </div>
        </div>
      </div>
      <template v-if="keyInputComponentOption">
        <div :data-appear="keyInputComponentOption.dataAppearType" class="key-input-wrap" v-show="!keyInputComponentOption.hide">
          <div :class="['top-input-wrap',keyInputComponentOption.buttonColor === 'orange' ? 'key-input-button-orange' : '']">
            <app-input
              :disabled="keyInputComponentOption.disabled"
              :clearable="true"
              class="app-filter-input-wrap"
              :class="[keyInputPreSelectComponentOption ? 'app-filter-input-pre-wrap' : '']"
              :ref="'component-' + keyInputComponentOption.keyName"
              :placeholder="keyInputComponentOption.placeholder || '请输入关键词'"
              @change="(val)=>{changeInput(val,keyInputComponentOption)}"
              @clearValue="changeInputEmpty(keyInputComponentOption)"
              @keyupEnter="()=>{handleInput(keyInputComponentOption)}">
              <div class="app-filter-input-dr" slot="prepend" v-if="keyInputPreSelectComponentOption">
                <drop-down-custom
                  trigger="hover"
                  :key="keyInputPreSelectComponentOption.keyName"
                  :showText="keyInputPreSelectComponentOption.label"
                  @change="(value)=>{dropDownChange(value,keyInputPreSelectComponentOption)}"
                  :ref="'component-' + keyInputPreSelectComponentOption.keyName"
                  :options="keyInputPreSelectComponentOption.children"
                  :setRowNum="keyInputPreSelectComponentOption.row"
                  :appearTime="keyInputPreSelectComponentOption.appearTime"
                  :appearCustomNum="keyInputPreSelectComponentOption.appearCustomNum"
                  :appearSpecific="true"
                  :disabled="addKeyWordsDialog.disableAddKeyword"
                  :class="[addKeyWordsDialog.disableAddKeyword ? 'disabled-wrap' : '']"
                ></drop-down-custom>
                <div class="search-border"></div>
              </div>
            </app-input>
            <app-button :disabled="keyInputComponentOption.disabled" @click="handleInput(keyInputComponentOption)">查一下</app-button>
            <slot name="appTopInputCenter"></slot>
            <add-key-words
              v-if="addKeyWordsDialog.enable"
              ref="addKeyWords"
              :functionTableId="functionTableId"
              @addSuccess="addSuccess"
              @resetKeywords="resetKeywords"
              :disable="addKeyWordsDialog.disableAddKeyword"
              :subTitle="addKeyWordsDialog.subTitle"
            />
            <import-company
              ref="importCompany"
              v-if="enableImportCompany"
              :functionTableId="functionTableId"
              @importSuccess="importSuccess"
              @resetCompany="resetCompany"
              :disable="disableImportCompany"
            />
          </div>
          <div class="control-item" v-if="savedConditionDialog.enable && savedConditionDialog.position === 2 && savedConditionsList.length > 0" @click="savedCondition">
            <span class="iconfont icon-baocunshaixuan size14"></span>
            <p>{{ savedConditionDialog.buttonName }}</p>
            <p>{{ savedConditionsList.length }}</p>
          </div>
        </div>
      </template>
      <div v-if="isTheme2" class="line"></div>
      <div data-appear="1" class="option-item selected-conditions" ref="conditions" style="padding-bottom: 10px" v-show="screenAppearText.length > 0">
        <div class="left">已选条件</div>
        <div class="middle" style="height: unset;flex: 1">
          <el-popover v-for="(item,index) in screenAppearText" placement="bottom" trigger="hover" popper-class="default-popover-tooltip tool-auto-width400" :key="'screenAppearText' + index" :open-delay="200">
            <p> {{ item.label }}：{{ item.mergeName }}</p>
            <div slot="reference"
                 :class="['conditions overrflow-with-ellipsis', 'active']"
                 @click="removeItem(item)">
              {{ item.label }}：{{ item.mergeName }}
              <span class="el-icon-close remove-icon"></span>
            </div>
          </el-popover>
        </div>
        <div class="right">
          <div v-if="saveConditionStatus && saveConditionDialog.position === 1" class="control-item" @click="saveCondition">
            <span class="iconfont icon-baocunshaixuan1"></span>
            <p>{{ saveConditionDialog.buttonName }}</p>
          </div>
          <div class="control-item" @click="clearCondition">
            <span class="iconfont icon-heimingdanchexiao"></span>
            <p>{{ resetFilterLabel }}</p>
          </div>
        </div>
      </div>
      <template v-for="(option,optionIndex) in options">
        <div v-if="option.componentType === 'keyInput' && !option.isTop" :data-appear="option.dataAppearType" class="key-input-wrap" v-show="!option.hide" :key="'option' + optionIndex">
          <app-input
            :disabled="option.disabled"
            :clearable="true"
            class="app-filter-input-wrap"
            :ref="'component-' + option.keyName"
            :placeholder="option.placeholder || '请输入关键词'"
            @change="(val)=>{changeInput(val,option)}"
            @clearValue="changeInputEmpty(keyInputComponentOption)"
            @keyupEnter="()=>{handleInput(option)}" />
          <app-button :disabled="option.disabled" @click="handleInput(option)">查一下</app-button>
        </div>
        <template v-if="option.componentType !== 'keyInput' && option.componentType !== 'keyInputPre'">
          <div v-show="!option.hide" :data-appear="option.dataAppearType" class="option-item" :class="option.className" :key="'option' + optionIndex">
            <div class="left" :style="{lineHeight:option.componentType === 'inputDialog' ? '36px' : '',width: option.leftWidth + 'px'}">
              {{ option.bindLineName || option.label }}
              <app-helptips
                v-if="option.labelToolTip && option.labelToolTip.enable"
                width="633"
                :mustShow="true"
                :helpText="option.labelToolTip.title.text"
                :subHelpText="option.labelToolTip.title.explain"
                iconClass="iconfont icon-icon_zhushi tool-izhushiv2 font14"
                className="popover-table"
                :liststyle="{ display: 'none' }"
              >
                <app-table
                  class="table-wrap"
                  slot="listBottom"
                  :columsProp="option.labelToolTip.columsProp"
                  :controlData="{showDatas:option.labelToolTip.controlData.showDatas}"
                  :paginationProp="{show:false}"
                  autoheight
                  :style="option.tableStyle"
                ></app-table>
              </app-helptips>
            </div>
            <template v-if="option.bindLineName">
              <div class="middle">
                <template v-for="(optionItem,optionItemIndex) in option.childrenComponents">
                  <drop-down-time-range
                    v-if=" optionItem.componentType === 'time'"
                    v-show="!optionItem.hide"
                    trigger="hover"
                    :key="optionItemIndex + 'more1' + optionItem.keyName"
                    title="认证时间"
                    @change="(value)=>{timeChange(value,optionItem)}"
                    :style="{marginRight: '30px',width:option.labelWidth ? option.labelWidth : ''}"
                    :ref="'component-' + optionItem.keyName"
                  ></drop-down-time-range>
                  <single-drop-down-vertical
                    :style="{width:option.labelWidth ? option.labelWidth : ''}"
                    v-else-if="optionItem.componentType === 'singleDropVertical'"
                    v-show="!optionItem.hide"
                    trigger="hover"
                    :key="optionIndex + 'more2' + optionItem.keyName"
                    @change="(value)=>{dropDownChange(value,optionItem)}"
                    :ref="'component-' + optionItem.keyName"
                    :unit="optionItem.unit"
                    :showText="optionItem.label"
                    :options="optionItem.children"
                    :appearTime="optionItem.appearTime"
                    :canStartEmpty="optionItem.canStartEmpty"
                    :canEndEmpty="optionItem.canEndEmpty"
                    :customNumberRegType="optionItem.customNumberRegType"
                    :appearCustomNum="optionItem.appearCustomNum"
                    :setRowNum="optionItem.row"
                    :setColumnNum="optionItem.column"
                    :enableCount="optionItem.enableCount"
                    :offsetX="optionItem.customOffsetX"
                    :offsetY="optionItem.customOffsetY"
                  ></single-drop-down-vertical>
                  <drop-down-cascader
                    :style="{width:option.labelWidth ? option.labelWidth : ''}"
                    v-else-if="optionItem.componentType === 'dropDownCascader'"
                    v-show="!optionItem.hide"
                    :key="optionIndex + 'more3' + optionItem.keyName"
                    trigger="hover"
                    class="dr-wrap"
                    style="padding-bottom: 0"
                    :disableCount="!optionItem.enableCount"
                    :ref="'component-' + optionItem.keyName"
                    :isLimit="optionItem.hasOwnProperty('isLimit') ? optionItem.isLimit : false"
                    :propMore="propMore"
                    :options="optionItem.children"
                    :defaultText="optionItem.label"
                    @change="(selectMenus,originSelects,b,c,d,isAll)=>{dropDownCascaderChange(selectMenus,optionItem)}">
                  </drop-down-cascader>
                  <drop-down-custom
                    v-else
                    :style="{width:option.labelWidth ? option.labelWidth : ''}"
                    v-show="!optionItem.hide"
                    trigger="hover"
                    :key="optionIndex + 'more4' + optionItem.keyName"
                    @change="(value)=>{dropDownChange(value,optionItem)}"
                    :ref="'component-' + optionItem.keyName"
                    :showText="optionItem.label"
                    :options="optionItem.children"
                    :setRowNum="optionItem.row"
                    :appearTime="optionItem.appearTime"
                    :appearCustomNum="optionItem.appearCustomNum"
                  ></drop-down-custom>
                </template>
              </div>
            </template>
            <template v-else>
              <div class="middle" :ref="option.keyName" :style="{height:option.middleHeight }">
                <flat-checkbox-group
                  v-if="option.componentType === 'flat-checkbox-group'"
                  v-show="!option.hide"
                  :ref="'component-' + option.keyName"
                  :option="option"
                  @change="(selectMenus) => { dropDownCascaderCustomChange(selectMenus,option) }"
                />
                <drop-down-cascader-custom
                  v-if="option.componentType === 'dropDownCascaderCustom'"
                  v-show="!option.hide"
                  :ref="'component-' + option.keyName"
                  :option="option"
                  @change="(selectMenus)=>{dropDownCascaderCustomChange(selectMenus,option)}"
                />
                <check-box-button-group
                  v-if="option.componentType === 'checkboxGroup'"
                  v-show="!option.hide"
                  :options="option.children"
                  :ref="'component-' + option.keyName"
                  :checkBoxType="option.checkBoxType"
                  :labelWidth="option.labelWidth"
                  :enableCount="option.enableCount"
                  :codeIsNumber="option.codeIsNumber"
                  :disabled="option.disabled"
                  :includeIndeterminate="option.includeIndeterminate"
                  @change="(value)=>{checkButtonChangeGroup(value,option)}"
                ></check-box-button-group>
                <radio-group
                  v-if="option.componentType === 'radioGroup'"
                  v-show="!option.hide"
                  :labelWidth="option.labelWidth"
                  :radioType="option.radioType"
                  :options="option.children"
                  :appearTime="option.appearTime"
                  :appearCustomNum="option.appearCustomNum"
                  :canStartEmpty="option.canStartEmpty"
                  :canEndEmpty="option.canEndEmpty"
                  :customNumberRegType="option.customNumberRegType"
                  :enableChangeButtonText="option.enableChangeButtonText"
                  :enableDisabledDate="option.enableDisabledDate"
                  :customIsRadio="option.customIsRadio"
                  :unit="option.unit"
                  :ref="'component-' + option.keyName"
                  :enableCount="option.enableCount"
                  @change="(value)=>{radioButtonChangeGroup(value,option)}"
                ></radio-group>
                <radio-group-new
                  v-if="option.componentType === 'radioGroupNew'"
                  v-show="!option.hide"
                  :labelWidth="option.labelWidth"
                  :radioType="option.radioType"
                  :options="option.children"
                  :appearTime="option.appearTime"
                  :appearCustomNum="option.appearCustomNum"
                  :canStartEmpty="option.canStartEmpty"
                  :canEndEmpty="option.canEndEmpty"
                  :customNumberRegType="option.customNumberRegType"
                  :enableChangeButtonText="option.enableChangeButtonText"
                  :enableDisabledDate="option.enableDisabledDate"
                  :customIsRadio="option.customIsRadio"
                  :unit="option.unit"
                  :ref="'component-' + option.keyName"
                  :enableCount="option.enableCount"
                  @change="(value)=>{radioButtonChangeGroup(value,option)}"
                ></radio-group-new>
                <commodity-server-pop
                  v-if="option.componentType === 'inputDialog'"
                  v-show="!option.hide"
                  :ref="'component-' + option.keyName"
                  @change="(value)=>{inputPopChange(value,option)}"
                  :limit="option.limit"
                  :keyName="option.keyName"
                  :label="option.label"
                  :relationKey="option.relationKey"
                  :relationLabel="option.relationLabel"
                ></commodity-server-pop>
              </div>
            </template>
            <div class="right" @click="toggle(option)" v-if="option.showMore">
              {{ option.expand ? '收起' : '更多' }}
              <span>
                <i class="iconfont icon-wenzilianjiantou font14" :class="[option.expand ? 'rotate--90' : 'rotate-90' ]"></i>
              </span>
            </div>
          </div>
        </template>
      </template>
    </div>
    <div v-if="canShrink" class="tixing">
      <div @click="shrink=!shrink;getShrinkHeight()"></div>
      <i :class="['iconfont',shrink ? 'icon-jiantou-zhankai' : 'icon-jiantou-shouqi']"></i></div>
    <app-loading
      v-if="loading"
    />
  </div>
</template>

<script src="./component.js"></script>
<style scoped lang="scss" src="./style.scss"></style>
