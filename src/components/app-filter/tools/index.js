export const handleLabelWidth = (value, labelWidth, numberValue = '') => {
  let num = parseInt(labelWidth, 10)
  let maxLength = 0
  // if (num === 100) {
  //   maxLength = 6
  // } else if (num === 120) {
  //   maxLength = 9
  // }
  if (maxLength) {
    let multiple = Math.ceil((value.length + (numberValue ? Math.ceil(numberValue.length / 2) : 0)) / maxLength)
    return multiple * num + ((multiple - 1) * 30) + 'px'
  }
  return labelWidth
}
