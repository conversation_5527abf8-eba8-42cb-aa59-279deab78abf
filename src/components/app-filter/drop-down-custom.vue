<template>
  <div
    class="d-wrap"
    :class="[
      {'drop-down-button': theme === 'button'},
      {'drop-down-button--selected': theme === 'button' && resultList.length > 0},
    ]"
  >
    <el-popover
      placement="bottom-start"
      :trigger="triggerTrans"
      :popper-class="popoverClass"
      :visible-arrow="false"
      :value="wrapPopVisible"
      @show="popShow"
      @hide="popHide"
      ref="popover"
      :open-delay="100"
      :close-delay="150"
      :transition="transition"
    >
      <div class="p-wrap" ref="allItem" :class="[setRowNum ? 'isTransverse' : '']" :style="popStyle" @mouseenter="wrapContentPopShow" @mouseleave="wrapPopHide">
        <el-checkbox :disabled="disabled" v-if="hasCheckAll" :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
        <el-checkbox
          v-for="(item,index) in options" v-model="item.checked" :label="item.code" :key="index"
          :disabled="disabled"
          @change="(value)=>{singleChange(value,item)}"
          :style="{marginBottom: (setRowNum && ((index+1) % setRowNum === 0)) ? '0' : ''}"
        >{{ item.name }}
        </el-checkbox>
        <drop-down-time-range
          v-if="appearTime"
          class="custom-time-wrap"
          :style="{marginBottom: (setRowNum && ((this.options.length+1) % setRowNum === 0)) ? '0' : ''}"
          :arrowType="0"
          :isCheckBox="true"
          :getWrapRef="getTimePopWrapRef"
          :limit15Days="limit15Days"
          @timeCheckboxClick="wrapPopHide"
          @timeMouseLeave="wrapPopHide"
          @timePopClearTimeout="wrapPopClearTime"
          @change="timeChange"
          ref="customTimeRef"
        ></drop-down-time-range>

        <drop-down-numbers
          v-if="appearCustomNum"
          :canStartEmpty="canStartEmpty"
          :canEndEmpty="canEndEmpty"
          :customNumberRegType="customNumberRegType"
          :unit="unit"
          placement="right-end"
          :isCheckBox="true"
          :arrowType="0"
          class="custom-number-wrap"
          @customPopClearTime="wrapPopClearTime"
          @customPopHideEvent="wrapPopHide"
          @change="customNumberChange"
          ref="customNumberRef"
          :style="{marginBottom: (setRowNum && ((this.options.length+1) % setRowNum === 0)) ? '0' : ''}"
        />
      </div>
      <div
        slot="reference"
        class="line"
        :class="[isActive || checkedNum > 0 ? 'isActive' : '']"
        @mouseenter="wrapPopShow"
        @mouseleave="wrapPopHide"
      >
        <el-checkbox v-if="prefixCheckAll" :disabled="disabled" style="margin-right: 3px;margin-bottom: 1px;"  :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange"></el-checkbox>
        <template v-if="appearSpecific">
          <span class="b-wrap" :title="appearCheckedName">{{ appearCheckedName }}</span>
          <span v-if="theme === 'button' && countVisible && checkedNum > 0">{{ checkedNum }}</span>
          <span v-else-if="countVisible && checkedNum > 1" style="color: #666666">({{ checkedNum }})</span>
          <i class="caret" :class="[{'active': isActive}]"></i>
<!--          <i :class="[isActive ? 'jtshang' : 'jtxia']"></i>-->
        </template>
        <template v-else>
          <span class="b-wrap">{{ showText }}</span>
          <span v-if="countVisible && checkedNum > 0">{{ checkedNum }}</span>
          <i class="caret" :class="[{'active': isActive}]"></i>
<!--          <i :class="[isActive ? 'jtshang' : 'jtxia']"></i>-->
        </template>
        <i
          class="drop-down-button__reset-icon iconfont icon-shanchu3 font12"
          v-if="theme === 'button'"
          @click="handleReset"
        />
      </div>
    </el-popover>
  </div>
</template>

<script>
import moment from 'moment'
import dropDownNumbers from './drop-down-numbers'
import dropDownTimeRange from './drop-down-time-range'

export default {
  name: 'drop-down-custom',
  components: { dropDownNumbers, dropDownTimeRange },
  props: {
    transition: { type: String, default: 'fade-in-linear' },
    trigger: { type: String, default: 'manual' },
    showText: { type: String, default: '' },
    vmodel: { type: Array, default: () => [] },
    options: { type: Array, default: () => [] },
    appearTime: { type: Boolean, default: false },
    appearCustomNum: { type: Boolean, default: false },
    setRowNum: { type: Number, default: 0 }, // 设置横向显示的情况几行显示,0未竖向
    canStartEmpty: { type: Boolean, default: false },
    canEndEmpty: { type: Boolean, default: false },
    appearSpecific: { type: Boolean, default: false },
    hasCheckAll: { type: Boolean, default: false },
    prefixCheckAll: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
    customNumberRegType: { type: Number, default: 1 },
    unit: { type: String, default: '万' },
    countVisible: {
      type: Boolean,
      default: false
    },
    theme: {
      type: String,
      default: 'text'
    },
    popoverClass: {
      type: String,
      default: 'opopover app-popover dr-down-margin0'
    },
    limit15Days: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isTransverseWidth: 0,
      isActive: false,
      resultList: [],
      wrapPopVisible: false,
      hasCustomNumberValue: false,
      hasCustomTimeValue: false,
      checkAll: false
    }
  },
  computed: {
    triggerTrans() {
      return this.trigger === 'hover' || !this.trigger ? 'manual' : this.trigger
    },
    popStyle() {
      // 2为边框占据的
      if (this.setRowNum) {
        let style = {
          width: this.setRowNum ? (this.isTransverseWidth + 2 + 'px') : 'auto'
        }
        if (this.setRowNum) {
          style.height = 35 * this.setRowNum + 20 + 2 + 'px'
        }
        return style
      } else {
        return {
          maxHeight: '300px',
          overflowY: 'auto'
        }
      }
    },
    checkedNum() {
      let base = this.resultList.filter((i) => {
        return !i.isCustom
      }).length

      if (this.appearTime && this.hasCustomTimeValue) {
        base++
      }
      if (this.appearCustomNum && this.hasCustomNumberValue) {
        base++
      }
      return base
    },
    appearCheckedName() {
      if (this.appearSpecific && this.resultList.length > 0) {
        return this.resultList.map(i => i.name).join('，')
      }
      return this.showText
    },
    isIndeterminate() {
      return this.resultList.length !== 0 && (this.options.length > this.resultList.length)
    }
  },
  created() {
    this.options.forEach((i, index) => {
      if (i.checked === undefined) {
        this.$set(i, 'checked', false)
      }
    })
  },
  mounted() {
    this.$emit('update:bindComponents', this)
  },
  methods: {
    handleReset () {
      this.setVmodel([])
      this.$emit('update:vmodel', this.resultList)
      this.$emit('change', this.resultList)
    },
    checkIsAll() {
      this.checkAll = (this.resultList.length === this.options.length) && (this.resultList.length > 0)
    },
    setVmodel(newValue) {
      this.resultList = []
      let list = newValue.filter((i) => {
        return !i.isCustom
      })

      if (this.appearTime) {
        let appearTime = newValue.find((i) => {
          return i.isCustom
        }) // 自定义时间模式，最多只有一个

        if (appearTime) {
          this.$refs.customTimeRef.setVmodel(appearTime.code ? appearTime.code.split('-') : [])
          this.resultList.push(appearTime)
        } else {
          this.$refs.customTimeRef.setVmodel([])
        }
        this.hasCustomTimeValue = !!appearTime
      }

      if (this.appearCustomNum) {
        let appearNum = newValue.find((i) => {
          return i.isCustom
        }) // 自定义数字模式，最多只有一个
        if (appearNum) {
          let num = appearNum.code.split('-')
          this.resultList.push(appearNum)
          this.$refs.customNumberRef.setVmodel(num)
        } else {
          this.$refs.customNumberRef.setVmodel([])
        }
        this.hasCustomNumberValue = !!appearNum
      }

      this.resultList.push(...list)
      this.retCheckList()
    },
    handleCheckAllChange(value) {
      this.options.forEach(i => {
        this.$set(i, 'checked', value)
      })
      if (value) {
        this.resultList = [...this.options]
      } else {
        this.resultList = []
      }
      this.$emit('update:vmodel', this.resultList)
      this.$emit('change', this.resultList)
    },
    retCheckList() {
      this.options.forEach((i) => {
        let match = this.resultList.find((j) => {
          return j.code === i.code
        })
        i.checked = !!match
      })
      this.checkIsAll()
    },
    singleChange(value, item) {
      this.resultList = this.resultList.filter((i) => {
        return i.isCustom
      })

      this.options.forEach(i => {
        if (i.checked) {
          this.resultList.push(i)
        }
      })
      this.checkIsAll()
      this.$emit('update:vmodel', this.resultList)
      this.$emit('change', this.resultList)
      this.resetPopTopPos()
    },
    resetPopTopPos() {
      this.$nextTick(() => {
        if (this.$refs.popover) {
          this.$refs.popover.updatePopper()
        }
      })
      // 重新设置高度
    },
    timeChange(value) {
      this.resultList = this.resultList.filter((i) => {
        return i.isCustom !== this.appearTime
      })
      if (value && value.length > 0) {
        this.resultList.push({
          code: value[0] + '-' + value[1],
          name: moment(value[0]).format('YYYY-MM-DD') + '至' + moment(value[1]).format('YYYY-MM-DD'),
          type: this.options[0].type,
          isCustom: this.appearTime
        })
        this.hasCustomTimeValue = true
      } else {
        this.hasCustomTimeValue = false
      }
      this.$emit('update:vmodel', this.resultList)
      this.$emit('change', this.resultList)

      this.$refs.popover.doClose()
    },
    customNumberChange(value) {
      this.resultList = this.resultList.filter((i) => {
        return i.isCustom !== this.appearCustomNum
      })
      if (value) {
        let result = {
          ...value,
          type: this.options[0].type, // 潜力企业用到，后封装大组件不需要
          isCustom: this.appearCustomNum
        }
        this.resultList.push(result)
        this.hasCustomNumberValue = true
      } else {
        this.resultList = this.resultList.filter((i) => {
          return !i.isCustom
        })
        this.hasCustomNumberValue = false
      }
      this.$emit('update:vmodel', this.resultList)
      this.$emit('change', this.resultList)
    },
    popShow() {
      this.isActive = true
      this.$nextTick(() => {
        this.isTransverseWidth = 0
        if (this.setRowNum) {
          for (let i = 0; i < this.$refs.allItem.children.length; i++) {
            let e = this.$refs.allItem.children[i]
            if (e && e.offsetWidth && (i % this.setRowNum === 0)) {
              this.isTransverseWidth += e.offsetWidth
            }
          }
        }
      })
    },
    popHide() {
      this.isActive = false
    },
    wrapPopShow() {
      if (this.trigger === 'click') {
        return
      }
      this.wrapPopClearTime()
      if (this.openWrapTimeId) {
        clearTimeout(this.openWrapTimeId)
        this.openWrapTimeId = null
      }
      this.openWrapTimeId = setTimeout(() => {
        this.wrapPopVisible = true
        this.openWrapTimeId = null
      }, 100)

      this.$nextTick(() => {
        if (this.$refs.popover?.popperElm) {
          if (!this.$refs.popover.popperElm.onmouseleave) {
            this.$refs.popover.popperElm.onmouseleave = this.wrapPopHide
          }
          if (!this.$refs.popover?.popperElm.onmouseenter) {
            this.$refs.popover.popperElm.onmouseenter = this.wrapPopClearTime
          }
        }
      })
    },
    wrapContentPopShow() {
      if (this.wrapTimeId) {
        clearTimeout(this.wrapTimeId)
        this.wrapTimeId = null
      }
    },
    wrapPopHide() {
      if (this.trigger === 'click') {
        return
      }
      if (this.openWrapTimeId) {
        clearTimeout(this.openWrapTimeId)
        this.openWrapTimeId = null
      }
      if (this.wrapTimeId) {
        clearTimeout(this.wrapTimeId)
        this.wrapTimeId = null
      }
      this.wrapTimeId = setTimeout(() => {
        this.wrapPopVisible = false
      }, 150)
    },
    wrapPopClearTime() {
      if (this.wrapTimeId) {
        clearTimeout(this.wrapTimeId)
        this.wrapTimeId = null
      }
    },
    getTimePopWrapRef() {
      return this.$refs.allItem
    }
  }
}
</script>

<style lang="scss">

.customNumPop.el-popper {
  margin: 0 0 -5px 1px;
}
</style>
<style scoped lang="scss">
.d-wrap {
  margin-right: 30px;

  .line {
    display: flex;
    align-items: center;
    height: 22px;
    cursor: pointer;

    span {
      color: #333333;
      font-size: 14px;
      line-height: 14px;
      display: block;
      padding-right: 5px;
    }

    //i {
    //  width: 0;
    //  height: 0;
    //  border-right: 4px solid transparent;
    //  border-left: 4px solid transparent;
    //
    //  &.jtshang {
    //    border-bottom: 4px dashed #666666;
    //  }
    //
    //  &.jtxia {
    //    border-top: 4px dashed #666666;
    //  }
    //}

    .caret.active {
      border-top-color: #128BED;
      transform: rotate(180deg);
    }

    &.isActive {
      .b-wrap, span {
        color: #128BED;
      }

      .caret {
        border-top-color: #128BED;
      }
      //i {
      //  &.jtshang {
      //    border-bottom: 4px dashed #128BED;
      //  }
      //
      //  &.jtxia {
      //    border-top: 4px dashed #128BED;
      //  }
      //}
    }
  }

}

.p-wrap {
  border: solid 1px #E4E7ED;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 5px 0;
  position: relative;
  display: flex;
  flex-direction: column;

  ::v-deep {
    .el-checkbox {
      line-height: 34px;
      padding: 0 15px;
      box-sizing: border-box;
      width: 100%;
      margin-right: 0;
      font-weight: normal;

      &:hover {
        background: #F2F8FE;
      }
    }
  }

  &.isTransverse {
    padding: 10px 0;
    max-height: 268px;
    display: flex;
    flex-wrap: wrap;

    ::v-deep {
      .el-checkbox {
        line-height: 35px;
        width: unset;

      }
    }
  }


}

.datePickerWrap {
  visibility: hidden;
  width: 100%;
  height: 1px;
  overflow: hidden;
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.custom-number-wrap, .custom-time-wrap {
  ::v-deep {
    .custom-item-checkbox {
      padding: 6px 15px 6px 15px;

      .el-checkbox__label {
        padding-left: 10px;
      }
    }
  }
}

::v-deep {
  .el-checkbox__label {
    color: #333333;
  }
}

.drop-down-button {
  margin-right: 0;

  .line {
    border: 1px solid #D8D8D8;
    height: 32px;
    line-height: 30px;
    padding: 0 12px;
    border-radius: 2px;
    transition: .1s;

    &.isActive {
      border-color: #128BED;
    }

    .b-wrap {
      display: inline-block;
      max-width: 90px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &--selected {
    &:hover {
      .drop-down-button__reset-icon {
        display: inline-flex;
      }

      .caret {
        display: none;
      }
    }
  }

  &__reset-icon {
    display: none;
    margin-left: -4px;
    position: relative;
    right: -3px;
    color: #88c5f6;

    &:hover {
      color: #128bed;
    }
  }
}
</style>
