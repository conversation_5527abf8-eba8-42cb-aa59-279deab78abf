## app-filter 的使用方式
```vue
<app-filter
  :title="title"
  ref="filter"
  :options="options"
  @change="handleFilterChange"
  :appearTop="true"
></app-filter>
```
### 配置参数
```src/routes/sip-magnifier/components/magnifier-container/component.js```
```javascript
export default {
  data() {
    return {
      options: [
        {
          label: '企业名称',
          placeholder: '请输入企业名称',
          dataAppearType: '1',
          keyName: 'searchKey',
          expand: false,
          showMore: false,
          hide: false,
          /**
           * 参数介绍:
           * keyInput: 输入框独立配置组件类型,
           * dropDownCascader：级联下拉框组件类型 src/components/app-filter/drop-dwon-cascader.vue
           * time：时间组件类型  src/components/app-filter/drop-down-time-range.vue
           * singleDropVertical： src/components/app-filter/single-drop-down-custom.vue
           * flat-checkbox-group：  src/components/app-filter/flat-checkbox-group.vue
           * dropDownCascaderCustom： src/components/app-filter/drop-down-cascader-custom.vue
           * checkboxGroup： src/components/app-filter/check-box-button-group.vue
           * radioGroup： src/components/app-filter/radio-group.vue
           * radioGroupNew ： src/components/app-filter/radio-group-new.vue
           * inputDialog： src/components/app-filter/commodity-server-pop.vue
           *
           */
          componentType: 'keyInput',
          changeFun: this.handleResetCurrentPage,
          value: '',
          isTop: true //如果配置了isTop，则该组件会固定在顶部，否则按照顺序展示
        },
        {
          label: '行政地区',
          dataAppearType: '1',
          keyName: 'area',
          showMore: false, //展示 收起、更多
          expand: false, // 配合 showMore:true，控制收起、更多
          forceHiddenMore: true,
          hide: false,
          componentType: 'dropDownCascaderCustom',
          changeFun: this.handleResetCurrentPage,
          children: []
        },
        {
          label: '登记状态',
          dataAppearType: '1',
          keyName: 'status',
          expand: false,
          showMore: false,
          hide: false,
          forceHiddenMore: true,
          // componentType: 'dropDownCascaderCustom',
          componentType: 'flat-checkbox-group',
          changeFun: this.handleResetCurrentPage,
          children: this.getStatusData()
        }
      ]
    }
  }
}
```

### componentType 详细配置
#### 1、keyInput  输入框独立配置组件类型
```javascript
        {
          label: '关键词',
          dataAppearType: '1',
          keyName: 'searchKeyList',
          expand: false,
          showMore: false,
          hide: false,
          disabled: false,
          componentType: 'keyInput',
          changeFun: this.chooseChange,
          value: '',
          isTop: true,
          buttonColor: 'orange',
          placeholder: '请输入关键词',
          disableLimitWordsLength: true
        }
```
#### 2、dropDownCascader  级联选择器，如地区选择
`src/components/app-filter/drop-dwon-cascader.vue`
```javascript
            {
              keyName: 'province',
              label: '省份地区',
              expand: false,
              showMore: false,
              hide: false,
              componentType: 'dropDownCascader',
              children: this.policyAreas,
              changeFun: this.handleResetCurrentPage,
              isLimit: true,
              enableCount: true
            }
```
#### 3、time  选择时间
`src/components/app-filter/drop-down-time-range.vue`
```javascript
            {
              keyName: 'province',
              label: '省份地区',
              expand: false,
              showMore: false,
              hide: false,
              componentType: 'dropDownCascader',
              children: this.policyAreas,
              changeFun: this.handleResetCurrentPage,
              isLimit: true,
              enableCount: true
            }
```
#### 4、singleDropVertical
` src/components/app-filter/single-drop-down-custom.vue`
```javascript
  {
    keyName: 'zjff',
    label: '融资频率',
    expand: false,
    showMore: false,
    hide: false,
    componentType: 'singleDropVertical',
    appearCustomNum: true,
    customNumberRegType: 2,
    canStartEmpty: true,
    children: CUSTOMER_NUMBER,
    unit: '次',
    changeFun: chooseChange
  }
```
#### 5、flat-checkbox-group
` src/components/app-filter/flat-checkbox-group.vue`
```javascript
  {
          label: '登记状态',
          dataAppearType: '1',
          keyName: 'status',
          expand: false,
          showMore: false,
          hide: false,
          forceHiddenMore: true,
          componentType: 'flat-checkbox-group',
          changeFun: this.handleResetCurrentPage,
          children: this.getStatusData()
 }
```
#### 6、dropDownCascaderCustom
`src/components/app-filter/drop-down-cascader-custom.vue`
```javascript
  {
    dataAppearType: '1',
    leftWidth: '96',
    // labelWidth: '100px',
    keyName: 'regTime',
    label: '登记年限',
    expand: false,
    showMore: false,
    middleHeight: '',
    componentType: 'dropDownCascaderCustom',
    enableChangeButtonText: true,
    align: 'right',
    children: [...years, { isSingleCustom: true }],
    changeFun: this.handleCopyrightChangeFilter
  }
```
#### 7、 checkboxGroup
` src/components/app-filter/check-box-button-group.vue`
```javascript
  {
    children: searchTypeList,
    dataAppearType: '1',
    keyName: 'tmSearchType',
    label: '查询方式',
    labelWidth: '120px',
    expand: false,
    showMore: false,
    middleHeight: '',
    componentType: 'checkboxGroup',
    checkBoxType: 'checkbox',
    changeFun: this.handleTrademarkChangeFilter
 }
```
#### 8、  radioGroup （暂没用）
`src/components/app-filter/radio-group.vue`
```javascript
  {
 }
```
#### 9、 radioGroupNew
` src/components/app-filter/radio-group-new.vue`
```javascript
  {
      label: '潜在上市',
      dataAppearType: '1',
      leftWidth: '120',
      keyName: 'zjpl',
      expand: false,
      showMore: false,
      hide: false,
      componentType: 'radioGroupNew',
      // radioType: 'button',
      changeFun: chooseChange,
      children: CUSTOMER_BOOLEAN2
  }
```
#### 10、  inputDialog
` src/components/app-filter/commodity-server-pop.vue`
```javascript
{
    dataAppearType: '1',
    keyName: 'goodsCode',
    label: '商品服务',
    labelWidth: '120px',
    expand: false,
    showMore: false,
    middleHeight: '',
    componentType: 'inputDialog',
    changeFun: this.handleTrademarkChangeFilter,
    limit: 20,
    relationKey: 'intClass',
    relationLabel: '国际分类'
}
```
