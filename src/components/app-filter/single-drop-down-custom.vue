<template>
  <div class="d-wrap">
    <el-popover
      placement="bottom-start" :trigger="triggerTrans" popper-class='opopover dr-down-margin0'
      :value="wrapPopVisible"
      @show="popShow"
      @hide="popHide"
      ref="popover"
      :open-delay="100" :close-delay="150"
      :transition="transition"
    >
      <div class="p-wrap" ref="allItem" :class="[setRowNum ? 'isTransverse' : '']" :style="popStyle" @mouseenter="wrapContentPopShow" @mouseleave="wrapPopHide">
        <el-radio-group v-model="radio" @input="singleChange" :style="{height: popStyle.height}">
          <el-radio
            v-for="(item,index) in options"
            :label="item.code"
            :key="index"
            :style="{width: singleItemWidth + 'px'}"
          >
            {{ item.name }}
            <template v-if="enableCount">
              (<span class="count">{{ item.count }}</span>)
            </template>
          </el-radio>
          <drop-down-numbers
            v-if="appearCustomNum"
            :canStartEmpty="canStartEmpty"
            :canEndEmpty="canEndEmpty"
            :customNumberRegType="customNumberRegType"
            :unit="unit"
            placement="right-end"
            :isCheckBox="false"
            :arrowType="0"
            class="custom-number-wrap"
            @customPopClearTime="wrapPopClearTime"
            @customPopHideEvent="wrapPopHide"
            @change="customNumberChange"
            :offsetX="offsetX"
            :offsetY="offsetY"
            ref="customNumberRef"
            :style="{width: singleItemWidth + 5 + 'px'}"
          />
        </el-radio-group>
      </div>
      <div slot="reference" class="line" :class="[isActive || checkHasValues ? 'isActive' : '']" @mouseenter="wrapPopShow" @mouseleave="wrapPopHide">
        <span class="b-wrap">{{ choseShowText ? choseShowText : showText }}</span>
        <i :class="[isActive ? 'jtshang' : 'jtxia']"></i>
      </div>
    </el-popover>
  </div>
</template>

<script>
import dropDownNumbers from './drop-down-numbers'

export default {
  name: 'single-drop-down-custom',
  components: { dropDownNumbers },
  props: {
    transition: { type: String, default: 'fade-in-linear' },
    unit: { type: String, default: '万' },
    trigger: { type: String, default: 'manual' },
    showText: { type: String, default: '' },
    options: { type: Array, default: () => [] },
    appearTime: { type: Boolean, default: false }, // 横向排布如果加入自定义情况未做布局，待有需求在做
    appearCustomNum: { type: Boolean, default: false },
    canStartEmpty: { type: Boolean, default: false },
    canEndEmpty: { type: Boolean, default: false },
    customNumberRegType: { type: Number, default: 1 },
    setRowNum: { type: Number, default: 0 }, // 设置横向显示的情况几行显示,0未竖向
    setColumnNum: { type: Number, default: 0 }, // 设置横向显示的情况几行计算宽度使用
    offsetX: { type: Number, default: 0 }, // 自定义的弹框偏移位置
    offsetY: { type: Number, default: 0 }, // 自定义的弹框偏移位置
    enableCount: { type: Boolean, default: false }
  },
  data() {
    return {
      isTransverseWidth: 0,
      isActive: false,
      wrapPopVisible: false,
      radio: '',
      choseShowText: '',
      hasCustomNumberValue: false,
      singleItemWidth: null
    }
  },
  computed: {
    triggerTrans() {
      return this.trigger === 'hover' || !this.trigger ? 'manual' : this.trigger
    },
    checkHasValues() {
      return this.radio || this.hasCustomNumberValue
    },
    popStyle() {
      // 2为边框占据的
      if (this.setRowNum) {
        let style = {
          width: this.setRowNum ? (this.isTransverseWidth + this.setColumnNum * 10 + 20 + 2 + 'px') : 'auto'
        }
        if (this.setRowNum && this.$refs.popover && this.$refs.popover.popperElm) {
          style.height = 47 * this.setRowNum + 17 + 'px'
        }
        return style
      } else {
        return {
          maxHeight: '300px',
          overflowY: 'auto'
        }
      }
    }
  },
  mounted() {
    this.options.forEach(i => {
      i.checked = false
    })
    if (this.setRowNum) {
      this.singleItemWidth = 0
      this.options.forEach(i => {
        let cur = $util.getTextWidth(i.name, 'body') + 30
        if (cur > this.singleItemWidth) {
          this.singleItemWidth = cur
        }
      })
    }

    this.$emit('update:bindComponents', this)
  },
  methods: {
    checkFillName(obj) {
      this.choseShowText = (!obj || (obj.name === '不限' && obj.code === 'unlimited')) ? '' : obj.name
    },
    setVmodel(newValue) {
      newValue = newValue.length > 0 ? newValue[0] : null
      if (!newValue) {
        this.radio = ''
        this.checkFillName()
        if (this.appearCustomNum) {
          this.$refs.customNumberRef.setVmodel([])
          this.hasCustomNumberValue = false
        }
      } else if (newValue.isCustom) {
        if (this.appearCustomNum) {
          this.$refs.customNumberRef.setVmodel(newValue.code.split('-'))
          this.hasCustomNumberValue = true
        }
        this.radio = ''
        this.checkFillName(newValue)
      } else {
        this.radio = newValue.code
        this.checkFillName(newValue)
        if (this.appearCustomNum) {
          this.$refs.customNumberRef.setVmodel([])
          this.hasCustomNumberValue = false
        }
      }
    },
    singleChange(value) {
      if (this.appearCustomNum) {
        this.$refs.customNumberRef.setVmodel([])
      }

      let match = this.options.find(i => {
        return i.code === value
      })

      this.$emit('change', match)

      this.checkFillName(match)
      this.$nextTick(() => {
        this.resetPopTopPos()
      })
    },
    customNumberChange(value) {
      this.radio = ''
      if (value) {
        let result = { ...value, type: this.options[0].type, isCustom: this.appearCustomNum }
        this.$emit('change', [result])
        this.checkFillName(result)
        this.hasCustomNumberValue = true
      } else {
        this.hasCustomNumberValue = false
      }
    },
    resetPopTopPos() {
      this.$nextTick(() => {
        if (this.$refs.popover) {
          this.$refs.popover.updatePopper()
        }
      })
      // 重新设置高度
    },
    popShow() {
      this.isActive = true
      this.$nextTick(() => {
        this.isTransverseWidth = 0
        if (this.setRowNum) {
          for (let i = 0; i < this.$refs.allItem.children[0].children.length; i++) {
            let e = this.$refs.allItem.children[0].children[i]
            if (e && e.offsetWidth && (i % this.setRowNum === 0)) {
              this.isTransverseWidth += e.offsetWidth
            }
          }
        }
        this.$refs.popover.popperElm.style.height = this.popStyle.height
        this.resetPopTopPos()
      })
    },
    popHide() {
      this.isActive = false
    },
    wrapPopShow() {
      if (this.trigger === 'click') {
        return
      }
      this.wrapPopClearTime()
      if (this.openWrapTimeId) {
        clearTimeout(this.openWrapTimeId)
        this.openWrapTimeId = null
      }
      this.openWrapTimeId = setTimeout(() => {
        this.wrapPopVisible = true
        this.openWrapTimeId = null
      }, 100)

      this.$nextTick(() => {
        if (this.$refs.popover.popperElm) {
          if (!this.$refs.popover.popperElm.onmouseleave) {
            this.$refs.popover.popperElm.onmouseleave = this.wrapPopHide
          }
          if (!this.$refs.popover.popperElm.onmouseenter) {
            this.$refs.popover.popperElm.onmouseenter = this.wrapPopClearTime
          }
        }
      })
    },
    wrapContentPopShow() {
      if (this.wrapTimeId) {
        clearTimeout(this.wrapTimeId)
        this.wrapTimeId = null
      }
    },
    wrapPopHide() {
      if (this.trigger === 'click') {
        return
      }
      if (this.openWrapTimeId) {
        clearTimeout(this.openWrapTimeId)
        this.openWrapTimeId = null
      }
      if (this.wrapTimeId) {
        clearTimeout(this.wrapTimeId)
        this.wrapTimeId = null
      }
      this.wrapTimeId = setTimeout(() => {
        this.wrapPopVisible = false
      }, 150)
    },
    wrapPopClearTime() {
      if (this.wrapTimeId) {
        clearTimeout(this.wrapTimeId)
        this.wrapTimeId = null
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../styles/common";

.d-wrap {
  margin-right: 30px;

  .line {
    display: flex;
    align-items: center;
    height: 22px;
    cursor: pointer;

    span {
      color: #333333;
      font-size: 14px;
      line-height: 14px;
      display: block;
      padding-right: 5px;
    }

    i {
      width: 0;
      height: 0;
      border-right: 4px solid transparent;
      border-left: 4px solid transparent;

      &.jtshang {
        border-bottom: 4px dashed #666666;
      }

      &.jtxia {
        border-top: 4px dashed #666666;
      }
    }

    &.isActive {
      .b-wrap, i, span {
        color: #128BED;
      }

      i {
        &.jtshang {
          border-bottom: 4px dashed #128BED;
        }

        &.jtxia {
          border-top: 4px dashed #128BED;
        }
      }
    }
  }

}
.p-wrap {
  border: solid 1px #E4E7ED;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 5px 0;
  position: relative;
  display: flex;
  flex-direction: column;

  ::v-deep {
    .el-radio-group {
      display: block;
    }

    .el-radio {
      padding: 6px 0;
      display: block;
      width: 100%;
    }

    .el-radio:hover {
      background: #f2f8fe;
    }

    .el-radio__input {
      display: none;
    }

    .el-radio__label {
      line-height: 22px;
      padding: 0 15px;
      font-weight: normal;
    }
    .el-checkbox {
      &:hover {
        background: #F2F8FE;
      }
    }
  }

  &.isTransverse {
    padding: 15px 10px;
    display: flex;
    flex-wrap: wrap;

    ::v-deep {
      .el-radio-group {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
      }
      .el-radio{
        border: 1px solid #D8D8D8;
        margin: 0 5px 15px;
        padding: 4px 0;
        border-radius: 2px;

        &.is-checked {
          background: #E2F1FD;
          border: 1px solid #128BED;
          .el-radio__label {
            color: #128BED;
          }
        }
      }
      .el-radio__label {
        padding: 0 12px;
        width: 100%;
        display: block;
        box-sizing: border-box;
        text-align: center;
      }
      .el-checkbox {
        line-height: 32px;
        width: unset;
        margin-left: 5px;
        border: 1px solid #D8D8D8;
        padding: 4px 12px;
        .el-checkbox__label{
          padding: 0;
        }
      }
      .el-radio:hover {
        background: none;
        border: 1px solid #128BED;
        &.is-checked {
          background: #E2F1FD;
        }
        .el-radio__label {
          color: #128BED;
        }
      }
    }
  }
}
.datePickerWrap {
  visibility: hidden;
  width: 100%;
  height: 1px;
  overflow: hidden;
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 999;
}
.custom-number-wrap {
  ::v-deep {
    .custom-item-checkbox {
      padding: 6px 15px 6px 10px;
      .el-checkbox__label{
        padding-left: 5px;
      }
    }
  }
}
</style>
