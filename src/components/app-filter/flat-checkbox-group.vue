<template>
  <div class="flat-checkbox-group">
    <div
      class="flat-checkbox-group-row"
      v-for="(item, index) in option.children"
      :key="item.code"
    >
      <div class="flat-checkbox-group-select-all">
        <el-checkbox
          @change="handleCheckAllChange(index)"
          :indeterminate="model[index].indeterminate"
          v-model="model[index].checkAll"
        >
          {{ item.name }}
        </el-checkbox>
      </div>
      <div class="flat-checkbox-group-children">
        <el-checkbox-group
          v-model="model[index].values"
          @change="(subValue) => handleSubCheckChange(index, subValue)"
        >
          <el-checkbox
            v-for="subItem in item.children"
            :label="subItem.code"
            :key="subItem.code"
          >
            {{ subItem.name }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'
export default {
  name: 'flat-checkbox-group',
  props: {
    option: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      model: []
    }
  },
  created() {
    this.option.children.forEach(() => {
      this.model.push({
        checkAll: false,
        indeterminate: false,
        values: []
      })
    })
  },
  methods: {
    /**
     * 各项全选状态改变事件
     * @param index
     */
    handleCheckAllChange (index) {
      const data = this.model[index]
      let values = []
      if (data.checkAll) {
        values = this.option.children[index].children.map(item => item.code)
      }
      // if (data.indeterminate) {
      //   values = []
      //   data.checkAll = false
      // }
      data.indeterminate = false
      this.model[index].values = values
      this.$emit('change', this.getSelectedItems())
    },
    /**
     * 子选项状态改变事件
     * @param index
     * @param subValue
     */
    handleSubCheckChange (index, subValue) {
      const data = this.model[index]
      const allValues = this.option.children[index].children.map(item => item.code)
      data.values = subValue
      data.indeterminate = subValue.length > 0 && subValue.length !== allValues.length
      data.checkAll = subValue.length === allValues.length
      this.$emit('change', this.getSelectedItems())
    },
    /**
     * 获取已选中的选项
     * @returns {*[]}
     */
    getSelectedItems () {
      const result = []
      this.model.forEach((item, index) => {
        if (_.isEmpty(item.values)) {
          return
        }
        let name
        if (this.option.children[index].children.length === item.values.length) {
          name = this.option.children[index].name
        } else {
          name = this.option.children[index].children.filter(subItem => item.values.includes(subItem.code)).map(subItem => subItem.name).join()
        }
        result.push({
          name,
          code: item.values.join(),
          keyName: this.option.keyName,
          label: this.option.label
        })
      })
      return result
    },
    /**
     * app-filter 重置方法
     */
    setVmodel () {
      this.model.forEach(item => {
        item.checkAll = false
        item.indeterminate = false
        item.values = []
      })
    }
  }
}
</script>

<style scoped lang="scss">
.flat-checkbox-group {
  display: flex;
  flex-direction: column;
  width: 100%;

  &-row {
    display: flex;
    margin-bottom: 15px;
  }

  &-children {
    flex: 1;
    background: #fafafa;
    padding: 0 10px;
    margin-left: -10px;
    border-radius: 4px;
  }
}

.el-checkbox {
  display: flex;
  align-items: center;
  height: 22px;
  color: #333;
  margin-right: 30px;

  ::v-deep {
    .el-checkbox__input {
      display: flex;
    }

    .el-checkbox__label {
      padding-left: 5px;
    }
  }
}

.el-checkbox-group {
  display: flex;
}
</style>
