<template>
  <app-popup :title="title" class="pop-wrap" :buttonOptions="[]">
    <div class="custom-conditions">
      <div class="content">
        <div class="item"
             v-for="(item,index) in customConditions"
             :key="index"
             @click="selectConditionWrap(item)"
             :class="[(index+1) === customConditions.length ? 'no-border' : '']"
        >
          <div class="header">
            <p v-if="!item.isEdit" class="title" :title="item.name">{{ item.name }}</p>
            <div v-else class="edit-wrap">
              <el-input :class="item.error ? 'red-border' : ''" :ref="item.id" v-model="item.name" @click.stop.native="startInput"></el-input>

              <el-button class="edit-sure" type="text" @click.stop="sureEdit(item)">确认</el-button>
              <el-button class="edit-cancel" type="text" @click.stop="cancelEdit(item)">取消</el-button>
            </div>
            <div class="icon-edit" @click.stop>
              <el-popover v-if="!item.isEdit" placement="bottom" trigger="hover" popper-class="default-popover-tooltip small-popover">
                <p>编辑</p>
                <span slot="reference" class="iconfont icon-icon_bii" @click.stop="startEdit(item,index)"></span>
              </el-popover>
              <el-popover placement="bottom" trigger="hover" popper-class="default-popover-tooltip small-popover">
                <p>删除</p>
                <span slot="reference" class="iconfont icon-shanchu4" @click.stop="removeCondition(item)"></span>
              </el-popover>
            </div>
          </div>
          <div class="error" v-if="item.isEdit && item.error">{{ item.errorName }}</div>
          <div class="filters-wrapper">
            <el-popover
              v-for="(j,index) in handleConditions(item)"
              :open-delay="200"
              placement="bottom" trigger="hover" popper-class="default-popover-tooltip small-popover" :key="'screenAppearText' + index">
              <p> {{ getFilterNames ? getFilterNames(j) : (j.label + ':' + j.mergeName) }}</p>
              <div slot="reference">
                <p slot="reference" class="single overrflow-with-ellipsis"> {{getFilterNames ? getFilterNames(j) : (j.label +':' +j.mergeName)}}</p>
              </div>
            </el-popover>
          </div>
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import _ from 'lodash'
import customerService from '../../services/customer'
import { postVue } from '../../services/common-service'

export default {
  name: 'saved-conditions',
  data() {
    return {
      title: '已存筛选',
      categoryName: '筛选方案',
      removeConfirmContent: '确定删除该条记录吗？',
      customConditions: [],
      changeCallBack: null,
      selectCondition: null,
      getCondition: null,
      getFilterNames: null,
      translateScreenAppearText: (data) => {
        let result = []
        data.forEach((i) => {
          let match = result.findIndex((j) => {
            return j.keyName === i.keyName
          })
          if (match >= 0) {
            result[match].mergeName = result[match].mergeName + ',' + i.name
          } else {
            result.push({ ...i, mergeName: i.name })
          }
        })
        return result
      }
    }
  },
  methods: {
    handleConditions(customConditions) {
      if (this.getFilterNames) { // 兼容老的解析方法
        return customConditions.screenAppearText
      } else {
        customConditions = customConditions.showContent
      }
      if (typeof customConditions === 'string') {
        customConditions = JSON.parse(customConditions)
      }
      if (customConditions) {
        if (customConditions.selects) {
          customConditions = customConditions.selects
        }
        let data = _.cloneDeep(customConditions)
        return this.translateScreenAppearText(data)
      }
      return ''
    },
    selectConditionWrap(item) {
      this.selectCondition(item)
      this.$modal.close()
    },
    validateGroupName(item) {
      if (!item.name || item.name.trim() === '') {
        item.error = true
        item.errorName = `请输入${this.categoryName}名称`
      } else if (item.name.length > 20) {
        item.error = true
        item.errorName = `${this.categoryName}应为1~20位字符`
      } else {
        item.error = false
        return true
      }
    },
    startInput() {

    },
    startEdit(item, index) {
      item.isEdit = true
      item.tempName = item.name
      this.$set(this.customConditions, index, { ...this.customConditions[index], error: false })
      this.$nextTick(() => {
        this.$refs[item.id][0].focus()
      })
    },
    cancelEdit(item) {
      item.isEdit = false
      item.name = item.tempName
    },
    async sureEdit(item) {
      if (!this.validateGroupName(item)) {
        return
      }
      let data = await customerService.updateCondition({
        name: item.name,
        id: item.id,
        defaultInd: 0,
        noticeFlag: 0
      })
      if (data) {
        this.$message.success('修改成功')
        item.isEdit = false
        if (this.changeCallBack) {
          this.changeCallBack(item)
        }
        if (this.getCondition) {
          this.getCondition()
        }
      }
    },
    removeCondition(item) {
      this.$uiService.alertWarning(this.removeConfirmContent)
        .then(() => {
          postVue('/search_cond/delete', { id: item.id })
            .then(() => {
              this.$message.success('删除成功')
              this.customConditions = this.customConditions.filter((i) => {
                return i.id !== item.id
              })
              if (this.customConditions.length === 0) {
                this.$modal.close()
              }
              if (this.getCondition) {
                this.getCondition()
              }
            })
        })
        .catch(() => {
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../styles/common.scss';

.pop-wrap {
  ::v-deep {
    .footerWrapper {
      border: 0;
    }

    ._contentDefault {
      padding: 0;
    }

    .popup-title {
      font-weight: bold;
    }
  }
}

.custom-conditions {
  .error {
    color: #F04040;
  }

  .red-border {
    ::v-deep {
      .el-input__inner {
        border: 1px solid #F04040
      }
    }
  }

  .content {
    overflow: auto;
    position: relative;

    .item {
      position: relative;
      cursor: pointer;
      padding: 10px 15px 0;

      &:hover {
        background: #F2F8FE;
      }

      &.no-border .filters-wrapper {
        border: 0;
      }

      .header {
        display: flex;
        justify-content: space-between;

        .icon-edit {
          display: flex;
          > span {
            margin-left: 10px;
          }
          span {
            line-height: 32px;
          }

          .iconfont{
            padding: 4px;
            font-size: 14px;
            color: #BBBBBB;
            border-radius: 2px;
          }
          .iconfont:hover {
            background: #E2F1FD;
            color: #128BED;
          }
        }

        .title {
          font-size: 14px;
          color: #333;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          cursor: pointer;
          width: 80%;
          line-height: 32px;
        }

        .icon-bianji {
          font-size: 16px;
          color: #999999;
          cursor: pointer;
        }

        .icon-shanchu {
          font-size: 16px;
          color: #999999;
          cursor: pointer;
        }

        .edit-wrap {
          display: flex;

          ::v-deep {
            .el-input__inner {
              line-height: 32px;
              height: 32px;
              width: 220px;
            }

            .el-button {
              padding: 0 8px;
              margin: 8px 0;
            }
          }

          .edit-sure {
            color: #128bde;
            padding-left: 12px;
          }

          .edit-cancel {
            color: #666666;
            border-left: 1px solid #EEEEEE;
            border-radius: 0;
            padding-left: 9px;
          }
        }
      }

      .filters-wrapper {
        display: flex;
        flex-wrap: wrap;
        padding-bottom: 5px;
        padding-top: 5px;
        border-bottom: 1px solid #EEEEEE;

        .single {
          max-width: 250px;
          color: #128BED;
          border: 1px solid #128BED;
          margin: 0 10px 10px 0;
          box-sizing: border-box;
          cursor: pointer;
          position: relative;
          line-height: 18px;
          border-radius: 2px;
          padding: 2px 6px;
          font-size: 12px;

          &:hover {
            background: #E5F2FD;
          }
        }
      }
    }
  }
}
</style>
