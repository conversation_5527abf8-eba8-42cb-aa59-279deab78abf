<template>
  <div>
    <div class="input-wrap" @click="visiblePop">
      <p v-if="resultItemsNumber" class="input-wrap__value">已选择{{ resultItemsNumber }}项</p>
      <p v-else class="input-wrap__placeholder">请选择商品服务</p>
    </div>
    <app-popup v-if="show" title="选择商品服务" :control="{show:show}" @closed="visibleHide" :options="popOptions" :buttonOptions="buttonOptions">
      <div class="content">
        <div class="left border-radius-4 ">
          <div class="category-wrap">
            <p class="category-title">国际分类</p>
            <el-radio-group v-model="radio" size="mini" class="radio-wrap" @input="radioChange">
              <el-radio-button v-for="(item,index) in localOptions" :key="item.code+index" :label="item.code">{{ item.code }}类</el-radio-button>
            </el-radio-group>
          </div>
          <div class="category-wrap" style="margin-top: 5px">
            <p class="category-title">{{ childrenItems.name }}</p>
            <el-scrollbar class="child-category">
              <el-checkbox
                v-for="(item,index) in childrenItems.children"
                v-model="item.value"
                :key="item.code+index"
                :label="item.code"
                :value="item.value"
                class="single-check-box"
                @change="(value)=>{singleChange(value,item)}"
              >{{ item.code }}&nbsp;{{ item.name }}
              </el-checkbox>
            </el-scrollbar>
          </div>
        </div>
        <div class="right">
          <div class="top">
            <p>已选择<span :style="{color:selects.length === 0 ? '#333333' : ''}">{{ selects.length }}</span></p>
            <div class="control-item" @click="resetAll" v-if="selects.length > 0">
              <span class="iconfont icon-heimingdanchexiao"></span>
              <p>重置筛选</p>
            </div>
          </div>
          <div class="select-wrap">
            <el-scrollbar>
              <div v-for="(item,index) in selects" :key="'select' + index" class="overrflow-with-ellipsis select-item" :title="item.code + item.name" @click="remove(item)">
                {{ item.code }}&nbsp;{{ item.name }}
                <i class="iconfont icon-icon_guanbixx"></i>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
      <app-loading v-if="isLoading"></app-loading>
    </app-popup>
  </div>
</template>

<script>
import _ from 'lodash'
import { postVue } from '../../services/common-service'

export default {
  name: 'commodity-server-pop',
  props: {
    limit: { type: Number, default: 0 },
    keyName: { type: String, default: '' },
    label: { type: String, default: '' },
    relationKey: { type: String, default: '' },
    relationLabel: { type: String, default: '' }
  },
  data() {
    return {
      popOptions: {
        width: '960px'
      },
      show: false,
      radio: '01',
      selects: [], // 右侧已选择
      resultItemsNumber: 0,
      buttonOptions: [
        { name: '取消', type: 'normal' },
        { name: '确定', type: 'primary', click: this.sureClick }
      ],
      tempSelects: [],
      tempLocalOptions: null,
      localOptions: []
    }
  },
  computed: {
    childrenItems() {
      let match = this.localOptions.find(i => {
        return i.code === this.radio
      })
      return match || {}
    }
  },
  mounted() {
    this.$emit('update:bindComponents', this)
  },
  methods: {
    radioChange(code) {
      let children = this.localOptions.find(option => option.code === code)
      if (!children.children || children.children.length === 0) {
        this.getList(code)
      }
    },
    getList(code) {
      return new Promise((resolve) => {
        let params = { condition: {} }
        if (code) {
          params.condition.code = code
        }
        this.isLoading = true
        postVue('/corp/common/qualLicense/getTradeMarkType', params)
          .then(response => {
            this.isLoading = false
            if (response && response.resultList) {
              if (code) {
                this.localOptions.forEach(option => {
                  if (option.code === code) {
                    option.children = response.resultList.map(z => {
                      z.name = z.description
                      z.value = false
                      return z
                    })
                  }
                })
                this.tempLocalOptions = _.cloneDeep(this.localOptions)
              } else {
                let options = response.resultList
                this.localOptions = options.map(i => {
                  i.name = i.code + '类 ' + i.description
                  delete i.description
                  i.children = i.child
                  delete i.child
                  return i
                })
                this.getList(this.localOptions[0].code)
              }
            }

            resolve()
          })
      })
    },
    setVmodel(newValue) {
      this.selects = newValue
      this.tempSelects = _.cloneDeep(this.selects)
      this.resultItemsNumber = this.selects.length

      this.localOptions.forEach(i => {
        i.children && i.children.forEach(z => {
          z.value = !!this.selects.find(m => {
            return m.code === z.code
          })
        })
      })

      this.tempLocalOptions = _.cloneDeep(this.localOptions)
    },
    resetAll() {
      this.selects = []
      this.localOptions.forEach(i => {
        i.children && i.children.forEach(z => {
          z.value = false
        })
      })
    },
    visiblePop() {
      if (this.localOptions && this.localOptions.length > 0) {
        if (this.tempLocalOptions) {
          this.localOptions = _.cloneDeep(this.tempLocalOptions)
        }
        if (this.tempSelects) {
          this.selects = _.cloneDeep(this.tempSelects)
        }
        this.show = true
      } else {
        this.getList().then(() => {
          this.show = true
          this.changeSelects()
        })
      }
    },
    visibleHide() {
      this.show = false
      this.radio = '01'
    },
    singleChange(value, item) {
      if (this.limit && (this.selects.length + 1) > this.limit) {
        item.value = false
        this.$message.error('最多支持选择' + this.limit + '个商品服务')
        return
      }
      this.changeSelects()
    },
    remove(item) {
      item.value = false
      this.changeSelects()
    },
    changeSelects() {
      let result = []
      this.localOptions.forEach(i => {
        i.children && i.children.forEach(z => {
          if (z.value === true) {
            result.push(z)
          }
        })
      })
      this.selects = result
    },
    sureClick() {
      this.resultItemsNumber = this.selects.length
      this.visibleHide()
      this.tempSelects = _.cloneDeep(this.selects)
      this.tempLocalOptions = _.cloneDeep(this.localOptions)

      let result = _.cloneDeep(this.selects)
      result.forEach(i => {
        i.keyName = this.keyName
        i.label = this.label
        this.localOptions.forEach(localOption => {
          if (localOption.children && localOption.children.length > 0) {
            localOption.children.forEach(m => {
              if (m.code === i.code) {
                i.parentCode = localOption.code
              }
            })
          }
        })
      })
      this.localOptions.forEach(localOption => {
        if (localOption.children && localOption.children.length > 0) {
          let parentItem = { ...localOption, isAll: false, isIndeterminate: false, specialParentItem: true }
          if (this.relationKey) {
            parentItem.keyName = this.relationKey
          }
          if (this.relationLabel) {
            parentItem.label = this.relationLabel
          }
          let matchFalse = localOption.children.find(j => !j.value)
          let matchTrue = localOption.children.find(j => j.value)
          if (matchFalse && matchTrue) { // 非全选情况加入父级
            parentItem.isIndeterminate = true
            result.push(parentItem)
          } else if (!matchFalse) { // 全选情况加入父级
            parentItem.isAll = true
            result.push(parentItem)
          }
        }
      })
      this.$emit('change', result)
    }
  }
}
</script>

<style scoped lang="scss">
.input-wrap {
  height: 36px;
  line-height: 34px;
  border: 1px solid #D8D8D8;
  border-radius: 2px;
  width: 300px;
  font-size: 14px;
  padding-left: 9px;
  cursor: pointer;

  &:hover {
    border-color: #128bed;
  }

  &__value {
    color: #333333;
  }

  &__placeholder {
    color: #BBBBBB;
  }
}

.content {
  display: flex;

  .category-title {
    font-size: 16px;
    line-height: 24px;
    color: #333333;
    padding: 10px 0;
  }

  .left {
    background: #F7F7F7;
    border: 1px solid #EEEEEE;
    overflow: hidden;
    flex: 1;

    .category-wrap {
      background: #FFFFFF;
      padding: 0 15px;

      .radio-wrap {
        ::v-deep {
          .el-radio-button {
            margin-bottom: 10px;

            &:hover .el-radio-button__inner {
              background: #128BED;
              color: #FFFFFF;
            }
          }

          .el-radio-button__inner {
            border: 0;
            border-radius: 2px;
            padding: 6px 10px;
          }
        }
      }

      .child-category {
        width: 100%;
        height: 240px;

        ::v-deep {
          .el-scrollbar__wrap {
            overflow-x: hidden;
          }
        }

        .single-check-box {
          display: block;
          margin-bottom: 10px;

          ::v-deep {
            .el-checkbox__label {
              line-height: 22px;
            }
          }
        }
      }
    }

  }

  .right {
    margin-left: 10px;
    width: 290px;
    padding: 0 0 0 0;
    border: 1px solid #EEEEEE;

    .top {
      margin: 0 15px;
      display: flex;
      justify-content: space-between;
      padding: 13px 0;
      border-bottom: 1px solid #EEEEEE;

      > p {
        line-height: 24px;
        font-size: 15px;
        color: #333333;

        > span {
          color: #128BED;
          padding-left: 5px;
        }
      }

      .control-item {
        display: flex;
        cursor: pointer;

        span, p {
          padding-left: 5px;
          color: #128BED;
          font-size: 14px;
          line-height: 24px;
        }

        &:hover {
          span, p {
            color: #0069BF;
          }
        }
      }
    }

    .select-wrap {
      //margin-left: 15px;

      ::v-deep {
        .el-scrollbar {
          height: 417px;
        }

        .el-scrollbar__wrap {
          overflow-x: hidden;
        }
      }

      .select-item {
        width: 100%;
        position: relative;
        line-height: 22px;
        padding: 9px 40px 9px 15px;
        color: #333333;
        cursor: pointer;

        i {
          font-size: 10px;
          position: absolute;
          right: 15px;
          top: 13px;
          z-index: 9;
          color: #BBBBBB;
          line-height: 22px;
        }

        &:hover {
          background: #F2F8FE;
        }
      }
    }
  }
}
</style>
