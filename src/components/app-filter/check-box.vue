<template>
  <el-checkbox
    :value="value"
    :indeterminate="isIndeterminate"
    @change="onChange"
  >{{ name }}
  </el-checkbox>
</template>

<script>
export default {
  name: 'check-box-button',
  props: {
    name: { type: String, default: '' },
    bindComponents: { default: null }
  },
  data() {
    return {
      value: false,
      isIndeterminate: false
    }
  },
  mounted() {
    this.$emit('update:bindComponents', this)
  },
  methods: {
    setVmodel(newValue) {
      if (newValue && newValue.checkAll) {
        this.value = true
      } else if (newValue && newValue.isIndeterminate) {
        this.isIndeterminate = true
      } else {
        this.value = newValue
      }
    },
    onChange(value) {
      this.value = !!value
      this.$emit('change', value)
    }
  }
}
</script>
