<template>
  <el-popover
    trigger="hover"
    :value="customNumPopVisible"
    :placement="placement"
    :visible-arrow="false"
    @show="popShow"
    @hide="popHide"
    ref="customNumPop"
    class="customNum"
    popper-class='singleDropDownCustomNumPop'
    v-bind="$attrs"
  >
    <div class="customize-capital">
      <div>
        <span>从</span>
        <el-input style="width:156px;" class="input30" v-model="customNumValue.start" :type="inputType" size="mini" :placeholder="placeholder[0]" :maxlength="inputMaxlength[0]"></el-input>
        <span class="unit-from ">{{ unit }}</span>
      </div>
      <div class="margin-t-0-6x" style="padding-bottom: 15px">
        <span>到</span>
        <el-input style="width:156px;" class="input30" v-model="customNumValue.end" :type="inputType" size="mini" :placeholder="placeholder[1]" :maxlength="inputMaxlength[1]"></el-input>
        <span class="unit-to">{{ unit }}</span>
      </div>
      <div style="display: flex;justify-content: flex-end;align-items: center">
        <app-button class="icon-button-reset" type="text" size="small" @click="resetCustomNum(true)"><span class="iconfont icon-heimingdanchexiao"></span>重置</app-button>
        <app-button class="icon-button-sure" type="text" size="small" @click="sureCustomNum">确定</app-button>
      </div>
    </div>
    <slot name="reference">
      <div slot="reference" class="custom-item" :class="[{'theme-inline': inline}]" @mouseenter="customPopShow" @mouseleave="customPopHide" v-bind="referenceAttrs">
        <el-checkbox v-if="referenceType === 'checkbox'"
          :class="['custom-item-checkbox',{'has-value': numCustomChecked}, {disableCheckBox: !isCheckBox},{'button-type': customButtonType}, {'pop-show-checkbox': customNumPopVisible}]"
          :value="numCustomChecked"
          @change="openSecondNum" label="appearCustomNum">
          {{ choseShowText || title }}<i :class="['jt',arrowClass]"></i>
        </el-checkbox>
        <label v-else-if="referenceType === 'common'" class="el-checkbox custom-item-checkbox"
               :class="[{'has-value': numCustomChecked}, {disableCheckBox: !isCheckBox},{'button-type': customButtonType}, {'pop-show-checkbox': customNumPopVisible}]">
          <span class="el-checkbox__label">{{ choseShowText || title }}<i :class="['jt',arrowClass]"></i></span>
        </label>
      </div>
    </slot>
  </el-popover>
</template>

<script>
let regularType = {
  1: /^[1-9]\d*$/,
  2: /^[0-9]\d*$/
}
export default {
  name: 'drop-down-numbers',
  props: {
    placement: { type: String, default: 'bottom' },
    unit: { type: String, default: '万' },
    isCheckBox: { type: Boolean, default: true },
    title: { type: String, default: '自定义' },
    vModule: { type: Array, default: () => [] },
    bindComponents: { default: null },
    canStartEmpty: { type: Boolean, default: false },
    canEndEmpty: { type: Boolean, default: false },
    customNumberRegType: { type: Number, default: 1 },
    arrowType: { type: Number, default: 1 }, // 1:上下，0：右
    customButtonType: { type: Number, default: 0 },
    enableChangeButtonText: { type: Boolean, default: false },
    offsetX: { type: Number, default: 0 },
    offsetY: { type: Number, default: 0 },
    topLimit: { type: Number, default: 999999999999999 },
    inline: { type: Boolean, default: false }, // 是否是行内展示
    referenceAttrs: { type: Object, default: () => ({}) },
    referenceType: { type: String, default: 'checkbox' }, // checkbox,common
    placeholder: { type: Array, default: () => (['', '']) },
    inputMaxlength: { type: Array, default: () => ([999, 999]) },
    inputType: { type: String, default: 'number' },
    verifyNumberFun: { type: Function, default: undefined }// promise
  },
  data() {
    return {
      customNumPopVisible: false,
      customNumValue: { start: '', end: '' },
      sureCustomNumValue: { start: '', end: '' },
      isActive: false
    }
  },
  computed: {
    choseShowText() {
      if (this.enableChangeButtonText && (this.sureCustomNumValue.start || this.sureCustomNumValue.end)) {
        return this.translateName()
      } else {
        return ''
      }
    },
    numCustomChecked() {
      return !!((this.sureCustomNumValue.start || this.sureCustomNumValue.end))
    },
    arrowClass() {
      if (this.isActive) {
        if (this.arrowType === 1) {
          return 'jtsx jtshang'
        } else {
          return 'jtzy jtyou'
        }
      } else {
        if (this.arrowType === 1) {
          return 'jtsx jtxia'
        } else {
          return 'jtzy jtyou'
        }
      }
    }
  },
  mounted() {
    this.$emit('update:bindComponents', this)
  },
  methods: {
    setVmodel(newValue) {
      if (Object.prototype.toString.call(newValue) === '[object Array]') {
        if (newValue.length === 0) {
          this.customNumValue.start = ''
          this.sureCustomNumValue.start = ''
          this.customNumValue.end = ''
          this.sureCustomNumValue.end = ''
        } else {
          this.customNumValue.start = newValue[0]
          this.sureCustomNumValue.start = newValue[0]
          this.customNumValue.end = newValue[1]
          this.sureCustomNumValue.end = newValue[1]
        }
      }
    },
    resetCustomNum(isEmit = false) {
      this.customNumValue.start = ''
      this.sureCustomNumValue.start = ''
      this.customNumValue.end = ''
      this.sureCustomNumValue.end = ''
      if (isEmit) {
        this.$emit('change', null)
      }
    },
    translateName() {
      let name = ''
      if (this.sureCustomNumValue.start && this.sureCustomNumValue.end) {
        name = `${this.sureCustomNumValue.start}${this.unit}-${this.sureCustomNumValue.end}${this.unit}`
      } else if (!this.sureCustomNumValue.start) {
        name = `最大${this.sureCustomNumValue.end}${this.unit}`
      } else if (!this.sureCustomNumValue.end) {
        name = `最小${this.sureCustomNumValue.end}${this.unit}`
      }
      return name
    },
    verifyNumber() {
      return new Promise((resolve, reject) => {
        let reg = regularType[this.customNumberRegType]
        if (this.canStartEmpty) {
          if (this.customNumValue.start && !reg.test(this.customNumValue.start)) {
            this.$message.warning('请输入正确的整数')
            reject(new Error('请输入正确的整数'))
            return
          }
        } else {
          if (!reg.test(this.customNumValue.start)) {
            this.$message.warning('请输入正确的整数')
            reject(new Error('请输入正确的整数'))
            return
          }
        }
        if (this.canEndEmpty) {
          if (this.customNumValue.end && !reg.test(this.customNumValue.end)) {
            this.$message.warning('请输入正确的整数')
            reject(new Error('请输入正确的整数'))
            return
          }
        } else {
          if (!reg.test(this.customNumValue.end)) {
            this.$message.warning('请输入正确的整数')
            reject(new Error('请输入正确的整数'))
            return
          }
        }
        resolve()
      })
    },
    sureCustomNum() {
      this.radio = ''
      const fun = this.verifyNumberFun ? this.verifyNumberFun : this.verifyNumber
      fun(this.customNumValue.start, this.customNumValue.end, this).then(() => {
        if (Number(this.customNumValue.start) > this.topLimit || Number(this.customNumValue.end) > this.topLimit) {
          this.$message.warning('您的输入已超过上限')
          return false
        }
        if (Number(this.customNumValue.end) < Number(this.customNumValue.start)) {
          this.$message.warning('后面的数字小于前面的数字，请检查')
          return false
        }
        this.sureCustomNumValue.start = this.customNumValue.start
        this.sureCustomNumValue.end = this.customNumValue.end
        let resultList = {
          arr: [this.sureCustomNumValue.start, this.sureCustomNumValue.end],
          code: `${this.sureCustomNumValue.start}-${this.sureCustomNumValue.end}`,
          name: this.translateName()
        }

        this.$emit('update:vmodel', resultList)
        this.$emit('change', resultList)

        if (this.$refs.customNumPop) {
          this.$refs.customNumPop.doClose()
        }
      }).catch(e => {})
    },
    customPopShow() {
      this.customPopClearTime()
      this.customNumPopVisible = true

      this.$nextTick(() => {
        if (!this.$refs.customNumPop.popperElm.onmouseleave) {
          this.$refs.customNumPop.popperElm.onmouseleave = this.customPopHideEvent
        }
        if (!this.$refs.customNumPop.popperElm.onmouseenter) {
          this.$refs.customNumPop.popperElm.onmouseenter = this.customPopClearTime
        }
      })
    },
    customPopHideEvent() {
      this.customPopHide()
      this.$emit('customPopHideEvent')
    },
    customPopHide() {
      if (this.customNumTimeId) {
        clearTimeout(this.customNumTimeId)
        this.customNumTimeId = null
      }
      this.customNumTimeId = setTimeout(() => {
        this.customNumPopVisible = false
      }, 150)
    },
    customPopClearTime() {
      if (this.customNumTimeId) {
        clearTimeout(this.customNumTimeId)
        this.customNumTimeId = null
      }
      this.$emit('customPopClearTime')
    },
    openSecondNum(value) {
      if (value === false) {
        this.customNumValue.start = ''
        this.sureCustomNumValue.start = ''
        this.customNumValue.end = ''
        this.sureCustomNumValue.end = ''
        this.$emit('update:vmodel', null)
        this.$emit('change', null)
      }
    },
    popShow() {
      this.isActive = true
      this.$nextTick(() => {
        if (this.offsetX) {
          this.$refs.customNumPop.popperElm.style.left = parseInt(this.$refs.customNumPop.popperElm.style.left) + this.offsetX + 'px'
        }
        if (this.offsetY) {
          this.$refs.customNumPop.popperElm.style.top = parseInt(this.$refs.customNumPop.popperElm.style.top) + this.offsetY + 'px'
        }
      })
    },
    popHide() {
      this.isActive = false
    }
  }
}
</script>
<style lang="scss">

.singleDropDownCustomNumPop.el-popper {
  margin: 0 0 -5px 0;
  padding: 15px 0;
}
</style>
<style lang="scss" scoped src="./common.scss"></style>

<style scoped lang="scss">
.customize-capital {
  > div {
    padding: 0 10px;
    position: relative;

    > span {
      color: #666666;
    }
  }

  .unit-from, .unit-to {
    position: absolute;
    line-height: 30px;
    right: 23px;
    top: 0;
    z-index: 9;
    color: #666666;
  }

  ::v-deep {
    .el-button--text {
      height: 22px;
      line-height: 22px;
    }
  }
}

  .el-popover__reference-wrapper {
    .custom-item {

      &.theme-inline:hover ::v-deep {
        .el-checkbox .el-checkbox__label {
          color: #128bed;
        }
      }

      &.theme-inline {
        display: inline-block;
        vertical-align: top;

        .el-checkbox {
          height: 100%;

          &.has-value ::v-deep {
            .el-checkbox__label {
              color: #128bed
            }
          }

          ::v-deep {
            .el-checkbox__input {
              display: none !important;
            }

            .el-checkbox__label {
              color: #666;
            }
          }
        }
      }
    }
  }

</style>
