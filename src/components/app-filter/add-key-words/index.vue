<template>
  <div :class="['import-btn',hasAddCompanyFlag ? 'has-import-btn' : '',disable ? 'disable' : '']" @click="importClick">
    <i class="iconfont icon-xinzeng2" :class="['iconfont',hasAddCompanyFlag ? 'icon-daoruqiyeminglu1n' : 'icon-xinzeng2']"></i>
    <span class="import-btn-text">{{ hasAddCompanyFlag ? '批量添加' : '批量添加关键词' }}</span>
    <template v-if="hasAddCompanyFlag">
      <em class="num">{{ importLength }}</em>
      <span class="import-btn-text">个关键词</span>
      <span class="reset" @click.stop="resetImportClick()">重置</span>
    </template>
  </div>
</template>

<script>
import addKeyWordsPop from './add-key-words-pop'
import commonService from '../../../services/common-service'

export default {
  name: 'add-key-words',
  components: { addKeyWordsPop },
  props: {
    functionTableId: {
      type: String,
      default: ''
    },
    disable: { type: Boolean, default: false },
    subTitle: { type: String, default: '' }
  },
  data () {
    return {
      hasAddCompanyFlag: false,
      importResultList: [],
      limit: 100
    }
  },
  mounted () {
    this.getLimitCount()
  },
  computed: {
    importLength() {
      return this.importResultList.filter(i => i.replace(/\s*/g, '')).length
    }
  },
  methods: {
    setVmodel(code) {
      this.hasAddCompanyFlag = true
      this.importResultList = code
    },
    getLimitCount () {
      commonService.postVue('/webapi/saas/expan_cust/tender/getMaxQueryKeywordNum', {})
        .then(response => {
          this.limit = response.result
        })
    },
    importClick () {
      if (this.disable) {
        return
      }
      let params = {
        limit: this.limit,
        defaultList: this.importResultList
      }
      if (this.subTitle) {
        params.subTitle = this.subTitle
      }
      this.$uiService.showDialog(addKeyWordsPop, params).then(list => {
        this.hasAddCompanyFlag = true
        this.importResultList = list
        this.$emit('addSuccess', this.importResultList)
      })
    },
    resetImportClick (disableReturn) {
      if (this.disable) {
        return
      }
      this.hasAddCompanyFlag = false
      this.importResultList = []
      if (!disableReturn) {
        this.$emit('resetKeywords')
      }
    },
    clearData() {
      this.hasAddCompanyFlag = false
      this.importResultList = []
    }
  }

}
</script>

<style lang="scss" scoped src="../common.scss"></style>
