<template>
  <app-popup ref="popRef" :title="title" :subTitle="subTitle" class="app-popup" :buttonOptions="popupButtonOptions">
    <div class="footer-left" slot="footerLeft">注：文本粘贴导入单次至多可匹配{{limit}}行</div>
    <input-text class="input-text-wrap" v-model="inputTextStr" :placeholder="placeholder" ref="inputText" :limitCount="Number(limit)" :showErrorStatus="true"/>
  </app-popup>
</template>

<script>
import inputText from '../../app-text-upload/input-text'

export default {
  name: 'add-key-words-pop',
  components: {
    inputText
  },
  computed: {
    popupButtonOptions () {
      return [{
        name: '取消',
        type: 'normal'
      }, {
        name: '批量查询',
        type: 'primary',
        click: this.localSaveCondition
      }]
    },
    inputList () {
      return this.inputTextStr.split('\n')
    }
  },
  data () {
    return {
      defaultList: [],
      title: '批量添加关键词',
      subTitle: '关键词搜索至多搜索近一年内的信息',
      limit: 50,
      inputTextStr: '',
      placeholder: `您可以输入项目名称、编号、关键词等，我们会自动为您匹配招投标信息

请 "换行" 输入关键词，例：
大数据
招标
云空间
GXLQZXHLM22
Face Two Face Limited`
    }
  },
  mounted () {
    setTimeout(() => {
      if (this.$refs.inputText) {
        this.$refs.inputText.setStrValue(this.defaultList.join('\n'))
      }
    })
  },
  methods: {
    localSaveCondition () {
      let nowLength = this.inputList.filter(i => i.trim()).length
      if (nowLength === 0) {
        this.$message.warning('请输入关键词')
        return
      }
      if (nowLength > Number(this.limit)) {
        this.$refs.inputText.chowMaxRowsError()
      } else {
        this.$refs.popRef.close()
        this.$promise.resolve(this.inputList)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.input-text-wrap {
  height: 314px;

  ::v-deep {
    .el-textarea__inner {
      border-radius: 2px !important;
    }
    ._input-text--placeholder-image {
      .el-textarea__inner {
        background-image: url("../../../assets/images/tender/tender-batch-placeholder.png") !important;
        background-size: 570px auto !important;

        &:focus {
          background-image: none !important;
        }
      }
    }
  }
}

.footer-left {
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  color: #999999;
}

.app-popup {
  ::v-deep{
    .popup-title{
      font-weight: bold;
    }
    ._popup_contentWrapper .footerWrapper ._footerbtn{
      width: 80px;
      padding: 0 !important;
    }
  }
}
</style>
