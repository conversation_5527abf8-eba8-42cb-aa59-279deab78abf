import dropDownCascader from './drop-dwon-cascader'
import dropDownCustom from './drop-down-custom'
import dropDownTimeRange from './drop-down-time-range'
import dropDownNumbers from './drop-down-numbers'
import dropDownCascaderCustom from './drop-down-cascader-custom'
import checkBox from './check-box'
import checkBoxButtonGroup from './check-box-button-group'
import commodityServerPop from './commodity-server-pop'
import singleDropDownVertical from './single-drop-down-custom'
import radioGroup from './radio-group'
import radioGroupNew from './radio-group-new'
import saveConditions from './save-conditions'
import savedConditions from './saved-conditions'
import importCompany from './import-company'
import addKeyWords from './add-key-words'
import FlatCheckboxGroup from './flat-checkbox-group.vue'
import { postVue } from '../../services/common-service'

import _ from 'lodash'
import { translateConditionZTB } from './old-conditions-translate'

export default {
  name: 'app-filter',
  components: {
    FlatCheckboxGroup,
    dropDownCascader,
    dropDownCustom,
    dropDownTimeRange,
    checkBox,
    checkBoxButtonGroup,
    commodityServerPop,
    radioGroup,
    singleDropDownVertical,
    dropDownNumbers,
    dropDownCascaderCustom,
    importCompany,
    addKeyWords,
    radioGroupNew
  },
  props: {
    // dataAppearType: '1', 1：初始不隐藏，2：初始隐藏
    // bindLineName: '更多筛选', // 多个配置合并一行的情况
    // childrenComponents:[],
    // keyName: 'provinces', //类型，亦是最后提交的属性名，必须唯一
    // label: '省份地区',
    // hide:false, //是否隐藏
    // showMore: false,//是否展示更多按钮，默认传false即可，渲染的时候会计算
    // middleHeight: '',//控制单行高度的，默认需要传，会自动渲染计算
    // expand: false,// more展示的时候是否属于展开，默认收起
    // componentType: 'dropDownCascader',//类型
    // radioType: 'radio',//当componentType为radioGroup时候生效：button 为按钮样式的
    // appearTime: true,//下拉框中是否包含自定义时间选择器
    // appearCustomNum: true,//下拉框中是否包含自定义数字范围选择
    // changeFun: ()=>{},//筛选改变时候触发，可用来修改options的展示
    // children: [
    //   {
    //     name:'',
    //     code:''
    //   }
    // ]
    title: {
      type: String,
      default: '筛选条件'
    },
    resetFilterLabel: {
      type: String,
      default: '重置筛选'
    },
    loading: {
      type: Boolean,
      default: false
    },
    functionCd: {
      type: String,
      default: ''
    },
    functionTableId: {
      type: String,
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    },
    isTheme2: {
      type: Boolean,
      default: false
    },
    appearTop: {
      type: Boolean,
      default: false
    },
    savedConditionDialog: {
      type: Object,
      default: () => {
        return {
          categoryName: '筛选方案',
          enable: false,
          buttonName: '已存筛选',
          removeConfirmContent: '确定删除该条记录吗？',
          savedEditChangeOptions: null,
          savedAutoChangeOptions: null
        }
      }
    },
    saveConditionDialog: {
      type: Object,
      default: () => {
        return {
          enable: false,
          buttonName: '保存筛选',
          firstLabelName: '筛选方案',
          placeholder: '请输入筛选方案名称',
          saveConditionReq: null, // 不使用默认保存筛选方法的时候传入
          translateCondition: null // 保存筛选的时候condcontent解析参数，不传默认前端属性
        }
      }
    },
    editConditionOptions: {
      type: Object,
      default: () => {
        return {
          enable: false,
          buttonName: '添加策略',
          openEditDialog: '筛选方案'
        }
      }
    },
    addKeyWordsDialog: {
      type: Object,
      default: () => {
        return {
          enable: false,
          disableAddKeyword: false
        }
      }
    },
    enableImportCompany: {
      type: Boolean,
      default: false
    },
    // 已存筛选需要特殊显示的时候
    specialScreenAppearFilter: { type: Function, default: (value) => { return value } },
    specialScreenAppearText: { type: Function, default: (value) => { return value } }
  },
  data() {
    return {
      propMore: {
        value: 'code',
        label: 'name',
        expandTrigger: 'hover',
        multiple: true
      },
      shrinkHeight: 340,
      shrink: true,
      selectedItems: [],
      savedConditionsList: [],
      disableImportCompany: false
    }
  },
  computed: {
    keyInputComponentOption() { // 输入框独立配置
      return this.options.find(o => o.componentType === 'keyInput' && o.isTop)
    },
    keyInputPreSelectComponentOption() { // 输入框独立配置
      return this.options.find(o => o.componentType === 'keyInputPre')
    },
    screenAppearText() {
      return this.translateScreenAppearText(this.selectedItems)
    },
    canShrink() {
      let match = this.options.find(i => i.dataAppearType === '2')
      // if (!match) {
      //   this.shrink = false
      // }
      // this.shrink = !!match
      return !!match
    },
    saveConditionStatus() {
      return this.saveConditionDialog.enable && !(this.selectedItems.find(i => i.keyName === 'allkeynos'))
    }
  },
  watch: {
    selectedItems: {
      handler(newValue, oldValue) {
        this.computeHeight()
        if (!this.debounceTimed) {
          this.debounceTimed = setTimeout(() => {
            this.debounceTimed = ''
            this.$emit('change', newValue)
          }, 200)
        }
      },
      deep: true
    },
    options: {
      handler(newValue, oldValue) {
        this.computeHeight()
      },
      deep: true
    }
  },
  mounted() {
    this.computeHeight()
    if (this.savedConditionDialog.enable) {
      this.getSavedConditionsList()
    }
    this.$nextTick(() => {
      this.options.forEach(option => {
        if (option.componentType === 'keyInput') {
          option.value = ''
          let component = this.$refs['component-' + option.keyName]
          if (option.isTop) {
            component.$refs.input.focus()
          } else {
            component[0].$refs.input.focus()
          }
        }
      })
    })
  },
  methods: {
    translateScreenAppearText(selectedItems) {
      let result = []
      if (selectedItems) {
        let data = _.cloneDeep(selectedItems)
        data = this.specialScreenAppearFilter(data)
        data.forEach((i) => {
          let match = result.findIndex((j) => {
            return j.keyName === i.keyName
          })
          if (match >= 0) {
            result[match].mergeName = result[match].mergeName + ',' + i.name
          } else {
            result.push({
              ...i,
              mergeName: i.name
            })
          }
        })
      }
      return this.specialScreenAppearText(result)
    },
    importSuccess(list) {
      this.$emit('importCompanyCallBack')
      this.clearSelectedFromKeyName('allkeynos')
      this.selectedItems.push({
        keyName: 'allkeynos',
        name: list.map(v => v.corpName).join(','),
        code: list.map(v => v.corpKeyNo),
        label: '已导入企业名录'
      })
      this.addKeyWordsDialog.disableAddKeyword = true
      let option = this.options.find(i => i.componentType === 'keyInput')
      if (option && option.changeFun) {
        option.changeFun(this.selectedItems, 'allkeynos')
      }
    },
    resetCompany() {
      this.selectedItems = this.selectedItems.filter(i => i.keyName !== 'allkeynos')
      this.addKeyWordsDialog.disableAddKeyword = false
      let option = this.options.find(i => i.componentType === 'keyInput')
      if (option && option.changeFun) {
        option.changeFun(this.selectedItems, option.keyName)
      }
    },
    addSuccess(list) {
      let option = this.options.find(i => i.componentType === 'keyInput')
      if (option) {
        list = list.map(i => i.trim())
        list = list.filter(i => i)
        this.clearSelectedFromKeyName(option.keyName)

        this.autoSelect([
          {
            label: '批量添加关键词',
            codeList: list,
            name: list.join(','),
            keyName: option.keyName,
            componentType: option.componentType
          }
        ], [option.keyName])
        if (option.changeFun) {
          option.changeFun(this.selectedItems, option.keyName)
        }
      }
      this.disableImportCompany = true
    },
    resetKeywords() {
      let match = this.options.find(i => i.componentType === 'keyInput')
      if (match) {
        this.resetItem(match)
        this.selectedItems = this.selectedItems.filter(i => i.keyName !== match.keyName)
      }
      this.disableImportCompany = false
      let option = this.options.find(i => i.componentType === 'keyInput')
      if (option && option.changeFun) {
        option.changeFun(this.selectedItems, option.keyName)
      }
    },
    saveCondition() {
      if (this.selectedItems.length === 0) {
        this.$message.warning('请至少选择一个策略条件')
        return
      }
      let params = {
        screenAppearText: this.screenAppearText,
        selectedItems: this.selectedItems,
        functionCd: this.functionCd
      }
      if (this.saveConditionDialog.buttonName) {
        params.title = this.saveConditionDialog.buttonName
      }
      if (this.saveConditionDialog.selectPushName) {
        params.selectPushName = this.saveConditionDialog.selectPushName
      }
      if (this.saveConditionDialog.firstLabelName) {
        params.firstLabelName = this.saveConditionDialog.firstLabelName
      }
      if (this.saveConditionDialog.placeholder) {
        params.placeholder = this.saveConditionDialog.placeholder
      }
      if (this.saveConditionDialog.translateCondition) {
        params.conditions = this.saveConditionDialog.translateCondition(this.selectedItems)
      }
      if (this.saveConditionDialog.saveConditionReq) {
        params.saveCondition = this.saveConditionDialog.saveConditionReq
      }
      this.$uiService.showDialog(saveConditions, params).then(() => {
        this.getSavedConditionsList()
      })
    },
    savedCondition() {
      let params = {
        customConditions: this.savedConditionsList,
        selectCondition: this.resetComponentsValue,
        getCondition: this.getSavedConditionsList,
        changeCallBack: (item) => {
          if (this.savedConditionDialog.savedEditChangeOptions) {
            this.savedConditionDialog.savedEditChangeOptions(item)
          }
        }
      }
      if (this.savedConditionDialog.removeConfirmContent) {
        params.removeConfirmContent = this.savedConditionDialog.removeConfirmContent
      }
      if (this.savedConditionDialog.buttonName) {
        params.title = this.savedConditionDialog.buttonName
      }
      if (this.translateScreenAppearText) {
        params.translateScreenAppearText = this.translateScreenAppearText
      }
      if (this.savedConditionDialog.categoryName) {
        params.categoryName = this.savedConditionDialog.categoryName
      }
      this.$uiService.showDialog(savedConditions, params).then(data => {

      })
    },
    getSavedConditionsList() {
      postVue('/search_cond/list', {
        functionCd: this.functionCd
      }).then(res => {
        this.savedConditionsList = translateConditionZTB(this.functionCd, res.resultList, this.options)
      })
    },
    resetComponentsValue(item) {
      item = translateConditionZTB(this.functionCd, [item], this.options)[0]
      let selects = JSON.parse(item.showContent)
      let conditions = selects
      if (selects.selects) {
        conditions = selects.selects
      }

      if (this.$refs.importCompany) {
        this.$refs.importCompany.resetImportClick(true)
      }
      if (this.$refs.addKeyWords) {
        this.$refs.addKeyWords.resetImportClick(true)
      }
      // 有关键词的情况需要禁用导入企业
      let hasKeyInput = conditions.find(i => i.componentType === 'keyInput')
      this.disableImportCompany = !!hasKeyInput
      if (hasKeyInput && hasKeyInput.codeList) {
        this.$refs.addKeyWords.setVmodel(hasKeyInput.codeList)
      }

      // 有导入企业情况禁用关键词相关
      let hasAllKeyNos = conditions.find(i => i.keyName === 'allkeynos')
      this.addKeyWordsDialog.disableAddKeyword = !!hasAllKeyNos
      if (hasAllKeyNos) {
        this.$refs.importCompany.setVmodel(hasAllKeyNos.code)
      }

      if (this.savedConditionDialog.savedAutoChangeOptions) {
        this.savedConditionDialog.savedAutoChangeOptions(selects.option || selects, item)
      }
      setTimeout(() => { // 改成异步，防止savedAutoChangeOptions修改了配置的显示隐藏导致无法赋值
        this.autoSelect(conditions)
      })
    },
    editCondition() {
      this.editConditionOptions.openEditDialog({
        screenAppearText: this.screenAppearText,
        selectedItems: this.selectedItems
      })
    },
    computeHeight(empty) {
      if (empty) {
        this.selectedItems = []
      }

      this.$nextTick(() => {
        this.adjustLayout()
      })
    },
    isButtonTypeComponent(option) {
      return (option.componentType === 'radioGroup' && option.radioType === 'button') || (option.componentType === 'checkboxGroup' && option.checkBoxType === 'button')
    },
    adjustLayout() {
      // 筛选条件收起展开控制
      this.options.forEach((option) => {
        if (option.forceHiddenMore) {
          option.showMore = false
          option.middleHeight = 'auto'
          return
        }
        if (!option.bindLineName) {
          if (option.componentType === 'inputDialog') {
            option.showMore = false
          } else {
            let height = this.isButtonTypeComponent(option) ? '24px' : '22px'
            let showMoreHeight = this.isButtonTypeComponent(option) ? 39 : 37
            if (option.componentType === 'radioGroup') {
              option.realHeight =
                this.$refs[option.keyName] &&
                this.$refs[option.keyName][0].children &&
                this.$refs[option.keyName][0].children[0].children &&
                this.$refs[option.keyName][0].children[0].children[0] &&
                this.$refs[option.keyName][0].children[0].children[0].scrollHeight
            } else if (option.componentType === 'checkboxGroup') {
              option.realHeight =
                this.$refs[option.keyName] &&
                this.$refs[option.keyName][0].children &&
                this.$refs[option.keyName][0].children[0] &&
                this.$refs[option.keyName][0].children[0].scrollHeight
            } else {
              option.realHeight = this.$refs[option.keyName] && this.$refs[option.keyName][0].scrollHeight
            }
            option.showMore = this.$refs[option.keyName] && this.$refs[option.keyName][0].scrollHeight > showMoreHeight
            option.middleHeight = option.expand ? (option.realHeight - 15 + 'px') : height
            this.getSingleHeight(option)
          }
        }
      })
      this.getShrinkHeight()
    },
    getSingleHeight(option) {
      // 控制单行高度
      if (option.showMore) {
        let height = this.isButtonTypeComponent(option) ? '24px' : '22px'
        option.middleHeight = option.expand ? (option.realHeight - 15 + 'px') : height
      }
    },
    toggle(option) {
      option.expand = !option.expand
      this.getSingleHeight(option)
      this.getShrinkHeight()
    },
    getShrinkHeight() {
      // 控制总外框高度
      this.$nextTick(() => {
        let resultHeight = 0
        if (this.$refs.content) {
          let children = this.$refs.content.children
          for (const element of children) {
            if ((this.shrink && element.dataset.appear === '1' && element.style.display !== 'none') || (!this.shrink && (element.dataset.appear === '1' || element.dataset.appear === '2') && element.style.display !== 'none')) {
              resultHeight += element.offsetHeight
            }
          }
        }
        this.shrinkHeight = resultHeight + 15 + (this.selectedItems.length > 0 ? 15 : 0) + (this.appearTop ? 15 : 0)
      })
    },
    dropDownChange(selectMenus, option) {
      let keyName = option.keyName
      this.clearSelectedFromKeyName(option.keyName)
      let unlimitedSelect = _.cloneDeep(selectMenus)
      if (Object.prototype.toString.call(selectMenus) === '[object Array]') {
        unlimitedSelect = unlimitedSelect.find(i => i.name === '不限' && i.code === 'unlimited')
      }
      if (unlimitedSelect && (unlimitedSelect.name === '不限' && unlimitedSelect.code === 'unlimited')) { // 不限为不选
        this.$refs['component-' + option.keyName][0].setVmodel([])
        return
      } else {
        if (Object.prototype.toString.call(selectMenus) === '[object Array]' && selectMenus.length > 0) {
          selectMenus.forEach((i) => {
            this.selectedItems.push({
              name: i.name,
              code: i.code,
              keyName: keyName,
              label: option.label,
              isCustom: i.isCustom
            })
          })
        } else if (Object.prototype.toString.call(selectMenus) === '[object Object]') {
          this.selectedItems.push({
            code: selectMenus.code,
            name: selectMenus.name,
            keyName: keyName,
            label: option.label,
            isCustom: selectMenus.isCustom
          })
        }
      }

      if (option.changeFun) {
        option.changeFun(this.selectedItems, option.keyName)
      }
    },
    dropDownCascaderCustomChange(selectMenus, option) {
      this.clearSelectedFromKeyName(option.keyName)
      this.selectedItems.push(...selectMenus)
      if (option.changeFun) {
        option.changeFun(this.selectedItems, option.keyName)
      }
    },
    dropDownCascaderChange(selectMenus, option) {
      this.clearSelectedFromKeyName(option.keyName)
      selectMenus.forEach((i) => {
        // isLast特殊处理，省市区，最后一级会使用上一级的code
        let param = {
          name: i.name,
          code: i.code,
          keyName: option.keyName,
          label: option.label,
          isCustom: i.isCustom,
          isLast: i.isLast,
          level: i.level,
          newParams: i.newParams
        }
        if (i.parent) {
          param.parentCode = i.parent.value
        }
        this.selectedItems.push(param)
      })
      if (option.changeFun) {
        option.changeFun(this.selectedItems, option.keyName)
      }
    },
    changeInput(val, option) {
      option.value = val
    },
    changeInputEmpty(option) {
      option.value = ''
      this.handleInput(option, true)
    },
    handleInput(option, notCheck) {
      if (option.value === undefined) {
        option.value = ''
      }

      option.value = option.value.trim()

      if (!notCheck && !option.value.length) {
        this.$message.warning('请输入关键词')
        return
      }

      // let valueList = option.value.split(' ')
      // valueList = valueList.filter(i => i.trim())

      if (!notCheck && !option.disableLimitWordsLength && option.value.length < 2) {
        this.$message.warning('请输入至少两个字符！')
        return
      }

      this.disableImportCompany = !!option.value

      let matchIndex = this.selectedItems.findIndex(i => i.keyName === option.keyName)
      if (!option.value) {
        this.clearSelectedFromKeyName(option.keyName)
      } else if (matchIndex >= 0) {
        if (this.$refs.addKeyWords) {
          this.$refs.addKeyWords.resetImportClick(true)
        }
        this.selectedItems[matchIndex].codeList = null
        this.selectedItems[matchIndex].code = option.value
        this.selectedItems[matchIndex].name = option.value
        this.selectedItems[matchIndex].label = option.label
      } else {
        this.selectedItems.push({
          code: option.value,
          name: option.value,
          keyName: option.keyName,
          label: option.label,
          componentType: option.componentType
        })
      }

      if (option.changeFun) {
        option.changeFun(this.selectedItems, option.keyName)
      }
    },
    timeChange(value, option) {
      this.clearSelectedFromKeyName(option.keyName)
      let keyName = option.keyName
      if (value && value.length > 0) {
        this.selectedItems.push({
          code: value[0] + '-' + value[1],
          name: value[0] + '-' + value[1],
          keyName: keyName,
          label: option.label,
          isSingleCustom: true
        })
      }

      if (option.changeFun) {
        option.changeFun(this.selectedItems, option.keyName)
      }
    },
    checkButtonChangeGroup(values, option) {
      this.clearSelectedFromKeyName(option.keyName)
      values.forEach(value => {
        this.selectedItems.push({
          ...value,
          keyName: option.keyName,
          label: option.label
        })
      })
      if (option.changeFun) {
        option.changeFun(this.selectedItems, option.keyName)
      }
    },
    radioButtonChangeGroup(value, option) {
      this.clearSelectedFromKeyName(option.keyName)
      if (value && value.code) {
        this.selectedItems.push({
          ...value,
          keyName: option.keyName,
          label: option.label
        })
      }
      if (option.changeFun) {
        option.changeFun(this.selectedItems, option.keyName)
      }
    },
    inputPopChange(values, option) {
      this.clearSelectedFromKeyName(option.keyName)
      this.selectedItems = this.selectedItems.filter(i => {
        if (i.keyName === option.relationKey) {
          if (!i.isInner) {
            return false
          } else if (values.find(m => m.code === i.code)) {
            return false
          }
        }
        return true
      })
      this.selectedItems.push(...values)
      // 修改了keyName 为 options.relationKey相关的配置，需要组件重新显示
      this.$refs['component-' + option.relationKey][0].setVmodel(this.selectedItems.filter(i => i.keyName === option.relationKey))

      if (option.changeFun) {
        option.changeFun(this.selectedItems, option.keyName)
      }
    },
    clearSelectedFromKeyName(keyName) {
      this.selectedItems = this.selectedItems.filter((j) => {
        return j.keyName !== keyName
      })
    }, // 移除操作
    removeItem(item) {
      this.selectedItems = this.selectedItems.filter((i) => {
        return i.keyName !== item.keyName
      })
      this.resetItem(item)
      this.$emit('removeItem', item, this.selectedItems)
    },
    resetItem(item, emptyAll) {
      if (item.componentType === 'keyInput' || emptyAll) {
        // 删除关键词要释放导入企业
        this.disableImportCompany = false
      }

      if (this.enableImportCompany && (item.keyName === 'allkeynos' || emptyAll)) {
        this.$refs.importCompany.clearData()
        this.disableImportCompany = false
        this.addKeyWordsDialog.disableAddKeyword = false
      }
      this.options.forEach((option) => {
        if (option.bindLineName) {
          option.childrenComponents.forEach(o => {
            let canNext = emptyAll || (item.keyName === o.keyName)
            if (canNext) {
              this.$refs['component-' + o.keyName][0].setVmodel([])
            }
          })
        } else {
          let canNext = emptyAll || (item.keyName === option.keyName)
          if (canNext) {
            if (option.componentType === 'keyInput') {
              option.value = ''
              let component = this.$refs['component-' + option.keyName]
              if (option.isTop) {
                component.setVmodel('')
              } else {
                component[0].setVmodel('')
              }
              if (this.addKeyWordsDialog.enable) {
                this.$refs.addKeyWords.clearData()
                this.addKeyWordsDialog.disableAddKeyword = false
                this.disableImportCompany = false
              }
            } else if (option.componentType === 'keyInputPre') {
              this.$refs['component-' + option.keyName].setVmodel([])
            } else {
              this.$refs['component-' + option.keyName][0].setVmodel([])
            }
          }
        }
      })
    }, // 清空所有筛选
    clearCondition() {
      this.selectedItems = []
      this.resetItem({}, true)
      this.$emit('removeAllItem', null, this.selectedItems)
    },
    autoMoreItemMatch(option, filterConditions) {
      if (option.componentType === 'time') {
        this.$refs['component-' + option.keyName][0].setVmodel(filterConditions.length > 0 && filterConditions[0].code ? filterConditions[0].code.split('-') : [])
      } else if (option.componentType === 'keyInputPre') {
        this.$refs['component-' + option.keyName].setVmodel(filterConditions)
      } else {
        this.$refs['component-' + option.keyName][0].setVmodel(filterConditions)
      }
    },
    autoItemPopMatch(item, conditions, components) {
      let temp = []
      item.forEach(childrenSecond => {
        let matchCondition = conditions.find(i => {
          return i.code === childrenSecond.code && i.name === childrenSecond.name
        })
        if (matchCondition) {
          if (childrenSecond.children && childrenSecond.children.length > 0) {
            childrenSecond.children.forEach(i => {
              if (i.children && i.children.length > 0) {
                i.children.forEach(j => {
                  temp.push([childrenSecond.code, i.code, j.code])
                })
              } else {
                temp.push([childrenSecond.code, i.code])
              }
            })
          } else {
            temp.push([childrenSecond.code])
          }
        }
        if (childrenSecond.children && childrenSecond.children.length > 0) {
          childrenSecond.children.forEach(childrenThird => {
            let matchCondition = conditions.find(i => {
              return i.code === childrenThird.code && i.name === childrenThird.name
            })
            if (matchCondition) {
              if (childrenThird.children && childrenThird.children.length > 0) {
                if (!matchCondition.isLast) {
                  childrenThird.children.forEach(j => {
                    temp.push([childrenSecond.code, childrenThird.code, j.code])
                  })
                }
              } else {
                temp.push([childrenSecond.code, childrenThird.code])
              }
            }
            if (childrenThird.children && childrenThird.children.length > 0) {
              childrenThird.children.forEach(j => {
                let matchCondition = conditions.find(i => {
                  return i.code === j.code && i.name === j.name
                })
                if (matchCondition) {
                  temp.push([childrenSecond.code, childrenThird.code, j.code])
                }
              })
            }
          })
        }
        components.setVmodel(temp)
      })
    },
    autoSelect(conditions, keyNames) {
      if (keyNames && keyNames.length > 0) {
        if (conditions.length > 0) {
          // 若传入的数据已有，删掉重新添加
          this.selectedItems = this.selectedItems.filter(i => !keyNames.find(j => j === i.keyName) && !conditions.find(z => z.code === i.code))
          this.selectedItems.push(...conditions)
        } else {
          // 若是空数据，直接删除此keyNames下的数据
          this.selectedItems = this.selectedItems.filter(i => {
            return !keyNames.find(j => j === i.keyName)
          })
        }
      } else {
        this.selectedItems = conditions
      }
      this.options.forEach((option) => {
        let filterConditions = this.selectedItems.filter(i => i.keyName === option.keyName)
        if (keyNames && keyNames.length > 0) {
          let matchKeyName = keyNames.find(i => i === option.keyName)
          if (!matchKeyName) {
            return
          }
        }

        if (option.bindLineName) {
          option.childrenComponents.forEach(o => {
            filterConditions = this.selectedItems.filter(i => i.keyName === o.keyName)
            if (o.componentType === 'dropDownCascader') {
              let components = this.$refs['component-' + o.keyName][0]
              this.autoItemPopMatch(o.children, filterConditions, components)
            } else {
              this.autoMoreItemMatch(o, filterConditions)
            }
          })
        } else if (option.componentType === 'keyInput') {
          let component = this.$refs['component-' + option.keyName]
          let selectValue = filterConditions[0] ? filterConditions[0].code : ''
          option.value = selectValue || ''
          if (option.isTop) {
            component.setVmodel(selectValue)
          } else {
            component[0].setVmodel(selectValue)
          }
        } else {
          this.autoMoreItemMatch(option, filterConditions)
        }
      })
    }
  }
}
