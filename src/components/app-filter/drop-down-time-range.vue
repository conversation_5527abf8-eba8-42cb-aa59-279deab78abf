<template>
  <div class="time-wrap" @mouseup.stop @mouseenter="timePopShow" @mouseleave="timePopHide" :style="{alignItems:isRadio ? 'center' : 'baseline'}">
    <el-checkbox
      v-if="!isRadio"
      ref="clickEl"
      :value="certificationTime.length > 0"
      :class="[
          'custom-item-checkbox',
          (this.certificationTime.length) ? 'has-value' : '',
          !isCheckBox ? 'disableCheckBox' : '',
          customButtonType ? 'button-type' : '',
          isActive ?'pop-show-checkbox' :''
        ]"
      @change="changeCheckBox">
      {{ choseShowText || title }}<i :class="['jt',arrowClass]"></i>
    </el-checkbox>
    <el-radio
      v-else
      ref="clickEl"
      :value="certificationTime.length > 0"
      :class="['custom-item-radio']"
      @change="changeCheckBox"
      :label="true"
    >{{ choseShowText || title }}<i :class="['jt',arrowClass]"></i></el-radio>

    <el-date-picker
      ref="timeValue"
      class="datePickerWrap"
      v-model="certificationTime"
      type="daterange"
      :align="align"
      :picker-options="pickerOptions"
      @change="timeChange"
      @blur="timeBlur"
      @focus="timeFocus"
      value-format="yyyyMMdd"
    >
    </el-date-picker>
  </div>
</template>

<script>
import _ from 'lodash'
import moment from 'moment'

export default {
  name: 'drop-down-time-range',
  props: {
    isCheckBox: { type: Boolean, default: false },
    align: { type: String, default: 'left' },
    title: { type: String, default: '自定义' },
    vModule: { type: Array, default: () => [] },
    getWrapRef: { default: null },
    bindComponents: { default: null },
    arrowType: { type: Number, default: 1 }, // 1:上下，0：右
    customButtonType: { type: Number, default: 0 },
    isRadio: { type: Boolean, default: false },
    enableChangeButtonText: { type: Boolean, default: false },
    enableDisabledDate: { type: Number, default: 0 },
    limit15Days: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    choseShowText() {
      if (this.enableChangeButtonText && this.certificationTime[0] && this.certificationTime[1]) {
        return moment(this.certificationTime[0]).format('YYYY-MM-DD') + ' 至 ' + moment(this.certificationTime[1]).format('YYYY-MM-DD')
      }
      return ''
    },
    arrowClass() {
      if (this.isActive) {
        if (this.arrowType === 1) {
          return 'jtsx jtshang'
        } else {
          return 'jtzy jtyou'
        }
      } else {
        if (this.arrowType === 1) {
          return 'jtsx jtxia'
        } else {
          return 'jtzy jtyou'
        }
      }
    },
    pickerOptions() {
      return {
        shortcuts: this.shortcuts,
        disabledDate: this.enableDisabledDate ? this.disabledDate : null,
        ...this.limit15Days ? {
          disabledDate: (current) => {
            return current && current > moment().subtract(15, 'days')
          }
        } : {}
      }
    }
  },
  data() {
    return {
      shortcuts: [
        {
          text: '今天',
          onClick(picker) {
            const time = moment().toDate()
            picker.$emit('pick', [time, time])
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = moment().toDate()
            const start = moment().subtract(7, 'days').toDate()
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = moment().toDate()
            const start = moment().subtract(1, 'months').toDate()
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = moment().toDate()
            const start = moment().subtract(3, 'months').toDate()
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近半年',
          onClick(picker) {
            const end = moment().toDate()
            const start = moment().subtract(0.5, 'years').toDate()
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一年',
          onClick(picker) {
            const end = moment().toDate()
            const start = moment().subtract(1, 'years').toDate()
            picker.$emit('pick', [start, end])
          }
        }
      ],
      certificationTime: [],
      isActive: false
    }
  },
  watch: {
    vModule(newValue) {
      if (!_.isEqual(this.certificationTime, newValue)) {
        this.$emit('change', newValue)
      }
      this.certificationTime = newValue
    }
  },
  mounted() {
    this.$emit('update:bindComponents', this)
  },
  methods: {
    disabledDate(time) {
      if (this.enableDisabledDate) {
        let tempTime = moment(time)
        let max = moment()
        let min = moment().subtract(1, 'years')
        return tempTime.isAfter(max, 'day') || min.isAfter(tempTime, 'day')
      } else {
        return false
      }
    },
    setVmodel(newValue) {
      this.certificationTime = newValue
    },
    resetTimePos() {
      // 下拉中自定义时间计算下拉位置
      if (this.getWrapRef) {
        setTimeout(() => {
          let wrapRef = this.getWrapRef()
          this.$refs.timeValue.popperElm.style.top = wrapRef.getBoundingClientRect().top + wrapRef.offsetHeight - this.$refs.timeValue.popperElm.offsetHeight - 10 + 'px'
          let posLeft = wrapRef.getBoundingClientRect().left
          let posSurplusRight = document.body.offsetWidth - posLeft - wrapRef.offsetWidth
          this.$refs.timeValue.popperElm.style.margin = '10px 0 0 0'
          if (posLeft > posSurplusRight) { // 哪边剩余空间大显示在哪边
            this.$refs.timeValue.popperElm.style.left = posLeft - this.$refs.timeValue.popperElm.offsetWidth + 'px'
          } else {
            this.$refs.timeValue.popperElm.style.left = posLeft + wrapRef.offsetWidth + 'px'
          }
          this.$refs.timeValue.popperElm.lastChild.style.display = 'none'
        })
      }
    },
    timeChange(value) {
      this.$emit('update:vModule', value)
      this.$emit('change', value)
    },
    changeCheckBox() {
      if (this.isCheckBox) {
        if (this.certificationTime.length > 0) {
          this.certificationTime = []
          this.timeChange([])
          this.$emit('timeCheckboxClick')
          this.timePopHide()
        } else {
          this.timePopShow()
        }
      }
    },
    timeBlur() {
      this.isActive = false
    },
    timeFocus() {
      this.isActive = true
    },
    timePopShow() {
      this.timePopClearTime()
      this.$refs.timeValue.focus()
      this.resetTimePos()
      this.$nextTick(() => {
        if (!this.$refs.timeValue.popperElm.onmouseleave) {
          this.$refs.timeValue.popperElm.onmouseleave = this.timePopHideEvent
        }
        if (!this.$refs.timeValue.popperElm.onmouseenter) {
          this.$refs.timeValue.popperElm.onmouseenter = this.timePopClearTime
        }
      })
    },
    timePopHideEvent() {
      this.timePopHide()
      this.$emit('timeMouseLeave')
    },
    timePopHide() {
      if (this.timeid) {
        clearTimeout(this.timeid)
        this.timeid = null
      }
      this.timeid = setTimeout(() => {
        this.$refs.timeValue.hidePicker()
      }, 150)
    },
    timePopClearTime() {
      if (this.timeid) {
        clearTimeout(this.timeid)
        this.timeid = null
      }
      this.$emit('timePopClearTimeout')
    }

  }
}
</script>
<style lang="scss" scoped src="./common.scss"></style>
<style scoped lang="scss">
@import "../../styles/common";

.time-wrap {
  display: flex;
  position: relative;
  height: 34px;

  ::v-deep {
    .el-checkbox__label {
      font-weight: normal;
      margin-left: 5px;
    }
    .el-radio:focus .el-radio__inner{
      box-shadow: none !important;
    }
  }

  .datePickerWrap {
    visibility: hidden;
    width: 56px;
    height: 21px;
    overflow: hidden;
    position: absolute;
    left: 0;
    top: 0;
  }
}
</style>
