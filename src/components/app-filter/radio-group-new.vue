<template>
  <div class="radio-content-wrap">
    <el-radio-group class="group-wrap" v-model="checkboxGroup" @change="onChange">
      <template  v-if="radioType !== 'button'">
        <el-radio
          class="radio-wrap"
          v-for="(option,index) in options"
          :key="option.name + option.code + index"
          :label="option.code"
          :style="{width:handleLabelWidth(option.name,labelWidth,'()' + option.count)}"
        >
          {{ option.name }}<template v-if="enableCount">(<span class="count">{{ option.count }}</span>)</template>
        </el-radio>
      </template>
      <template v-else>
        <el-radio-button
          class="bt-wrap"
          v-for="(option,index) in options"
          :key="option.name + option.code + index"
          :label="option.code"
        >
          {{ option.name }}<template v-if="enableCount">(<span class="count">{{ option.count }}</span>)</template>
        </el-radio-button>
      </template>
    </el-radio-group>
    <drop-down-time-range
      :style="{marginLeft: radioType !== 'button' ? '30px' : ''}"
      v-if="appearTime"
      class="custom-time-wrap"
      :arrowType="1"
      :isCheckBox="false"
      :isRadio="customIsRadio"
      @change="timeChange"
      ref="customTimeRef"
      :enableChangeButtonText="enableChangeButtonText"
      :customButtonType="radioType === 'button' ? 1 : 0"
      :enableDisabledDate="enableDisabledDate"
    ></drop-down-time-range>
    <drop-down-numbers
      v-if="appearCustomNum"
      :canStartEmpty="canStartEmpty"
      :canEndEmpty="canEndEmpty"
      :customNumberRegType="customNumberRegType"
      :enableChangeButtonText="enableChangeButtonText"
      :unit="unit"
      placement="bottom"
      :isCheckBox="false"
      :arrowType="1"
      class="custom-number-wrap"
      @change="customNumberChange"
      ref="customNumberRef"
      :customButtonType="radioType === 'button' ? 1 : 0"
    />
  </div>

</template>

<script>
import dropDownNumbers from './drop-down-numbers'
import dropDownTimeRange from './drop-down-time-range'
import moment from 'moment'
import { handleLabelWidth } from './tools'

export default {
  name: 'radio-group',
  components: { dropDownNumbers, dropDownTimeRange },
  props: {
    labelWidth: { default: null },
    radioType: { type: String, default: 'radio' },
    options: { type: Array, default: () => [] },
    bindComponents: { default: null },
    appearTime: { type: Boolean, default: false },
    appearCustomNum: { type: Boolean, default: false },
    canStartEmpty: { type: Boolean, default: false },
    canEndEmpty: { type: Boolean, default: false },
    customNumberRegType: { type: Number, default: 1 },
    unit: { type: String, default: '万' },
    enableChangeButtonText: { type: Boolean, default: false },
    enableDisabledDate: { type: Number, default: 0 },
    enableCount: { type: Boolean, default: false },
    customIsRadio: { type: Boolean, default: false }
  },
  data() {
    return {
      checkboxGroup: '',
      handleLabelWidth: handleLabelWidth
    }
  },
  mounted() {
    this.$emit('update:bindComponents', this)
  },
  methods: {
    setVmodel(newValue) {
      newValue = newValue && newValue.length ? newValue[0] : {}
      if (newValue.isCustom) {
        this.checkboxGroup = ''
        this.setCustomValue(newValue.code ? newValue.code.split('-') : [])
      } else {
        this.checkboxGroup = newValue.code
        this.setCustomValue([])
      }
    },
    setCustomValue(value) {
      if (this.appearTime) {
        this.$refs.customTimeRef.setVmodel(value)
      }
      if (this.appearCustomNum) {
        this.$refs.customNumberRef.setVmodel(value)
      }
    },
    onChange(value) {
      let result = this.options.find(i => {
        return value === i.code
      })
      if (result) {
        this.setCustomValue([])
      }
      this.$emit('change', result)
    },
    customNumberChange(value) {
      if (value) {
        this.checkboxGroup = ''
        this.$emit('change', {
          ...value,
          isCustom: this.appearCustomNum
        })
      }
    },
    timeChange(value) {
      if (value && value.length > 0) {
        this.checkboxGroup = ''
        this.$emit('change', {
          code: value[0] + '-' + value[1],
          name: moment(value[0]).format('YYYY-MM-DD') + '至' + moment(value[1]).format('YYYY-MM-DD'),
          isCustom: this.appearTime
        })
      } else {
        this.$emit('change', {})
      }
    }
  }
}
</script>

<style scoped lang="scss">
.radio-content-wrap {
  display: flex;
  flex-wrap: wrap;
}
.group-wrap {
  display: flex;
  flex-wrap: wrap;
}

.bt-wrap {
  border: 0;
  padding: 1px 10px;
  line-height: 22px;
  font-size: 14px;
  box-sizing: border-box;
  border-radius: 2px;
  display: block;
  margin-right: 10px;
  margin-bottom: 15px;
  box-shadow:none !important;

  .count {
    color: #999999;
  }

  ::v-deep {
    .el-radio-button__inner {
      background: none;
      border: 0;
      border-radius: 2px;
      padding: 0;
      transition: none;
      display: block;
      line-height: 22px;
      box-shadow: inherit;
      color: #333333;
      font-weight: normal;

      &:hover {
        color: #128BED;

        .count {
          color: #128BED;
        }
      }
    }

  }

  &.is-active,&:hover {
    background: #128BED;
    border-radius: 2px;

    .count {
      color: #FFFFFF;
    }

    ::v-deep {
      .el-radio-button__inner {
        color: #FFFFFF;

        &:hover .count {
          color: #FFFFFF;
        }
      }
    }
  }
}

.radio-wrap {
  height: 22px;
  align-items: center;
  display: flex;
  margin-bottom: 15px;
  ::v-deep {
    .el-radio__input {
      display: flex;
      align-items: center;
    }
    .el-radio__label{
      font-weight: normal;
      padding-left: 5px;
    }
  }
}

.custom-time-wrap{
  ::v-deep {
    &.pop-show-checkbox{
      background: none;
      //background: #128BED;
      .el-checkbox__label{
        //color: #FFFFFF;
      }
    }

    .el-radio.custom-item-radio {
      line-height: 22px;
      margin-bottom: 10px;
    }
  }
}


</style>
