<template>
  <app-popup :title="title" class="app-popup" :buttonOptions="popupButtonOptions">
    <el-form class="form-wrap" ref="conditionForm" :model="formModel" :rules="formRules" label-width="87px" @submit.native.prevent="localSaveCondition">
      <el-form-item :label="firstLabelName" prop="conditionName">
        <el-input class="default-input" v-model="formModel.conditionName" v-placeholder="placeholder" min="1" max="100" size="medium" v-focus>
          <i v-if="formModel.conditionName" slot="suffix" class="input-clear-icon iconfont icon-shanchu3" @click="formModel.conditionName=''"></i>
        </el-input>
      </el-form-item>
      <el-form-item :label="secondLabelName" prop="conditions" class="condition-form-wrap" v-if="screenAppearText.length > 0">
        <div class="conditions-wrap">
          <el-popover v-for="(item,index) in screenAppearText" placement="bottom" trigger="hover" popper-class="default-popover-tooltip tool-auto-width400" :key="'screenAppearText' + index" :open-delay="200">
            <p> {{ item.label }}：{{ item.mergeName }}</p>
            <div slot="reference" :class="['conditions overrflow-with-ellipsis', 'active']">
              {{ item.label }}：{{ item.mergeName }}
            </div>
          </el-popover>
        </div>
      </el-form-item>
      <el-form-item label="平台推送" prop="setAsDefault" class="condition-form-wrap condition-select" v-if="selectPushName && !disableSubscribeFlag">
        <el-switch v-model="formModel.setAsDefault"></el-switch>
        <p>首页查看</p>
        <p>{{selectPushName}}</p>
      </el-form-item>
      <el-form-item label="邮件推送" prop="setEmailPush" class="condition-form-wrap condition-select" v-if="selectPushName && !disableSubscribeFlag">
        <el-switch v-model="formModel.setEmailPush"></el-switch>
        <p>邮件推送</p>
        <p>{{selectPushName}}</p>
      </el-form-item>
    </el-form>
  </app-popup>
</template>

<script>
import customerService from '../../services/customer'

export default {
  name: 'saved-conditions',
  props: {
    saveFunction: {
      type: Function,
      default: () => {
      }
    }
  },
  data () {
    return {
      selectPushName: '',
      placeholder: '请输入筛选方案名称',
      screenAppearText: [],
      selectedItems: [],
      functionCd: '',
      title: '保存筛选',
      formModel: {
        conditionName: '',
        setAsDefault: false,
        setEmailPush: false
      },
      firstLabelName: '筛选方案',
      secondLabelName: '筛选条件',
      formRules: {
        conditionName: [{
          required: true,
          validator: this.validateGroupName,
          trigger: 'blur'
        }]
      },
      popupButtonOptions: [{
        name: '取消',
        type: 'normal'
      }, {
        name: '确定',
        type: 'primary',
        click: this.localSaveCondition
      }],
      errorName: '',
      errorNameLimit: '',
      saveCondition: null,
      isHideDefaultTips: false,
      hasNotSetEmail: false,
      isHidePush: false,
      disableSubscribeFlag: true
    }
  },
  mounted () {
    this.errorName = '请输入' + this.firstLabelName + '名称'
    this.errorNameLimit = this.firstLabelName + '名称应为1~20位字符'
    customerService.getCustomerPushSetting({})
      .then(res => {
        this.disableSubscribeFlag = !res.result ? true : res.result.disableSubscribeFlag !== false
      })
  },
  methods: {
    validateGroupName (rule, value, callback) {
      if (!value || value.trim() === '') {
        callback(new Error(this.errorName))
      } else if (value.length > 20) {
        callback(new Error(this.errorNameLimit))
      } else {
        callback()
      }
    },
    localSaveCondition () {
      this.$refs.conditionForm.validate(isValid => {
        if (isValid) {
          if (this.saveCondition) {
            const promise = this.saveCondition({
              name: this.formModel.conditionName,
              selectedItems: this.selectedItems,
              defaultInd: this.formModel.setAsDefault ? 1 : 0,
              noticeFlag: this.formModel.setEmailPush ? 1 : 0
            })
            if (promise instanceof Promise) {
              promise.then(_ => {
                this.$promise.resolve({ name: this.formModel.conditionName })
              })
            }
            this.$modal.close()
          } else {
            customerService
              .saveCondition({
                name: this.formModel.conditionName,
                functionCd: this.functionCd,
                defaultInd: this.formModel.setAsDefault ? 1 : 0,
                noticeFlag: this.formModel.setEmailPush ? 1 : 0,
                condContent: this.conditions ? JSON.stringify(this.conditions) : JSON.stringify(this.selectedItems),
                showContent: this.showConditions ? JSON.stringify(this.showConditions) : JSON.stringify(this.selectedItems)
              })
              .then(data => {
                this.$message.success('保存成功')
                this.$modal.close()
                this.$promise.resolve({ name: this.formModel.conditionName })
              })
              .catch(() => {
              })
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../styles/common.scss';

.conditions-wrap {
  display: flex;
  flex-wrap: wrap;
}

.conditions {
  max-width: 250px;
  color: #128BED;
  border: 1px solid #128BED;
  margin: 0 10px 5px 0 !important;
  padding: 0 6px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  height: 22px;
  cursor: pointer;
  position: relative;
  line-height: 20px;
  border-radius: 2px;
  font-size: 12px;

  &:hover {
    background: #E5F2FD;
  }

  .remove-icon {
    position: absolute;
    right: 6px;
    top: 0;
    font-size: 10px;
    font-weight: 300;
    line-height: 20px;
  }
}

.form-wrap {
  ::v-deep {
    .el-form-item {
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .el-form-item__label {
      padding-right: 12px !important;
      color: #333;
    }
  }

  .condition-form-wrap {
    ::v-deep {
      .el-form-item__label {
        line-height: 22px;
        color: #333;
      }
    }
  }
  .condition-select{

    ::v-deep{
      .el-form-item__content{
        display: flex;
        align-items: center;
        height: 22px;
        > p {
          padding-left: 10px;
        }
        > p:nth-last-child(1) {
          color: #999999;
          font-size: 14px;
        }
        > p:nth-last-child(2) {
          color: #666666;
          font-size: 14px;
        }
      }
    }
  }
}

::v-deep {
  .popup-title {
    font-weight: bold;
  }

  .el-form-item__error {
    padding-top: 0;
  }
}

</style>
