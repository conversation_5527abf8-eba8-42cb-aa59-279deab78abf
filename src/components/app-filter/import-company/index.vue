<template>
  <div :class="['import-btn',hasAddCompanyFlag ? 'has-import-btn' : '',disable ? 'disable' : '']" @click="importClick">
    <i class="iconfont icon-xinzeng2" :class="['iconfont',hasAddCompanyFlag ? 'icon-daoruqiyeminglu1n' : 'icon-xinzeng2']"></i>
    <span class="import-btn-text">{{ hasAddCompanyFlag ? '已导入' : '导入企业名录' }}</span>
    <template v-if="hasAddCompanyFlag">
      <em class="num">{{ importResultList.length }}</em>
      <span class="import-btn-text">家企业</span>
      <span class="reset" @click.stop="resetImportClick()">重置</span>
    </template>
  </div>
</template>

<script>
import appAddImportCompany from '../../app-add-import-company'

export default {
  name: 'import-company',
  props: {
    functionTableId: { type: String, default: '' },
    disable: { type: Boolean, default: false }
  },
  data() {
    return {
      hasAddCompanyFlag: false,
      importResultList: []
    }
  },
  methods: {
    setVmodel(code) {
      this.hasAddCompanyFlag = true
      this.importResultList = code
    },
    importClick() {
      if (this.disable) {
        return
      }
      this.$uiService.showDialog(appAddImportCompany, {
        functionTableId: this.functionTableId,
        functionTableGroupsId: 'bene_id',
        defaultType: 'paste',
        removeItems: ['add'],
        uploadNotSource: true
      }).then(res => {
        res.resultList = res.resultList.filter(i => i.corpKeyNo)
        if (res && res.resultList && res.resultList.length > 0) {
          this.importResultList = res.resultList.filter(v => v.corpKeyNo) || []
          if (res.addCompanyFlag && this.importResultList.length) {
            this.hasAddCompanyFlag = res.addCompanyFlag
          }
          this.$emit('importSuccess', this.importResultList)
        }
      })
    },
    resetImportClick(disableReturn) {
      if (this.disable) {
        return
      }
      this.hasAddCompanyFlag = false
      this.importResultList = []
      if (!disableReturn) {
        this.$emit('resetCompany')
      }
    },
    clearData() {
      this.hasAddCompanyFlag = false
      this.importResultList = []
    }
  }

}
</script>
<style lang="scss" scoped src="../common.scss"></style>

<style scoped lang="scss">

</style>
