@import '../../styles/common.scss';

.system-conditions {
  position: relative;
  box-sizing: border-box;

  .top-control {
    display: flex;
    justify-content: space-between;
    padding: 0 0 13px;
    border-bottom: 1px solid #EEEEEE;
    margin-bottom: 15px;

    > p {
      font-size: 15px;
      line-height: 24px;
      color: #333333;
      font-weight: bold;
    }

    .control-line {
      margin: 0 10px;
      height: 14px;
      width: 1px;
      background: #E3E3E3;
    }

    .control-item-wrap {
      display: flex;
      align-items: center;
    }
  }

  .control-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0 5px;
    border-radius: 2px;

    > span {
      color: #128BED;
      font-size: 12px;
    }

    > p {
      padding-left: 4px;
      color: #333333;
      font-size: 14px;
    }

    &:hover {
      background: #F2F8FE;

      > p {
        color: #128BED;
      }
    }
  }

  .filter-conditions {
    overflow: hidden;
    padding: 15px;
    background: #FFFFFF;
    position: relative;
    border-radius: 0 0 4px 4px;

    .mask {
      position: absolute;
      left: 0;
      bottom: 0;
      background: linear-gradient(to top, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
      height: 50px;
      width: 100%;
      z-index: 2;
    }

    .option-item {
      width: 100%;
      display: flex;
      padding-bottom: 15px;

      &.selected-conditions {
        border-bottom: 1px solid #EEEEEE;
        margin-bottom: 15px;
      }

      .dr-wrap {
        padding-bottom: 15px;
        padding-right: 30px;
        height: 22px;
        box-sizing: unset;

        ::v-deep {
          .el-button--mini {
            font-size: 14px;
          }
        }
      }


      .left {
        font-size: 14px;
        color: #999999;
        line-height: 22px;
        width: 76px;
        padding-right: 10px;
      }

      .middle {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        overflow: hidden;

        .conditions {
          max-width: 250px;
          color: #128BED;
          border: 1px solid #128BED;
          margin: 0 10px 5px 0 !important;
          padding: 0 20px 0 6px;
          -webkit-box-sizing: border-box;
          box-sizing: border-box;
          height: 22px;
          cursor: pointer;
          position: relative;
          line-height: 20px;
          border-radius: 2px;
          font-size: 12px;

          &:hover {
            background: #E5F2FD;
          }

          .remove-icon {
            position: absolute;
            right: 6px;
            top: 0;
            font-size: 10px;
            font-weight: 300;
            line-height: 20px;
          }
        }

        label {
          padding-bottom: 15px;
        }
      }

      .right {
        cursor: pointer;
        color: #999999;
        font-size: 14px;
        line-height: 22px;
        display: flex;

        &:hover {
          color: #128bed;
        }

        span {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 22px;
        }

        i {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .control-item {
          display: flex;
          margin-left: 10px;
          padding: 0 5px;
          height: 22px;

          .iconfont {
            color: #128BED;
            font-size: 14px;
          }

          p {
            color: #128BED;
            padding-left: 5px;
          }

          &:hover {
            background: #F2F8FE;
          }
        }
      }
    }

    .key-input-wrap {
      display: flex;
      width: 650px;
      padding-bottom: 15px;
      justify-content: space-between;

      > div {
        display: flex;
      }

      ::v-deep {
        .el-input__inner {
          height: 40px;
          line-height: 40px;
          border-radius: 4px 0 0 4px;
          font-size: 16px;
        }

        .el-button {
          border-radius: 0 4px 4px 0;
          height: 40px;
          padding: 0 24px;
          font-size: 16px;
          line-height: 24px;
        }
      }

      .top-input-wrap {
        flex: 1;
        margin-right: 10px;
      }

      .key-input-button-orange {
        ::v-deep {
          .el-input__inner {
            border-right: 0;
          }

          .el-button {
            background: #FF722D;
            border: 1px solid #FF722D;

            &:hover {
              background: #E66322;
              border: 1px solid #E66322;
            }
          }
        }
      }
    }

  }

  .tixing {
    position: relative;

    > div {
      margin: 0 auto;
      width: 80px;
      height: 0;
      border-top: 22px solid #FFF;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      cursor: pointer;
    }

    i {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      font-size: 10px;
      color: #666666;
      pointer-events: none;
    }
  }

  .theme2 {
    padding: 0 0 15px 0;

    .key-input-wrap {
      padding: 15px;
      width: 100%;
      background: linear-gradient(180deg, #E0F1FF 0%, #FCFEFF 100%);
      border-radius: 4px 4px 0 0;

      ::v-deep {
        .el-input {
          flex: 1;
          max-width: 534px;
        }
      }
    }

    .option-item {
      padding: 0 15px 15px;
    }

    .line {
      margin: 0 15px 15px;
      height: 1px;
      background: #EEEEEE;
    }
  }
}

.app-filter-input-wrap {

  &.app-filter-input-pre-wrap {
    height: 40px;

    ::v-deep {
      .el-input__inner {
        border-radius: 0 !important;
        border-color: #EEEEEE;
        transition: none;
        border-left: 0;

        &:hover {
          border-color: #EEEEEE;
        }

        &:focus {
          border-color: #128BED;
        }
      }

      .el-input-group__prepend {
        overflow: hidden;
        padding: 0;
        border: 1px solid #EEEEEE;
        border-right: 0;

        &:hover {
          border-color: #128BED;

          .search-border {
            background: #128BED;
          }
        }
      }

      .d-wrap {
        margin-right: 0;
        height: 39px;
        margin-top: -1px;

        .line {
          height: unset;
          padding-right: 8px;
          background: #F3F7FD;

          i.jtxia {
            border-top: 4px dashed #999999;
          }

          i.jtshang {
            border-bottom: 4px dashed #999999;
          }

        }
      }

      .not-allowed {
        ::v-deep {
          .el-input-group__prepend {
            &:hover {
              border-color: #EEEEEE;

              .search-border {
                background: #EEEEEE;
              }
            }
          }
        }
      }

      .b-wrap {
        max-width: 150px;
        padding: 0 4px 0 8px;
        line-height: 40px;
        color: #666666 !important;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        background: #F3F7FD;
      }
    }

    &.focus-wrap {
      ::v-deep {
        .el-input-group__prepend {
          border-color: #128BED;
        }
      }
    }

    .disabled-wrap {
      ::v-deep {
        .b-wrap {
          color: #BBBBBB !important;
        }

        i.jtxia {
          border-top: 4px dashed #BBBBBB !important;
        }

        i.jtshang {
          border-bottom: 4px dashed #BBBBBB !important;
        }
      }

    }
  }

  .app-filter-input-dr {
    position: relative;

    .search-border {
      position: absolute;
      right: 0;
      top: 0;
      z-index: 9;
      width: 1px;
      height: 100%;
      background: #EEEEEE;
    }
  }
}
