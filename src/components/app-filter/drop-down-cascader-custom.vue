<template>
  <div class="cascader-wrap">
    <template v-for="(item,itemIndex) in option.children">
      <drop-down-numbers
        class="dr-number-wrap"
        v-show="!option.hide"
        :unit="option.unit"
        :topLimit="option.customNumberTopLimit"
        :enableChangeButtonText="option.enableChangeButtonText"
        :key="option.keyName + itemIndex"
        v-if="item.isSingleCustom && item.customType === 'number'"
        trigger="hover"
        title="自定义"
        :isCheckBox="true"
        @change="(value)=>{customNumberChange(value, option, item)}"
        :ref="'component-' + option.keyName + '-' + itemIndex"
        :style="{marginRight:'30px'}"
      ></drop-down-numbers>
      <drop-down-time-range
        class="time-range-wrap"
        v-show="!option.hide"
        :key="option.keyName + itemIndex + 'range'"
        v-else-if="item.isSingleCustom"
        trigger="hover"
        title="自定义"
        :isCheckBox="true"
        @change="(value)=>{customTimeChange(value, option, item)}"
        :ref="'component-' + option.keyName + '-' + itemIndex"
        :style="{marginRight:'30px',height:'22px'}"
        :enableChangeButtonText="option.enableChangeButtonText"
      ></drop-down-time-range>
      <drop-down-cascader
        v-show="!option.hide"
        :key="option.keyName + itemIndex + 'cascader'"
        v-else
        :style="{width:item.labelWidth || option.labelWidth}"
        class="dr-wrap"
        :class="[option.is26 ? 'dr-wrap-26' : '',((!item.children || item.children.length <= 0) && !firstCheckBox) ? 'dr-wrap-button-single' : '']"
        :appendToBody="appendToBody"
        :firstCheckBox="firstCheckBox"
        trigger="hover"
        :placement="option.placement"
        :disableCount="!option.enableCount"
        :ref="'component-' + option.keyName + '-' + itemIndex"
        :isLimit="false" :defaultText="item.name"
        :propMore="propMore" :options="item.children"
        @change="(selectMenus,cascaderModel,b,c,d,isAll)=>{dropDownSingleCascaderChange(selectMenus,item,option,isAll)}">
      </drop-down-cascader>
    </template>
  </div>
</template>

<script>
import dropDownCascader from './drop-dwon-cascader'
import dropDownTimeRange from './drop-down-time-range'
import dropDownNumbers from './drop-down-numbers'
import moment from 'moment'

export default {
  name: 'drop-down-cascader-custom',
  components: {
    dropDownCascader,
    dropDownTimeRange,
    dropDownNumbers
  },
  props: {
    option: {
      type: Object,
      defalut: () => {
        return {}
      }
    },
    labelWidth: { default: null }
  },
  computed: {
    firstCheckBox() {
      return this.option.firstCheckBox === undefined ? true : this.option.firstCheckBox
    },
    appendToBody() {
      return this.option.appendToBody === undefined ? true : this.option.appendToBody
    }
  },
  data () {
    return {
      propMore: {
        value: 'code',
        label: 'name',
        expandTrigger: 'hover',
        multiple: true
      },
      selectedItems: []
    }
  },
  methods: {
    setVmodel (selects) {
      this.selectedItems = selects
      let keyName = this.option.keyName
      this.option.children.forEach((item, index) => {
        let components = this.$refs['component-' + keyName + '-' + index][0]
        // 最外层item匹配
        let matchCondition = selects.find(i => {
          return i.code === item.code || (item.isSingleCustom && i.isSingleCustom)
        })
        if (matchCondition) {
          if (matchCondition.isSingleCustom) {
            components.setVmodel(matchCondition.code ? matchCondition.code.split('-') : []) // 最外层item匹配成功
          } else {
            components.setVmodel({ checkAll: true })
          }
        } else if (item.children && item.children.length > 0) { // pop内部匹配
          this.autoItemPopMatch(item.children, selects.filter(i => !i.isSingleCustom), components)
        } else if (components) {
          components.setVmodel([])
        }
      })
    },
    autoItemPopMatch (item, conditions, components) {
      let temp = []
      item.forEach(childrenSecond => {
        let matchCondition = conditions.find(i => {
          return i.code === childrenSecond.code
        })
        if (matchCondition) {
          if (childrenSecond.children && childrenSecond.children.length > 0) {
            childrenSecond.children.forEach(i => {
              if (i.children && i.children.length > 0) {
                i.children.forEach(j => {
                  temp.push([childrenSecond.code, i.code, j.code])
                })
              } else {
                temp.push([childrenSecond.code, i.code])
              }
            })
          } else {
            temp.push([childrenSecond.code])
          }
        } else if (childrenSecond.children && childrenSecond.children.length > 0) {
          childrenSecond.children.forEach(childrenThird => {
            let matchChildCondition = conditions.find(i => {
              return i.code === childrenThird.code
            })
            if (matchChildCondition) {
              if (childrenThird.children && childrenThird.children.length > 0) {
                if (!matchChildCondition.isLast) {
                  childrenThird.children.forEach(j => {
                    temp.push([childrenSecond.code, childrenThird.code, j.code])
                  })
                }
              } else {
                temp.push([childrenSecond.code, childrenThird.code])
              }
            }
            if (childrenThird.children && childrenThird.children.length > 0) {
              childrenThird.children.forEach(j => {
                let matchChildCondition = conditions.find(i => {
                  return i.code === j.code
                })
                if (matchChildCondition) {
                  temp.push([childrenSecond.code, childrenThird.code, j.code])
                }
              })
            }
          })
        }
        components.setVmodel(temp)
      })
    },
    customTimeChange (value, option, item) {
      this.selectedItems = this.selectedItems.filter(i => !i.isSingleCustom)
      if (value.length > 0) {
        this.selectedItems.push({
          code: value[0] + '-' + value[1],
          name: moment(value[0]).format('YYYY-MM-DD') + '至' + moment(value[1]).format('YYYY-MM-DD'),
          keyName: option.keyName,
          label: option.label,
          isSingleCustom: true
        })
      }
      this.$emit('change', this.selectedItems)
    },
    customNumberChange (value, option, item) {
      this.selectedItems = this.selectedItems.filter(i => !i.isSingleCustom)
      if (value) {
        this.selectedItems.push({
          code: value.code,
          name: value.name,
          keyName: option.keyName,
          label: option.label,
          isSingleCustom: true
        })
      }
      this.$emit('change', this.selectedItems)
    },
    clearSelects (item) {
      let allItems = [{ code: item.code }]
      if (item.children && item.children.length > 0) {
        item.children.forEach((i) => {
          allItems.push({ code: i.code })
          if (i.children && i.children.length > 0) {
            i.children.forEach((j) => {
              allItems.push({ code: j.code })
              if (j.children && j.children.length > 0) {
                j.children.forEach(z => {
                  allItems.push({ code: z.code })
                })
              }
            })
          }
        })
      }
      this.selectedItems = this.selectedItems.filter((j) => {
        let match = allItems.find((z) => {
          return z.code === j.code || j.isCustom
        })
        return !match
      })
    },
    dropDownSingleCascaderChange (selectMenus, item, option, includeFather) {
      this.clearSelects(item)
      let keyName = option.keyName
      if (selectMenus === true || includeFather) {
        this.selectedItems.push({
          code: item.code,
          name: item.name,
          keyName: keyName,
          label: option.label,
          level: item.level
        })
      } else if (Object.prototype.toString.call(selectMenus) === '[object Array]' && selectMenus.length > 0) {
        let result = []
        selectMenus.forEach((i) => {
          result.push({
            itemShowName: i.itemShowName,
            name: i.name,
            code: i.code,
            keyName: keyName,
            label: option.label,
            isCustom: i.isCustom,
            level: i.level,
            parentCode: item.code,
            isLast: i.isLast,
            newParams: item.newParams
          })
        })
        this.selectedItems.push(...result)
      }
      this.$emit('change', this.selectedItems)
    }
  }

}
</script>

<style scoped lang="scss">
.cascader-wrap {
  display: flex;
  flex-wrap: wrap;

}
.dr-number-wrap{
  ::v-deep {
    .pop-show-checkbox {
      background: none;
    }

    .custom-item .custom-item-checkbox .el-checkbox__label {
      padding-left: 5px;
    }
  }
}
.dr-wrap {
  padding-bottom: 15px;
  padding-right: 30px;
  height: 22px;
  box-sizing: unset;
}
.dr-wrap-26{
  height: 26px;

  ::v-deep{
    .el-checkbox-button__inner,.el-checkbox-button__inner span {
      height: 26px;
      line-height: 26px;
    }
  }
}
.dr-wrap-button-single{
  padding: 0 !important;

  ::v-deep{
    .el-checkbox-button.is-checked:first-child .el-checkbox-button__inner {
      box-shadow: -1px 0 0 0 #71b9f4 !important;
    }
  }
}
</style>
