import moment from 'moment'

export const translateConditionZTB = (functionCd, resultList, options) => {
  resultList.forEach(i => {
    let result = []
    let conditions = JSON.parse(i.showContent)
    if (functionCd === 'srch_expan_tender_cust') { // 招投标
      // 兼容老的保存的数据处理
      if (Object.prototype.toString.call(conditions) === '[object Object]') {
        if (conditions && conditions.area) {
          let option = options.find(m => m.keyName === 'region')
          conditions.area.forEach(m => {
            option.children.forEach(f => {
              if (f.code === String(m[0])) {
                f.children.forEach(s => {
                  if (m.length === 2) {
                    if (String(s.code) === String(m[1])) {
                      result.push({ keyName: option.keyName, label: option.label, ...s })
                    }
                  } else if (m.length === 3) {
                    if (s.code + '00' === String(m[1])) {
                      s.children.forEach(t => {
                        let code = String(t.code)
                        if (code.length < 6) {
                          code += '00'
                        }
                        if (code === String(m[2])) {
                          result.push({ keyName: option.keyName, label: option.label, ...t })
                        }
                      })
                    }
                  }
                })
              }
            })
          })
        }
        // 搜索范围
        if (conditions && conditions.ssfwData && conditions.ssfwData.model && conditions.ssfwData.model.length > 0) {
          let option = options.find(m => m.keyName === 'searchFields')
          conditions.ssfwData.model.forEach(m => {
            option.children.forEach(t => {
              if (t.code === m) {
                result.push({ keyName: option.keyName, label: option.label, ...t })
              }
            })
          })
        }
        // 业主类型
        if (conditions && conditions.zbOwerData && conditions.zbOwerData.model && conditions.zbOwerData.model.length > 0) {
          let option = options.find(m => m.keyName === 'ownertype')
          conditions.zbOwerData.model.forEach(m => {
            option.children.forEach(t => {
              if (t.code === m) {
                result.push({ keyName: option.keyName, label: option.label, ...t })
              }
            })
          })
        }
        // 发布日期
        if (conditions && conditions.zbYearData && conditions.zbYearData.model && conditions.zbYearData.model.length > 0) {
          let option = options.find(m => m.keyName === 'publishdate')
          let maxYear = 0
          let lastValue = null
          conditions.zbYearData.model.forEach(m => {
            option.children.forEach(t => {
              if (t.code === m) {
                if (Number(t.code) >= maxYear) {
                  maxYear = Number(t.code)
                  lastValue = t
                }
              }
            })
          })
          if (lastValue) {
            result.push({ keyName: option.keyName, label: option.label, ...lastValue })
          }
        }
        if (conditions && conditions.zbYearData && conditions.zbYearData.customer && conditions.zbYearData.customer.code) {
          let option = options.find(m => m.keyName === 'publishdate')
          let arr = conditions.zbYearData.customer.code.split('至')
          arr[0] = moment(arr[0]).format('YYYYMMDD')
          arr[1] = moment(arr[1]).format('YYYYMMDD')
          result.push({ keyName: option.keyName, label: option.label, code: arr.join('-'), name: conditions.zbYearData.customer.name, isCustom: true })
        }
        // 预算金额
        if (conditions && conditions.zbBudgetData && conditions.zbBudgetData.model && conditions.zbBudgetData.model.length > 0) {
          let option = options.find(m => m.keyName === 'budgetvalue')
          conditions.zbBudgetData.model.forEach(m => {
            option.children.forEach(t => {
              if (t.code === m) {
                result.push({ keyName: option.keyName, label: option.label, ...t })
              }
            })
          })
        }
        if (conditions && conditions.zbBudgetData && conditions.zbBudgetData.customer && conditions.zbBudgetData.customer.code) {
          let option = options.find(m => m.keyName === 'budgetvalue')
          result.push({ keyName: option.keyName, label: option.label, code: conditions.zbBudgetData.customer.code.replace(/万/g, ''), name: conditions.zbBudgetData.customer.name, isSingleCustom: true })
        }
        // 行业分类
        if (conditions && conditions.zbProjectData && conditions.zbProjectData.model && conditions.zbProjectData.model.length > 0) {
          let option = options.find(m => m.keyName === 'industryv2')
          conditions.zbProjectData.model.forEach(m => {
            option.children.forEach(t => {
              if (t.code === m) {
                result.push({ keyName: option.keyName, label: option.label, ...t })
              }
            })
          })
        }
        // 信息类型
        if (conditions && conditions.zbProgressData && conditions.zbProgressData.length > 0) {
          let option = options.find(m => m.keyName === 'ifbprogress')
          conditions.zbProgressData.forEach(m => {
            let match = option.children.find(t => t.code === m)
            if (match) {
              result.push({ keyName: option.keyName, label: option.label, ...match })
            }
          })
          conditions.zbProgressData.forEach(m => {
            option.children.forEach(z => {
              if (z.children && z.children.length > 0) {
                let match = z.children.filter(i => i.name !== '不限').find(t => t.code === m)
                if (match) {
                  result.push({ keyName: option.keyName, label: option.label, ...z })
                  result.push({ keyName: 'ifbprogress-second', label: '二级进度', ...match })
                }
              }
            })
          })
        }
        i.showContent = JSON.stringify(result)
      }
    }
  })
  return resultList
}
