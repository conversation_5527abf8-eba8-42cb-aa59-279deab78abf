<template>
  <div
    class="checkBox"
    :class="[
      {'drop-down-button': theme === 'button'},
      {'drop-down-button--selected': theme === 'button' && selectMenus.length > 0},
      {'drop-down-button--text-auto-width': textAutoWidth},
    ]"
  >
    <div class="checkBox-title" :class="titleClass" v-if="title.length">{{ title }}{{ noAppendTitleColon ? '' : '：' }}
    </div>
    <div class="checkBox-container" v-show="!hideItems">

      <el-checkbox :class="['appear-button', getButtonClass()]" v-if="firstCheckBox && options && options.length <= 0"
        :indeterminate="isIndeterminate" v-model="checkAll" @change="changeCheck()"><span>{{ showText
        }}</span></el-checkbox>
      <el-checkbox-button :class="['appear-button', 'appear-button-single', getButtonClass()]"
        v-else-if="options && options.length <= 0" :indeterminate="isIndeterminate" v-model="checkAll"
        @change="changeCheck()"><span>{{ showText }}</span></el-checkbox-button>

      <el-popover :open-delay="100" :close-delay="150" :transition="transition" :append-to-body="appendToBody"
        ref="popoverRef" v-else :placement="placement" :trigger="trigger" :popper-class="popoverClass"
        @show="handleShowDropdown" @hide="handleHideDropdown">
        <div>
          <el-cascader-panel class="morePopover dropdown-item-content" ref='cascader' v-model='cascaderModel'
            :options="options" :props='propMore' @change='cascaderChange'
            @expand-change="checkNodeTestColor"></el-cascader-panel>
          <slot name="dropdown-suffix"></slot>
        </div>
        <template slot="reference">
          <slot name="reference" :scopeData="scopeData">
            <el-checkbox :class="['appear-button', getButtonClass()]" v-if="firstCheckBox"
                         :indeterminate="isIndeterminate" v-model="checkAll" @change="changeCheck()">
              <span>{{ showText }}</span><span class="caret"></span>
            </el-checkbox>
            <el-button :class="['appear-button', getButtonClass()]" v-else size='mini' class="dd-button-all"
                       type="text">
              <span>{{ showText }}</span>
              <span class="drop-down-button__text-count" v-if="selectedCountVisible">{{ selectedCount }}</span>
              <span class="caret"></span>
              <i
                class="drop-down-button__reset-icon iconfont icon-shanchu3 font12"
                v-if="theme === 'button'"
                @click="handleReset"
              />
            </el-button>
          </slot>
        </template>
      </el-popover>
    </div>
  </div>
</template>

<script>
export default {
  name: 'dropdownItem',
  props: {
    theme: {
      type: String,
      default: 'text'
    },
    canFirstCount: { type: Boolean, default: false },
    placement: { type: String, default: 'bottom-start' },
    transition: { type: String, default: 'fade-in-linear' },
    uniqueKey: { type: String, default: '' },
    title: { type: String, default: '' },
    noAppendTitleColon: { type: Boolean, default: false },
    titleClass: { type: String, default: '' },
    options: { type: Array, default: () => [] },
    defaultText: String,
    isLimit: { type: Boolean, default: true },
    appendToBody: { default: true, type: Boolean },
    vmodel: {
      type: Array,
      default() {
        return []
      }
    },
    propMore: {
      type: Object,
      default() {
        return {
          expandTrigger: 'hover',
          multiple: true
        }
      }
    },
    showNormal: { type: Boolean, default: false },
    hideItems: { type: Boolean, default: false },
    limitMessage: { type: String, default: '' },
    trigger: { type: String, default: 'click' },
    disableCount: { type: Boolean, default: false },
    firstCheckBox: { type: Boolean, default: false },
    bindComponents: { default: null },
    popoverClass: {
      type: String,
      default: 'opopover dr-down-margin0'
    },
    reservedNodeSubset: {
      type: Boolean,
      default: false
    },
    selectedTextFunc: {
      type: Function
    },
    selectedCountVisible: {
      type: Boolean,
      default: true
    },
    textAutoWidth: {
      type: Boolean,
      default: true
    }
  },
  data: function () {
    this.lastSelections = []
    return {
      selectedCount: 1,
      selectMenus: [],
      isSelected: false,
      cascaderModel: [],
      dropdownMenuVisible: false,
      checkAll: false
    }
  },
  computed: {
    scopeData() {
      return {
        showText: this.showText,
        selectedCount: this.selectedCount,
        dropdownMenuVisible: this.dropdownMenuVisible
      }
    },
    isIndeterminate() {
      return this.cascaderModel.length !== this.getTotal() && this.cascaderModel.length > 0
    },
    showText: function () {
      if ((this.showNormal && !this._isEmpty(this.cascaderModel)) || !this._isEmpty(this.cascaderModel)) {
        this.isSelected = true
        let scount = this.selectedCount
        if (this.checkAll && this.canFirstCount) {
          scount = this.setAllFirstCount(this.cascaderModel)
          if (!scount) {
            scount = this.selectedCount
          }
        }
        if (this.selectedTextFunc) {
          return this.selectedTextFunc(this.selectMenus) || this.defaultText
        } else {
          return `${this.defaultText} ${this.disableCount ? '' : scount}`
        }
      } else {
        this.isSelected = false
        return this.defaultText
      }
    }
  },
  mounted() {
    this.$emit('update:bindComponents', this)
  },
  methods: {
    handleReset () {
      this.setVmodel([])
      this.$emit('reset')
      this.$emit('change', [])
    },
    setAllFirstCount(list) {
      let cF = []
      list.forEach(li => {
        if (li[0] && !cF.find(e => e === li[0])) {
          cF.push(li[0])
        }
      })
      return cF.length
    },
    singleClick() {
      this.checkAll = !this.checkAll
      this.changeCheck()
    },
    changeCheck(isNoBack) {
      if (this.options && this.options.length <= 0) {
        if (!isNoBack) {
          this.$emit('change', this.checkAll)
        }
      } else {
        if (this.checkAll) {
          this.cascaderModel = []
          this.options.forEach((i) => {
            if (i.children && i.children.length > 0) {
              i.children.forEach((j) => {
                this.cascaderModel.push([i.code, j.code])
              })
            } else {
              this.cascaderModel.push([i.code])
            }
          })
          if (!isNoBack) {
            this.$emit('change', this.checkAll)
          }
        } else {
          this.cascaderModel = []
          if (!isNoBack) {
            this.$emit('change', this.checkAll)
          }
        }
      }
      this.resetPopPosTop()
    },
    getTotal() {
      let num = 0
      this.options.forEach((i) => {
        if (i.children && i.children.length > 0) {
          i.children.forEach((j) => {
            num++
          })
        } else {
          num++
        }
      })
      return num
    },
    getButtonClass() {
      if (this.dropdownMenuVisible) {
        return `dd-button2 item-expand ${this.trigger}-trigger`
      } else if (this.isSelected) {
        return `dd-button2 ${this.trigger}-trigger`
      } else {
        return `dd-button ${this.trigger}-trigger`
      }
    },
    refresh() {
      this.cascaderChange(null, true)
    },

    checkIsAll(conditions) {
      let num = 0
      let diguiCheck = (i) => {
        if (i.children && i.children.length > 0) {
          i.children.forEach(z => {
            diguiCheck(z)
          })
        } else {
          num += 1
        }
      }
      this.options.forEach(i => {
        diguiCheck(i)
      })
      return num === conditions.length
    },
    setVmodel(vmodel, close = true) {
      if ((vmodel && vmodel.checkAll) || (vmodel && vmodel.length === 1 && vmodel[0].isCheckedAll)) {
        // 特殊情况，恢复全部数据的时候
        this.checkAll = true
        this.changeCheck(true)
      } else {
        if (vmodel.length !== 0 && this.checkIsAll(vmodel)) {
          this.checkAll = true
          this.changeCheck(true)
          return
        }
        this.cascaderModel = vmodel
        this.$nextTick(() => {
          this.cascaderChange(this.cascaderModel, null, true)
        })
      }
      if (close) {
        this.$refs?.popoverRef?.doClose()
      }
    },
    reset(vmodel) {
      this.cascaderModel = vmodel
    },
    reValue(p, c) {
      this.cascaderModel = this.getAreaArray(p, c)

      this.$nextTick(() => {
        this.cascaderChange({}, true)
      })
    },
    nodeSelected: function (nodes) {
      if (nodes && nodes.length > 0) {
        nodes.forEach((node) => {
          if (node.checked) {
            // 暂时5级
            let parent = null
            let sparent = null
            if (node.parent) {
              if (node.parent.parent) {
                if (node.parent.parent.parent) {
                  if (node.parent.parent.parent.parent) {
                    parent = node.parent.parent.parent.parent
                  } else {
                    parent = node.parent.parent.parent
                  }
                } else {
                  parent = node.parent.parent
                }
              } else {
                parent = node.parent
              }
            }
            if (parent) {
              sparent = { label: parent.label, value: parent.value }
            }
            let result = { label: node.label, value: node.value, parent: sparent, ...node.data }
            if (!this.reservedNodeSubset) {
              delete result.children
            }
            this.selectMenus.push(result)
          } else {
            if (node.indeterminate && node.hasChildren) {
              this.nodeSelected(node.children)
            }
          }
        })
      }
    },
    checkNodeTestColor() {
      // 级联选择器组件：由于indeterminate样式在里层，通过js控制改父级兄弟元素的文字样式
      this.$nextTick(() => {
        let elements = this.$refs.cascader.$el.getElementsByClassName('el-cascader-node')
        let arr = Array.from(elements)
        arr.forEach((item) => {
          if (item.children[0].children[0].getAttribute('class').includes('is-indeterminate')) {
            item.children[1].style.color = '#128BED'
          } else {
            item.children[1].style = ''
            // item.children[1].removeAttr('style')
          }
        })
        let posLeft = this.$refs.cascader.$el.getBoundingClientRect().left
        let parentWrapEl = this.$refs.cascader.$el.parentElement.parentElement
        if ((document.body.offsetWidth - posLeft) <= parentWrapEl.offsetWidth) {
          parentWrapEl.style.left = document.body.offsetWidth - parentWrapEl.offsetWidth + 'px'
        }
      })
    },
    cascaderChange: function (params, refresh, noback) {
      if (!this.$refs.cascader) {
        this.checkAll = params?.length > 0
        if (!noback) {
          this.$emit('change', this.checkAll)
        }
        return
      }
      if (this.isLimit && this.setLimit()) {
        let warning = this.limitMessage || '每项最多选择5个条件'
        this.$message.warning(warning)
        this.$nextTick(() => {
          this.$refs.cascader.menus.splice(1, this.$refs.cascader.menus.length - 1)
        })
        return
      }
      this.selectMenus = []
      if (this.$refs.cascader.menus && this.$refs.cascader.menus.length > 0) {
        this.nodeSelected(this.$refs.cascader.menus[0])
      }
      this.selectedCount = this.selectMenus.length
      this.lastSelections = this.cascaderModel.join(',')
      this.checkAll = this.cascaderModel.length === this.getTotal()

      if (!noback) {
        this.$emit('change', this.selectMenus, this.cascaderModel, this.title, refresh, this.uniqueKey, this.checkAll)
        this.resetPopPosTop()
      }
      this.checkNodeTestColor()
    },
    resetPopPosTop() {
      this.$nextTick(() => {
        if (this.$refs.popoverRef) {
          this.$refs.popoverRef.updatePopper()
        }
      })
    },
    countRefresh() {
      this.selectMenus = []
      if (this.$refs.cascader.menus && this.$refs.cascader.menus.length > 0) {
        this.nodeSelected(this.$refs.cascader.menus[0])
      }
      this.selectedCount = this.selectMenus.length
    },
    setLimit() {
      let isMax5 = false
      let top5 = []
      this.cascaderModel.forEach((e) => {
        let index = top5.findIndex(x => x === e[0])
        if (index < 0) {
          top5.push(e[0])
        }
      })


      if (top5.length > 5) {
        isMax5 = true
        this.cascaderModel = this.cascaderModel.filter(v => this.lastSelections.includes(v))
      }


      return isMax5
    },
    getAreaArray: function (province, cityORarea) {
      let areaOptionTemp = this.options
      let allData = []
      if (province) {
        let pros = province.split(',')
        let prosDis = []
        pros.forEach(element => {
          if (prosDis.findIndex(e => e === element) < 0 && element) {
            prosDis.push(element)
          }
        })
        prosDis.forEach(eleP => {
          let currentPro = areaOptionTemp.find(e => e.value + '' === eleP + '')
          if (currentPro && currentPro.children && currentPro.children.length > 0) {
            currentPro.children.forEach(childP => {
              if (childP && childP.children && childP.children.length > 0) {
                childP.children.forEach(subChildP => {
                  allData.push([eleP, childP.value, subChildP.value])
                })
              } else {
                allData.push([eleP, childP.value])
              }
            })
          }
        })
      }

      if (cityORarea) {
        let cas = cityORarea.split(',')
        let areas = []
        cas.forEach(element => {
          if (areas.findIndex(e => e === element) < 0 && element) {
            areas.push(element)
          }
        })

        areaOptionTemp.forEach(p => { /// 省
          if (p && p.children && p.children.length > 0) {
            p.children.forEach(c => { /// 市
              if (areas.findIndex(a => a + '' === c.value + '') >= 0) {
                if (c.children && c.children.length > 0) {
                  c.children.forEach(d => {
                    allData.push([p.value, c.value, d.value])
                  })
                } else {
                  allData.push([p.value, c.value])
                }
              }
              if (c.children && c.children.length > 0) {
                c.children.forEach(dd => { /// 区
                  if (areas.findIndex(a => a + '' === dd.value + '') >= 0) {
                    allData.push([p.value, c.value, dd.value])
                  }
                })
              }
            })
          }
        })
      }
      return allData
    },
    handleShowDropdown() {
      this.dropdownMenuVisible = true
      this.resetPopPosTop()
    },
    handleHideDropdown() {
      this.dropdownMenuVisible = false
    }
  }
}

</script>

<style lang="scss" scoped>
.dd-more-container {
  display: flex;
}

.dd-button-all {
  height: 100%;

  ::v-deep {
    &>span {
      display: flex;
      align-items: center;

      span:first-child {
        margin-right: 5px;
        line-height: 22px;
      }
    }
  }
}

.dd-button {
  color: #333333;
  font-size: 12px;
  padding: 7px 0
}

.dd-button2 {
  padding: 7px 0;
  font-size: 12px;

  .caret {
    border-top-color: #128BED;
  }
}

.fontsize {
  font-size: 12px;
}

.count-title {
  line-height: 26px;
}

.customer-button {
  border-width: 0px;
}

.customer-button:hover,
.customer-button:focus {
  background-color: #ffffff;
}

.checkBox {
  display: flex;
}

.checkBox-title {
  text-align: right;
  font-size: 12px;
  color: #999999;
  vertical-align: middle;
  line-height: 26px;
  // padding-top: 2px;
  margin: 0px 20px 0px 10px;
}

.checkBox-container {
  display: flex;
  align-items: center;
  position: relative;

  span {
    display: block;
    height: 100%;
  }

  >span {
    display: flex;
    align-items: center;
  }


  ::v-deep {
    .el-popover__reference-wrapper {
      display: flex;
      align-items: center;
      height: 100%;
    }

    .el-checkbox {
      display: flex;
      align-items: center;
      height: 100%;
    }

    .el-checkbox__input {
      display: flex;
      height: 100%;
      align-items: center;
    }

    .el-checkbox__label {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0;

      span {
        line-height: 100%;
        height: unset;
        margin-left: 5px;
      }
    }

    .popover-content.placement-top-start {
      margin: 0;
    }
  }
}


.confirm {
  text-align: center;
  padding-top: 20px;
}

.timer-pop {
  width: 1px;
  height: 1px;
  margin-left: -65px;
  margin-top: 18px;
  background-color: transparent;
}

.poptimer-range {
  display: flex;

}

.btn-pop-all {
  padding-top: 8px;
}


.appear-button {
  padding: 0;
  font-weight: normal;
  border: 0;
  height: 100%;
  line-height: unset;

  &.hover-trigger:hover {
    color: #128bed;

    .caret {
      border-top-color: #128BED;
      transform: rotate(180deg);
    }
  }

  &.item-expand {
    color: #128BED;

    .caret {
      border-top-color: #128BED;
      transform: rotate(180deg);
    }

    ::v-deep {
      .el-checkbox__inner {
        border: 1px solid #128BED;
      }
    }
  }
}

.appear-button-single {
  ::v-deep {
    .el-checkbox-button__inner {
      height: 22px;
      padding-top: 0;
      padding-bottom: 0;
      line-height: 22px;

      span {
        color: #333333;
      }
    }
  }

  &.is-checked {
    ::v-deep {
      .el-checkbox-button__inner {
        span {
          color: #FFFFFF;
        }
      }
    }
  }
}

.caret {
  display: inline-block;
  width: 0;
  height: 0;
  vertical-align: middle;
  border-top: 4px dashed #666666;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
  transition: transform 0.15s linear;
}

.drop-down-button {
  .el-button {
    border: 1px solid #D8D8D8;

    &:hover {
      border-color: #128bed;
      color: #128bed;
    }

    .caret {
      border-top-color: inherit;
    }

    > span span:first-child {
      display: inline-block;
      max-width: 90px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .dd-button2 {
    border-color: #128bed;
    color: #128bed;
  }

  &--selected {
    .el-button {
      border-color: #128bed;
      color: #128bed;
    }

    .drop-down-button__text-count {
      display: inline-flex !important;
    }

    &:hover {
      .drop-down-button__reset-icon {
        display: inline-flex;
      }

      .caret {
        display: none;
      }
    }
  }

  &__reset-icon {
    display: none;
    margin-left: -4px;
    position: relative;
    right: -3px;
    color: #88c5f6;

    &:hover {
      color: #128bed;
    }
  }

  &__text-count {
    margin-right: 5px;
    display: none !important;
  }

  &--text-auto-width {
    .el-button {
      > span span:first-child {
        max-width: none;
      }
    }
  }
}
</style>

<style lang="scss">
.morePopover.dropdown-item-content {
  .el-icon-arrow-right {
    color: #999;
  }

  .el-cascader-menu__wrap {
    height: auto !important;
    max-height: 300px !important;

    &.el-scrollbar__wrap {
      margin-bottom: 0 !important;
      margin-right: 0 !important;
      overflow: auto;

      & ~ .el-scrollbar__bar {
        display: none;
      }

      & ~ .el-scrollbar__bar {
        display: none;
      }
    }
  }


  .el-cascader-menu__list {
    min-height: auto !important;
  }

  .el-checkbox {
    position: absolute;
    width: calc(100% - 10px);
    left: 10px;
  }

  .el-cascader-node__label {
    margin-left: 10px;
    padding: 0 15px 0 10px;
  }

}
</style>
