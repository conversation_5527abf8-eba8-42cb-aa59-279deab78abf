<template>
  <base-popup ref="pop" @outerclick="onBgClick" :transbg="_transbg" :needNoTransparent="needNoTransparent" v-if="control.show" :zIndex="zIndex">
    <transition :name="_transitionName">
      <div v-if="showContent" class="_popup_contentWrapper" :class="[options.contentWrapperClass||'', {'full-width': _isFullWidth}]" :style="options.contentWrapperStyle||''">
        <div class='right_close' @click="close"><i class='iconfont icon-icon_guanbixx' /></div>
        <slot />
      </div>
    </transition>
  </base-popup>
</template>

<script>
  import basePopup from '../app-popup/base-popup'
  import { removePopup } from '../app-popup/utils'
  import getScrollbarWidth from 'element-ui/src/utils/scrollbar-width'

  export default {
    name: 'app-slidepop',
    components: { basePopup },
    props: {
      control: { type: Object, required: true }, // 默认传入变量{show:false}，弹窗实例将自己的 引用设置在改对象中，在其父类直接调用内部的方法  例：this.control.ref.close();
      zIndex: { default: 999 },
      options: {
        default: () => {
          return {
            contentWrapperStyle: '',
            contentWrapperClass: ''
          }
        }
      },
      disableBodyScroll: {
        default: false,
        type: Boolean
      },
      outerClick: {}, // 阴影部分的点击事件
      transbg: { default: false, type: Boolean }, // 是否全透明
      needNoTransparent: { default: false, type: Boolean }, // 是否需要不透明
      fullWidth: { default: false, type: Boolean } // 是否宽度全屏,目前仅华为嵌入需要值 true
    },
    data() {
      return {
        showContent: false
      }
    },
    computed: {
      _transitionName() {
        return this.fullWidth ? 'linear-in' : 'slideRight'
      },
      _isFullWidth() {
        return this.fullWidth || false
      },
      _transbg() {
        return this.transbg || this._isFullWidth
      }
    },
    watch: {
      'control.show'(val) {
        if (val) {
          this.contentStateChange()
        }
      }
    },
    methods: {
      onBgClick: function() {
        if (this.outerClick) {
          this.outerClick()
        } else {
          this.close()
        }
      },
      close: function() {
        if (this.fullWidth) {
          window.onSysMessageSend && window.onSysMessageSend('popup-closed')
        }
        this.showContent = false
        if (this.$refs.pop) {
          removePopup(this.$refs.pop.idKey)
          this.$refs.pop.close(() => {
            if (this.control && this.control.show !== undefined) {
              this.control.show = false
            }
            if (this.$parent && this.$parent.closeCall) {
              this.$parent.closeCall()
            }
            this.control.ref = undefined
            this.$emit('closed')
          }, this)
        } else {
          if (this.control && this.control.show !== undefined) {
            this.control.show = false
          }
          if (this.$parent && this.$parent.closeCall) {
              this.$parent.closeCall()
            }
          this.control.ref = undefined
          this.$emit('closed')
        }
        if (this.disableBodyScroll) {
          document.body.style.overflow = ''
          if (document.documentElement.scrollHeight > window.innerHeight) {
            document.body.style.paddingRight = ''
          }
        }
      },
      contentStateChange() {
        if (this.control.show) {
          setTimeout(() => {
            this.showContent = true
          }, 200)
        }
      },
      closePop(data) {
        if (data.data === 'close-popup') {
          this.close()
        }
      }
    },
    beforeMount() {
      if (this.disableBodyScroll) {
        document.body.style.overflow = 'hidden'
        if (document.documentElement.scrollHeight > window.innerHeight) {
          document.body.style.paddingRight = `${getScrollbarWidth()}px`
        }
      }
    },
    created() {
      if (__PLUGIN__ && this.fullWidth) {
        window.onSysMessageSend && window.onSysMessageSend('popup-opened')
        window.addEventListener('message', this.closePop, false)
      }
    },
    mounted() {
      this.contentStateChange()
      if (this.control) {
        this.control.ref = this
      }
      window.AppRuntimeContext.eventBus.$on(window.AppRuntimeContext.eventBusEvents.CLOSE_ALL_POPUP, () => {
        this.close()
      })
    },
    beforeDestroy() {
      window.removeEventListener('message', this.closePop)
    }
  }

</script>

<style lang="scss" scoped src="./my-slidepop.scss"></style>
