@import "../../styles/common";

._popup_contentWrapper {
  width: 78%;
  max-width: 1360px;
  min-width: 1017px;
  height: 100%;
  right: 0;
  top: 0;
  position: fixed;
  box-sizing: border-box;
  background: white;
  &.full-width {
    width: 100%;
    max-width: 100%;
  }

  .right_close {
    display: inline-block;
    position: absolute;
    right: 12px;
    top: 20px;
    font-size: 18px;
    color: #bbb;
    cursor: pointer;
    z-index: 10;
    line-height: 18px;

    &:hover {
      color: #128BED;
    }

    .iconfont {
      font-size: 18px;
    }
  }
}

.slideRight-enter-active {
  animation: slideRight 0.3s;
}

.slideRight-leave-active {
  animation: slideRightclose 0.3s;
}

@keyframes slideRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  25% {
    transform: translateX(75%);
    opacity: 0.25;
  }

  50% {
    transform: translateX(50%);
    opacity: 0.5;
  }

  75% {
    transform: translateX(25%);
    opacity: 0.75;
  }

  to {
    transform: translateX(0%);
    opacity: 1;
  }
}

@keyframes slideRightclose {
  from {
    transform: translateX(0%);
    opacity: 1;
  }

  25% {
    transform: translateX(25%);
    opacity: 0.75;
  }

  50% {
    transform: translateX(50%);
    opacity: 0.5;
  }

  75% {
    transform: translateX(75%);
    opacity: 0.25;
  }

  to {
    transform: translateX(100%);
    opacity: 0;
  }
}


.linear-in-enter-active {
  animation: linearIn 0.15s;
}

.linear-in-leave-active {
  animation: linearOut 0.15s;
}

@keyframes linearIn {
  from {
    opacity: 0;
  }

  25% {
    opacity: 0.25;
  }

  50% {
    opacity: 0.5;
  }

  75% {
    opacity: 0.75;
  }

  to {
    opacity: 1;
  }
}

@keyframes linearOut {
  from {
    opacity: 1;
  }

  25% {
    opacity: 0.75;
  }

  50% {
    opacity: 0.5;
  }

  75% {
    opacity: 0.25;
  }

  to {
    opacity: 0;
  }
}
