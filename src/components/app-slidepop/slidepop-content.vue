<template>
  <div class="slidepop-content">
    <div class="popheader flex flex-aligin-center" :class="headerclass" :style="getPopeaderStyle">
      <slot name="popheader"></slot>
      <slot name="hr">
        <div class="header-right flex flex-aligin-center">
          <slot name="hrcontent"></slot>
        </div>
      </slot>
    </div>
    <div class="popcontent" ref="popcontent" :class="contentclass" :style="getPopcontentStyle">
      <app-scrollbar
        ref="app-scrollbar"
        :height="scrollbarHeight"
        :max-height="scrollbarMaxHeight"
        v-if="scrollbar"
        verticalScrollbarRight="-10px"
        resize
      >
        <slot></slot>
      </app-scrollbar>
      <slot v-else></slot>
    </div>
  </div>

</template>

<script>
  export default {
    name: 'app-slidepopcontent',
    props: {
      headerheight: {default: '56px', type: String},
      headerclass: {default: '', type: String},
      headerStyle: {type: Object, default: () => {}},
      contentclass: {default: '', type: String},
      contentStyle: {default: () => {}, type: Object},
      /**
       * 是否开启自定义滚动条
       */
      scrollbar: {
        type: Boolean,
        default: false
      },
      /**
       * 自定义滚动条高度
       */
      scrollbarHeight: {
        type: String,
        default: '100%'
      },
      /**
       * 自定义滚动条最大高度
       */
      scrollbarMaxHeight: {
        type: String
      }
    },
    computed: {
      getPopeaderStyle () {
        let obj = {height: this.headerheight}
        Object.assign(obj, this.headerStyle)
        return obj
      },
      getPopcontentStyle () {
        let obj = {height: `calc(100% - ${this.headerheight})`}
        Object.assign(obj, this.contentStyle)
        return obj
      }
    },
    methods: {
      getHeight () {
        return this.$refs.popcontent.offsetHeight
      }
    }

  }
</script>

<style scoped lang="scss">
  $background-grey-color: #F9F9FB;

  .slidepop-content {
    height: 100%;
    width: 100%;
    overflow: auto;

  }

  .popheader {
    position: relative;
    padding: 16px 15px;
    box-sizing: border-box;

    .title {
      font-size: 16px;
      font-weight: 500;
    }

    .header-right {
      position: absolute;
      right: 45px;
      top: 0;
      height: 100%;
    }
  }

  .popcontent {
    border-top: 1px solid #EEEEEE;
    box-sizing: border-box;
    height: calc(100% - 68px);
    padding: 15px;
    overflow: auto;
  }
</style>
