/**
 * 目前只支持1个单选
 * 待完善： 支持多选，支持多级等
 */
import filterlistCheckbox from '../app-popover-select/filterlist-checkbox/index.vue'
import { modelMixin } from '../utils'
export default {
  mixins: [modelMixin],
  props: {
    title: { default: '主题', type: String },
    width: {
      default: 'auto',
      type: String
    },
    list: {
      default: () => {},
      type: [Array, Object]// object用于 mode=multiple 多选
    },
    defaultArray: {
      default: () => ([
        undefined,
        '',
        null
      ]),
      type: Array
    },
    size: { default: 'mini', type: String },
    mode: { default: 'single', type: String }, // single 单选 multiple 多选
    border: { default: false, type: Boolean }, // 是否拥有边框 padding: 0 12px; height:32px
    borderSelectedActived: { default: false, type: Boolean },
    useFilterInput: { default: false, type: Boolean }, // 是否需要过滤输入框
    multipleShowSelectedText: { default: false, type: Boolean }, // 是否在多选模式下，选中项显示选中的文本
    inputPlaceholder: { default: '请输入', type: String },
    options: {
      type: Object,
      default: () => ({
        notShowSelect: false, // 仅在single 单列表有效，选中后不会展示选中项，只向父组件触发change事件
        labelkey: 'label', // 只针对单个列表
        valuekey: 'value'// 只针对单个列表
      })
    },
    multipleOptions: { default: () => {}, type: Object },
    popperClass: { default: '', type: String },
    formatTextShow: { default: undefined, type: Function }// popperMinWidth100
  },
  data() {
    return {
      popIsShow: false,
      searchValue: ''
    }
  },
  computed: {
  },
  mounted() {
    if (this.mode === 'multiple') {
      this.list.lastSelectModel = this.list.model
    }
  },
  methods: {
    generateReferenceDom() {
      let title = this.title
      // let countStr = ''
      let isSelected = false
      let domTitle = ''
      if (this.mode === 'single') {
        isSelected = !this.defaultArray.includes(this.selfModel)
      } else if (this.mode === 'multiple' && !this._isEmpty(this.list.model)) {
        // countStr = !this.border ? <span style="margin-left: 4px;">{this.list.model.length}</span> : ''
        isSelected = true
      }
      if (isSelected && !this.options.notShowSelect) {
        switch (this.mode) {
          case 'single':
            for (let row of this.list) {
              if (`${row[this.options.valuekey || 'value']}` === `${this.selfModel}`) {
                if (this.formatTextShow) {
                  title = this.formatTextShow(row)
                } else {
                  title = row[this.options.labelkey || 'label']
                }
                break
              }
            }
            break
          case 'multiple':
            if (this.border && this.borderSelectedActived) {
              title = `${title} ${this.list.model.length}`
            } else if (this.multipleShowSelectedText) {
              let arr = []
              this.list.dataList.forEach(row => {
                if (this.list.model.includes(row[this.options.valuekey || 'value'])) {
                  arr.push(row)
                }
              })
              if (this.formatTextShow) {
                title = this.formatTextShow(arr)
              } else {
                title = arr.map(v => v[this.options.labelkey || 'label']).join(',')
              }
              domTitle = title
            }
            break
        }
      }

      return <span
        class={['reference-box overrflow-with-ellipsis', `size-${this.size}`, { border: this.border || false }, { actived: this.popIsShow },
          { isSelected: isSelected && (!this.border || this.borderSelectedActived) }, { 'is-empty-select': !isSelected }]}
        title={domTitle}
        slot="reference">
        {title}
        <span class="caret"></span>
      </span>
    },
    selectItem(item) {
      if (!this.options.notShowSelect) {
        this.selfModel = item[this.options.valuekey || 'value']
      }
      this.$emit('change', item[this.options.valuekey || 'value'], item)
      this.closePop()
      this.searchValue = ''
    },
    generateFilterInput() {
      return <div class="box-search-input">
        <app-input
          v-model={this.searchValue}
          placeholder={this.inputPlaceholder}
          prefixIcon={{ icon: 'el-icon-search', click: this.filterSearch }}
          clearable={true}
        />
      </div>
    },
    generateDefaultContent() {
      switch (this.mode) {
        case 'single':
          let list = this.list || []
          let formatSearchedItem = (str) => {
            return str
          }
          if (this.useFilterInput && this.searchValue.trim()) {
            list = list.filter(v => v[this.options.labelkey || 'label'].indexOf(this.searchValue.trim()) > -1)
            formatSearchedItem = (str) => {
              return `${str}`.replace(new RegExp(`${this.searchValue.trim()}`, 'g'), `<em>${this.searchValue.trim()}</em>`)
            }
          }
          let contents
          if (list.length) {
            contents = list.map((item) => {
              return <li
                class={[{ actived: item[this.options.valuekey || 'value'] !== '' && this.selfModel === item[this.options.valuekey || 'value'] }]}
                key={item[this.options.valuekey || 'value']}
                onClick={() => this.selectItem(item)}
                domPropsInnerHTML={ formatSearchedItem(item[this.options.labelkey || 'label']) }
              >
              </li>
            })
          } else {
            contents = <app-nodata
              imgWidth={60}
              text="没有匹配到相关数据"
            />
          }
          return (
            <ul class="dropdown_menulist-box">
              {contents}
            </ul>
          )
        case 'multiple':
          if (!this.popIsShow) {
            return ''
          }
          const listeners = {
            on: {
              sureclick: (val) => {
                this.list.lastSelectModel = val
                this.$emit('change', val)
                this.closePop()
              },
              cancelclick: () => {
                this.list.lastSelectModel = []
                this.$emit('change', [])
                this.closePop()
              }
            }
          }
          let $attrs = {
            props: { allText: '', ...this.multipleOptions?.attrs || {} }
          }
          return (<filterlistCheckbox
            currentData={this.list}
            labelkey={this.options.valuekey || 'value'} valuekey={this.options.labelkey || 'label'} // 之前组件写反了
            { ...$attrs }
            { ...listeners }
          />)
      }
    },
    closePop() {
      this.popIsShow = false
    },
    showPop() {
    },
    hidePop() {
      if (this.mode === 'multiple') {
        this.list.model = this.list.lastSelectModel
      }
    },
    reset() {
      switch (this.mode) {
        case 'single':
          this.selfModel = ''
          break
        case 'multiple':
          this.list.lastSelectModel = []
          this.list.model = []
          break
      }
    }
  },
  render() {
    // 弹窗显示的内容
    const contentDom = this.$slots.default || this.generateDefaultContent()
    let inputDom
    if (this.useFilterInput && this.mode === 'single') {
      inputDom = this.generateFilterInput()
    }

    // 初始显示的内容区域
    const referenceDom = this.$slots.reference || this.generateReferenceDom()
    return <el-popover
      width={this.width}
      v-model={this.popIsShow}
      transition="el-zoom-in-top"
      popper-class={`app-dropdown-select-pop ${this.popperClass}`}
      placement={this.$attrs.placement || 'bottom-start'}
      trigger={this.$attrs.trigger || 'hover'}
      {...this.$attrs}
      onShow={this.showPop}
      onHide={this.hidePop}
      content="">
      {inputDom}
      {contentDom}
      {referenceDom}
    </el-popover>
  }
}
