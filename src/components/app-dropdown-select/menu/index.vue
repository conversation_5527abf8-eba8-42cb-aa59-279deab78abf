<script>
import { modelMixin } from '../../utils'

export default {
  mixins: [modelMixin],
  props: {
    dataList: { default: () => [], type: Array },
    options: { default: () => {}, type: Object }
  },
  data() {
    return {}
  },
  methods: {
    selectItem(item) {
      if (!this.options.notShowSelect) {
        this.selfModel = item[this.options.valuekey || 'value']
      }
      this.$emit('change', item[this.options.valuekey || 'value'], item)
      this.$emit('closePop')
    },
    getList() {
      const contents = this.dataList.map((item) => {
        return <li
          class={[{ actived: item[this.options.valuekey || 'value'] !== '' && this.selfModel === item[this.options.valuekey || 'value'] }]}
          key={item[this.options.valuekey || 'value']}
          onClick_stop={() => this.selectItem(item)}
        >
          {item[this.options.labelkey || 'label']}
        </li>
      })
      return (
        <ul class="dropdown_menulist-box">
          {contents}
        </ul>
      )
    }
  },
  render() {
    return this.getList()
  }
}
</script>

<style scoped lang="scss">


</style>
