<script src="./component.jsx">
</script>


<style scoped lang="scss">
@import "../../styles/mixins/common";
@mixin size2pading($paddingSize: 12px) {
  padding-left: $paddingSize;
  padding-right: 4px + 8px + $paddingSize;
  .caret {
    right: $paddingSize;
  }
}

.reference-box {
  display: inline-block;
  width: 100%;
  cursor: pointer;
  color: #333;
  position: relative;
  background: #fff;
  vertical-align: middle;

  &:hover,&.actived,&.isSelected {
    color: #128bed;
    .caret {
      border-top-color: #128bed;
    }
  }
  &.actived {
    .caret {
      transform: translateY(-50%) rotate(180deg)!important;
    }
  }
  .caret {//8px * 4px
    margin-left: 4px;
  }
  &.border {
    border: 1px solid #D8d8d8;
    border-radius: 2px;
    padding-left: 12px;
    height: 32px;
    line-height: 30px;
    padding-right: 24px;
    .caret {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
    }
    &:hover,&.actived,&.isSelected {
      border-color: #128bed;
    }
    &.size-small {
      height: 32px;
      line-height: 30px;
      font-size: 14px;
      @include size2pading(12px);
    }
    &.size-mini {
      height: 28px;
      line-height: 26px;
      @include size2pading(12px);
    }
    &.size-tiny {
      height: 24px;
      line-height: 22px;
      @include size2pading(8px);
    }
  }
}

ul.dropdown_menulist-box {
  color: #333;
  font-size: 14px;
  min-width: 150px;
  max-height: 300px;
  overflow-y: auto;
  >li {
    position: relative;
    display: flex;
    align-items: center;
    height: 32px;
    padding: 0 15px;
    cursor: pointer;
    &:first-child {
      margin-top: 4px;
    }
    &:last-child {
      margin-bottom: 4px;
    }
    &:hover {
      background: #F2F8FE;
      color: #128bed;
      >.__arrowRight {
        color: #128bed;
      }
    }
    &.actived {
      color: #128bed;
      >.__arrowRight {
        color: #128bed;
      }
    }

    >.__arrowRight {
      position: absolute;
      right: 15px;
      color: #bbb;
    }
  }
}

.box-search-input {
  padding: 10px 15px;
  border-bottom: 1px solid #D8d8d8;
  flex: 1;
  ::v-deep {
    @include baseInputPlaceHolder(#bbb);
  }
}
</style>
<style lang="scss">
.el-popover.el-popper.app-dropdown-select-pop {
  padding: 0;
  min-height: 40px;
  min-width: 100px;

  &.popperMinWidth100 {
    ul.dropdown_menulist-box {
      min-width: 100px;
    }
  }

}

.el-popover.el-popper.app-dropdown-select-pop[x-placement^=bottom] {
    margin-top: 5px;
}

.el-popover.el-popper.app-dropdown-select-pop[x-placement^=top] {
    margin-bottom: 5px;
}

.el-popover.el-popper.app-dropdown-select-pop[x-placement^=left] {
    margin-right: 5px;
}

.el-popover.el-popper.app-dropdown-select-pop[x-placement^=right] {
    margin-left: 5px;
}

.el-table .el-table__body-wrapper .el-table__row .cell .reference-box.is-empty-select {
  color: #bbb;
}
</style>
