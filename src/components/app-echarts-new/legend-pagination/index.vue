<template>
  <div v-if="total > pageSize" class="chart-legend-pagination">
    <span class="iconfont icon-jiantoumianshang" :class="[{disabled: currentPage <= 1}]" @click="scrollToPre"></span>
    {{ currentPage }}/{{ showTotal }}
    <span class="iconfont icon-jiantoumianxia" :class="{disabled: currentPage >= showTotal}" @click="scrollToNext"></span>
  </div>

</template>

<script>
import _ from 'lodash'
export default {
  props: {
    total: { default: 0 },
    pageSize: { default: 5 },
    currentPage: { default: 1 }
  },
  data() {
    return {}
  },
  computed: {
    showTotal() {
      let r = Math.floor(this.total / this.pageSize)
      return r + (this.total % this.pageSize === 0 ? 0 : 1)
    }
  },
  methods: {
    scrollToPre: _.debounce(function () {
      if (this.currentPage <= 1) return
      this.$emit('page-scroll', -1)
    }, 200),
    scrollToNext: _.debounce(function () {
      if (this.currentPage >= this.showTotal) return
      this.$emit('page-scroll', 1)
    }, 200)
  }
}
</script>

<style scoped lang="scss">
.chart-legend-pagination {
  display: inline-flex;
  align-items: center;
  color: #fff;
  > span.iconfont {
    font-size: 9px;
    color: #128bed;
    cursor: pointer;

    &.disabled {
      cursor: not-allowed;
    }
    &:hover:not(.disabled) {
      color: #0069bf;
    }
    &.icon-jiantoumianxia {
      margin-left: 2px;
    }
    &.icon-jiantoumianshang {
      margin-right: 2px;
    }

    &.disabled {
      opacity: 60%;
    }
  }
}

</style>
