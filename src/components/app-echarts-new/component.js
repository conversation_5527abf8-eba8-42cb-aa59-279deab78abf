import { cloneDeep, debounce, isEmpty } from 'lodash'
import { deviceInfo } from '@/core/device-info'
import { mergeOption } from './config'
let echarts = require('echarts')

export default {
  name: 'app-echarts-new',
  components: {
    LegendPagination: () => import('./legend-pagination')
  },
  props: {
    option: {
      type: Object
    },
    title: {
      type: String
    },
    mode: { type: String, default: 'default' }, // default, cloudmap:图表模式下
    type: {
      type: String
    },
    height: {
      type: String,
      default: '300px'
    },
    width: {
      type: String,
      default: '100%'
    },
    chartObj: {
      type: Object
    },
    oDefault: {
      type: Object
    },
    color: {
      type: Array,
      default: () => [
        '#5B8FF9',
        '#61DDAA',
        '#65789B',
        '#F6BD16',
        '#7262fd',
        '#78D3F8',
        '#9661BC',
        '#F6903D',
        '#008685',
        '#F08BB4',
        '#CDDDFD',
        '#CDF3E4',
        '#65789B',
        '#FCEBB9',
        '#D3CEFD',
        '#D3EEF9',
        '#DECFEA',
        '#FFE0C7',
        '#BBDEDE',
        '#FFE0ED'
      ]
    },
    /**
     * 禁止鼠标移入执行ScrollIntoView方法
     */
    forbidScrollIntoView: {
      type: Boolean,
      default: false
    },
    needMerge: {
      type: Boolean,
      default: true
    },
    rendererType: {
      type: String,
      default: 'canvas'
    },
    // 不需要饼图legend上的count
    noPieLegendCount: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      scroll: false,
      hoverIndex: '',
      echartsWidth: '',
      noNeedScrollFlg: 0,
      legendPagination: {
        currentPage: 1
      },
      realHeight: 0
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.width !== 'auto') {
        this.echartsWidth = $(this.$refs.main).width() + 'px'
      }
      this.draw()
      this.$emit('draw-callback', this.chart)
      this.noNeedScrollFlg = this.noNeedScroll()
    })
    this.$_debounceInit = debounce(this.init, 100)
    window.addEventListener('resize', this.$_debounceInit)
  },
  watch: {
    option: {
      handler(newVal, oldVal) {
        if (newVal) {
          if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
            this.legendPagination.currentPage = 1
            this.draw()
            this.$emit('draw-callback', this.chart)
          }
        }
      },
      immediate: false,
      deep: true
    }
  },
  computed: {
    pieStyle() {
      if (isEmpty(this.option)) return {}
      const { legend, pieLengendCenter } = this.option
      const { top, bottom, left, right, width = 200, height = 238, otherStyle = {} } = (legend || {})
      const leftValue = typeof left === 'number' ? `${left}px` : left
      const rightValue = typeof right === 'number' ? `${right}px` : right
      const style = {
        top: top + 'px',
        left: leftValue,
        right: rightValue,
        bottom: bottom + 'px',
        width: `${width}`.includes('%') ? width : (width + 'px'),
        height: height + 'px',
        ...(otherStyle || {})
      }
      if (pieLengendCenter) {
        style.maxHeight = style.height
        style.height = 'auto'
        delete style.bottom
      }
      return style
    },
    pieData() {
      const { legend } = this.option
      return legend?.data || []
    },
    showPieDataList() {
      if (this.currentIsMoreLegendList) {
        let startNumber = (this.legendPagination.currentPage - 1) * this.limitLegendCount
        return this.pieData.slice(startNumber, startNumber + this.limitLegendCount)
      }
      return this.pieData
    },
    currentIsMoreLegendList() {
      return this.mode === 'cloudmap' && this.pieData.length > this.limitLegendCount
    },
    limitLegendCount() {
        // 138
      let limit = Math.floor((this.realHeight - 20) / 28)
      return Math.min(5, limit)
    }
  },
  beforeDestroy() {
    if (this.chart && !this.chart.isDisposed) {
      this.chart.clear()
      this.chart.dispose()
    }
    if (__BROWSER__) {
      window.removeEventListener('resize', this.$_debounceInit)
    }
  },
  methods: {
    getSortNumber(number) {
      return (this.legendPagination.currentPage - 1) * this.limitLegendCount + number
    },
    getRealIndex(index) {
      return !this.currentIsMoreLegendList ? index : (this.legendPagination.currentPage - 1) * this.limitLegendCount + Number(index)
    },
    init() {
      try {
        this.chart.resize({
          height: this.$refs.main.offsetHeight,
          width: this.$refs.main.offsetWidth
        })
        this.realHeight = this.$refs.main.offsetHeight
      } catch (err) {}
    },
    noNeedScroll() {
      let pieHover = this.$refs.pieHover
      let pieHoverWrap = this.$refs.pieHoverWrap
      if (pieHover && pieHoverWrap) {
        if (pieHover.clientHeight > pieHoverWrap.clientHeight) {
          return true
        }
      }

      return false
    },
    draw() {
      let option = cloneDeep(this.option)
      if (isEmpty(option)) return
      const that = this
      if (this.needMerge) {
        option = mergeOption(option, this.oDefault)
      }

      if (option.series?.length) {
        option.series.forEach((item) => {
          if (item.type === 'pie' && !item.color?.length) {
            item.color = this.color
          }
        })
      }
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.main, null, {
          renderer: this.rendererType
        })
        this.realHeight = this.$refs.main.offsetHeight
      } else {
        this.chart.clear()
      }
      let height = `${this.height}`.includes('%') ? this.$refs.main.offsetHeight : this.height
      this.chart.resize({ height: height })
      this.chart.setOption(option)

      // 新增echarts事件
      const self = this
      this.chart.off('click')
      this.chart.on('click', function (params) {
        self.$emit('chartClick', params)
      })
      // 饼图点击
      if (this.type === 'pie') {
        this.chart.off('click')
        this.chart.off('mouseover')
        this.chart.off('mouseout')
        this.chart.on('click', function ({ data }) {
          that.$emit('echartsClick', data)
        })
        this.chart.on('mouseover', ({ dataIndex }) => {
          const realIndex = !this.currentIsMoreLegendList ? dataIndex : ((that.legendPagination.currentPage - 1) * this.limitLegendCount + Number(dataIndex))
          this.hoverIndex = realIndex
          const target = that.$refs.pieHoverItem?.[realIndex]
          if (target) {
            if (!deviceInfo.isIE() && !this.forbidScrollIntoView) {
              target.scrollIntoView({
                block: 'nearest',
                behavior: 'smooth',
                inline: 'start'
              })
            } else {
              target.parentNode.scrollTop = target.offsetTop
            }
          }
          this.$emit('pie-mouseover', this.chart, dataIndex)
        })
        this.chart.on('mouseout', () => {
          that.hoverIndex = ''
          this.$emit('pie-mouseover', this.chart, undefined)
        })

        this.$nextTick(() => {
          $(this.$refs.pieHoverItem).on('mouseover', (e) => {
            const dataset = e.currentTarget.dataset
            const index = dataset.index
            const realIndex = this.getRealIndex(index)
            that.hoverIndex = ''
            that.chart.dispatchAction({
              type: 'highlight',
              dataIndex: realIndex
            })
            this.$emit('legend-mouseover', this.chart, e)
          })
          $(this.$refs.pieHoverItem).on('mouseout', (e) => {
            const dataset = e.currentTarget.dataset
            const index = dataset.index
            const realIndex = this.getRealIndex(index)
            that.chart.dispatchAction({
              type: 'downplay',
              dataIndex: realIndex
            })
            this.$emit('legend-mouseout', this.chart, e)
          })
          $(this.$refs.pieHoverItem).on('click', (e) => {
            const dataset = e.currentTarget.dataset
            const index = dataset.index
            const data = that.option?.series?.[0]?.data?.[index]
            that.$emit('echartsClick', data)
          })
        })
      }

      // 折线图点击
      // 柱状图点击
      if (this.type === 'line' || this.type === 'bar') {
        this.chart.off('click')
        this.chart.on('click', function (params) {
          const { dataIndex } = params
          const data = option?.filterData?.[dataIndex] || {}
          that.$emit('echartsClick', data)
        })
      }
    },
    toggleCount() {
      if (this.option.series && this.option.series.length) {
        this.option.series.forEach((x) => {
          x.label = x.label || {}
          x.label.show = !x.label.show
        })
      }
    },
    download() {
      return this.chart.getDataURL()
    },
    mouseenter() {
      this.$emit('enter', this.chartObj)
    },
    mouseleave() {
      this.$emit('leave', this.chartObj)
    },
    //
    legendPageScroll(count) {
      this.legendPagination.currentPage += count
    }
  }
}
