<template>
  <div class="app-echarts app-echarts-new" :class="[{'mode-cloud-map': mode==='cloudmap'}]" ref="appEchartsNew" @mouseenter="mouseenter" @mouseleave="mouseleave">
    <div v-if="title" class="title">{{ title }}</div>
    <div ref="main" class="main" :style="{ height: height, width: echartsWidth || width }"></div>
    <template v-if="type === 'pie'">
      <template v-if="option.pieLengendCenterContent">
        <div ref="pieHover" class="pie-hover" :style="pieStyle"
          :class="[{ 'center-content': option.pieLengendCenterContent }, { hasTitle: !!title }]">
          <div ref="pieHoverWrap" class="center-content-wrap" :style="noNeedScrollFlg?'overflow-y: hidden;':''">
            <div v-for="(item, index) in pieData" :key="'pieData' + index" class="pie-hover-item" :data-index="index"
              :class="{ hoverIndex: hoverIndex === index }" ref="pieHoverItem">
              <div class="num" :style="{ background: item.color || color[index % 20] }">{{ noPieLegendCount?'':(index + 1) }}</div>
              <slot name="name" :item="item">
                <span>{{ item.name }}</span>
              </slot>
              <div class="percent">{{ item.value }}%</div>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div ref="pieHover" class="pie-hover" :style="pieStyle"
          :class="[{ center: option.pieLengendCenter }, { hasTitle: !!title }]">
          <div class="pie-list-items" ref="pieListRef">
            <div v-for="(item, index) in showPieDataList" :key="'pieData' + index" class="pie-hover-item" :data-index="index"
                 :class="{ hoverIndex: hoverIndex === index }" ref="pieHoverItem">
              <div class="num" :style="{ background: item.color || color[index % 20] }">{{ noPieLegendCount?'':getSortNumber(index + 1) }}</div>
              <span class="name">
              <slot name="name" :item="item">
                <span> {{ item.name }}</span>
              </slot>
              </span>
              <div class="percent">{{ item.value }}%</div>
            </div>
          </div>
          <legend-pagination v-if="mode === 'cloudmap'" :total="pieData&&pieData.length || 0" class="legend-pagination" :style="{left: pieStyle.left}"
                             @page-scroll="legendPageScroll" :currentPage="legendPagination.currentPage"></legend-pagination>
        </div>
      </template>
    </template>

    <slot></slot>
  </div>
</template>
<script src="./component.js"></script>

<style lang="scss" scoped src="./style.scss"></style>
