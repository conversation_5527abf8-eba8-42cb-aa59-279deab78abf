.main {
  width: 100%;
  height: 400px;
}

.app-echarts-new.app-echarts {
  position: relative;
  .title {
    color: #333;
    font-size: 14px;
    font-weight: bold;
    line-height: 40px;
    padding-left: 15px;
  }

  .pie-hover {
    position: absolute;
    overflow-x: hidden;
    overflow-y: auto;

    &.center-content {
      display: inline-flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: center;
      overflow-x: hidden !important;
      overflow-y:  hidden !important;

      .center-content-wrap{
        // margin: 0 auto;
        // margin-left: 10px;
        width: 100%;
        overflow-x: hidden;
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background-color: #BBBBBB;
        }

        &::-webkit-scrollbar-thumb:hover {
          background-color: #BBBBBB;
        }
      }
    }

    &.center-content-pd{
      padding-top: 65px;
    }


    &.center {
      top: 50% !important;
      transform: translateY(-50%);
      &.hasTitle {
        top: calc(50% + 20px) !important;
      }
    }

    .pie-hover-item {
      padding-left: 21px;
      padding-right: 56px;
      position: relative;
      font-size: 12px;
      margin-bottom: 10px;
      padding-bottom: 1px;
      cursor: pointer;

      &:last-child {
        margin-bottom: 3px;
      }

      &:hover,
      &.hoverIndex {
        color: #128bed;
        background: rgba(18, 139, 237, 0.08);
        border-radius: 4px;
      }

      .num {
        position: absolute;
        top: 1px;
        left: 0px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        text-align: center;
        color: #fff;
        line-height: 12px!important;
        font-size: 12px!important;
        font-family: "Arial";
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }

      .percent {
        position: absolute;
        top: 0px;
        right: 10px;
        text-align: right;
      }
    }
  }


  &.mode-cloud-map {
    .pie-hover {
      top: 50%!important;
      bottom: unset!important;
      transform: translateY(-50%);

      .pie-list-items {
        max-height: 145px;
        overflow-y: hidden;
      }
      .pie-hover-item {
        color: #fff;
        display: flex;
        align-items: center;
        .name {
          display: inline-block;
          line-height: 16px;
          height: 16px;
          max-width: 90%;
        }
        &:hover,
        &.hoverIndex {
          color: #128bed;
        }
      }
    }

    .legend-pagination {
      //position: absolute;
      //top: calc((100% - 137px) / 2 + 137px) ;
    }
  }
}
