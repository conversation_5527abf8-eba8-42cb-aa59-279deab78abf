import _ from 'lodash'
let echarts
if (__BROWSER__) {
  echarts = require('echarts')
}

export const mergeOption = (option, oDefault) => {
  let defaultOption = oDefault ? _.cloneDeep(oDefault) : {
    tooltip: {
      trigger: 'item'
    },
    toolbox: {
      right: 18,
      top: 5,
      feature: {
        saveAsImage: {
          onClick: () => {
          },
          title: '保存为图片'
        }
      }
    }
  }
  const chartType = option.series[0].type
  const notMerge = option.notMerge
  if (!notMerge) {
    if (chartType === 'map') {
      defaultOption.tooltip = {
        formatter: function (params, ticket, callback) {
          var mapSeriesNum = 0
          if (params.value && params.value > 0) {
            mapSeriesNum = params.value
            return params.seriesName + '<br />' + params.name + ':' + mapSeriesNum
          } else {
            return params.seriesName + '<br />' + '此地无数据'
          }
        }
      }
      defaultOption = _.extend(defaultOption, {
        visualMap: {
          min: 0,
          left: 40,
          top: 90,
          text: ['高', '低'], // 文本，默认为数值文本
          calculable: true,
          inRange: {
            color: ['#BFEFFF', '#128BED']
          }
        },
        series: [{
          type: 'map',
          mapType: 'china',
          itemStyle: {
            emphasis: {
              areaColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0, color: '#00EEEE' // 0% 处的颜色
              }, {
                offset: 1, color: '#00FF7F' // 100% 处的颜色
              }], false)
            }
          },
          label: {
            normal: {
              show: false
            },
            emphasis: {
              show: true
            }
          }
        }]
      })
    } else if (chartType === 'pie') {
      defaultOption = _.extend(defaultOption, {
        color: ['rgb(81, 130, 228)', 'rgb(81, 180, 241)', 'rgb(105, 212, 219)', 'rgb(63, 178, 126)', 'rgb(155, 204, 102)', 'rgb(200, 203, 74)'],
        series: [
          {
            type: 'pie',
            radius: '40%',
            center: ['50%', '50%'],
            selectedOffset: 2,
            avoidLabelOverlap: true,
            roseType: false,
            label: {
              normal: {
                textStyle: {
                  fontSize: 12,
                  color: '#666'
                }
              }
            },
            labelLine: {
              normal: {
                smooth: 0.2,
                length: 10,
                length2: 20
              }
            }
          }
        ]
      })
    } else if (chartType === 'bar' || chartType === 'line') {
      defaultOption = _.extend(defaultOption, {
        color: ['#67aef5'],
        xAxis: {
          type: 'category',
          boundaryGap: true,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#666'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0, // 可以设置成 0 强制显示所有标签。
            textStyle: {
              color: '#666'
            }
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          axisLine: {
            show: true
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: '#D8d8d8',
              width: 0.5
            }
          },
          axisTick: {
            show: true
          }
        },
        series: [{
          barWidth: 30,
          label: {
            normal: {
              show: true,
              position: 'top'
            }
          }
        }]
      })
    }
  }
  return _.merge(defaultOption, option, { tooltip: { show: false, trigger: 'none', formatter: '' } })
}
