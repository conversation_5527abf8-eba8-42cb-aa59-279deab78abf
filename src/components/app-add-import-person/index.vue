<template>
  <app-popup ref="popRef" title="" :control="{show:true}" :buttonOptions="buttonOptions" :options="popOptions" @closed="close" :bgClose="false">
    <div slot="title" class="title-wrapper">
      <template v-if="importResultPage">
        导入完成
      </template>
      <template v-else>
        <span v-for="(item,index) in tabs" :key="item.type" @click="changeTab(item)"
              :class="['title-item', {actived: currentSelect === item.type}]">
        {{item.name}}
      </span>
      </template>
    </div>
    <template v-if="importResultPage">
      <import-result :data-list="resultList" @importSuccess="importSuccess"></import-result>
    </template>
    <template v-else-if="currentSelect === 'add'">
      <add ref="addRef"></add>
    </template>
    <template v-else-if="currentSelect === 'addByKeyword'">
      <add-by-keyword ref="addByKeywordRef"></add-by-keyword>
    </template>
    <template v-else-if="currentSelect === 'import'">
      <batch-import :uploadsuccess="uploadsuccess"></batch-import>
    </template>
    <app-loading :control="loadingProp" v-if="loadingProp.show"></app-loading>
  </app-popup>
</template>

<script>
import { loadingMixins } from '../../utils/mixinsUtils'
import personCheckService from '../../services/setting/person-check'
import _ from 'lodash'

export default {
  name: 'app-add-import-company',
  mixins: [loadingMixins],
  components: {
    add: () => import('./include/add'),
    addByKeyword: () => import('./include/add-by-keyword.vue'),
    batchImport: () => import('./include/batch-import'),
    importResult: () => import('./include/import-result')
  },
  props: {
    limitCount: { default: 100, type: [Number, String] }
  },
  data() {
    return {
      tabs: [{ name: '按企业添加', type: 'add' }, { name: '按人名+关键词添加', type: 'addByKeyword' }, { name: '批量上传', type: 'import' }],
      currentSelect: 'add',
      importResultPage: false,
      resultList: [],
      needUpdateGroupName: false
    }
  },
  computed: {
    buttonOptions() {
      let arr = [
        { name: '取消', type: 'normal' },
        { name: '确定', type: 'primary', click: this.sureClick }
      ]
      if (this.importResultPage) {
        arr.splice(0, 1)
      }
      return arr
    },
    popOptions() {
      return {
        width: ['addByKeyword', 'add'].includes(this.currentSelect) ? '600px' : '960px',
        contentStyle: {
          'padding-bottom': this.importResultPage ? '0' : '15px'
        }
      }
    },
    _functionTableId() {
      return this.functionTableId || 'bene_id'
    }
  },
  created() {
    if (this.defaultType && ['add', 'addByKeyword', 'import'].includes(this.defaultType)) {
      this.currentSelect = this.defaultType
    }
  },
  methods: {
    importSuccess() {
      this.addCompanyFlag = 'Y'
    },
    importStaff(fileUrl, fileName) {
      // $util.zhugeTrackTwoLevel('企业列表上传', '批量操作')
      this.showLoadding()
      return new Promise(resolve => {
        this.showLoadding()
        personCheckService
          .importStaff({
            url: fileUrl,
            from: 'monitor'
          })
          .then(res => {
            if (+res.status !== 200) {
              this.hideLoadding()
              this.$message.error(res.msg)
              return
            }
            this.addCompanyFlag = 'Y'
            if (_.isEmpty(res.result.personList)) {
              this.$message.success('添加成功')
              this.$refs.popRef.close()
              this.hideLoadding()
            } else {
              this.resultList = res.result.personList || []
              this.importResultPage = true
              this.hideLoadding()
            }
            resolve()
          }).catch(e => {
            this.catchData(e)
            this.hideLoadding()
            resolve(e)
          })
      })
    },
    uploadsuccess({ url, fileName }) {
      this.uploadFileName = fileName
      return new Promise((resolve, reject) => {
        this.importStaff(url, fileName)
      })
    },
    catchData(e) {
      try {
        let err = JSON.parse(e.toString().replace('Error:', ''))
        if (['115'].includes(`${err.status}`)) { // '114', '112',
          setTimeout(() => {
            this.$refs.popRef.close()
            this.$promise.resolve(true)
          }, 150)
        }
      } catch (e) {
      }
    },
    changeTab(item) {
      this.currentSelect = item.type
    },
    sureClick() {
      if (this.importResultPage) {
        this.$refs.popRef.close()
        this.$promise.resolve(this.addCompanyFlag === 'Y')
      } else {
        if (this.currentSelect === 'add') {
          this.$refs.addRef.doAddCompany().then(res => {
            this.$promise.resolve(true)
            this.$refs.popRef.close()
          })
        } else if (this.currentSelect === 'addByKeyword') {
          this.$refs.addByKeywordRef.doAddCompany().then((res) => {
            this.$promise.resolve(true)
            this.$refs.popRef.close()
          })
        } else if (this.currentSelect === 'import') {
          this.$promise.resolve(this.addCompanyFlag === 'Y')
          this.$refs.popRef.close()
        }
      }
    },
    close() {
      if (this.needUpdateGroupName) {
        window.AppRuntimeContext.eventBus.$emit(window.AppRuntimeContext.eventBusEvents.GROUP_HAS_CHANGED)
      }
      this.$promise.resolve(this.addCompanyFlag === 'Y')
    }
  }
}
</script>

<style scoped lang="scss">
.title-wrapper {
  height: 50px;
  display: flex;
  align-items: center;
  .title-item {
    line-height: 49px;
    margin-right: 30px;
    color: #999999;
    font-size: 15px;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    &:hover {
      color: #999999;
    }
    &.actived {
      color: #333;
      border-bottom: 3px solid #128BED;
    }

  }

}

</style>
