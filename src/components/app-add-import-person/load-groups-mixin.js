import codeTableService from '../../services/codetable'

export default {
  data() {
    return {
      groupList: []
    }
  },
  created() {
    this.initGroupLists()
  },
  methods: {
    initGroupLists() {
      codeTableService.loadStaffGroup({
        condition: {
          loadGroup: 'true'
        }
      }).then(res => {
        this.groupList = [{ groupName: '企业分组', groupValue: '未分组', isEmptyType: true }].concat(res.resultList.map(v => ({ groupName: v, groupValue: v })))
      })
    }
  }
}
