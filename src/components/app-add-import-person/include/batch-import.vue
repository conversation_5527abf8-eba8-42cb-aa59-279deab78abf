<template>
  <el-upload ref="upload" :onSuccess="success" :onError="error"  class="upload-type-item only-one-item" drag :action="action"
             :class="['pop-upload-wrapper', uploadClass]"
             multiple :before-upload="beforeAvatarUpload" :headers="headers">
    <div class="upload-type-content-wrapper">
      <div style="text-align: center;">
        <img src="/img/funcpage/icon-uploadfile.png" height="40px" class="img-tip"><br>
        <span class="text-desc">点击或将文件拖拽到这里上传</span>
        <!--                      <a class="download-template" :href="tmpurl" @click.stop="mbClick" v-if="tmpurl">下载模板</a>-->
      </div>
    </div>
    <ul class="upload-type-desc-wrapper isHorizontal">
      <li class="upload-type-desc">
        <a :href="tmpurl" v-if="tmpurl" @click.stop=" "><i class="iconfont icon-daochu1"></i>下载模板</a> 按模板样式编辑好数据，切勿增减列
      </li>
      <li class="upload-type-desc">
        <span class="color-danger">可填写分组，方便您以后查找和管理</span>
      </li>
      <li class="upload-type-desc">
        选择编辑好的文件，上传文件；等待一段时间，系统提示上传结果
      </li>
    </ul>
  </el-upload>
</template>

<script>
import { getHeaderParams } from '../../../utils/localstorage'

export default {
  name: 'batch-import',
  props: {
    action: { default: '/app-upload', type: String },
    uploadClass: { default: '', type: String },
    limitCount: { default: 100 },
    maxFileSize: { default: 2, type: Number },
    uploadsuccess: { type: Function }
  },
  data() {
    return {
      headers: getHeaderParams(),
      tmpurl: `/template/人员监控列表导入模板.xlsx?version=${Math.random()}`
    }
  },
  methods: {
    success(url, file, fileList) {
      if (this.uploadsuccess) {
        this.uploadsuccess({
          url,
          fileName: file.name,
          row: (this.tabList && this.tabList.length ? this.tabList[this.currentSelect] : null)
        }).then(res => {
          this.$refs.upload && this.$refs.upload.clearFiles()
        }).catch(() => {
          this.status = 2
          this.$refs.upload && this.$refs.upload.clearFiles()
        })
      }
    },
    error() {
      this.$message.error('文件上传失败，请稍后重试')
    },
    beforeAvatarUpload(file) {
      if (!$util.getIsExcelFile(file, this.isJSJW)) {
        this.$message.error('仅支持Excel（xls、xlsx）文件!')
        return false
      }
      const isLtM = file.size / 1024 / 1024 < (this.maxFileSize)
      if (!isLtM) {
        this.$message.error(`上传文件大小不能超过 ${this.maxFileSize}MB!`)
      }
      return isLtM
    }
  }
}
</script>

<style scoped lang="scss">
.pop-upload-wrapper {
  height: 171px;
  ::v-deep .el-upload.el-upload--text {
    width: 100%;
    .el-upload-dragger {
      height: 171px;
      background:#F5F9FF;
      width: 100%;
      border: 1px dashed #C2C2C2;
    }
    .upload-type-content-wrapper {
      padding-top: 35px;
    }
    .text-desc {
      margin-top: 10px;
      display: inline-block;
      line-height: 24px;
      color: #999;
    }
  }
  ::v-deep .el-upload-list.el-upload-list--text {
    display: none;
  }

  .upload-type-desc-wrapper {
    margin-top: 20px;
    width: 100%;
    text-align: center;
    line-height: 22px;

    &.isHorizontal {
      .upload-type-desc {
        display: inline-block;
        margin-right: 20px;
        vertical-align: top;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    .upload-type-desc {
      text-align: left;
      font-size: 12px;
      font-weight: normal;
      line-height: 20px;
      height: 20px;
      color: #999999;
      position: relative;
      padding-left: 11px;

      &:before {
        content: ' ';
        display: inline-block;
        width: 6px;
        height: 6px;
        background: #BBBBBB;
        border-radius: 50%;
        position: absolute;
        left: 0;
        top: 7px;
      }
      a {
        background: #E2F1FD;
        border-radius: 2px;
        line-height: 20px;
        display: inline-block;
        padding: 0 7px;
        margin-right: 5px;
        i {
          font-size: 12px;
        }
        &:hover {
          background: #CAE6FC;
          color: #128bed;
          i{
            color: #128bed;
          }
        }
      }
    }
  }
}

</style>
