<template>
  <paste-input-content ref="pasteInputContentRef" :use-ref="parentRef" :careToSelfDealwidth="false" :limit="limit">
    <el-popover
      placement="bottom-end" width="400" popper-class="addcomp-pop-selectgroup"
      v-model="visiable" slot="operation"
      trigger="hover" v-if="childrenHasGroupPermission">
        <span class="operation-add-group" :class="[{visiable: visiable || currentSelect}]" @click.stop="''" slot="reference">
          <i class="iconfont icon-xinzeng" v-if="!(currentSelect && currentSelect !== '未分组')"></i>
          <span :title="currentText" class="current-text overrflow-with-ellipsis">{{currentText}}</span>
          <i class="el-icon-caret-bottom"></i>
        </span>
        <ul>
          <li v-for="(item,index) in groupList" :key="`${item.groupValue}-${index}`" @click="handleCommand(item.groupValue)"
              :title="item.isEmptyType ? '' : `${item.groupValue}（${item.corpCount}）`"
              :class="['el-dropdown-menu__item overrflow-with-ellipsis', {actived: currentSelect === item.groupValue}]">
              <template v-if="item.isEmptyType">
                未分组
              </template>
              <template v-else>
                {{item.groupValue}}（{{item.corpCount}}）
              </template>
          </li>
        </ul>
    </el-popover>
  </paste-input-content>
</template>

<script>
import loadGroupsMixin from '../load-groups-mixin'

export default {
  name: 'batch-paste',
  mixins: [loadGroupsMixin],
  components: {
    pasteInputContent: () => import('../../app-text-upload/content')
  },
  props: {
    uploadsuccess: { default: undefined, type: Function },
    limit: { type: Number, default: 10 }
  },
  data() {
    return {
      currentSelect: '',
      visiable: false,
      isAddCompList: false,
      childrenHasGroupPermission: !$util.getHasNoPermission('rvrs:cmp:onegroup')
    }
  },
  computed: {
    currentText() {
      return this.currentSelect && this.currentSelect !== '未分组' ? this.currentSelect : '添加分组至'
    }
  },
  created() {
    this.parentRef = this
  },
  methods: {
    handleCommand(val) {
      this.currentSelect = val
      this.visiable = false
    },
    sureSuccess() {
      return this.$refs.pasteInputContentRef.sureSuccess(this.currentSelect)
      .then(res => {
        if (this.uploadsuccess) {
          this.uploadsuccess({
            uploadCompanyList: res.map(v => ({ corpName: v.corpName, corpKeyNo: v.corpKeyNo })),
            groupValue: this.currentSelect
          }).then(() => {

          }).catch(() => {
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .operation-add-group {
    font-size: 14px;
    color: #666;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    height: 19px;

    .current-text {
      display: inline-block;
      max-width: 120px;
    }

    &.visiable,
    &:hover {
      color: #128BED;
    }
    .icon-xinzeng {
      font-size: 13px;
      margin-right: 5px;
    }
    .el-icon-caret-bottom {
      font-size: 12px;
      margin-left: 2px;
    }
  }

  ::v-deep .el-dropdown-menu__item.actived {
    color: #128BED;
  }

</style>
<style lang="scss">
  .el-popper.addcomp-pop-selectgroup {
    padding: 5px 0 0;
    width: unset!important;
    max-width: 300px;
    max-height: 300px;
    overflow: auto;
  }
</style>
