<template>
  <div class="import-success-wrapper">
    <div class="result-text">
      成功导入 <span class="color-primary">{{ successCount }}</span> 条，失败 <span
      class="color-danger">{{ faildCount }}</span> 条
      <a class="color-primary" @click="downloadErrorDetail" v-if="faildCount>0">下载失败明细</a>
      <a class="color-primary" v-if="successCount||faildCount" @click="exportResult" v-permission="[{name: 'rvrs:hidden_export', isInverse: true}]">导出结果</a>
    </div>
    <app-table :controlData="tableControlData" :columsProp="columsProp" :paginationProp="tablePaginationProp"
               :options="selectCompGroupsOptions" style="margin-top: 10px" autoheight>
      <template slot="header-importResult">
        导入结果
        <colum-header-filter :options="resultFilterOptions" v-model="filterModel" @change="doFilter"></colum-header-filter>
      </template>
      <template slot="content-modifiedColum" slot-scope='scope'>
        <div class="result-company-text">
          <div class="comp-text overrflow-with-ellipsis" :title="scope.row.corpName">{{ scope.row.corpName }}</div>
          <span class="modify-note margin-l-0-3x" v-if='!scope.row.status'>
            <i class="iconfont icon-icon_bii" @click.stop="editRecord(scope.row)"/>
          </span>
        </div>
      </template>
    </app-table>
  </div>
</template>

<script>
import { tableDataMixins } from '../../../utils/mixinsUtils'
import columHeaderFilter from '../../app-add-import-company/include/colum-header-filter'
import { exportExcelBase } from '../../../utils/exportexcel/export-excel-utils'
import updateRecordDialog from '../../../routes/riskMonitor/monitor-list/person-batch/components/update-record'

export default {
  name: 'import-result',
  mixins: [tableDataMixins],
  components: {
    columHeaderFilter
  },
  props: {
    dataList: { default: () => [], type: Array },
    functionTableId: { default: 'bene_id', type: String }
  },
  data() {
    return {
      selectCompGroupsOptions: {
        emptyShow: true,
        isNet: false
      },
      filterModel: '',
      columsProp: [
        { colName: '企业名称', prop: 'name', minWidth: 124, contentSlotName: 'content-modifiedColum' },
        { colName: '姓名', prop: 'staffName', width: 80 },
        { colName: '标识符', prop: 'staffKeyNo', minWidth: 80 },
        { colName: '分组', prop: 'group', width: 80 },
        {
          colName: '导入结果',
          prop: 'statusDesc',
          width: 100,
          headerSlotName: 'header-importResult',
          format(row) {
            return row.status ? '<span class="color-success">成功</span>' : '<span class="color-danger">失败</span>'
          }
        },
        {
          colName: '失败原因',
          prop: 'errorRsn',
          minWidth: 100,
          format(row) {
            return !row.status ? `<span class="color-danger" title="${row.errorRsn}">${row.errorRsn}</span>` : '-'
          }
        }
      ],
      resultFilterOptions: {
        list: []
      }
    }
  },
  created() {
    this.tablePaginationProp.isNet = false
    this.tablePaginationProp.pageSize = 10
    this.tablePaginationProp.themeV2 = true
    this.tablePaginationProp.showMini = true
  },
  mounted() {
    this.doFilter()
  },
  computed: {
    successCount() {
      return (this.dataList || []).filter(v => v.status).length || 0
    },
    faildCount() {
      return (this.dataList || []).length - this.successCount
    }
  },
  methods: {
    generateOptions() {
      let arr = [
        { name: '全部', value: '' }
      ]
      if (this.successCount > 0) {
        arr.push({ name: '成功', value: true })
      }
      if (this.faildCount > 0) {
        arr.push({ name: '失败', value: false })
      }
      this.resultFilterOptions.list = arr
    },
    downloadErrorDetail() {
      let errorList = this.dataList.filter(v => !v.status)
      if (!errorList.length) {
        this.$message.warning('暂无导入失败的数据')
        return
      }
      let header = ['企业名称', '姓名', '标识符', '分组', '导入结果', '失败原因']

      let data = errorList.map(x => [
        x.corpName || '-',
        x.staffName || '-',
        x.staffKeyNo || '-',
        x.group || '-',
        '失败',
        x.errorRsn || '-'
      ])
      exportExcelBase({
        header,
        data
      }, '导入失败明细')
    },
    exportResult() {
      let header = ['企业名称', '姓名', '标识符', '分组', '导入结果', '失败原因']

      let data = this.dataList.map(x => [
        x.corpName || '-',
        x.staffName || '-',
        x.staffKeyNo || '-',
        x.group || '-',
        x.status ? '成功' : '失败',
        x.errorRsn || '-'
      ])
      exportExcelBase({
        header,
        data
      }, '批量操作结果')
    },
    doFilter(val) {
      this.generateOptions()
      if ([undefined, ''].includes(val)) {
        this.tableControlData.allDatas = this.dataList || []
      } else {
        this.tableControlData.allDatas = this.dataList.filter(v => v.status === val)
      }
      this.tablePaginationProp.total = this.tableControlData.allDatas.length
    },
    editRecord(row) {
      this.$uiService
        .showDialog(updateRecordDialog, { formModel: { corpName: row.corpName, name: row.staffName, keyNo: row.staffKeyNo, group: row.group } })
        .then(data => {
          row.errorRsn = '-'
          if (data.updated) {
            this.$emit('importSuccess')
            row.status = true
            row.corpName = data.corpName
            row.searchKey = data.corpName
            row.corpKeyNo = data.corpKeyNo
            row.corpName = data.data.corpName
            row.staffName = data.data.staffName
            row.staffKeyNo = data.data.staffKeyNo
            row.group = data.data.group
            row.filterCode = 'S'
            this.$nextTick(() => {
              if (this.faildCount === 0) {
                this.filterModel = ''
              }
              this.doFilter(this.filterModel)
            })
          }
        })
    }
  }
}
</script>

<style scoped lang="scss">
.result-text {
  background: #F2F8FE;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;

  > span {
    margin: 0 5px;
  }

  > a {
    margin-left: 10px;
  }
}

.result-company-text {
  display: inline-flex;
  height: 20px;
  align-items: center;
  .comp-text {
    max-width: 285px;
  }

  .modify-note {
    cursor: pointer;
    color: #bbb;
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;

    &:hover {
      color: #128BED;
      background: #E2F1FD;
    }

    .iconfont.icon-icon_bii {
      font-size: 13px;
    }
  }
}
</style>
