<template>
  <el-form ref="addCompany" :model="model" size="large" label-width="89px"
   label-position="left" class="add-by-keyword-wrapper">
    <el-form-item label="人名+关键词" style="margin-bottom: 15px;">
      <el-autocomplete v-model="personSearchKey" :fetch-suggestions="queryPersonSearchAsync"
      placeholder="请输入需要添加监控的人名名称+关键字，如：'雷军 小米" :hide-loading="true"
      @select="personHandleSelect" style="width: 100%;">
        <template slot-scope="{item}">
          <div class="person-name">
            <div class="margin-r-0-6x">{{item.operName}}</div>
            <div>{{item.name}}</div>
          </div>
        </template>
      </el-autocomplete>
    </el-form-item>
    <el-form-item label="人员分组" style="margin-bottom:0">
      <el-input v-model="model.groupName" placeholder='请输入人员分组' :maxlength="30"></el-input>
    </el-form-item>
  </el-form>
</template>

<script>
import companyListService from '../../../services/enterprise-center/company-list'
import personCheckService from '../../../services/setting/person-check'
import _ from 'lodash'

export default {
  components: {
    appCompanySearch: () => import('../../app-company-search')
  },
  data() {
    return {
      model: {
        name: '',
        keyNo: '',
        groupName: ''
      },
      personSearchKey: '',
      selectedPerson: '',
      childrenHasGroupPermission: !$util.getHasNoPermission('rvrs:cmp:onegroup')
    }
  },
  methods: {
    queryPersonSearchAsync(queryString, cb) {
      if (queryString === '' || typeof (queryString) === 'undefined') {
        var r = []
        cb(r)
        return
      }
      let params = {
        searchKey: queryString
      }

      companyListService.searchAutocomplete4Person(params).then(res => {
        let list = res.resultList.map(v => ({
          keyNo: v['keyNo'],
          name: v['name'],
          operName: v['operName'],
          value: v['operName'] + ' ' + v['name']
        }))
        setTimeout(() => {
          cb(list)
        }, 0.01)
      })
    },

    personHandleSelect(item) {
      this.selectedPerson = item
    },

    doAddCompany() {
      return new Promise(resolve => {
        if (_.isEmpty(this.selectedPerson)) {
          this.$message.warning('请先输入人名+关键词')
          return
        }
        if (this.model.groupName.length > 30) {
          this.$message.warning('分组名称最多可输入30个字符')
          return
        }
        let params = {
        tabVal: 'keyword',
        group: this.model.groupName,
        staffInfo: (this.selectedPerson.keyNo + '|' + this.selectedPerson.operName)
      }

      personCheckService.addMonitorStaff(params)
        .then((res) => {
          this.$emit('importSuccess')
          this.$message.success('添加成功')
          resolve()
        })
      })
    }
  }
}
</script>


<style lang="scss" scoped>
  .add-by-keyword-wrapper {
    ::v-deep .el-form-item .el-form-item__label {
      padding-right: 0;
    }
  }
  .person-name {
      display: flex;
  }
</style>
