<template>
  <el-form ref="addCompany" :model="model" size="large" label-width="89px" label-position="left">
    <el-form-item prop="keyNo" label="企业名称" style="margin-bottom: 15px">
      <app-company-search ref="companyRef" placeholder="请输入企业名称或统一社会信用代码" icon="''" size="large" class="overrflow-with-ellipsis"
      @select="selectCompany"></app-company-search>
    </el-form-item>
    <el-form-item prop="selectPersons" label=" " style="margin-bottom: 0;margin-top: -10px;" v-if="personList && personList.length">
      <el-radio-group v-model="isAllSelect">
        <el-radio :label="1" @click.native.stop.prevent="selectAllClick()">全选</el-radio>
        <el-radio :label="2" @click.native.stop.prevent="unselectAllClick()">反选</el-radio>
      </el-radio-group>

      <el-checkbox-group v-model="model.selectPersons" class="select-person-list height24">
        <el-checkbox :label="item.staffKeyNo" v-for="item in personList" :key="item.staffKeyNo" @click.native="itemClick">
          {{item.staffName}}<span class="staff-desc">
            ({{item.title.join(',')}})
          </span>
        </el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item prop="groupName" label="人员分组" style="margin-bottom: 0;" v-if="childrenHasGroupPermission">
      <app-input v-model="model.groupName" size="large" placeholder="请输入人员分组" :maxlength="30"></app-input>
    </el-form-item>
  </el-form>
</template>

<script>
import throughService from '../../../services/through'
import personCheckService from '../../../services/setting/person-check'
import _ from 'lodash'

export default {
  name: 'add',
  components: {
    appCompanySearch: () => import('../../app-company-search')
  },
  props: ['keyNo', 'name', 'groupName'],
  data() {
    return {
      model: {
        name: '',
        keyNo: '',
        groupName: '',
        selectPersons: []
      },
      personList: [],
      isReverseSelectClick: false,
      childrenHasGroupPermission: !$util.getHasNoPermission('rvrs:cmp:onegroup')
    }
  },
  computed: {
    isAllSelect() {
      if (this.isReverseSelectClick) {
        return 2
      }
      return this.personList.length > 0 && this.personList.length === this.model.selectPersons.length ? 1 : 0
    }
  },
  created() {
    if (this.keyNo) {
      this.selectCompany({ keyNo: this.keyNo })
    }
    this.model.groupName = this.groupName || ''
  },
  mounted() {
    if (this.name) {
      this.$nextTick(() => {
        this.$refs.companyRef.$refs.search.setInputContent(this.name)
      })
    }
  },
  methods: {
    selectCompany(val) {
      if (_.isEmpty(val)) {
        return
      }
      const { keyNo } = val
      this.model.selectPersons = []
      let params = {
        keyNo
      }
      throughService.getManagerlist(params).then(res => {
        this.personList = res.resultList || []
        if (this.personList.length === 0) {
          this.$message.warning('暂无董监高')
        }
      })
    },
    selectAllClick(val) {
      this.model.selectPersons = this.personList.map(v => v.staffKeyNo)
      this.isReverseSelectClick = false
    },
    unselectAllClick() {
      this.model.selectPersons = this.personList.filter(v => !this.model.selectPersons.includes(v.staffKeyNo)).map(v => v.staffKeyNo)
      this.isReverseSelectClick = true
    },
    itemClick() {
      this.isReverseSelectClick = false
    },
    doAddCompany() {
      return new Promise(resolve => {
        if (!this.model.selectPersons.length) {
          this.$message.warning('请选择自然人')
          return
        }
        if (this.model.groupName.length > 30) {
          this.$message.warning('分组名称最多可输入30个字符')
          return
        }

      let selectRows = this.personList.filter(v => this.model.selectPersons.includes(v.staffKeyNo))
      let params = {
        tabVal: 'corp',
        group: this.model.groupName,
        staffInfo: selectRows.map(v => `${v.staffKeyNo}|${v.staffName}`).toString()
      }
      personCheckService.addMonitorStaff(params)
        .then((res) => {
          this.$emit('importSuccess')
          this.$message.success('添加成功')
          resolve(selectRows)
        })
      })
    }
  }
}
</script>


<style lang="scss" scoped>
  .select-person-list {
    background: #F7F7F7;
    padding: 10px;
    padding-bottom: 0;
    margin-bottom: 15px;
    max-height: 120px;
    overflow: auto;
    .staff-desc {
      color: #999999;
    }
  }
</style>
<style lang="scss">
.el-select-dropdown.el-popper.add-comp-group-select {
  width: 502px;
  min-width: unset!important;
}
</style>
