<template>
  <div class="_overflow-operate">
    <div :class="options.operationsConfig.position" @click="clickLineToExpand" v-if="options.isOverFlow">
      <slot name="operate"></slot>
    </div>
    <div class="_content" :style="getStyle" ref="wrapperBodyRef">
      <slot></slot>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'app-overflow-operate',
    props: {
      options: {
        type: Object,
        default: () => {
          return {
            operationsConfig: {
              width: 0,
              position: 'bottom'
            },
            noNeed: false,
            defaultHeight: 40,
            isOverFlow: false,
            currentIsExpand: false,
            expandByOneLine: true
          }
        }
      },
      list: {} // 传入该list监听渲染变化，实时的计算
    },
    data() {
      return {}
    },
    computed: {
      getStyle() {
        if (this.options.noNeed) {
          return
        }
        return {
          width: `calc(100% - ${this.options.operationsConfig.width}px)`,
          height: this.options.isOverFlow && this.options.currentIsExpand ? 'auto' : `${this.options.defaultHeight}px`
        }
      }
    },
    watch: {
      list() {
        this.options.currentIsExpand = false
        this.$nextTick(() => {
          this.measureInnerHeight()
        })
      }
    },
    methods: {
      clickLineToExpand() {
        if (this.options.expandByOneLine) {
          this.options.currentIsExpand = !this.options.currentIsExpand
        }
      },
      measureInnerHeight() { // 计算高度
        this.options.isOverFlow = this.$refs.wrapperBodyRef.scrollHeight > this.options.defaultHeight + 10
      }
    },
    created() {
      if (this.options.currentIsExpand === undefined) {
        this.$set(this.options, 'currentIsExpand', false)
      }
      if (this.options.defaultHeight === undefined) {
        this.$set(this.options, 'defaultHeight', 40)
      }
      if (this.options.isOverFlow === undefined) {
        this.$set(this.options, 'isOverFlow', false)
      }
      if (this.options.operationsConfig === undefined) {
        this.$set(this.options, 'operationsConfig', {})
      }
      if (this.options.operationsConfig.position === undefined) {
        this.$set(this.options.operationsConfig, 'position', 'bottom')
      }
      if (this.options.expandByOneLine === undefined) {
        this.$set(this.options, 'expandByOneLine', true)
      }
    },
    mounted() {
      this.options.currentIsExpand = false
      this.measureInnerHeight()
    }
  }

</script>

<style scoped lang="scss">
  ._overflow-operate {
    display: inline-block;
    white-space: normal;

    ._content {
      overflow: hidden;
      padding: 0 10px;
      background: #F7F7F7;
    }

    .bottom {
      line-height: 20px;
    }
  }

</style>
