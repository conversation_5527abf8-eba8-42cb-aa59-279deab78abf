@import '../../styles/common.scss';
@import '../../styles/baseFlexAdapter.scss';

.qcc-main-sidebar {
  display: block;
  position: absolute;
  left: 0;
  top: 51px;
  bottom: 0;
  border-right: $sidebar-left-border;

  // background-color: #324157;
  .el-menu {

    .el-submenu {
      height: 50px;
      background-color: #3f3f4e;

      .el-submenu__title {
        height: 50px;
        padding-left: 15px !important;
        line-height: 50px;
      }
    }

  }


  .el-submenu .el-menu-item {
    width: 130px;
    height: 40px;
    padding-left: 45px !important;
    line-height: 40px;
    margin-top: 1px;
    &:first-child{
      margin-top: 0;
    }
  }

  .el-menu-item.is-active {
    background: $sidebar-item-hover-color;
    color: $sidebar-link-active-color;
  }

  .sidebar::-webkit-scrollbar {
    width: 0;
  }

  .sidebar-el-menu:not(.el-menu--collapse) {
    width: 171px;
  }

  .el-menu--collapse {
    width: 47px;
  }

  .el-submenu__title:hover {
    background-color: #3f3f4e;

  }

  .title-icon {
    font-size: 14px;
    margin-right: 10px;
    color: #fff;
  }

  .collapse-btn {
    position: absolute;
    right: 15px;
    font-size: 12px;
    color: #fff;
    // background:red;
  }

  .el-submenu__icon-arrow {
    display: none;
  }

}


.el-menu--vertical {
  // background: red;

  .el-menu--popup-right-start {
    margin-left: 0px;
    display: none;
  }

}
