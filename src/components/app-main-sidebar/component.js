import _ from 'lodash'
import { mapState, mapActions } from 'vuex'
import menuHelper from '../../data/menu/menuHelper'
import appMenu from '../../components/app-menu/pro-theme'

export default {
  name: 'sidebar',
  props: {
    data: Array
  },
  components: { appMenu },
  data() {
    return {
      items: menuHelper.getAllAvailableMenus(),
      activePath: '',
      rootPath: ''
    }
  },
  watch: {
    '$route.path': {
      handler(to, from) {
        let func = menuHelper.getFuncIdbyPath(to)
        let target
        const allSubMenus = this.allMenus.reduce((result, item) => {
          return result.concat(item.subs)
        }, [...this.allMenus])
        if (this.rootPath && this.allMenus && this.allMenus.length) {
          target = _.find(allSubMenus, x => x.func === func)
        }
        if (target) {
          if (this.allMenus.filter(x => x.path === this.$route.path).length) {
            this.activePath = this.$route.path
          }
        }
        // 如何当前没有菜单或者是同类菜单就不请求接口
        if (!target || !this.allMenus.length) {
          menuHelper
            .getUserMenus(func)
            .then(data => {
              this.setMenus({
                advancedMenus: data.advancedMenus,
                headerMenus: data.headerMenus,
                mainMenus: data.mainMenus,
                allMenus: data.allMenus,
                settingMenus: data.settingMenus,
                showHomeButtonFlag: data.showHomeButtonFlag
              })
            })
            .then(() => {
              this.$nextTick(() => {
                this.rootPath = this.allMenus.length ? this.allMenus[0].path : ''
                if (this.allMenus.length) {
                  // 如果to path 不再菜单中，那么取其同类中在菜单中的，把其设为选中
                  let targetPath = this.allMenus[0].subs.find(x => x.path === to)
                  if (!targetPath) {
                    let cateMenus = menuHelper.getAllAvailableMenus().filter(x => x.func === func)
                    cateMenus.forEach(x => {
                      let obj = this.allMenus[0].subs.find(y => y.path === x.path)
                      if (obj) {
                        this.activePath = obj.path
                      }
                    })
                    if (!this.activePath && this.allMenus[0].subs) {
                      this.activePath = this.allMenus[0].subs[0].path
                    }
                  } else {
                    this.activePath = this.$route.path
                  }
                }
              })
            })
            .catch(() => {})
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapState('appState', {
      allMenus: state => state.allMenus
    }),
    ...mapState('sidebar', {
      isCollapse: state => state.isCollapse
    })
  },
  created() {

  },
  methods: {
    ...mapActions('appState', ['setMenus']),
    ...mapActions('sidebar', ['setCollapse']),

    selectAction(value) {
      this.activePath = value
    },
    collapseClick() {
      this.setCollapse(!this.isCollapse)
    },
    handleOpen(key, keyPath) {
      // console.log(key, keyPath)
    },
    handleClose(key, keyPath) {
      this.$refs.mainMenu.open(key)
    }
  },
  mounted() {}
}
