<template>
  <div class="entry-main-bg border-radius-4">
    <img class="title-img" :src="titleSrc" height="54px">
    <slot></slot>

  </div>
</template>

<script>
export default {
  name: 'app-func-entry-wrapper',
  props: {
    titleSrc: { default: '/img/funcpage/icon-title-batch-benethrough.png', type: String }
  },
  data() {
    return {

    }
  }
}
</script>

<style scoped lang="scss">
.entry-main-bg {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 30px;
  flex-direction: column;
  .title-img {
    margin-bottom: 25px;
  }
}


</style>
