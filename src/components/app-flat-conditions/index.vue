<template>
  <div class="app-flat-conditions">
    <div class="conditions-item" :class="handleItemClass(item)" v-for="item in conditions" :key="item.name" @click="handleClick(item)">
      <span>{{ item.name }}</span><span v-if="showCount">({{ item.count }})</span>
      <slot name="extra" :item="item"></slot>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'
export default {
  name: 'app-flat-conditions',
  props: {
    modelValue: {
      type: Array
    },
    list: {
      type: Array,
      default: () => ([])
    },
    showCount: {
      type: Boolean,
      default: true
    },
    showAll: {
      type: Boolean,
      default: true
    }
  },
  model: {
    prop: 'modelValue',
    event: 'modelChange'
  },
  computed: {
    conditions () {
      let result = [...this.list]
      if (this.showAll) {
        result.unshift({
          name: '全部',
          count: result.reduce((sum, item) => {
            return sum + parseInt(item.count || 0)
          }, 0),
          value: this.allOptionValue
        })
      }
      return result
    }
  },
  data () {
    return {
      value: [],
      allOptionValue: 'all'
    }
  },
  watch: {
    modelValue (val) {
      this.value = val
    }
  },
  methods: {
    handleItemClass (item) {
      return [
        { disabled: item.count === 0 },
        {
          active: this.value.length > 0
            ? _.isArray(item.value) ? _.intersection(this.value, item.value).length > 0 : this.value.includes(item.value)
            : (item.value === this.allOptionValue)
        }
      ]
    },
    handleClick (item) {
      if (item.count === 0) {
        return
      }
      this.value = _.isArray(item.value) ? item.value : [item.value]
      if (this.showAll && item.value === this.allOptionValue) {
        this.value = []
      }
      this.$emit('modelChange', this.value)
      this.$emit('change', this.value)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-flat-conditions {
  display: flex;
  flex-wrap: wrap;
}
.conditions-item {
  height: 28px;
  background: #F7F7F7;
  padding: 3px 10px;
  border-radius: 2px;
  color: #333333;
  cursor: pointer;
  line-height: 22px;
  display: flex;
  margin-top: 5px;
  &.conditions-item {
    margin-right: 10px;
  }
  &.disabled {
    background: #F7F7F7;
    color: #999999;
    cursor: not-allowed;
  }
  &.active {
    background: #E2F1FD;
    color: #128BED;
  }
  &:not(.disabled):hover {
    background: #E2F1FD;
  }
}
</style>
