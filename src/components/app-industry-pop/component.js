export default {
  name: 'app-industry-pop',
  props: {
    industry: Object
  },
  data () {
    return {
      placement: 'bottomRight',
      visible: false
    }
  },
  inject: {
    navInfo: {
      default: null
    },
    tab: {
      default: null
    }
  },
  computed: {
    industryList () {
      const strList = []
      if (this.industry) {
        const { Industry: An, SubIndustry: Bn, MiddleCategory: Cn, SmallCategory: Dn } = this.industry
        if (An) {
          strList.push(An)
        }
        if (Bn) {
          strList.push(Bn)
        }
        if (Cn) {
          strList.push(Cn)
        }
        if (Dn && Cn !== Dn) {
          strList.push(Dn)
        }
      }
      return strList
    },
    industryCode () {
      return this.industry?.IndustryCode + (this.industry?.SmallCategoryCode || this.industry?.MiddleCategoryCode || this.industry?.SubIndustryCode || '')
    }
  },
  methods: {
    visibleChange (visible) {
      this.visible = visible
    }
  }
}
