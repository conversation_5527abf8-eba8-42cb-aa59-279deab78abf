<template>
  <a-popover
    v-if="industry"
    v-model="visible"
    :destroy-tooltip-on-hide="false"
    :mouse-enter-delay="0.2"
    overlay-class-name="app-industry-pop-modal"
    :placement="placement"
    max-width="none"
    @visibleChange="visibleChange"
  >
    <template v-slot:content>
      <span v-for="(item,index) in industryList" :key="'industryItem'+index">
          <span>{{ item }}</span><app-icon v-if="!(index===industryList.length-1)" type="wenzilianjiantou" class="arrow" />
      </span><span v-if="industryCode" class="text-gray">（{{ industryCode }}）</span>
    </template>
    <div :class="['app-industry-pop']">
      <span v-if="industry">{{ industry.Industry || industry.SubIndustry || industry.MiddleCategory || industry.SmallCategory || '-' }}</span>
    </div>
  </a-popover>
  <div v-else class="app-industry-pop">-</div>
</template>

<script src="./component.js"></script>

<style src="./style.scss" lang='scss'></style>
