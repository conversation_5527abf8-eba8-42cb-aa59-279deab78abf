/**
* @Description:
* <AUTHOR>
* @date 2019/8/7 13:55
*/
<template>
  <div style="display: inline-block;width: 100%;position: relative" :class="`inputNumber-${size}`">
    <el-input-number v-model="modelBind" ref="input" @change="changeInput" :size="size" :disabled="disabled"
      :placeholder="placeholder" @blur="blurInput" @focus="focusInput" controls-position="right" :step="getStep"
      :controls="controls" :min="options.min" :max="options.max || undefined" :precision="precision"
      class="_inputNumberComp"></el-input-number>
    <slot></slot>
    <span v-if="percentText" class="__percent-text">%</span>
    <span v-if="clearable" class="el-icon-error el-icon-error">%</span>
  </div>
</template>

<script>
import { myModelMixins } from '../../utils/mixinsUtils'

export default {
  name: 'app-input-number',
  mixins: [myModelMixins],
  props: {
    controls: { default: false, type: Boolean },
    size: { default: 'large' },
    disabled: { default: false },
    placeholder: { defult: '请输入' },
    hasDefault: { default: false },
    clearable: { default: false },
    keyEnter: {},
    precision: {}, // 精度
    percentText: { default: false, type: Boolean }, // 精度
    options: {
      default() {
        return { min: 0, max: 100000000 }
      }
    }
  },
  data() {
    return {
      modelBind: ''
    }
  },
  computed: {
    getMin() {
      return this.options.min || 0
    },
    getMax() {
      return this.options.max || 0
    },
    getStep() {
      return this.options.step || undefined
    }
  },
  methods: {
    changeInput(val) {
      this._changeModel(val)
      this.$emit('change', val)
    },
    click() {
      this.$emit('click')
    },
    blurInput(e) {
      if (!this.hasDefault) {
        if (e.target.value === '') {
          this._changeModel(this.options.min || 0)
        }
      }

      this.$emit('blurInput', e)
    },
    focusInput(e) {
      this.$emit('focusInput', e)
    },
    onKeyDownListener(e) {
      if (e.keyCode === 13) {
        if (this.keyEnter) {
          this.keyEnter(e)
        } else {
          this.$emit('keyEnter', e)
        }
      }
    }
  },
  mounted() {
    var nativeInput = this.$refs.input
    if (nativeInput) {
      // if (isIE()){
      //   nativeInput.value = this.modelBind
      // }
    }

    // nativeInput.handleInput = (value) => {
    //   let vv = +value || 0
    //   if (this.options.max <= vv) {
    //     nativeInput.userInput = this.options.max
    //   } else {
    //     nativeInput.userInput = vv
    //   }
    //   this.$emit('input', value)
    // }

    this.$refs.input.$refs.input.$refs.input.onkeyup = function (value) {
      value.target.value = value.target.value.replace(/[^0-9\.]/g, '') // eslint-disable-line
    }
  }
}

</script>

<style lang="scss" src="./index.scss"></style>
