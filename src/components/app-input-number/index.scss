@mixin baseInputNumber($height, $paddingLeft:10px, $numberLabelWidth: 18px) {
  ._inputNumberComp {
    height: $height;
    line-height: $height - 2px;
    padding: 0;
    width: 100%;

    .el-input-number__increase,
    .el-input-number__decrease {
      line-height: ($height - 2px)/2 !important;
      width: $numberLabelWidth;
      //border-right: 1px solid #dcdfe6;
    }

    div.el-input {
      height: 100%;
      line-height: 100%;

      input {
        height: 100%;
        line-height: 100%
      }
    }

    .el-input-number__increase {
      border-top-right-radius: 3px !important;
    }

    .el-input-number__decrease {
      border-bottom-right-radius: 3px !important;
    }

    .el-input__inner {
      padding-left: $paddingLeft;
      padding-right: $paddingLeft + 5px;
      text-align: left;
    }
  }
  .__percent-text {
    line-height: $height;
  }
}

.inputNumber-small {
  @include baseInputNumber(32px);
}

.inputNumber-medium {
  @include baseInputNumber(36px);

  &.lin70 {
    ._inputNumberComp {
      width: 70px;
    }
  }
}

.inputNumber-large {
  @include baseInputNumber(40px, 8px);
}

.inputNumber-mini {
  @include baseInputNumber(26px, 3px, 14px);
}

.__percent-text {
  position: absolute;
  right: 24px;
  top: 0;
  color: #999999;
  font-size: 14px;
  line-height: 40px;
}
