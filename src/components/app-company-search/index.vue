<template>
  <div class="_app-company-search">
    <app-autocomplete ref="search" :size="size" :placeholder="placeholder" searchFromNet :icon="icon" selectedShowKey="nativeName"
                      :querySearchData="querySearchData" nameKey="name" valueKey="keyNo" @keyEnter="doSearchList"
                      @change="autoCompleteChange" @iconClick="doSearchList" :clearable="clearable" :closeIconSize="closeIconSize"></app-autocomplete>
  </div>
</template>
<script>
import { searchAutocompleteWithImgReason } from '../../services/enterprise-center/company-list'
import { requestService } from '../../services/broswer-service'
export default {
  name: 'app-company-search',
  props: {
    placeholder: { default: '请输入企业名称查看企业详情', type: String },
    size: { default: 'medium', type: String },
    icon: { default: 'el-icon-search', type: String },
    isHK: { default: false, type: <PERSON>olean },
    clearable: { default: false, type: Boolean },
    functionTableId: { default: 'bene_id', type: String },
    closeIconSize: { default: '12', type: String }
  },
  data() {
    return {
      select: ''
    }
  },
  methods: {

    doSearchList(val) {
      // if (val.replace(/\s+/g, '').length > 1) {
      //   this.$router.replace({ name: 'search', query: { searchkey: val } })
      // } else {
      //   this.$message({
      //     message: '请输入至少两个字符',
      //     type: 'warning'
      //   })
      // }
    },
    querySearchData(text, call) {
      this.getRequestService(text).then(res => {
        if (res.status === '200') {
          const list = res.resultList.length > 5 ? res.resultList.slice(0, 5) : res.resultList
          list.forEach((item) => {
            const matchArr = item.name.match(/[^<em></em>]+(?=<\/em>)/g)
            if (matchArr && matchArr.join('').length >= text.length) {
              item.reason = '公司名称'
            }
          })
          call(list)
        } else {
          call()
        }
      }).catch(e => {
        call()
      })
    },
    getRequestService(text) {
      const params = {
        searchKey: text
      }
      if (this.functionTableId === 'radar_id') {
        params.from = 'monitor'
      }
      return this.isHK ? requestService.searchAutocompleteHK(params) : searchAutocompleteWithImgReason(params)
    },
    autoCompleteChange(val) {
      // console.log(val)
      this.select = val || ''
      this.$emit('select', val)
    },
    getCurrentSelect() {
      if (this.$refs.search.getInputContent().trim()) {
        return this.$refs.search.getInputContent().trim()
      }
      return undefined
    },
    getCurrentInput() {
      return this.$refs.search.getInputContent().trim()
    },
    clearContent() {
      this.$refs.search.clearInputContent()
    }
  },
  mounted() {
  }
}

</script>
<style lang="scss">

</style>
