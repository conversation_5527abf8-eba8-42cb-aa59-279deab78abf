<template>
  <app-popup
    :button-options="options.buttonOptions"
    :options="options"
    :title="title"
    :control="control"
    @closed="close">
    <el-form
      :class="options.label ? '' : 'nolabel'"
      ref="formModelRef"
      :model="formModel"
      :label-width="labelWidth"
      :rules="formRules">
      <el-form-item :prop="formRules.inputText ? 'inputText' : ''" :label="options.label" style="line-height: unset">
        <el-input
          :type="rows === 1 ? 'input' : 'textarea'"
          resize="none"
          :rows="rows"
          v-model="formModel.inputText"
          ref="_dialogInputId"
          :maxlength="maxLength"
          :placeholder="options.placeholder"></el-input>
      </el-form-item>
    </el-form>
  </app-popup>

</template>

<script>
export default {
  name: 'app-input-dialog',
  data() {
    return {
      control: {},
      formRules: {},
      formModel: {
        inputText: ''
      }
    }
  },
  computed: {
    labelWidth: function () {
      return this.options.label ? ((this.options.label.length * 14 + 12) + 'px') : '12px'
    },
    rows() {
      return this.options.inputRows || 5
    },
    maxLength() {
      return this.options.maxLength || 1000
    }
  },
  methods: {
    checkInputText: function () {
      this.$refs.formModelRef.validate(res => {
        if (res) {
          this.$promise.resolve(this.formModel.inputText, this)
          this.close()
        }
      })
    },
    close: function () {
      if (this.control.ref && this.control.ref.close) {
        this.control.ref.close()
      }
      setTimeout(() => {
        this.$modal.close()
      }, 200)
    },
    getInputFocus: function () {
      var that = this
      setTimeout(function () {
        if (that.$refs._dialogInputId) {
          that.$refs._dialogInputId.$refs.textarea.focus()
          that.$refs._dialogInputId.$refs.textarea.setSelectionRange(0, that.$refs._dialogInputId.$refs.textarea.value.length)
        }
      }, 1000)
    }
  },
  created: function () {
    if (this.options === undefined) {
      this.$set(this, 'options', {})
    }

    if (this.options.buttonOptions === undefined) {
      this.$set(this.options, 'buttonOptions', [
        {
          name: this.options.cancelText || '取消',
          size: 'large',
          type: ' ',
          plain: true,
          disabled: false,
          click: () => {
            this.$promise.reject()
            this.close()
          }
        },
        {
          name: this.options.sureText || '确定',
          size: 'large',
          type: 'primary',
          disabled: false,
          click: this.checkInputText
        }
      ])
    } else {
      this.buttonOptions.forEach((item) => {
        item.ref = this
      })
    }
    if (this.options.footerStyle === undefined) {
      this.$set(this.options, 'footerStyle', {})
    }
    this.$set(this.options.footerStyle, 'margin-top', '-22px')
    if (this.options.inputValidator) {
      this.$set(this.formRules, 'inputText', [{ validator: options.inputValidator, trigger: ['validate'] }])
    } else {
      this.$set(this.formRules, 'inputText', [{ validator: (rule, value, callback) => {
        if (!value || value.trim() === '') {
          callback(new Error(this.options.placeholder || '请输入'))
        } else {
          if (this.options.maxLength !== undefined && value.length > this.options.maxLength) {
            callback(new Error(this.options.maxLengthTips || `最多可输入${this.options.maxLength}个文字`))
          } else {
            callback()
          }
        }
      },
      trigger: ['validate'] }])
    }
    if (this.options && this.options.inputValue) {
      this.formModel.inputText = this.options.inputValue
    }
  }
}
</script>

<style lang="scss">
 .el-form.nolabel .el-form-item .el-form-item__content{
  margin-left: 0!important;
}
</style>
