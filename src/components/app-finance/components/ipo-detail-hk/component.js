import { getHKIPOInfo } from '../../../../routes/companyDetail/hk-stock/service'
export default {
  name: 'data-detail',

  props: {
    viewData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },

  data () {
    return {
      dataSource: {},
      relatedSec: '',
      companyStatus: {},
      popupOptions: {
        width: '1000px',
        contentStyle: 'padding:0px '
      }
    }
  },

  // watch: {
  //   viewData: {
  //     handler (_val = {}) {
  //       this.dataSource = _val.dataSource || {}
  //       this.fetchData()
  //     },
  //     immediate: true
  //   }
  // },
  mounted () {
    this.fetchData()
    this.$emit('loadSuccess')
  },
  methods: {
    fetchData () {
      // console.log(this.dataSource)
      const searchParams = {
        relatedSec: this.relatedSec
      }
      getHKIPOInfo(searchParams)
        .then((res) => {
          if (res.Status !== 200) {
            this.noData = true
            return
          }
          this.companyStatus = res?.Result ?? {}
        })
    }
  }
}
