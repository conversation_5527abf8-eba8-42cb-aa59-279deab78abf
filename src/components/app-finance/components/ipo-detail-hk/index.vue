<template>
  <app-popup ref="refPop" :title="title" :options="popupOptions" :buttonOptions="[]">
    <div v-if="dataSource.Round" class="component-detail">
      <!-- IOP -->
      <table class="ntable">
        <tr>
          <td width="22%" class="tb">招股开始</td>
          <td width="28%">{{ companyStatus.BeginDate | dateformat }}</td>
          <td width="22%" class="tb">招股结束</td>
          <td width="28%">{{ companyStatus.EndDate | dateformat }}</td>
        </tr>
        <tr>
          <td class="tb">定价日期</td>
          <td>{{ companyStatus.PricingDate | dateformat }}</td>
          <td class="tb">发行结果公告日期</td>
          <td>{{ companyStatus.IssueresultDate | dateformat }}</td>
        </tr>
        <tr>
          <td class="tb">暗盘交易日期</td>
          <td>{{ companyStatus.DarkDate | dateformat }}</td>
          <td class="tb">上市日期</td>
          <td>{{ companyStatus.ListDate | dateformat }}</td>
        </tr>
        <tr>
          <td class="tb">招股价格上限</td>
          <td>{{ companyStatus.PriceLimitUpper }}</td>
          <td class="tb">招股价格下限</td>
          <td>{{ companyStatus.PriceLimitLower }}</td>
        </tr>
        <tr>
          <td class="tb">发行价</td>
          <td>{{ companyStatus.IssuePrice || "-" }}</td>
          <td class="tb">入场费</td>
          <td>{{ companyStatus.AdmissionFee || "-" }}</td>
        </tr>
        <tr>
          <td class="tb">每手股数</td>
          <td>{{ companyStatus.LotSize || "-" }}</td>
          <td class="tb">申购一手中签率</td>
          <td>{{ companyStatus.LotWinningRate || "-" }}</td>
        </tr>
        <tr>
          <td class="tb">全球发售股数</td>
          <td>{{ companyStatus.IssueNum || "-" }}</td>
          <td class="tb">认购倍数</td>
          <td>{{ companyStatus.OversubscrMultiple || "-" }}</td>
        </tr>
        <tr>
          <td class="tb">香港公开发售数目</td>
          <td>{{ companyStatus.HkIssueNum || "-" }}</td>
          <td class="tb">国际配售数目</td>
          <td>{{ companyStatus.IntIssueNum || "-" }}</td>
        </tr>
        <tr>
          <td class="tb">首发面值</td>
          <td>{{ companyStatus.IssueParvalue || "-" }}</td>
          <td class="tb">承销方式</td>
          <td>{{ companyStatus.IssueMethod || "-" }}</td>
        </tr>
        <tr>
          <td class="tb">计划募资总额</td>
          <td>{{ companyStatus.PlanRaisefundsTotal || "-" }}</td>
          <td class="tb">计划募资净额</td>
          <td>{{ companyStatus.PlanRaisefundsNet || "-" }}</td>
        </tr>
        <tr>
          <td class="tb">实际募资总额</td>
          <td>{{ companyStatus.RaisefundsTotal || "-" }}</td>
          <td class="tb">实际募资净额</td>
          <td>{{ companyStatus.RaisefundsNet || "-" }}</td>
        </tr>
      </table>
    </div>
    <app-nodata v-else style="border: 1px solid #e4eef6" :padding="31" />
  </app-popup>
</template>

<script src="./component.js"></script>
