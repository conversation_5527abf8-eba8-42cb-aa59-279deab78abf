<template>
  <app-popup ref="refPop" :title="title" :options="popupOptions" :buttonOptions="[]">
    <div v-if="dataSource.Round" class="component-detail">
      <!-- 上市辅导 核准制 -->
      <table v-if="dataSource.Round === '新股申报' && dataSource.ListSection === 1" class="ntable">
        <tr>
          <td width="22%" class="tb">上会状态</td>
          <td width="26%">{{ companyStatus.PermissionStatus || '-' }}</td>
          <td width="24%" class="tb">拟上市地</td>
          <td width="28%">{{ dataSource.ListSectionDesc || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">发行前总股本</td>
          <td>{{ companyStatus.TotalSharesBeforeIPO ? companyStatus.TotalSharesBeforeIPO + '万股' : '-' }}</td>
          <td class="tb">预计发行后总股本</td>
          <td>{{ companyStatus.TotalSharesAfterIPO ? companyStatus.TotalSharesAfterIPO + '万股' : '-' }}</td>
        </tr>
        <tr>
          <td class="tb">预计发行股数</td>
          <td>{{ companyStatus.IssueSharesPlan ? companyStatus.IssueSharesPlan + '万股' : '-' }}</td>
          <td class="tb">占发行后总股本比</td>
          <td>{{ companyStatus.IssueSharesPlanRatio ? companyStatus.IssueSharesPlanRatio + '%' : '-' }}</td>
        </tr>
        <tr>
          <td class="tb">主承销商</td>
          <td>
            <app-coy :coy-arr="companyStatus.MainUnderwriterList" />
          </td>
          <td class="tb">承销方式</td>
          <td>{{ companyStatus.UnderwritingMethod || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">发审委委员</td>
          <td colspan="3">
            {{ companyStatus.IssuanceAppraisalCommitteeMember || '-' }}
          </td>
        </tr>
        <tr>
          <td class="tb">利润分配方案</td>
          <td colspan="3">
            {{ companyStatus.DividendPlan || '-' }}
          </td>
        </tr>
      </table>
      <!-- IOP -->
      <table v-if="dataSource.Round === 'A股IPO'" class="ntable">
        <tr>
          <td width="22%" class="tb">成立日期</td>
          <td width="28%">{{ companyStatus.EstablishDate| dateformat }}</td>
          <td width="22%" class="tb">上市日期</td>
          <td width="28%">{{ companyStatus.ListDate | dateformat }}</td>
        </tr>
        <tr>
          <td class="tb">发行总量</td>
          <td>{{ companyStatus.IssueNum ? companyStatus.IssueNum + '股' : '-' }}</td>
          <td class="tb">每股发行价</td>
          <td>{{ companyStatus.IssuePrice || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">定价中签率</td>
          <td>{{ companyStatus.IssueOnlineRate || '-' }}</td>
          <td class="tb">网下配售中签率</td>
          <td>{{ companyStatus.IssueOfflineRate || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">网上发行日期</td>
          <td>{{ companyStatus.IssueDateOnline | dateformat }}</td>
          <td class="tb">发行方式</td>
          <td>{{ companyStatus.IssueMethod || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">发行总市值</td>
          <td>{{ companyStatus.IssueMarketValue || '-' }}</td>
          <td class="tb">募集资金净额</td>
          <td>{{ companyStatus.IssueActualAmount || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">首日开盘价</td>
          <td>{{ companyStatus.OpenPriceFirstDay || '-' }}</td>
          <td class="tb">首日收盘价</td>
          <td>{{ companyStatus.ClosePriceFirstDay || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">发行市盈率</td>
          <td>{{ companyStatus.IssuePERatio || '-' }}</td>
          <td class="tb">首日换手率</td>
          <td>{{ companyStatus.ExchangeRateFirstDay || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">每股面值</td>
          <td colspan="3">{{ companyStatus.IssueParValue || '-' }}</td>
        </tr>
      </table>
      <table v-if="dataSource.Round === '新股申报' && [3,4].includes(dataSource.ListSection)" class="ntable">
        <tr>
          <td width="22%" class="tb">审核状态</td>
          <td width="28%">{{ companyStatus.RegisterStatus || '-' }}</td>
          <td width="20%" class="tb">评估机构</td>
          <td width="30%">
            <app-coy :coy-arr="companyStatus.AssetEvaluatInstitution || '-' " />
          </td>
        </tr>
        <tr>
          <td class="tb">融资金额</td>
          <td>{{ companyStatus.FinanceAmount ? companyStatus.FinanceAmount + '亿元' : '-' }}</td>
          <td class="tb">证监会行业</td>
          <td>{{ companyStatus.IndustryName || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">受理日期</td>
          <td>{{ companyStatus.ApprovalDate| dateformat }}</td>
          <td class="tb">保荐机构</td>
          <td>
            <app-coy :coy-arr="companyStatus.SponsorInstitution" />
          </td>
        </tr>
        <tr>
          <td class="tb">更新日期</td>
          <td>{{ companyStatus.StatusUpdateDate| dateformat }}</td>
          <td class="tb">律师事务所</td>
          <td>
            <app-coy :coy-obj="companyStatus.LawFirm" />
          </td>
        </tr>
        <tr>
          <td class="tb">发行前总股本</td>
          <td>{{ companyStatus.TotalSharesBeforeIPO ? companyStatus.TotalSharesBeforeIPO + '万股' : '-' }}</td>
          <td class="tb">会计师事务所</td>
          <td>
            <app-coy :coy-obj="companyStatus.AccountingFirm" />
          </td>
        </tr>
        <tr>
          <td class="tb">拟发行后总股本</td>
          <td>{{ companyStatus.TotalSharesAfterIPO ? companyStatus.TotalSharesAfterIPO + '万股' : '-' }}</td>
          <td class="tb">保荐代表人</td>
          <td>{{ companyStatus.SponsorRepresentative || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">拟发行股份数</td>
          <td>{{ companyStatus.IssueSharesPlan ? companyStatus.IssueSharesPlan + '万股' : '-' }}</td>
          <td class="tb">签字律师</td>
          <td>{{ companyStatus.SignedLawyer || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">占总股本比例</td>
          <td>{{ companyStatus.IssueSharesPlanRatio ? companyStatus.IssueSharesPlanRatio + '%' : '-' }}</td>
          <td class="tb">签字会计师</td>
          <td>{{ companyStatus.SignedAccountant || '-' }}</td>
        </tr>
        <tr>
          <td class="tb">注册地</td>
          <td>{{ companyStatus.RegisterAddress || '-' }}</td>
          <td class="tb">签字评估师</td>
          <td>{{ companyStatus.SignedValuer || '-' }}</td>
        </tr>
      </table>
    </div>
    <app-nodata
      v-else
      style="border: 1px solid #e4eef6"
      :padding="31"
    />
  </app-popup>
</template>

<script src="./component.js"></script>

<style scoped lang="scss">
.ntable {
  margin-bottom: 0;

  & + .ntable {
   margin-top: 20px;
  }
}
</style>
