import { fetchAStockIPOInfo, fetchAStockListingGuidance } from '../../../../routes/companyDetail/ipo/service'

export default {
  name: 'app-finance-ipo-detail',

  props: {
    viewData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },

  data () {
    return {
      title: '',
      popupOptions: {
        width: '1000px',
        contentStyle: 'padding:0px '
      },
      dataSource: {},
      companyStatus: {}
    }
  },

  watch: {
    // viewData: {
    //   handler (_val = {}) {
    //     this.dataSource = _val.dataSource || {}
    //     if (_val.dataSource) this.fetchData()
    //   },
    //   immediate: true
    // }
  },
  mounted () {
    // this.$emit('loadSuccess')
    this.fetchData()
  },
  methods: {
    fetchData () {
      const reqApi = this.dataSource.Round === 'A股IPO' ? fetchAStockIPOInfo : fetchAStockListingGuidance
      const searchParams = {
        keyNo: this.dataSource.KeyNo
      }
      if (this.dataSource.Round !== 'A股IPO') {
        searchParams.type = this.dataSource.ListSection === 1 ? 'permission' : 'register'//  1 permission-核准制   3/4 register-注册制",
      }
      reqApi(searchParams).then((res) => {
        if (res.Status !== 200) {
          return
        }
        this.companyStatus = res?.Result ?? {}
      })
    }
  }
}
