<template>
  <div class="app-per-oversea" v-if="alreadyYouKnow" :style="getStyle()">
    <div class="app-per-oversea-sub">
      <div class="o-img-con">
        <img src="./tip.png" alt="">
      </div>
      <template v-if="forbiddenOverseaIpFlag">
        <div class="fg fg-bold margin-b15">根据相关法律法规，企查查专业版仅支持中国大陆地区IP访问（不包含港澳台）您目前为非大陆IP，请确认IP是否异常</div>
      </template>
      <template v-else>
        <div class="fg">根据相关法律法规，企查查不支持中国大陆地区(不包含港、澳、台)以外IP访问您目前为非大陆IP，访问可能会受到影响，请确认IP是否异常</div>
        <div class="i-know">
          <app-button class="i-know-btn" @click="iknow">我知道了</app-button>
        </div>
      </template>

      <div><span class="color-999">客服热线：</span><a>400-088-8275</a></div>
      <div class=" margin-10 "><span class="color-999">客服工作时间：周一至周五 09:00-20:00</span></div>
    </div>
  </div>
</template>

<script>
  // 这是对海外的限制，以后可以应用到其他权限设定显示上
  import commonService from '../../services/common-service'
  import { fetchUser, saveUser } from '../../utils/localstorage'
  export default {
    name: 'app-permission-oversea',
    data() {
      return {
        isPlugin: __QCC__ && __PLUGIN__,
        alreadyYouKnow: $util.isNotPermission4OverseaUser(),
        forbiddenOverseaIpFlag: $util.forbiddenOverseaIpFlag()
      }
    },
    created() {

    },
    methods: {
      getStyle() {
        let ss = ''
        if (this.isNewVersion) {
          ss = ''
        } else if (this.isPlugin) {
          ss = 'z-index: 100000000000;background: #fff;'
        } else {
          ss = 'margin-top:50px'
        }

        return ss
      },
      iknow() {
        commonService.postVue('/webapi/saas/common/user/confirmOverseaIpLimit', {}).then(() => {
          const user = fetchUser()
          user.showOverseaIpLimitFlag = !user.showOverseaIpLimitFlag
          saveUser(user)
          location.reload()
        })
      }
    }
  }

</script>
<style lang="scss">
  .app-per-oversea {
    height: 100%;
    width: 100%;
    letter-spacing: 0em;
    color: #333333;
    position: relative;

    .app-per-oversea-sub {
      width: 490px;
      position: absolute;
      top: calc((100% - 5px)*0.40);
      left: 50%;
      transform: translateX(-50%)translateY(-50%);
      text-align: center;

      .o-img-con {
        margin-bottom: 12px;

        img {
          width: 60px;
        }
      }


      .color-999 {
        color: #999;
      }

      .margin-10 {
        margin-top: 10px;
      }

      .margin-b15 {
        margin-bottom: 15px;
      }

      .fg {
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
      }

      .fg-bold {
        // font-weight: bold;
      }

      .i-know {
        margin: 20px 0px 15px 0px;

        .i-know-btn {
          height: 32px !important;
          line-height: 30px !important;
        }
      }
    }
  }

</style>
