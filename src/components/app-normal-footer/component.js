export default {
  name: 'comfooter',
  props: ['showFooter'],
  data() {
    return {
      titleList: [
        {
          title: '2014-2025'
        },
        {
          title: '苏ICP备15042526号-5',
          url: 'https://beian.miit.gov.cn/',
          enable: true
        },
        {
          title: '版权所有'
        },
        {
          title: '企查查科技股份有限公司'
        },
        {
          title: '增值电信业务经营许可证：苏ICP证B2-20180251'
        }
      ]
    }
  },
  computed: {
    show: function() {
      this.$emit('update:showFooter', __QCC__ || __QCCPRO_G__)
      return __QCC__ || __QCCPRO_G__
    }
  },
  methods: {
    skipOtherPage: function(url) {
      if ($formatter.isBlank(url)) {
        return
      }
      window.open(url)
    }
  },

  components: {}
}
