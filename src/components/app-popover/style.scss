.__app-popover-body {
  //width: 100px;
  //height: 100px;
  //background: red;
  display: inline-flex;
  align-items: center;
  position: relative;
  cursor: pointer;

 ::v-deep .caret {
    display: inline-block;
    width: 0;
    height: 0;
    vertical-align: middle;
    border-top: 4px dashed #666666;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
    transition: all linear 0.2s;
    &.inner {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
    }

    &.rotate {
      border-top-color: #128BED;
      transform:  translateY(-50%)  rotate(180deg);
    }
    &.actived {
      border-top-color: #128BED;
    }
  }

  .popover-content {
    position: absolute;
    white-space: nowrap;
    background: #fff;
    //border: solid 1px #E4E7ED;
    border-radius: 4px;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.1);
    width: auto;
    z-index: 999;
    &.placement-bottom-start {
      left: 0;
      top: 100%;
      margin-top: 2px;
    }
    &.placement-bottom-end {
      right: 0;
      top: 100%;
      margin-top: 2px;
    }
    &.placement-top-start {
      left: 0;
      bottom: 100%;
      margin-bottom: 2px;
    }
    &.placement-top-end {
      right: 0;
      bottom: 100%;
      margin-bottom: 2px;
    }
  }
}
