import _ from 'lodash'
import myclickOutside from '../app-select-dropmenu/clickOutSide'
export default {
  name: 'app-popover',
  directives: { myclickOutside: myclickOutside },
  props: {
    visiable: { default: true, type: Boolean },
    placement: { default: 'bottom-start', type: String }, // bottom-start,bottom-end,top-start,top-end
    caret: { default: false, type: Boolean },
    caretInner: { default: false, type: Boolean }, // 箭头是否在框内
    caretActived: { default: false, type: Boolean },
    trigger: {
      type: String,
      default: 'hover',
      validator: value => ['click', 'focus', 'hover', 'manual'].indexOf(value) > -1
    }
  },
  model: {
    prop: 'visiable'
  },
  data() {
    return {
      contentInit: this.visiable,
      show: false
    }
  },
  watch: {
    visiable(val) {
      if (!val) {
        this.onPopContentChange(false)
      }
    }
  },
  created() {
    this.onPopContentChange = _.debounce(this._onMouneEventChange, 150)
  },
  methods: {
    mouseMoveEvent(is) {
      if (this.trigger === 'hover') {
        this._onMouneEventChange(is)
      }
    },
    _onMouneEventChange(is) {
      // console.log('_onMouneEventChange=>' + is)
      this.contentInit = true
      this.show = is
      this.$emit('input', this.show)
      this.$emit(is ? 'show' : 'hide')
    },
    closeDropMenu(isReset) {
      if (!this.show) return
      this.onPopContentChange = function () {}
      setTimeout(() => {
        this.show = false
        this.$emit('input', this.show)
        setTimeout(() => {
          this.onPopContentChange = _.debounce(this._onMouneEventChange, 150)
        }, 600)
      }, 200)
      this.$emit('hide')
      this.$emit('close')
    },
    handleMenuEnter() {},
    doDestroy() {}
  }
}
