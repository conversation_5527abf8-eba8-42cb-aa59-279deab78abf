<template>
  <div class="__app-popover-body"
       @mouseenter="mouseMoveEvent(true)"
       @mouseover="mouseMoveEvent(true)"
       @mouseleave="mouseMoveEvent(false)"
       @click="onPopContentChange(true)"
       v-myclick-outside="closeDropMenu">
    <slot name="reference"></slot>
    <template v-if="caret">
      <span :class="['caret', {inner: caretInner}, {rotate: show}, {actived: caretActived}]"></span>
    </template>
    <transition name="el-zoom-in-top" :beforeEnter="handleMenuEnter" :afterLeave="doDestroy">
      <div class="popover-content" :class="[`placement-${placement}`]" v-if="contentInit" v-show="show">
        <slot></slot>
      </div>
    </transition>
  </div>
</template>
<script src="./component.js"></script>

<style scoped lang="scss" src="./style.scss"></style>
