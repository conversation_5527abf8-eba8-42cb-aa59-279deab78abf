<script>
export default {
  name: 'app-file',
  props: {
    type: {
      type: String,
      default: ''
    },
    url: {
      type: String,
      default: ''
    },
    disabledSkip: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
    }
  },
  computed: {
    fileIcon () {
      let type = this.type
      if (!type && this.url) {
        type = $util.getFileExtension(this.url)
      }
      switch (type) {
        case 'link':
        case 'ie':
          return require('../../assets/images/file/link.svg')
        case 'rar':
        case 'zip':
        case '7z':
        case '7zip':
        case 'tar':
        case 'gz':
          return require('../../assets/images/file/rar.svg')
        case 'doc':
        case 'docx':
        case 'wps':
        case 'word':
          return require('../../assets/images/file/word.svg')
        case 'excel':
        case 'xls':
        case 'xlsx':
          return require('../../assets/images/file/excel.svg')
        case 'ppt':
          return require('../../assets/images/file/ppt.svg')
        case 'pdf':
          return require('../../assets/images/file/pdf.svg')
        case 'txt':
          return require('../../assets/images/file/txt.svg')
        case 'jpeg':
        case 'gif':
        case 'png':
        case 'jpg':
        case 'bmp':
          return require('../../assets/images/file/img.svg')
        case 'other':
          return require('../../assets/images/file/other.svg')
      }
    }
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
  }
}
</script>

<template>
  <component
    href="javascript:;"
    class="app-file"
    v-bind="$attrs"
    v-on="$listeners"
    :is="disabledSkip ? 'div' : 'a'"
    :class="[{
      'disabled-skip': disabledSkip,
      'hover-link': disabledSkip
    }]"
  >
    <div class="app-file__icon">
      <slot name="icon">
        <img :src="fileIcon" v-if="fileIcon">
      </slot>
    </div>
    <div class="app-file__text">
      <slot></slot>
    </div>
    <div class="app-file__extra" v-if="$scopedSlots.extra">
      <slot name="extra"></slot>
    </div>
  </component>
</template>

<style scoped lang="scss">
.app-file {
  display: inline-flex;
  line-height: 22px;
  justify-content: center;
  align-items: flex-start;

  &__icon {
    height: 16px;
    margin-top: 3px;

    img {
      //width: 14px;
    }
  }

  &__text {
    margin-left: 5px;
  }

  &__extra {
    margin-left: 5px;
    margin-top: 3px;
  }

  &.disabled-skip {
    color: #333;

    &:hover {
      color: #333;
    }
  }
}
</style>
