<template>
  <section class="border-radius-4 app-panel" :class="isNew ? 'app-panel-new' : ''">
    <div :class="{ 'title': true, 'small-title': smallTitle, 'padding-h-0-6x': !isNew, 'border-radius-tl-tr-4': !isNew }"
      v-if="title || $slots.title">
      <slot name='title'>{{ title }}</slot>
    </div>
    <div class="content border-radius-bl-br-4" :style="style">
      <slot>
        <app-nodata v-if="showNodata" text='暂无数据' class="none"></app-nodata>
      </slot>
    </div>
  </section>
</template>

<script>
export default {
  name: 'app-panel',
  props: {
    height: {
      type: [String, Number],
      default: 'auto'
    },
    title: {
      type: [String, Number],
      default: ''
    },
    showNodata: {
      type: Boolean,
      default: true
    },
    isNew: {
      type: Boolean,
      default: false
    },
    smallTitle: {
      type: Boolean,
      default: false
    },
    contentStyle: { default: () => { return {} }, type: Object },
    minHeight: {
      type: [String, Number],
      default: 0
    }

  },
  computed: {
    style() {
      const { height, minHeight } = this
      return {
        height: height === 'auto' ? 'auto' : `${height}px`,
        ...this.contentStyle,
        minHeight: `${minHeight}px`
      }
    }
  }
}

</script>

<style lang="scss" scoped>
@import '../../styles/common.scss';

.title {
  @include flex-def;
  @include flex-zBetween;
  @include flex-cCenter;
  height: 45px;
  color: $base-black-color;
  line-height: 19px;
  background-color: $base-white-color-1;
  border-bottom: 1px solid #eee;
  box-sizing: border-box;
  font-size: 14px;
}

.content {
  background-color: $base-white-color;
  // overflow: hidden;
  position: relative;

  .none {
    position: absolute !important;
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%;
  }
}


.app-panel-new {
  border: 1px solid #EEEEEE;
  border-radius: 2px;
  box-sizing: border-box;
  background: #FFFFFF;

  .title {
    line-height: 24px;
    color: #333333;

    background-color: $base-white-color;
    border-bottom-width: 0px;
    height: 52px;
    padding: 0px 15px;

    ::v-deep {
      .left-title {
        font-size: 16px;
        font-weight: bold;
      }
    }
  }

  .small-title {
    height: 42px;
  }
}
</style>
