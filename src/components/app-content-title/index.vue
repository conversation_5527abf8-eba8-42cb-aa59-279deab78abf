<template>
  <div class="app-content-title">
    <a @click="back" class="app-content-t-link">
      <img src="../../assets/images/ic_reply.png" />&nbsp;
      <span v-html="title"></span>
    </a>
    <span class="t-spli">{{separator}} </span>
    <slot>
      <span v-html="text"></span>
    </slot>
    <slot name="func">
    </slot>
  </div>
</template>

<script>
  export default {
    name: 'app-content-title',
    props: {
      title: { default: '', type: String },
      separator: { default: '/', type: String },
      text: { default: '', type: String }
    },
    data() {
      return {}
    },
    methods: {
      back() {
        this.$emit('back')
      }
    }
  }

</script>

<style scoped lang="scss">
  .app-content-title {
    padding: 5px 15px 15px 15px;
    font-size: 14px;
    display: flex;
    align-items: center;
    color: #333;

    .app-content-t-link {
      display: flex;
      align-items: center;
    }

    .t-spli {
      color: #999999;
      margin: 0px 5px;
    }
  }

</style>
