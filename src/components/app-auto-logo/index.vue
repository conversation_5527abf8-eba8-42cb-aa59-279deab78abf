<script>
import _ from 'lodash'
import familyNames from '@/store/family-names'
import qs from '@/core/query-string'
import { deviceInfo } from '@/core/device-info'

import './style-new.scss'
import logoHelper from '../../utils/logo-helper'

const AUTO_FLAG = 'image.qcc.com/auto/'

const colors = ['E59E9E', '72C1A0', 'E5B072', 'AE9EE5', '9EA6E5', '8BABE5', '72BCCE', 'E5A687', 'C7AE8E', '97BB72', 'D294D2', 'E5BF72', 'BD97DF', '7BB1DD']

const defaultImgUrl = 'https://qcc-static.qcc.com/resources/web/omaterial/no_image.png'

export default {
  name: 'app-auto-logo',

  props: {
    logo: {
      type: String,
      default: ''
    },
    name: { // 人员姓名
      type: String
    },
    isPerson: {
      type: Boolean,
      default: false
    },
    size: { // 图片大小
      type: [Number, String]
    },
    fontSize: { // 废弃，根据size自动计算
      type: String
    },

    radius: {
      type: String,
      default: '4px'
    },

    keyNo: {
      type: String,
      default: ''
    },
    hasImage: {
      type: Boolean,
      default: false
    },
    border: {
      type: String
    },
    useDefaultLogo: { // 无图片情况下，是否使用默认图标
      type: Boolean,
      default: false
    },
    overView: { // hover 预览
      type: Boolean,
      default: false
    },
    overViewStyle: { // 预览样式附加
      type: String
    },
    isNeedFullScreenPreview: { // 是否需要全屏预览 跟 overView 不可同时为true
      default: false,
      type: Boolean
    }
  },

  data() {
    return {
      // imageUrl: '',
      loadingLogoSuccess: true,
      shortName: '',
      domSize: undefined
    }
  },
  computed: {
    cSize() {
      if (this.size) {
        return _.isNumber(this.size) ? `${this.size}px` : this.size
      }
      if (this.domSize) {
        return `${this.domSize}px`
      }
    }
  },
  watch: {
    logo: function () {
      this.setLogo()
    },
    keyNo: function () {
      this.setLogo()
    },
    name() {
      this.setLogo()
    }
  },
  created() {

  },
  mounted() {
    this.setLogo()
  },
  render() {
    this.imageUrl = ''
    if (this.logo) {
      // 提取shortName
      if (this.logo.includes(AUTO_FLAG)) {
        const query = this.logo.split('?')?.[1]
        if (query) {
          const queryObj = qs.parse(query)
          this.shortName = queryObj?.shortName || ''
          this.logoColor = queryObj?.color
        }
      }
      if (!this.shortName) {
        this.imageUrl = this.logo
        // 测试失败
        // this.imageUrl = 'https://image.qcc.com/logo/80af5085726bb6b9c7770f1e4d0580f5.jpg?x-oss-process=style/logo_200'
      }
    } else if (this.keyNo && this.hasImage) {
      this.imageUrl = logoHelper.getLogoByKeyNo(this.keyNo, this.hasImage)
    } else if (this.useDefaultLogo || (!this.name && !this.shortName)) {
      this.imageUrl = defaultImgUrl
    }
    let showTextLogo = false
    if (this.shortName) { // 企业logo，auto的用文字渲染
      showTextLogo = true
    } else if (this.name && !this.imageUrl) { // 人员名字显示
      showTextLogo = true
    }
    // image加载失败时，重新渲染字或者空房子
    if (!this.loadingLogoSuccess) {
      if (this.name) {
        showTextLogo = true
      } else {
        this.imageUrl = (this.isPerson ? defaultImgUrl : 'https://image.qcc.com/logo/default.jpg')
      }
    }
    // 国际版不允许显示人员头像，该部分人员移除imageUrl，统一走textLogo
    if (this.isQCCPRO_G && ((this.keyNo && this.keyNo.substring(0, 1) === 'p') || (!this.keyNo && this.isPerson))) {
      this.imageUrl = ''
      showTextLogo = true
    }
    const size = _.isNumber(this.size) ? `${this.size}px` : this.size
    return (
      <span class={{ 'app-auto-logo': true }}
        key={this.keyNo + this.name}
        ref="appAutoLogo"
        style={{
          border: this.border,
          borderRadius: this.radius,
          width: size,
          height: size
        }}>
        {showTextLogo ? this.getTextLogo() : this.getImageLogo()}
        {this.$slots.default}
      </span>
    )
  },
  methods: {
    getImageLogo() {
      const option = {
        class: 'img-logo',
        style: {
          borderRadius: this.radius
        },
        attrs: {
          src: this.imageUrl
        },
        on: {
          error: () => {
            this.loadingLogoSuccess = false
          }
        }
      }
      if (this.isNeedFullScreenPreview) { // 全屏预览功能
        return (
          <app-preview-image attrs={{
            imagesList: [this.imageUrl],
            options: {
              width: '100%',
              height: '100%',
              border: 'none',
              margin: '0px',
              'border-raduis': this.radius
            },
            showMask: !!this.imageUrl,
            maskOptions: {
              'border-raduis': this.radius
            }
          }}>
            <img {...option} />
          </app-preview-image>
        )
      } else if (this.overView && !this.imageUrl.includes(AUTO_FLAG) && this.imageUrl !== defaultImgUrl) { // 预览功能事件
        option.on.mouseover = () => {
          const $mountImg = $(this.$refs.appAutoLogo)
          const position = $mountImg.offset()
          const styleStr = `position: fixed;left: ${position.left + $mountImg.width() + 10}px; top: ${position.top - $(window).scrollTop()}px;` +
            (this.overViewStyle || '')
          const $overView = $(
            `<span id="appAutoLogoOverView" class="over-view" style="${styleStr}">
                    <img src="${this.imageUrl}">
                </span>`)
          $mountImg.append($overView)
        }
        option.on.mouseout = () => {
          $(this.$refs.appAutoLogo).find('#appAutoLogoOverView').remove()
        }
      }
      return <img {...option} />
    },
    getTextLogo() {
      // 获取logo文字
      let ltext = this.getLtext()
      let fontSize
      const backgroundColor = this.logoColor || this.getLogoColor()
      const style = {
        borderRadius: this.radius,
        backgroundColor: `#${backgroundColor}`
      }
      if (ltext?.length && this.cSize) { // 字体统一处理
        const size = parseInt(this.cSize)
        const dszie = size - 2
        if (ltext.length === 1) {
          fontSize = `${Math.ceil(dszie / 1.8)}px`
        } else if (ltext.length === 2) {
          fontSize = `${Math.ceil(dszie / 2.6)}px`
        } else if (ltext.length === 3 || ltext.length === 4) {
          fontSize = `${Math.ceil(dszie / 3)}px`
        } else if (ltext.length > 4) {
          fontSize = `${Math.ceil(dszie / 3.6)}px`
        }
        if (ltext.length > 2) {
          ltext = ltext.replace(/(.{2})(?=.)/, '$1\n')
        }
      }
      if (parseInt(fontSize) < 12 && !deviceInfo.isIE()) { // 谷歌浏览器12px 限制
        const scale = parseInt(fontSize) / 12
        style.fontSize = '12px'
        style.width = `${100 / scale}%`
        style.height = `${100 / scale}%`
        style.transform = `scale(${scale})`
        style.transformOrigin = 'top left'
      } else if (fontSize !== '18px') {
        style.fontSize = fontSize
      }
      const lTextSpan = <span class="text-span" domPropsInnerHTML={ltext}></span>
      this.ltext = ltext
      return (
        <span class="text-logo" style={style}>
          {lTextSpan}
        </span>
      )
    },

    getLogoColor() {
      let lkeyNo
      let colorKey
      if (this.keyNo) {
        lkeyNo = this.keyNo
      } else if (this.logo?.includes(AUTO_FLAG)) {
        lkeyNo = this.logo.split(AUTO_FLAG)?.[1]
      }
      if (lkeyNo) {
        colorKey = parseInt(lkeyNo.split('').find(s => s <= 'd') ?? '0', 16)
      } else if (this.name) {
        const hashCode = (str) => {
          let hash = 5381
          for (let i = 0; i < str.length; i++) {
            hash = (hash * 33) ^ str.charCodeAt(i)
          }
          return hash >>> 0 // 将结果转为无符号 32 位整数
        }
        colorKey = hashCode(this.name) % colors.length
      } else {
        colorKey = _.random(colors.length, false)
      }
      return colors[colorKey]
    },
    // 修复抖动 (一开始是文字 图片加载后变成图片的 )
    setLogo() {
      if (this.ltext?.length && !this.size) { // 用来计算文字摆放
        this.domSize = this.$refs.appAutoLogo.offsetWidth
      }
    },
    getLtext() {
      let ltext = ''
      if (this.shortName) {
        ltext = this.shortName
      } else if (this.name) {
        const nameText = this.name.replace(/<[^>]+>/g, '')
        ltext = nameText[0]
        if (nameText.length >= 3) { // 复姓的情况
          const surname = nameText.substr(0, 2)
          if (familyNames.includes(surname)) {
            ltext = surname
          }
        }
      }
      if (ltext.length > 5) { // 最长5个字
        ltext = ltext.substring(0, 5)
      }
      return ltext
    }
  }
}
</script>

<style lang="scss" src="./style-new.scss"></style>
