.app-auto-logo {
  display: inline-block;
  border: solid 1px #eee;
  border-radius: 4px;
  position: relative;
  width: 32px;
  height: 32px;



  .img-logo {
    object-fit: contain;
    border-radius: 4px;
    height: 100%;
    width: 100%;
    vertical-align: top; // window 会偏离
  }

  .text-logo {
    display: inline-block;
    background-color: #E79177;
    border-radius: 4px;
    line-height: 1.2;
    font-size: 18px;
    padding: 1px;
    width: 100%;
    height: 100%;
    vertical-align: top;
    overflow: hidden;

    .text-span {
      align-items: center;
      display: flex;
      height: 100%;
      justify-content: center;
      position: relative;
      text-align: center;
      user-select: none;
      -webkit-user-select: none;
      width: 100%;
      color: #fff !important;
      white-space: pre;
    }
  }

  .over-view {
    position: absolute;
    display: inline-block;
    width: 100px;
    height: 100px;
    left: 50px;
    z-index: 99;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, .15);

    &>img {
      display: inline-block;
      width: 100% !important;
      height: 100% !important;
      border-radius: 4px;
      object-fit: contain;   
    }
  }

  // &.text-logo {
  //   border: none;
  // }
}