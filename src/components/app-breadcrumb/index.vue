<template>
  <div class='app-breadcrumb flex flex-between padding-r-0-6x' :class="[left?'padding-l-0-6x':'',isNewBread ? 'no-bg' : '']">
    <slot name="title">
      <div class='app-breadcrumb-title'>
        <return-bread
          :isNewBread="isNewBread"
          :title="formatTitle()"
          :v2="v2"
          :secondTitle="secondTitle"
          :secondButton="secondButton"
          @back="back"
          :cropInfo="cropInfo"
          :widthAuto="$attrs.breadWidthAuto"></return-bread>
        <video-pop theme="t-blue" style="line-height: 49px;display: inline-block;margin-left: 5px;" v-if="showVideoIntro && !['__HZJW__'].includes(ENV)" :url="$route.meta.videoUrl">
          <span style="font-size:14px;margin-left:5px">视频介绍</span>
        </video-pop>
        <slot name="titletips"></slot>
      </div>
    </slot>
    <div class='app-breadcrumb-right'>
      <slot>
      </slot>
    </div>
  </div>
</template>
<script>
import returnBread from './components/return-bread'

export default {
  name: 'app-breadcrumb',
  props: {
    title: { type: String, default: '' },
    left: { type: Boolean, default: true },
    needReplace: { type: Boolean, default: false },
    isNewBread: { type: Boolean, default: false },
    v2: { type: Boolean, default: false },
    secondTitle: { type: String, default: '' },
    secondButton: { default: null },
    cropInfo: { default: null }
  },
  components: {
    videoPop: () => import('../../routes/welcome/components/video-pop'),
    returnBread
  },
  computed: {
    showVideoIntro() {
      return this.$route && this.$route.meta && this.$route.meta.showVideo && this.$route.meta.videoUrl
    }
  },
  methods: {
    formatTitle() {
      let title = this.title || (this.isNotFinancial ? (this.$route.meta.nfTitle || this.$route.meta.title) : this.$route.meta.title) || ''

      if (this.needReplace) {
        return title
      }
      return title.replace(new RegExp('/', 'g'), '<span style="margin: 0 5px;">/</span>')
    },
    back() {
      this.$emit('back')
    }
  }
}

</script>
<style lang="scss" scoped>
@import '../../styles/common.scss';

.app-breadcrumb {
  height: 50px;
  background-color: #fff;
  box-sizing: border-box;

  ._header-right {
    @include inline-flex-def;
    @include flex-cCenter;
    width: auto;

    > div,
    > span,
    > button {
      display: inline-block;
    }
  }
}

.app-breadcrumb.no-bg {
  background: none;
  height: 40px;
  padding-top: 10px;
  box-sizing: border-box;
}

.app-breadcrumb-title,
.app-breadcrumb-right {
  line-height: 50px;
  color: $base-dimgray-color;
  font-size: 14px;
  display: flex;
  align-items: center;

  .preTitle {
    color: $base-black-color;
  }
}

.no-bg .app-breadcrumb-title {
  padding-left: 15px;
}

</style>
