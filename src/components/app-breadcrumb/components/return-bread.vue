<template>
  <div v-if="!isNewBread" style="padding-right: 5px">
    <template v-if="$slots.breadcrumbTitle">
      <slot name="breadcrumbTitle" />
    </template>
    <span v-else v-html="title" />
  </div>
  <div v-else>
    <div class="app-breadcrumb-together app-breadcrumb-v2" v-if="v2">
      <a @click="back" :class="[isForbidBack?'no-click disabled':'']">
        <span v-if="!hideBackImg && !isForbidBack" class="iconfont icon-icon_fanhui" />
      </a>
      <span v-html="title" class="breadcrumb-title" />
      <template v-if="secondTitle">
        <span class="breadcrumb-title mb">/</span>
        <span class="breadcrumb-title" v-html="secondTitle" />
      </template>
      <template v-if="cropInfo">
        <span class="breadcrumb-title  mb">/</span>
        <div class="test" v-html="getBreadcrumbTitleNew" />
        <template v-if="corpAnnualStatus && corpAnnualStatus.length">
          <app-tags
            v-for="(tag, index) in corpAnnualStatus"
            :key="`app-tag-${index}`"
            style="margin-left: 5px"
            v-bind="appTagsAttrs"
            :text="tag"
            :toolTipContent="'经营状态源于国家企业信用信息公示系统，该企业最新公示'+corpAnnualStatusYear+'年年报'"
          />
        </template>
      </template>
    </div>
    <div class="app-breadcrumb-together" v-else>
      <a @click="back" :class="[isForbidBack?'no-click disabled':'']">
        <span v-if="!hideBackImg && !isForbidBack" class="iconfont icon-icon_fanhui" />
        <span v-html="title" :class="[{disabled: isForbidBack}]" />
      </a>
      <span v-if="secondTitle || cropInfo">/</span>
      <div class="test" v-if="cropInfo" v-html="getBreadcrumbTitleNew" />
      <a v-if="secondTitle" @click="goto" v-html="secondTitle" :class="[secondButton||widthAuto ? '' : 'normal']" :title="secondButton?'':secondTitle" />
      <template v-if="corpAnnualStatus && corpAnnualStatus.length">
        <app-tags
          v-for="(tag, index) in corpAnnualStatus"
          :key="`app-tag-${index}`"
          style="margin-left: 5px"
          v-bind="appTagsAttrs"
          :text="tag"
          :toolTipContent="'经营状态源于国家企业信用信息公示系统，该企业最新公示'+corpAnnualStatusYear+'年年报'"
        />
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'return-bread',
  props: {
    hideBackImg: { type: Boolean, default: false },
    isNewBread: { type: Boolean, default: false },
    v2: { type: Boolean, default: false },
    title: { default: '', type: String }, // 一级名称
    secondTitle: { type: String, default: '' }, // 二级名称
    secondButton: { default: null }, // 二级触发事件
    cropInfo: { default: null },
    widthAuto: { default: undefined }
  },
  computed: {
    appTagsAttrs() {
      return {
        type: 'line-fill',
        autoWidth: false,
        toolTipWidth: 390,
        toolTipMaxWidth: 0,
        placement: 'bottom',
        ...(this.cropInfo?.appTagsAttrs || {})
      }
    },
    creditCode() {
      if (this.cropInfo) {
        return this.cropInfo.creditCode || this.cropInfo.creditNo || this.cropInfo.license || (this.cropInfo.djinfo ? this.cropInfo.djinfo.creditCode : '') || ''
      }
      return ''
    },
    gsNo() {
      if (this.cropInfo) {
        return this.cropInfo.no || ''
      }
      return ''
    },
    getBreadcrumbTitleNew() {
      if (this.cropInfo) {
        let exString = this.cropInfo.name || this.cropInfo.companyNameEn || this.cropInfo.companyName || this.cropInfo.enName || (this.cropInfo.djinfo ? this.cropInfo.djinfo.name : '')
        if (this.creditCode) {
          exString = exString + `（${this.creditCode}）`
        } else if (this.gsNo) {
          exString = exString + `（${this.gsNo}）`
        }

        let length = 80
        if (window.innerWidth < 1400 && window.innerWidth >= 1200) {
          length = 60
        } else if (window.innerWidth < 1200) {
          length = 45
        }
        return $util.getCompanyOrPersonLinkerByOrg(`${exString}`, this.cropInfo.keyNo, this.cropInfo?.orgType || null, { length: length, mustJumpLink: this.cropInfo.mustJumpLink })
      }
      return ''
    },
    corpAnnualStatus() {
      if (this.cropInfo?.shortStatus) {
        return _.isArray(this.cropInfo.shortStatus) ? this.cropInfo.shortStatus : [this.cropInfo.shortStatus]
      }
      return []
    },
    corpAnnualStatusYear() {
      if (this.cropInfo && this.cropInfo.corpAnnRptStatus && this.cropInfo.corpAnnRptStatus.stdStatus) {
        return this.cropInfo.corpAnnRptStatus.yearStr
      }
    },
    isForbidBack() {
      return __QCCPRO_G__ || (__PLUGIN__ && this.$route.query.hideBackFlag === 'Y')
    }
  },

  methods: {
    annualRptStatusClass(status) {
      return $util.annualRptStatusClass(status)
    },
    isEmptyObj(obj) {
      return Object.getOwnPropertyNames(obj).length === 0
    },
    back() {
      this.$emit('back')
    },
    goto() {
      if (this.secondButton) {
        this.secondButton()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-breadcrumb-together {
  display: flex;
  align-items: center;
  white-space: nowrap;
  a {
    color: #128BED;
    display: flex;
    align-items: center;

    &.no-click {
      color: #333;
      cursor: default;
      >a,
      >span {
        color: #333;
        cursor: default;
      }
    }

    .iconfont {
      padding-right: 10px;
      font-size: 15px;
    }

    span {
      color: #128BED;
    }
  }

  a:hover {
    color: $color-primary-hover;
  }

  > span {
    color: #999999;
    padding: 0 5px;
  }

  .normal {
    color: #333333;
    cursor: default;
    width: 900px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
  }

  a.normal:hover {
    color: #333333;
  }

  &.app-breadcrumb-v2 {
    .breadcrumb-title {
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      color: #999999;
      padding: 0px 0px;

      &.mb{
        padding: 0px 5px;
      }
    }
  }
  ::v-deep {
    .default-tags {
      margin-right: 0px;

      &:last-child {
        margin-right: 10px;
      }
    }
  }
}
</style>
