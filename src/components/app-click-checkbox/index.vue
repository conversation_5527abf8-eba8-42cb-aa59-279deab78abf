/**
* @Description:
* <AUTHOR>
* @date 2019/8/8 15:02
*/
<template>
  <div class="_app_click_checkbox_default" ref="p">
    <div class="global-checkbox-mask" v-if="mask" style='position: absolute;left: 0;top: 0;width: 100%;height: 100%;z-index: 99;' :style="popStyle" @click.stop='clickStop'></div>
    <el-checkbox v-model="checked" @change="change" :indeterminate="indeterminate" :disabled="disabled">
      <template v-if="mask">
        <slot></slot>
      </template>
      <span v-else-if="!labelCanClick" @click.prevent>
        <slot></slot>
      </span>
      <span v-else>
        <slot></slot>
      </span>
    </el-checkbox>
  </div>
</template>

<script>
  export default {
    name: 'app-click-checkbox',
    props: {
      checkstate: { default: false, type: [Boolean, String, Number] },
      left: { default: '0', type: String },
      outerNotChange: { default: false, type: <PERSON><PERSON><PERSON> },
      indeterminate: { default: undefined, type: [<PERSON><PERSON><PERSON>, Object] },
      disabled: { default: false, type: Boolean },
      mask: { default: true, type: Boolean },
      labelCanClick: { default: false, type: Boolean }
    },
    model: {
      prop: 'checkstate',
      event: 'emitModel'
    },
    data() {
      return {
        checked: false,
        isFromOuterChange: false
      }
    },
    computed: {
      popStyle() {
        let str = `left: ${this.left}px;width:calc(100% - ${this.left}px)`
        return str
      }
    },
    watch: {
      checkstate(val, valold) { // 同步外部的值
        if (this.checked === this.checkstate) {
          return
        }
        if (this.outerNotChange) {
          this.isFromOuterChange = true
        }
        this.checked = this.checkstate
        this.change(val)
      }
    },
    methods: {
      change(val) {
        if (this.outerNotChange && this.isFromOuterChange) {
          this.isFromOuterChange = false
          return
        }
        this.$emit('emitModel', val)
        this.$emit('change', val)
      },
      clickStop: function(e) {
        this.$emit('click', this.checkState)
      }
    },
    mounted() {
      this.checked = this.checkstate
    }
  }

</script>

<style scoped lang="scss">
  ._app_click_checkbox_default {
    position: relative;
    display: inline-block;
    cursor: pointer;
  }
</style>
