<script src="./component.jsx"></script>

<style scoped lang="scss">
  .app-filter-wrapper {
    height: 440px;

    ::v-deep .__checkbox_select-btn-wrapper {
      border-color: #eee;
    }
    ul.app-filter-list-content {
      font-size: 14px;
      padding: 5px 15px;
      overflow: auto;
      height: calc(100% - 40px);
      >li {
        padding: 10px 0 15px 0;
        border-bottom: 1px solid #eee;
        &:last-child {
          border-bottom: none;
        }

        .filter-content-title {
          color: #999;
          font-weight: normal;
          margin-bottom: 10px;
        }

        ::v-deep {
          .el-checkbox-group {
            .el-checkbox {
              padding: 4px 0 4px 10px;
              margin-right: 0;
              .el-checkbox__label {
                padding: 0 8px;
                font-size: 13px;
              }
            }
          }
          .customNum .el-checkbox  .el-checkbox__label,
          .el-radio-group .el-radio-button__inner{
            font-size: 13px;
          }
        }
      }

    }
  }
</style>
