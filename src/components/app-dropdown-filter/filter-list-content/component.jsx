/**
 * @Description: 用于集团成员企业过滤器的筛选项
 * <AUTHOR>
 * @date 2023/8/23 15:55
*/
import AppRadioGroup from '../../app-radio-group/index.vue'
import dropDownNumbers from '../../app-filter/drop-down-numbers.vue'
import selectBtns from '../../app-popover-select/select-btns'
import _ from 'lodash'

export default {
  props: {
    list: {
      default: undefined,
      type: Array
    }
  },
  data() {
    return {
      params: {
        rangeFilters: [],
        isConsolidated: '',
        isGovReported: '',
        statusCodes: []
      }
    }
  },
  computed: {
    hasSelected() {
      return Object.values(this.params).some(v => {
        if (v instanceof Array) {
          return v.length > 0
        } else {
          return v !== ''
        }
      })
    }
  },
  methods: {
    onCustomizationChange(val, group, lastChild, isCustomization = false) {
      if (isCustomization && lastChild.type === 'customization') {
        if (!val) {
          group.model = ''
          lastChild.model = ''
        } else {
          const { arr } = val
          let obj = { type: 'customization', model: arr }
          group.model = obj
          lastChild.model = arr
        }
      } else {
        lastChild.model = []
        try {
          this.$refs[`custom_${group.title}`].resetCustomNum()
        } catch (e) {}
      }
      this.onChange(val, group)
      // this.$emit('change', val, group)
    },
    onChange(val, group) {
      // this.$emit('change', val, group)
      const { key } = group
      if (['controlratio', 'controlshareholdratio', 'registcapiamount'].includes(key)) { // 构造数据结构
        let arr = []
        if (!_.isObject(val)) {
          arr = (val || '').split(',')
        } else {
          arr = val.arr
        }
        this.params.rangeFilters = this.params.rangeFilters.filter(v => v.type !== key)
        if (arr.length === 2) {
          this.params.rangeFilters.push({
            type: key,
            rangeList: [{ start: arr[0], end: arr[1] }]
          })
        } else {
          delete this.params.rangeFilters[key]
        }
      } else {
        this.params[key] = val
      }
      console.log(key, this.params)
      this.$emit('change', val, group)
    },
    getFilterItemContent(item) {
      const lastChild = item.children.length > 0 ? item.children[item.children.length - 1] : undefined
      const lastNeedCustomization = lastChild && lastChild.type === 'customization'
      switch (item.type) {
        case 'radio':
          return (<AppRadioGroup v-model={item.model} themeBlock={true} onChange={val => this.onCustomizationChange(val, item, lastChild)}>
            {item.children.map((child, index) => {
              return (lastNeedCustomization && index === item.children.length - 1) ? '' : (
                <el-radio-button
                  key={index}
                  label={child.value}
                  value={child.value}>
                  {child.desc}
                </el-radio-button>
              )
            })}
             {lastNeedCustomization && (
               <dropDownNumbers ref={`custom_${item.title}`} v-model={lastChild.model} referenceType="common" inline={true}
                                onChange={val => this.onCustomizationChange(val, item, lastChild, true)}
                                referenceAttrs={{ style: { height: '28px', margin: '3px', color: '#666' } }} {...{ attrs: lastChild.numberOptions }}></dropDownNumbers>
             )}
          </AppRadioGroup>)
        case 'checkbox':
          return (<el-checkbox-group v-model={item.model} onChange={val => this.onChange(val, item)}>
            {item.children.map((child, index) => {
              return (lastNeedCustomization && index === item.children.length - 1) ? '' : (
                <el-checkbox
                  key={index}
                  label={child.value}
                  value={child.value}
                >
                  {child.desc}
                </el-checkbox>
              )
            })}
          </el-checkbox-group>)
      }
    }
  },
  render() {
    const listDom = (this.list || []).map((item, index) => {
      return (
        <li>
          <h4 class="filter-content-title">
            {item.title}
            {item.desc && <app-tooltip defaultStyle="color:#d6d6d6" autoWidth={true} maxWidth={400} placement="bottom" style="margin-left: 5px;cursor: pointer;"
                                     text={item.desc}></app-tooltip>}
          </h4>
          {this.getFilterItemContent(item)}
        </li>
      )
    })
    const listeners = {
      on: {
        cancelclick: () => {
          this.params.rangeFilters = []
          this.params.isConsolidated = ''
          this.params.isGovReported = ''
          this.params.statusCodes = []
          this.list.forEach(t => {
            t.model = t.type === 'radio' ? '' : []
            if (t.type === 'radio') {
              if (t.children.length > 0 && t.children[t.children.length - 1].model) {
                t.children[t.children.length - 1].model = []
                try {
                  this.$refs[`custom_${t.title}`].resetCustomNum()
                } catch (e) {}
              }
            }
          })
          this.$emit('reset')
        },
        sureclick: () => {
          this.$emit('close')
        }
      }
    }
    return (
      <div class="app-filter-wrapper">
        <ul class="app-filter-list-content">
          {listDom}
        </ul>
        <selectBtns {...listeners} hasSelect={this.hasSelected}></selectBtns>
      </div>
    )
  }
}
