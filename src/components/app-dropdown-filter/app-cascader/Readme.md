示例1:

```vue
<template>
  <div>
      <app-cascader name="全部地区" :type="type" :multi="true" trigger="mhover" @change="change"></app-cascader>
      <div v-if="selectData">{{selectData}}</div>  
  </div>
  
</template>
<script>
  export default {
    data() {
      return { 
        type:'area',
        selectData: null
      }
    },
    methods: {
      change(selectData) {
        this.selectData = selectData
      }
    }
  }
</script>
```

示例2:

```vue
<template>
  <app-cascader type="industry" :multi="true" trigger="hover">
    <template slot="name" slot-scope="data">
      <span>全部行业</span> 
      <span v-if="data.select.length>0">已选{{data.select.length}}项</span> 
    </template>
  </app-cascader>
</template>
```
