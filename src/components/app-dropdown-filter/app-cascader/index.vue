<template>
  <div ref="cascaderDrop" class="app-cascader common-filter" :class="{ 'drop-hover': trigger === 'hover' }" @mouseenter="changePlacement(true)">
    <span data-toggle="dropdown" aria-expanded="false" class="toggle">
      <!-- @slot 下拉按钮插槽 -->
      <!-- @binding {selectData} 选中数据信息 -->
      <slot name="name" :select="selectData">
        <a class="cascader-text" :class="selectData.length ? 'selected-text' : ''">
          <span v-if="nameType===2 && selectData.length">
            已选<span v-if="selectData.length">{{ selectData.length }}</span>项
          </span>
          <template v-else-if="nameType===3">
            <span v-if="!selectData.length">{{ name }}</span>
            <span v-if="selectData.length">{{ selectData.length }} 项</span>
          </template>
          <span v-else>
            {{ name }}
            <span v-if="selectData.length">{{ selectData.length }}</span>
          </span>
          <span class="caret"></span>
        </a>
      </slot>
    </span>
    <div
      ref="dropdownMenu"
      class="dropdown-menu"
      :style="{ width: showFullSelection ? 'auto' : `${(1 + hItems.length) * 201}px`,left:`${left}px` }"
      @click="clickClose">
      <div :class="['clearfix', showFullSelection && 'show-full-selection']" :style="{ height: autoHeight ? 'auto' : `${canSearch ? slimScrollHeight + 48 : slimScrollHeight}px`, maxHeight: autoHeight && `${canSearch ? slimScrollHeight + 48 : slimScrollHeight}px` }">
        <div class="pull-left">
          <div class="search-area" v-if="canSearch">
            <input v-model="searchKey" autocomplete="off" type="text" :placeholder="myplaceholder" class="form-control" maxlength="30" :disabled="disabled" />
            <app-icon type="sousuo" class="sousuo-icon"></app-icon>
            <app-icon type="shanchulianxifangshi" class="clear-icon" v-show="searchKey" @click.native="deleteValue('searchKey')"></app-icon>
          </div>
          <ul :style="{ maxHeight: autoHeight && `${slimScrollHeight}px` }" class="drop-col" ref="firstPart">
            <template v-if="list.some(item=>!item.hidden)">
              <li v-for="(item, index) in list" :key="`drop_col_${index}`" v-show="canSearch ? !item.hidden ? true : false : true">
                <a
                  class="item"
                  :title="item.name"
                  :class="hItems[0] == item ? 'hover' : ''"
                  @mouseenter="getSubList(item)">
                  <label v-if="multi" class="text">
                    <app-checkbox
                      :name="item.showName || item.name"
                      :checked="!!item.checked"
                      :disabled="disabled"
                      :data-item="item"
                      :indeterminate="item.checked === -1"
                      @change="itemClick(item, $event)" />
                  </label>
                  <span v-else class="text" v-html="item.showName || item.name" @click="clickItem(item)"></span>
                  <i v-if="item.list && item.list.length > 0" class="fa fa-caret-right"></i>
                </a>
              </li>
            </template>
            <template v-else>
              <app-nodata style="padding:20px 10px" text="暂无相关选项" />
            </template>
          </ul>
        </div>
        <div class="pull-left" v-for="(hItem, hIndex) in hItems" :key="`subList_${hItem.level}_${hIndex}`">
          <div v-if="canSearch" class="search-area"></div>
          <ul :style="{ maxHeight: autoHeight && `${slimScrollHeight}px` }" class="drop-col">
            <li
              v-for="(item, index) in hItem.list"
              :key="`drop_col_${hItem.level}_${index}`"
              v-show="!item.hidden">
              <a
                class="item"
                :title="item.name"
                :class="{'hover':hItems[hIndex+1] == item,'can-close-item':!item.list||item.list.length===0}"
                @click="clickItem(item)"
                @mouseenter="getSubList(item, hItem.level)">
                <label v-if="multi" class="text">
                  <app-checkbox
                    :name="item.showName || item.name"
                    :checked="!!item.checked"
                    :disabled="disabled"
                    :data-item="item"
                    :indeterminate="item.checked === -1"
                    @change="itemClick(item, $event)" />
                </label>
                <span v-else class="text" v-text="item.name"></span>
                <i v-if="item.list && item.list.length > 0" class="fa fa-caret-right"></i>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
<script src="./component.js"></script>
<style lang="scss" src="./style.scss" scoped></style>
