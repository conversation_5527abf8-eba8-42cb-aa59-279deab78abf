.app-cascader {
  display: inline-block;
  position: relative;

  .cascader-text {
    color: #333;

    &.selected-text {
      color: #128bed;
    }

    .caret {
      margin-bottom: 2px;
      transition: transform 0.15s linear;
    }
  }

  .clearfix {
    margin-right: -1px;
    overflow: hidden;
    display: flex;

    >.pull-left {
      width: 200px;
      position: relative;
      overflow: hidden;
      display: inline-flex;
      flex-direction: column;

      .search-area {
        position: relative;
        height: 48px;
        width: 200px;
        padding: 5px 15px 10px 15px;
        border-bottom: 1px solid #eee;
        flex-shrink: 0;

        .form-control {
          position: absolute;
          height: 32px;
          width: calc(100% - 30px);
          padding-left: 30px;
          padding-right: 20px;
          border: 1px solid #eee;

          &:hover {
            border-color: #128bed;
          }

          &:focus {
            border-color: #128bed;
          }
        }

        >i {
          position: absolute;
          font-size: 14px;
        }

        .clear-icon {
          cursor: pointer;
          right: 20px;
          top: 14px;
          color: #bbb;
        }

        .sousuo-icon {
          color: #999;
          top: 14px;
          left: 25px;
        }
      }

      &:not(:last-child) .drop-col {
        border-right: 1px solid #eee;
      }
    }
  }

  ul.drop-col {
    height: 100%;
    margin-bottom: 0px;
    padding-left: 0px;
    position: relative;
    overflow: hidden;
    // height: 285px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 8px;
      height: 5px;
    }

    li {
      >a {
        display: inline-flex;
        padding: 6px 5px 6px 15px;
        color: #333;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        &:hover,
        &.hover {
          background-color: #f3f9fd;
          color: #128bed;
        }

        .text {
          width: 160px;
          line-height: 18px;
          display: inline-block;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-right: 5px;
          margin-bottom: 0px;
          white-space: nowrap;
        }

        label.text {
          font-weight: normal;

          >input {
            margin-right: 5px;
            font-size: 24px;
            position: relative;
            display: inline-block;
            margin-top: -3px;
            vertical-align: middle;
          }
        }

        i {
          position: static;
          margin-right: 0px;
        }

        ::v-deep .ant-checkbox-wrapper {
          .ant-checkbox+span {
            display: inline-block;
            vertical-align: top;
            width: 146px;
            line-height: 18px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
    }
  }

  &.drop-hover .dropdown-menu {
    display: block;
    visibility: hidden;
    transition: all 0s linear 0.2s;
  }

  &.drop-hover:hover {

    .cascader-text {
      color: #128bed;
    }

    .caret {
      transform: rotate(180deg);
    }

    .dropdown-menu {
      visibility: visible;
    }
  }

  &.open {

    .caret {
      transform: rotate(180deg);
    }

  }

  .show-full-selection {
    display: flex;

    >.pull-left {
      width: auto;
      min-width: 200px;

      .search-area {
        width: 100%;
      }
    }

    ul.drop-col {
      li {
        >a {

          .text {
            width: auto;
          }

          i {
            margin-right: 5px;
          }

          ::v-deep .ant-checkbox-wrapper {
            .ant-checkbox+span {
              width: auto;
            }
          }
        }
      }
    }
  }

  ::v-deep em {
    font-style: normal;
    color: #FD5266;
  }
}
