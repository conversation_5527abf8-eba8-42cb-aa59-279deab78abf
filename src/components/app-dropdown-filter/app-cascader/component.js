import _ from 'lodash'

import { areaList } from '../../../data/area'
import { getTree } from '../../../data/emerging-industry'
// import { industryList, industryList4, qccIndustryList } from '../../../store/industryList2'

if (__BROWSER__) {
  var resizeDetector = require('element-resize-detector')
}

/**
 * 级联筛选组件
 * data 和 type 2选1，type目前支持area|industry|ensh，优先type
 * name 支持 slot
 */
export default {
  name: 'AppCascader',

  props: {
    /**
     * current 支持设置同步
     * @values {selectData:[{name:'酒、饮料和精制茶制造业',code:'15'}]}
     */
    current: {
      type: Object,
      default: null
    },
    name: {
      type: String,
      default: '全部'
    },
    multi: {
      type: Boolean,
      default: false
    },
    /**
     * 筛选类型
     * @values area，industry,industry4(四级国标)
     */
    type: {
      type: String
    },
    trigger: {
      type: String,
      default: 'click'
    },
    data: {
      type: Array,
      default: () => [
        {
          name: '全部',
          code: '',
          list: []
        }
      ]
    },
    /**
     * 仅在地区和行业选择时可用
     */
    canSearch: {
      type: Boolean,
      default: false
    },
    /**
     * 限制多选选中的个数
     */
    checkLimit: {
      type: Number
    },
    /**
     * 名称展示类型，
     * @values 2，已选num项
     * @values 3. num项
     */
    nameType: {
      type: Number
    },
    disabled: {
      type: Boolean,
      default: false
    },
    slimScrollHeight: {
      type: Number,
      default: 270
    },
    /**
     * 限制使用，
     * @values login
     * @values vip
     * @values {type:'vip',from:25}
     */
    limits: {
      type: [String, Object]
    },
    // 根据到达边的距离，自动移动下拉框
    autoPlacement: {
      type: Boolean,
      default: false
    },
    // 根据data内部checked属性控制选中状态，不走current逻辑
    autoChange: {
      type: Boolean,
      default: true
    },
    // 展示完整的选项，没有。。。
    showFullSelection: {
      type: Boolean,
      default: false
    },
    // 下拉选项自适应高度
    autoHeight: {
      type: Boolean,
      default: false
    },
    // 地区筛选类型 all 包含港澳台 inLand 不包含港澳台
    areaType: {
      type: String,
      default: 'all'
    }
  },
  data() {
    return {
      list: [],
      hItems: [],
      selectData: [],
      needWatch: true,
      searchKey: '',
      hasValue: false,
      left: 0
    }
  },
  watch: {
    current(val) {
      if (this.needWatch) {
        this.setCurrent()
      }
    },
    data: {
      handler(val) {
        if (!this.autoChange && this.needWatch) {
          this.list = val
          /**
           * 修复list和hItems不同步问题
           */
          this.hItems.forEach(el => {
            const target = this.list.find(v => el.code === v.code)
            if (target) {
              el.checked = target.checked
              if (el?.list?.length) {
                el.list.forEach(x => {
                  target.list.forEach(m => {
                    if (x.code === m.code) {
                      x.checked = m.checked
                    }
                  })
                })
              }
            }
          })
        }
      },
      deep: true
    },
    searchKey: {
      handler: _.debounce(function (val) {
        this.search()
      }, 300)
    }
  },
  computed: {
    myplaceholder() {
      if (this.type === 'area') {
        return '输入地区名称搜索'
      } else if (['industry', 'qccIndustry', 'ensh'].includes(this.type)) {
        return '输入行业名称搜索'
      } else {
        return '输入关键词搜索'
      }
    }
  },
  mounted() {
    if (this.trigger === 'mhover') {
      this.setHoverEvent()
    }
    if (this.type === 'area') {
      this.transArea()
    } else if (this.type === 'industry' || this.type === 'industry4') {
      this.transIndustry(this.type)
    } else if (this.type === 'qccIndustry') {
      this.transQccIndustry()
    } else if (this.type === 'ensh') {
      this.transEnsh()
    } else {
      this.list = this.data
      if (this.canSearch) {
        this.list.forEach(item => {
          item.hidden = false
          item.showName = item.name
        })
      }
    }

    this.setCurrent()
    // if (this.canSearch) {
    //   $(this.$refs.firstPart).on('scroll', this.handleScroll)
    // }

    if (this.autoPlacement) {
      // 监听窗口缩放
      if (__BROWSER__) {
        window.addEventListener('resize', () => this.changePlacement())
      }
      // 监听元素宽度变化
      const detector = resizeDetector()
      detector.listenTo(this.$refs.dropdownMenu, () => this.changePlacement())
    }
  },
  beforeDestroy() {
    if (__BROWSER__) {
      window.removeEventListener('resize', this.changePlacement())
    }
  },
  methods: {
    reset() {
      this.hItems = []
      this.selectData = []
    },
    resetList() {
      const rFunc = list => {
        list.forEach(item => {
          item.checked = false
          if (item.list) {
            rFunc(item.list)
          }
        })
      }
      rFunc(this.list)
      this.hItems = []
    },
    setCurrent() {
      if (this.current) {
        this.selectData = this.current.selectData
      } else {
        this.selectData = []
      }
      if (this.autoChange) {
        this.resetList()
        if (this.multi && this.current) {
          // parentChain 上级item，如果下级选中上级要改为半选
          const setFunc = (list, chain) => {
            list.forEach(item => {
              this.selectData.forEach(sItem => {
                if (sItem.code === item.code && sItem.name === item.name) {
                  item.checked = 1
                  return false
                }
              })
              if (item.checked === 1 || item.checked === true) {
                if (item.list) {
                  item.list.forEach(childItem => {
                    childItem.checked = true
                  })
                }
                if (chain.length) {
                  for (let i = chain.length - 1; i >= 0; i--) {
                    chain[i].checked = this.listCheckedStatus(chain[i].list)
                  }
                }
              }

              if (item.list) {
                const parentChain = [...chain]
                parentChain.push(item)
                setFunc(item.list, parentChain)
              }
            })
          }
          setFunc(this.list, [])
        }
      }
    },
    getSubList(item, level = 0) {
      if (this.searchKey && level === 0) {
        this.expandSub(item)
      } else {
        if (item.list && item.list.length > 0) {
          const hItem = item
          hItem.level = level + 1
          if (hItem.level > this.hItems.length) {
            this.hItems.push(hItem)
          } else {
            this.hItems = this.hItems.slice(0, level)
            this.hItems[level] = hItem
          }
        } else if (level === 1 && this.hItems[0]) {
          this.hItems = [this.hItems[0]]
        } else if (level === 0) {
          this.hItems = []
        }
      }
      this.$forceUpdate()
    },
    // 默认展开一条有效数据的所有
    expandSub(data) {
      if (!data) {
        this.hItems = []
        return
      }
      const res = []
      const getSub = (item, level) => {
        if (!item.hidden && item.list?.length) {
          const hItem = item
          hItem.level = level + 1
          res.push(hItem)
          const obj = item.list.find(ele => !ele.hidden)
          if (obj?.list?.length) {
            getSub(obj, hItem.level)
          }
        }
      }
      getSub(data, 0)
      this.hItems.splice(0, this.hItems?.length, ...res)
    },
    itemClick(item, e) {
      if (e.target.checked && this.checkLimit) {
        if (this.selectData.filter(v => v.level === 1).length >= this.checkLimit) {
          e.preventDefault()
          this.$message.error('每项最多选择5个条件')
          return
        }
      }
      if (this.multi) {
        item.checked = e.target.checked
        this.chainChange(item)
        for (let i = this.hItems.length - 1; i >= 0; i--) {
          this.hItems[i].checked = this.listCheckedStatus(this.hItems[i].list)
        }
        if (!this.autoChange) {
          /**
           * 修复hItems变化，list不同步问题
           */
          this.list.forEach(el => {
            this.hItems.forEach(v => {
              if (el.code === v.code) {
                el.checked = +(v.checked)
                if (el?.list?.length) {
                  el.list.forEach(m => {
                    v.list.forEach(n => {
                      if (m.code === n.code) {
                        m.checked = +(n.checked)
                      }
                    })
                  })
                }
              }
            })
          })
        }
        this.selectData = this.gParams()
        /**
         * 选择时触发
         * @property {object} selectData
         */
        this.$emit('change', this.selectData)
        this.needWatch = false
        setTimeout(() => {
          this.needWatch = true
        }, 300)
      } else {
        // 暂未支持完整，要调整交互样式
        this.$emit('change', this.item)
      }
      e.stopPropagation()
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },
    // 多选后向后联动
    chainChange(item) {
      const checkFunc = (vo) => {
        vo.checked = item.checked
        if (vo.list) {
          vo.list.forEach(subVo => {
            checkFunc(subVo)
          })
        }
      }
      checkFunc(item)
    },
    // 多选后检查前面的
    listCheckedStatus(list) {
      var allChecked = true
      var nullChecked = true
      list.forEach(item => {
        if (item.all) return
        if (item.checked === -1) {
          nullChecked = false
          allChecked = false
        } else if (item.checked) {
          nullChecked = false
        } else {
          allChecked = false
        }
      })
      if (allChecked) {
        return 1
      } else if (nullChecked) {
        return 0
      } else {
        return -1
      }
    },
    gParams() {
      const params = []
      let rootCode = ''
      const gFunc = (list, level, parentCode) => {
        list.forEach(item => {
          if (level === 1) {
            rootCode = item.code
          }
          if (item.checked === 1 || item.checked === true) {
            const p = {
              name: item.name,
              code: item.code,
              level: level,
              rootCode
            }
            if (parentCode) p.parentCode = parentCode
            params.push(p)
          } else if (item.checked === -1) { // 半选
            gFunc(item.list, level + 1, item.code)
          }
        })
      }
      gFunc(this.list, 1)
      return params
    },
    transArea() {
      if (this.areaType === 'all') {
        this.list = areaList
      } else if (this.areaType === 'inLand') {
        const filterCode = ['HK', 'MO', 'TW']
        this.list = areaList.filter(item => {
          return !filterCode.includes(item.code)
        })
      }

      if (this.canSearch) {
        this.list.forEach(item => {
          item.hidden = false
          item.showName = item.name
        })
      }
    },
    transIndustry(type) {
      // if (type === 'industry4') {
      //   this.list = industryList4
      // } else {
      //   this.list = industryList
      // }
      if (this.canSearch) {
        this.list.forEach(item => {
          item.hidden = false
          item.showName = item.name
        })
      }
    },
    transQccIndustry() {
      // this.list = qccIndustryList
      if (this.canSearch) {
        this.list.forEach(item => {
          item.hidden = false
          item.showName = item.name
        })
      }
    },
    transEnsh() {
      this.list = getTree()
      if (this.canSearch) {
        this.list.forEach(item => {
          item.hidden = false
          item.showName = item.name
        })
      }
    },
    setHoverEvent() {
      var $cascaderDrop = $(this.$refs.cascaderDrop)
      let timeout1
      let timeout2
      const delay1 = 200
      const delay2 = 200
      $cascaderDrop.hover(() => {
        window.clearTimeout(timeout1)
        window.clearTimeout(timeout2)
        timeout1 = window.setTimeout(() => {
          $cascaderDrop.addClass('open')
        }, delay1)
      }, () => {
        timeout2 = window.setTimeout(() => {
          window.clearTimeout(timeout1)
          $cascaderDrop.removeClass('open')
        }, delay2)
      })
      $cascaderDrop.click(function (e) {
        e.stopPropagation()
      })
    },
    showDrop() {
      window.setTimeout(() => {
        $(this.$refs.cascaderDrop).trigger('mouseover')
        this.getSubList(this.list[0])
      }, 300)
    },
    deleteValue(type) {
      this[type] = ''
      this.list.forEach(item => {
        item.hidden = false
        item.showName = item.showName.replace(/<\/?.+?>/g, '')
      })
    },
    search() {
      const fatherArr = []
      // 由外向内
      const checkFunc = (arr, father) => {
        arr.forEach(item => {
          this.$set(item, 'showName', item.name.replace(/<em>|<\/em>/g, ''))
          this.$set(item, 'hidden', true)
          if (this.searchKey) {
            if (item.name.indexOf(this.searchKey) > -1) {
              item.showName = item.name.replace(new RegExp(this.searchKey, 'g'), `<em>${this.searchKey}</em>`)
              item.hidden = false
            } else {
              item.hidden = (father && !father.hidden) ? father.hidden : true
            }
          } else {
            item.hidden = false
          }
          if (item.list?.length) {
            fatherArr.push(item)
            checkFunc(item.list, item)
          }
        })
      }
      // 由内向外
      checkFunc(this.list)

      const length = fatherArr.length
      for (let i = length - 1; i >= 0; i--) {
        if (fatherArr[i].list?.length && fatherArr[i].hidden) {
          fatherArr[i].hidden = this.searchFatherFilter(fatherArr[i].list)
        }
      }

      // 默认展示并展开第一条有效数据
      const obj = this.list.find(item => !item.hidden)
      this.expandSub(obj)
    },
    // 获取父级展示状态
    searchFatherFilter(arr) {
      let hidden
      if (arr.every(ele => ele.hidden === true)) {
        hidden = true
      } else {
        hidden = false
      }
      return hidden
    },
    changePlacement(value) {
      const toLeft = this.$refs.cascaderDrop ? this.$refs.cascaderDrop.getBoundingClientRect().left : 0
      const menuWidth = this.$refs.dropdownMenu ? parseInt(this.$refs.dropdownMenu.offsetWidth) : 0
      const clientWidth = document.body.clientWidth
      // 当元素宽度变大时，进行刷新位置
      // value为true 重新刷新位置
      if (this.left > clientWidth - (toLeft + menuWidth) - 20 || value) {
        if ((toLeft + menuWidth) > clientWidth) {
          this.left = clientWidth - (toLeft + menuWidth) - 20
        } else {
          this.left = 0
        }
      }
    },
    clickItem(checkedItem) {
      if (!this.multi && this.trigger === 'click') {
        if (this.type === 'area') {
          // console.log(item)
          let str = ''
          let hasCode = false
          areaList.forEach((item) => {
            if (item?.list?.length) {
              item.list.forEach(subItem => {
                if (subItem?.list?.length) {
                  subItem.list.forEach(thirdItem => {
                    if (thirdItem.code === checkedItem.code) {
                      hasCode = true
                      str = item.name + subItem.name + thirdItem.name
                    }
                  })
                } else {
                  if (subItem.code === checkedItem.code) {
                    hasCode = true
                    str = item.name + subItem.name
                  }
                }
              })
            }
          })
          if (hasCode) {
            this.$emit('select', { name: str, code: checkedItem.code })
          }
        } else {
          this.$emit('select', checkedItem)
        }
      }
    },
    clickClose(e) {
      if (!this.multi && this.trigger === 'click' && this.type === 'area' && (e.target?.className?.indexOf('can-close-item') !== -1 || e.target?.parentNode?.className?.indexOf('can-close-item') !== -1)) {
        return
      }
      e.stopPropagation()
    }
  }
}
