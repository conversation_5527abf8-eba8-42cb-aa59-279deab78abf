.dropdown-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  font-size: 13px;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid transparent;
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);

  &>li>a {
    padding: 6px 15px;
    font-size: 14px;

    &.selected {
      color: #128bed;
    }

    &:hover {
      color: #128bed;
      background-color: #f2f9fc;
    }
  }

  &.dropdown-menu-container {
    overflow-x: hidden;
  }
}
.btn {
    display: inline-flex;
    align-items: center;
    margin-bottom: 0;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    padding: 6px 10px;
    font-size: 12px;
    line-height: 16px;
    border-radius: 3px;
    user-select: none;

  &:hover {
    color: #128bed !important;
    border-color: #128bed !important;
  }

  .glossary-info {
    margin-left: 0px;

    &:hover {
      color: #128bed;
    }
  }

  &.has-clear-btn {

    .clear-btn {
      font-size: 12px;
      display: none;
    }

  }

  &.btn-selected {
    color: #128bed !important;
    border-color: #128bed !important;

    &.has-clear-btn {

      &:hover {
        padding-right: 10px !important;

        .caret {
          display: none;
        }

        .clear-btn {
          display: inline-block;
          color: #88C5F6;
          margin-right: -2px;
          vertical-align: -1px;

          &:hover {
            color: #128bed;
          }

          &:active {
            color: #0069BF;
          }
        }

      }

    }

  }
}

.dropdown-menu li>a {

  &.selected {
    color: #128bed;

    .text-gray {
      color: #128bed;
    }
  }
}

