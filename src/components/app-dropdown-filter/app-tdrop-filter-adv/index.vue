<template>
  <span v-if="list && list.length>0" class="tdrop tdrop-adv">
      <span :class="['btn']" data-toggle="dropdown" ref="dropBtn">
        <span class="desc">{{filterDesc || name}}</span>
        <span class="caret"></span>
      </span>
      <div
        class="dropdown-menu dropdown-menu-container"
        :style="{ width: `${ hItems.length * ( subColWidth || colWidth ) + colWidth + 1}px`,left:`${dropBtnWidth - colWidth}px` }">
        <div class="clearfix">
          <div
            ref="menus"
            class="pull-left dropdown-level">
            <ul class="drop-col" ref="firstPart" :style="{ width: `${colWidth}px` }">
              <li v-if="showDefault">
                <a @mouseenter="getSubList({}, 0)" @click="filter">{{allText}}</a>
              </li>
              <template v-for="(item, index) in list">
                <li v-if="item.desc" :key="`drop_col_${index}`">
                  <!-- :title="item.desc" -->
                  <a
                    class="item"
                    :class="[hItems[0] && hItems[0].value === item.value ? 'hover' : '', currentValue === item.value && 'selected', {'text-gray': !item.count}]"
                    @mouseenter="getSubList(item)"
                    @click="filter($event, item)">
                    <span class="text" v-html="getText(item)" :title="item.desc"></span>
                    <i v-if="item.list && item.list.length > 0" class="fa fa-caret-right"></i>
                  </a>
                </li>
              </template>
            </ul>
          </div>
          <template v-for="(hItem, hIndex) in hItems">
            <div
              v-if="hItem.list && hItem.list.length"
              :class="`pull-left dropdown-level sub-level-${hItem.level}`"
              :key="`subList_${hItem.level}_${hIndex}`">
              <ul class="drop-col" :style="{ width: `${subColWidth || colWidth}px` }">
                <li
                  v-for="(item, index) in hItem.list"
                  :key="`drop_col_${hItem.level}_${index}`">
                  <a
                    class="item"
                    :class="[hItems[hIndex+1] && hItems[hIndex+1].value === item.value ? 'hover' : '', currentValue === item.value && 'selected', {'text-gray': !item.count}]"
                    @mouseenter="getSubList(item, hItem.level)"
                    @click="filter($event, item)">
                    <span class="text" v-html="getText(item)" :title="item.desc"></span>
                    <i v-if="item.list && item.list.length > 0" class="fa fa-caret-right"></i>
                  </a>
                </li>
              </ul>
            </div>
          </template>
        </div>
      </div>
  </span>
</template>
<style lang="scss" src="./style.scss" scoped></style>
<script src="./component.js"></script>

<style lang="scss">
.tdrop {
  display: inline-block;
  position: relative;

  .btn {
    border: 1px solid #d8d8d8 !important;
    color: #666 !important;
    background-color: #fff !important;
    box-shadow: none;

    &:hover {
      color: #128bed !important;
      border-color: #128bed !important;
    }

    &:active {
      color: #336CB4 !important;
      border-color: #336CB4 !important;
    }

    &.active {
      color: #128bed !important;
      border-color: #128bed !important;
    }

    .desc {
      display: inline-block;
      max-width: 90px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: bottom;
    }

  }

  .dropdown-menu {
    background: #fff;
    max-height: 400px;
    overflow-y: auto;

    &>li>a {
      padding: 6px 15px;
      font-size: 14px;

      &:hover {
        color: #128bed;
        background-color: #f2f9fc;
      }
    }
  }

  .tdrop-content {
    width: 500px;
    padding: 15px 15px;
  }

  .tdrop-content-line:not(:first-child) {
    border-top: solid 1px #eee;
    padding-top: 10px;
    margin-top: 10px;
  }

  .tdrop-head {
    color: #999;
    margin-bottom: 5px;
  }

  .caret {
    margin-left: 5px;
    transition: transform 0.15s linear;
  }

  &.open {

    .caret {
      transform: rotate(180deg);
    }

  }
}
</style>
