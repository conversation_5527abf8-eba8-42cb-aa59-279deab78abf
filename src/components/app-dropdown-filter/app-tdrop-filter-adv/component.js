import _ from 'lodash'
export default {
  name: 'app-tdrop-filter-adv',
  model: {
    /**
     * @model
     * 同步当前选中项
     */
    prop: 'currentValue',
    event: 'currentValueChange'
  },
  props: {
    items: {
      type: Array
    },
    name: {
      type: String,
      default: ''
    },
    keyField: {
      type: String,
      default: ''
    },
    // 默认选项Name
    allText: {
      type: String,
      default: '不限'
    },
    // 是否显示默认选项
    showDefault: {
      type: Boolean,
      default: true
    },
    showCount: {
      type: Boolean,
      default: true
    },
    filterCount: {
      type: Boolean,
      default: false
    },
    sortDate: {
      type: Boolean,
      default: false
    },
    sortOther: {
      type: Boolean,
      default: false
    },
    cache: {
      type: Boolean
    },
    // 埋点事件名称
    eventName: {
      type: String,
      default: '企业主页维度筛选'
    },
    // 注释信息
    infos: {
      type: Object
    },
    // 默认选中显示值
    defaultDesc: {
      type: String,
      default: ''
    },
    // 默认选中显示值的value
    currentValue: {
      type: String,
      default: ''
    },
    // 是否可选有子列表的选项
    canFilterList: {
      type: Boolean,
      default: false
    },
    // 选项宽度
    colWidth: {
      type: Number,
      default: 220
    },
    // 子列表的选项宽度
    subColWidth: {
      type: Number,
      default: 0
    },
    expandSelected: {
      type: Boolean,
      default: false
    },
    /**
     * 缓存组，只刷新count
     */
    cacheGroup: {
      type: Boolean
    },
    /**
     * 按钮宽度获取为auto时，取默认宽度
     */
    defaultWidth: {
      type: [Number, String],
      default: 93
    }
  },
  data() {
    return {
      filterDesc: '',
      isCascader: false, // 是否有二级
      cacheList: null,
      hItems: [],
      dropBtnWidth: 0
    }
  },
  computed: {
    list() {
      if (this.cacheList) {
        if (this.cache) {
          return this.cacheList
        }


        if (this.cacheGroup && this.cacheList) {
          return this.setItemValue(this.cacheList, this.items)
        }
      }
      if (this.items?.length) {
        this.items.forEach(item => {
          if (item?.list?.length) {
            this.isCascader = true
          }
        })
        if (this.filterCount) { // 过滤count为0
          return this.items.filter(item => item.count > 0)
        }
        // 日期升降序
        if (this.sortDate) {
          this.items.sort((a, b) => {
            return parseInt(b.value) - parseInt(a.value)
          })
        }
        if (this.sortOther) {
          this.items.map((v, index) => {
            if (v.desc === '其他') {
              this.items.push(v)
              this.items.splice(index, 1)
            }
            return v
          })
        }
        return this.items
      }
      return []
    }
  },
  watch: {
    defaultDesc: {
      handler(val) {
        this.filterDesc = val
      },
      immediate: true
    },
    filterDesc() {
      this.computedDropBtnWidth()
    },
    list() {
      if (this.expandSelected) {
        this.expandByValue()
        return
      }
      // 当筛选某一项时，再次点击下拉，自动展开至对应选中位
      if (this.filterDesc && this.filterDesc !== '不限') {
        this.expandAll()
      }
    }
  },
  created() { },
  mounted() {
    if (this.cache || this.cacheGroup) {
      this.cacheList = _.cloneDeep(this.list)
    }
    this.$nextTick(() => {
      this.computedDropBtnWidth()
    })
  },
  methods: {
    getText({ desc, count }) {
      return `<span class="margin-r-5px">${desc}</span>${this.showCount && count ? `<span class="show-count text-gray">(${count})</span>` : ''}`
    },
    getSubList(item, level = 0) {
      if (item.list && item.list.length > 0) {
        const hItem = item
        hItem.level = level + 1
        if (hItem.level > this.hItems.length) {
          this.hItems.push(hItem)
        } else {
          this.hItems = this.hItems.slice(0, level)
          this.hItems[level] = hItem
        }
      } else if (level === 1 && this.hItems[0]) {
        this.hItems = [this.hItems[0]]
      } else if (level === 0) {
        this.hItems = []
      }
      this.$forceUpdate()
    },
    filter(e, item) {
      if (item) {
        if (!this.canFilterList && item.list?.length) {
          return e.stopPropagation()
        } else {
          this.filterDesc = item.desc
          this.$emit('currentValueChange', item.value)
          this.$emit('filter', { key: this.keyField, ...item })
        }
      } else {
        this.filterDesc = ''
        this.$emit('currentValueChange', '')
        this.$emit('filter', { key: this.keyField, value: '', desc: '' })
      }
      this.hItems = []
    },
    clearSelect() {
      this.filterDesc = ''
      this.$emit('currentValueChange', '')
      this.$emit('filter', { key: this.keyField, value: '', desc: '' })
      this.hItems = []
    },
    // 计算下拉触发按钮的宽度
    computedDropBtnWidth() {
      if (this.$refs?.dropBtn) {
        this.$nextTick(() => {
          let width = window.getComputedStyle(this.$refs.dropBtn).width
          if (width === 'auto') {
            width = this.defaultWidth
          }
          this.dropBtnWidth = parseInt(width) + 6
        })
      }
    },
    expandAll() {
      if (this.list?.length) {
        const level1 = this.list[0]
        if (level1.list?.length) {
          this.hItems[0] = { ...level1, level: 1 }
          if (level1.value !== this.currentValue && level1.list[0].value !== this.currentValue) {
            const level2 = level1.list[0]
            this.hItems.push({
              ...level2,
              level: 2
            })
          }
        }
      }
    },
    expandByValue() {
      if (!this.list?.length) {
        return
      }

      const desc = this.filterDesc
      const findPath = (nodes, path) => {
        if (!Array.isArray(nodes) || !nodes.length) {
          return null
        }

        for (const node of nodes) {
          const currentPath = path.concat(node)

          if (node.desc === desc) {
            return currentPath
          }

          const nextPath = findPath(node.list, currentPath)

          if (nextPath) {
            return nextPath
          }
        }


        return null
      }

      const path = findPath(this.list, [])

      if (path && path.length > 1) {
        this.hItems = path.slice(0, -1).map((node, i) => ({
          ...node,
          level: i + 1
        }))
      } else {
        this.hItems = []
      }
    },
    setItemValue(cacheList, list) {
      return cacheList.map(item => {
        const mitem = list?.find(v => v.value === item.value)
        if (mitem) {
          item.count = mitem.count
        } else {
          item.count = 0
        }
        if (item?.list) {
          this.setItemValue(item.list, mitem?.list)
        }
        return item
      })
    }
  }
}
