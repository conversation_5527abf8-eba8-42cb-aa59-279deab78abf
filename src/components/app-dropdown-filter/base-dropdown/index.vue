<script src="./component.jsx"></script>

<style scoped lang="scss">
  .popover-button {
    position: relative;
  }
  .el-icon-arrow-down {
    transition: linear all 0.2s;
    &.arrow-rotate {
      transform: rotate(180deg);
    }
  }
</style>
<style lang="scss">
  .dropdown-filters-popper.el-popper {
    margin: 0 0 -5px 0;
    padding: 0;
  }
  .popover-button.el-button.el-button--normal {
    color: #666;
  }
</style>
