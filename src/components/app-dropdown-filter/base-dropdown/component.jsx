import { omit } from 'lodash'
import { modelMixin } from '../../utils'
export default {
  name: 'BaseDropdown',
  mixins: [modelMixin],
  props: {
    title: {
      type: String,
      default: ''
    },
    count: {
      type: Number,
      default: 0
    },
    btnAttrs: {
      default: () => ({
        type: 'normal'
      }),
      type: Object
    }
  },
  data() {
    return {
    }
  },
  render() {
    const scopedSlots = {
      reference: () => this.$slots.reference || (
        <app-button type="normal" class={['popover-button', { 'button--hover': this.count > 0 }]} {...{ attrs: this.btnAttrs }}>
          <span class="margin-r-0-3x">{this.title || ''}</span>
          {this.count > 0 && <span class="count">{this.count}</span>}
          <i class={['el-icon-arrow-down', { 'arrow-rotate': this.selfModel }]}></i>
        </app-button>
      )
    }
    const attrs = omit(this.$attrs, ['btnAttrs', 'title', 'popper-class', 'count'])
    return (
      <el-popover v-model={this.selfModel} {...{ attrs }} {...this.$listeners} popper-class={`${this.$attrs.popperClass || ''} dropdown-filters-popper`} scopedSlots={scopedSlots}>
        {this.$slots.default}
      </el-popover>
    )
  }
}
