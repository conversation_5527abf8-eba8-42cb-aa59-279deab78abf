<template>
  <div class="_revelationship-reveal-container" :style="options.containerStyle">
    <img :src="options.iconnew ? logos.emptyLogo : logos.emptyTipsLogo" :width="imgWidth" />
    <div class="empty-content" :class="notMagintop">
      <span v-html="_emptyText"></span>
      <slot>
        <a class="no-border" v-if="options.btnText" @click="options.btnClick">{{options.btnText}}</a>
      </slot>
    </div>
  </div>
</template>

<script>
  import config from '../../config/client'

  export default {
    name: 'correlation-empty',
    props: ['options'],
    data() {
      return {
        logos: config.customize
      }
    },
    computed: {
      imgWidth() {
        return this.options.emptyImgWidth ? this.options.emptyImgWidth : 100
      },
      notMagintop() {
        return __QCC__ && !!this.options.iconnew ? 'notMagintop' : ''
      },
      _emptyText() {
        if (!__QCC__ && !__PLUGIN__) {
          return (this.options.emptyText || '暂无数据').replace('小查提示您', '')
        }
        return this.options.emptyText || '暂无数据'
      }
    },
    created() {}
  }

</script>

<style scoped lang="scss">
  @import "../../styles/common";

  ._revelationship-reveal-container {
    font-size: 14px;
    text-align: center;

    .empty-content {
      margin-top: 15px;
      margin-bottom: 15px;
      color: #909399;

      &.notMagintop {
        margin-top: 0;
      }
    }
  }

</style>
