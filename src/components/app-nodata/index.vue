<template>
  <div v-show="show"
       class="_app-nodata-wrapper"
       :class="[{ small: isSmallWidth },{ 'is-floating': isappendDomFlag }, { 'height-full': fullHeight }]"
       :style="_style">
    <div class="content-wrapper" :style="contentStyle">
      <img :src="getImgUrl" :width="imageWidth" class="emptyIcon" />
      <slot>
        <p class="_empty-text-wrapper">
          <span v-html="_text" class="_empty-text"></span>
        </p>
      </slot>
    </div>
  </div>
</template>

<script>
  import config from '../../config/client'
  /**
   *  兼容：原大小 图宽80 高度132 或 图宽 120， 高度176
   *  现： 图宽60 高度132 或 图宽 100， 高度176
   *
   */
  export default {
    name: 'app-nodata',
    props: {
      text: { default: '暂无数据' },
      show: { default: true, type: Boolean },
      options: {},
      contentStyle: { default: '' },
      imgtype: { default: 1, type: [Number, String] }, // 定义图片的类型
      imgWidth: { default: 100, type: [Number, String] },
      imgSrc: { default: '', type: [String, Object] },
      fullHeight: { default: false, type: Boolean }
    },
    data() {
      return {}
    },
    computed: {
      _text() {
        if (!__QCC__ && !__PLUGIN__) {
          return (this.appendOptions_?.text || this.text || '暂无数据').replace('小查提示您', '')
        }
        return this.appendOptions_?.text || this.text || '暂无数据'
      },
      _style() {
        let style = {}
        if (this.isappendDomFlag && this.appendOptions_.style) {
          if (this.appendOptions_.style.top) {
            style.height = `calc(100% - ${this.appendOptions_.style.top})`
          }
          if (this.appendOptions_.style.left) {
            style.width = `calc(100% - ${this.appendOptions_.style.left})`
          }
          style = { ...style, ...this.appendOptions_.style }
        }
        return style
      },
      isappendDomFlag() {
        return !!this.appendDomFlag_
      },
      imageWidth() {
        return this.appendOptions_?.imgWidth || this.imgWidth || 100
      },
      isSmallWidth() {
        let isSmallWidth = false
        try {
          isSmallWidth = Number(this.appendOptions_?.imgWidth || this.imgWidth || 100) <= 80
        } catch (e) {} finally {
          isSmallWidth = Number(this.imgWidth || 100) <= 80
        }
        return isSmallWidth
      },
      getImgUrl() {
        if (this.imgSrc) {
          return this.imgSrc
        }
        switch (this.imgtype.toString()) {
          case '1':
            return config.customize.emptyLogo
          case '2':
            return config.customize.emptyTipsLogo
        }
      }
    }
  }

</script>

<style scoped lang="scss">
._app-nodata-wrapper {
  font-size: 14px;
  color: #999999;
  text-align: center;
  position: relative;
  &.height-full {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &.is-floating {
    position: absolute;
    z-index: 99;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    display: flex;
    align-items: center;
  }
  &.small {
    >div.content-wrapper {
      padding-top: 20px;
      padding-bottom: 19px;
      font-size: 12px;
      ._empty-text-wrapper {
        height: 18px;
        ._empty-text {
          display: inline-block;
          line-height: 18px;
          height: 18px;
        }
      }
    }
  }
  >div.content-wrapper {
    display: inline-block;
    width: 100%;
    padding: 20px 10px;
    text-align: center;
    ._empty-text-wrapper {
      height: 20px;
      ._empty-text {
        display: inline-block;
        line-height: 20px;
        height: 20px;
      }
    }
  }
  img.emptyIcon {
    margin-bottom: 12px;
  }
}


</style>
