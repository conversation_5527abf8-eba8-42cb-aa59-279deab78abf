<template>
  <div v-if="showBreadCrumb" class="__app-container" :class="[{scroll: scroll}, {fixedBreadCrumb: fixedBreadCrumb}, layout]">
    <slot name='header'>
      <app-breadcrumb v-bind="$attrs"
        :title='title'
        :needReplace="needReplace"
        :isNewBread="isNewBread"
        @back="back"
        :v2="breadcrumbv2"
        :secondTitle="secondTitle"
        :secondButton="secondButton"
        :cropInfo="cropInfo"
        :widthAuto="$attrs.breadWidthAuto">
        <slot slot="titletips" name="titletips"></slot>
        <slot name="headerRight"></slot>
      </app-breadcrumb>
    </slot>
    <template v-if="fixedBreadCrumb">
      <div class="__fixedBreadCrumb" :id="containerBodyId" :class="[isNewBread ? '__newFixedBreadCrumb' : '']">
        <div class="__app-container-body" :style="bodyStyle" :class="[bodyClass, isNewBread ? '__new-bread-head' : '']">
          <slot></slot>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="__app-container-body" :id="containerBodyId" :style="bodyStyle" :class="[bodyClass,isNewBread ? '__new-bread-head' : '']">
        <slot></slot>
      </div>
    </template>
  </div>
  <div v-else class="__app-container-without-bread-crumb" :class="[{scroll: scroll}]">
    <div class="__app-container-body" :id="containerBodyId" :style="bodyStyle" :class="[bodyClass,isNewBread ? '__new-bread-head' : '']">
      <slot></slot>
    </div>
  </div>
</template>
<script>
export default {
  name: 'app-container',
  props: {
    title: { type: String, default: '' },
    needReplace: { type: Boolean, default: false },
    scroll: { type: Boolean, default: false },
    fixedBreadCrumb: { type: Boolean, default: false },
    showBreadCrumb: { type: Boolean, default: true },
    breadcrumbv2: { type: Boolean, default: true },
    miniScreenAdapter: { type: Boolean, default: false },
    isMiniScreen: { type: [Object, String, Boolean], default: '' },
    bodyStyle: { type: Object, default: () => { return {} } },
    bodyClass: { type: String, default: '' },
    layout: { type: String, default: '' },
    isNewBread: { type: Boolean, default: false },
    secondTitle: { type: String, default: '' },
    secondButton: { default: null },
    cropInfo: { default: null },
    containerBodyId: { default: '', type: String }
  },
  methods: {
    back() {
      this.$emit('back')
    }
  }
}

</script>


<style lang="scss" scoped>
@import "../../styles/common";

.__app-container {
  width: 100%;
  background-color: #F7F7F7;
  height: 100%;

  &.side-layout {
    .__app-container-body {
      overflow: visible;
    }
  }

  .__app-container-body {
    height: calc(100% - 70px);
    width: calc(100% - 20px);
    margin-left: 10px;
    margin-top: 10px;
    box-sizing: border-box;
    overflow: auto;
    overflow-x: hidden;

    &.__new-bread-head {
      height: calc(100% - 60px);
    }

    .__content-body {
      height: 100%;

      &.padding10 {
        padding: 10px;
      }

      &.bg-white {
        background: white;
      }
    }

  }

  &.scroll {
    overflow: auto;

    .__app-container-body {
      height: auto;
      min-height: calc(100% - 70px);
      margin-bottom: 10px;

      &.__new-bread-head {
        min-height: calc(100% - 60px);
      }
    }

    &.fixedBreadCrumb {
      height: calc(100% - 10px);

      .__fixedBreadCrumb {
        width: calc(100% - 10px);
        margin-top: 10px;
        height: calc(100% - 70px);
        overflow: auto;

        &.__newFixedBreadCrumb {
          height: calc(100% - 60px);
        }

        .__app-container-body {
          min-height: 100%;
          width: calc(100% - 10px);
          margin-top: 0;
          margin-bottom: 0px;
        }
      }
    }
  }
}

.__app-container-without-bread-crumb {
  width: 100%;
  background-color: #F7F7F7;
  height: 100%;

  .__app-container-body {
    height: 100%;
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    overflow: auto;

    .__content-body {
      height: 100%;

      &.padding10 {
        padding: 10px;
      }

      &.bg-white {
        background: white;
      }
    }
  }

  &.scroll {
    overflow: auto;

    .__app-container-body {
      height: auto;
    }
  }
}

</style>
