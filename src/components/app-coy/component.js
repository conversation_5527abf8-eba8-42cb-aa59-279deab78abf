import { isEmpty, isArray, isObject } from 'lodash'

/**
 * 跳转详情链接封装，根据Org自动判断类型，多用于NameAndKeyNos数组
 * @displayName app-coy
 */
export default {
  name: 'app-coy',
  props: {
    /**
     * 传数组
     * @values [{KeyNo:'9cce0780ab7644008b73bc2120479d31',Name:'小米科技有限责任公司'}]
     */
    coyArr: {
      type: [Array, Object]
    },
    coyMaxlength: {
      type: Number,
      default: 10
    },
    /**
     * 传对象
     * @values {KeyNo:'9cce0780ab7644008b73bc2120479d31',Name:'小米科技有限责任公司'}
     */
    coyObj: {
      type: Object
    },
    /**
     * 从数组里找名字等于coyArrFname的一个元素
     */
    coyArrFname: {
      type: String
    },
    needLink: {
      type: Boolean,
      default: true
    },
    innerA: {
      // 在a标签里面
      type: Boolean,
      default: false
    },
    moreGov: {
      type: Boolean,
      default: false
    },
    /**
     * 分隔符
     * @values ,、 <br>、空字符
     */
    sep: {
      type: String,
      default: '，'
    },
    /**
     * 阻止冒泡
     */
    stopPropagation: {
      type: Boolean,
      default: false
    },
    // 是否需要登录
    loginPrevent: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    handledCoyArr() {
      let coyArr = []
      if (isArray(this.coyArr)) {
        coyArr = this.coyArr
      } else if (isObject(this.coyArr)) {
        coyArr = [this.coyArr]
      }
      // 数据格式兼容
      if (coyArr) {
        let newCoyArr = coyArr
          .filter((item) => item)
          .map((item) => ({
            ...item,
            KeyNo: item.keyno || item.KeyNo,
            Name: item.name || item.Name
          }))
        if (this.coyMaxlength) {
          newCoyArr = newCoyArr.splice(0, this.coyMaxlength)
        }
        if (this.coyArrFname) {
          let noTagsStr = this.coyArrFname.replace(/<[^>]+>/g, '')
          const newCoyObj = newCoyArr.find((item) => {
            if (item.Name === noTagsStr) {
              item.Name = this.coyArrFname
              return true
            }
            return false
          })
          if (newCoyObj) {
            newCoyArr = [newCoyObj]
          } else {
            newCoyArr = []
          }
        }
        return newCoyArr
      }
    }
  },
  render: function (h) {
    let eleTag = this.innerA ? 'object' : 'span'
    if (this.handledCoyArr && this.handledCoyArr.length) {
      let eles = []
      this.handledCoyArr.forEach((v, k) => {
        eles.push(this.createCoy(h, v))
        if (k !== this.handledCoyArr.length - 1) {
          eles.push(h('span', { domProps: { innerHTML: this.sep } }))
        }
      })
      return h(eleTag, eles)
    } else if (this.coyObj) {
      return h(eleTag, [this.createCoy(h, this.coyObj)])
    } else {
      return h('span', '-')
    }
  },
  methods: {
    createCoy(h, obj) {
      let keyNo = obj.KeyNo || obj.keyNo || ''
      let companyKeyNo = obj.CompanyKeyNo || obj.companyKeyNo || ''
      let name = obj.Name || obj.name || '-'
      let org = obj.Org || obj.org
      if (!isEmpty(org)) {
        org = parseInt(org)
      }
      if (keyNo) {
        let orgPlink = `/companyDetail?keyNo=${keyNo}`

        if (org === 99) {
          orgPlink = `/product-info?id=${keyNo}`
          keyNo = ''
        } else if (org === 13 || org === -3) {
          orgPlink = `/investAgency?investId=${keyNo}`
          keyNo = ''
        } else if (org === 6) {
          orgPlink = '/' /// //私募---未做
          keyNo = ''
        } else if (org === 16) {
          orgPlink = `/webfund/${keyNo}` /// //公募
          keyNo = ''
          // return h('span', { domProps: { innerHTML: name } })
        } else if (org === -1) {
          return h('span', { domProps: { innerHTML: name } }) // 无法判断
        } else if (keyNo[0] === 'p') {
          orgPlink = `/beneficaryDetail?personId=${keyNo}`
          keyNo = ''
        }
        return h('app-qcclink', {
          domProps: {
            innerHTML: name,
            target: '_blank'
          },
          props: {
            href: orgPlink,
            loginPrevent: this.loginPrevent,
            stopPropagation: this.stopPropagation,
            keyNo: this.isForbidOurterLinkerFlag ? keyNo : ''
          }
        })
      } else if ((org === -2 || !org) && companyKeyNo && name) {
        return h('app-qcclink', {
          domProps: {
            innerHTML: name,
            target: '_blank'
          },
          props: {
            href: `/beneficaryDetail?companyId=${companyKeyNo}&personName=${encodeURIComponent(
              name
            )}`
          }
        })
      } else if (this.moreGov) {
        return h('app-qcclink', {
          domProps: {
            innerHTML: name,
            target: '_blank'
          },
          props: {
            href: `/search/?searchkey=${encodeURIComponent(name)}` /// 更多机构
          }
        })
      } else {
        return h('span', {
          domProps: { innerHTML: name }
        })
      }
    }
  }
}
