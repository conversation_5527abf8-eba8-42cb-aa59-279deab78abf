<template>
  <div>
    <div ref="textContent" class="text-content">
      <span v-html="showText"></span>
      <div class="_more-text auto" v-if="hasMoreLess">
        <span v-if="!showMore">...</span>
        <a @click="seeMore">{{showMore ? '收起': '更多'}}</a>
      </div>
    </div>
  </div>
</template>
<script>
  import { windowResizeMixins } from '../../utils/global-util'
  export default {
    name: 'more-less-text',
    mixins: [windowResizeMixins],
    props: {
      options: { default: undefined, type: Object }
    },
    data() {
      return {
        showMore: false,
        showText: ''
      }
    },
    computed: {
      allText() {
        return this.options.columprop.format(this.options.row)
      },
      textLength() {
        return this.options.columprop.options && this.options.columprop.options.textLength ? this.options.columprop.options.textLength : 0
      },
      hasMoreLess() {
        if (this.textLength && this.textLength < this.allText.length) {
          return true
        } else {
          return false
        }
      }
    },
    watch: {
      allText() {
        this.setShowText()
      }
    },
    methods: {
      seeMore() {
        this.showMore = !this.showMore
        this.setShowText()
      },
      setShowText() {
        let res = this.allText
        if (!this.showMore) {
          if (this.hasMoreLess) {
            res = res.substring(0, this.textLength)
          }
        }
        this.showText = res
      }
    },
    mounted() {
      this.setShowText()
    }
  }

</script>
<style lang="scss" scoped>
  .text-content {
    width: 100%;
    position: relative;
    overflow: 'auto';
    max-height: unset;

    ._more-text {
      position: absolute;
      right: 0;
      bottom: 0;
      background: white;
      cursor: pointer;

      &.auto {
        position: relative;
        display: inline-block;
        width: auto;
      }
    }
  }

</style>
