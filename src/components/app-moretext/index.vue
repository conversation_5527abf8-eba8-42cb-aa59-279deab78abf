<template>
  <div>
    <div ref="textContent" class="text-content" :style="textContentStyle">
      <span v-html="text"></span>
      <div class="_more-text" :class="textLength ? 'auto' : ''" v-if="!hideButton"><span v-if="!showMore && !textLength">...</span> <a @click="seeMore">{{showMore ? '收起': '更多'}}</a></div>
    </div>
  </div>
</template>
<script>
  import { windowResizeMixins } from '../../utils/global-util'
  import { showContent } from '../../routes/companyDetail/detail-pops'
  export default {
    name: 'app-moretext',
    mixins: [windowResizeMixins],
    props: {
      options: { default: undefined, type: Object }
    },
    data() {
      return {
        showMore: false,
        hideButton: true
      }
    },
    computed: {
      text() {
        let res = this.options.columprop.format(this.options.row)
        if (this.textLength && this.textLength < res.length) {
          res = res.substring(0, this.textLength) + '...'
        }
        this.$nextTick(() => {
          this.showMore = false
          this.calculateText()
        })
        return res
      },
      textLength() {
        return this.options.columprop.options && this.options.columprop.options.textLength ? this.options.columprop.options.textLength : 0
      },
      type() {
        // return this.options.columprop.options && this.options.columprop.options.type ? this.options.columprop.options.type : ''
        return this.options.columprop.options ? (this.options.columprop.options.type || 0) : ''
      },
      line() {
        return this.options.columprop.options && this.options.columprop.options.line ? this.options.columprop.options.line : 3
      },
      lineHeight() {
        return this.options.columprop.options && this.options.columprop.options.lineHeight ? this.options.columprop.options.lineHeight : 19
      },
      textContentStyle() {
        let res = {
          lineHeight: this.lineHeight + 'px'
        }
        if (!this.showMore && !this.textLength) {
          res.maxHeight = `${this.lineHeight * this.line}px`
          res.overflow = 'hidden'
        }
        return res
      }
    },
    methods: {
      seeMore() {
        if (!this.type && this.type !== 0) {
          this.showMore = !this.showMore
        } else {
          showContent('行政处罚内容详情', this.options.columprop.format(this.options.row) + '<br><br>', this.type)
        }
      },
      onWindowSizeChange() {
        this.$nextTick(() => {
          this.showMore = false
          this.calculateText()
        })
      },
      // 计算文字 显示展开 收起
      calculateText() {
        if (this.textLength) {
          let text = this.options.columprop.format(this.options.row)
          this.hideButton = text.length <= this.textLength
        } else {
          this.hideButton = this.$refs.textContent.scrollHeight <= (this.line * this.lineHeight)
        }
      }
    }
  }

</script>
<style lang="scss" scoped>
  .text-content {
    width: 100%;
    position: relative;
    overflow: 'auto';
    max-height: unset;

    ._more-text {
      position: absolute;
      right: 0;
      bottom: 0;
      background: white;
      cursor: pointer;

      &.auto {
        position: relative;
        display: inline-block;
        width: auto;
      }
    }
  }

</style>
