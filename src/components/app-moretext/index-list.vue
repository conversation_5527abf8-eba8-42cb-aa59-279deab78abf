<template>
  <div class="text-content-c">
    <div ref="textContent" class="text-content" v-if="hasData">
      <div :class="isMoreShow&&!showMore?'text-content-maxLength':''">
        <template v-for="(line,index) in listData">
          <div :class="isInline?'textContent-line':''" :key="index+'text-content'" v-show="needShow(line,index)">
            <span v-html="line"></span>
            <span v-if="isInline && (index!==(listData.length-1))" v-html="dotStyle"></span>
          </div>
        </template>
      </div>
      <div class="_more-text auto" v-if="hasMoreLess">
        <span v-if="!showMore">...</span>
        <a @click="seeMore">{{showMore ? '收起': '更多'}}</a>
      </div>
    </div>
    <span v-else>-</span>
  </div>
</template>
<script>
  import { windowResizeMixins } from '../../utils/global-util'
  export default {
    name: 'more-less-list',
    mixins: [windowResizeMixins],
    props: {
      options: { default: undefined, type: Object }
    },
    data() {
      return {
        showMore: false,
        showText: ''
      }
    },
    computed: {
      isMoreShow() {
        return this.options.columprop.isMoreShow
      },
      listMoreLine() {
        return this.options.columprop.listMoreLine || 4
      },
      isInline() {
        return this.options.columprop.isInline
      },
      dotStyle() {
        return this.options.columprop.dotStyle || ','
      },
      listData() {
        return this.options.columprop.format(this.options.row)
      },
      hasData() {
        return this.listData && this.listData.length
      },
      hasMoreLess() {
        if (this.listData && this.listData.length > this.listMoreLine) {
          return true
        } else {
          return false
        }
      }
    },
    watch: {
      'options.row'(newName, oldName) {
        this.$nextTick(() => {
          this.showMore = false
        })
      }
    },
    methods: {
      onWindowSizeChange() {
        this.$nextTick(() => {
          this.showMore = false
        })
      },
      needShow(item, index) {
        if (this.isMoreShow) {
          return true
        }

        if (index < this.listMoreLine) {
          return true
        } else {
          if (this.hasMoreLess) {
            if (!this.showMore) {
              return false
            } else {
              return true
            }
          } else {
            return false
          }
        }
      },
      seeMore() {
        this.showMore = !this.showMore
      }
    }
  }

</script>
<style lang="scss" scoped>
  .text-content-c {
    position: relative;
    width: 100%;

    .text-content {
      width: 100%;

      overflow: 'auto';
      max-height: unset;

      .text-content-maxLength {
        max-height: 78px;
      }

      .textContent-line {
        display: inline;
      }

      ._more-text {
        position: absolute;
        right: 0;
        bottom: 0;
        background: white;
        cursor: pointer;

        &.auto {
          // position: relative;
          display: inline-block;
          width: auto;
        }
      }
    }
  }

</style>
