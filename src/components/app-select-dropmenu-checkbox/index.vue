<template>
  <span :class="'app-select-dropmenu-checkbox'+(isMiniScreen?' app-select-dropmenu-checkbox-min ':'') ">
    <span class="checkbox-title" v-if="!isMiniScreen">{{title}}：</span>
    <app-select-dropmenu style="margin-right:20px" :style="{width:selectWidth}" :text='inputText' :inputTitle="inputText" ref="changeRangeId" :popStyle="{width: popWidth}" :emptyShowText="emptyShowText">
      <filterlist-checkbox slot="selectContent" :selectBtnStyle="selectBtnStyle" :currentData="currentData" @cancelclick="filterSelectClose" dataLoadFinished labelkey='value' valuekey='name' @sureclick="filterSelectSuccess" allText="全部"></filterlist-checkbox>
    </app-select-dropmenu>
  </span>
</template>

<script>
  import filterlistCheckbox from '../app-select-dropmenu/filterlist-checkbox'
  const mScreen = (document.body.offsetWidth < 1450)
  export default {
    name: 'app-select-dropmenu-checkbox',
    components: { filterlistCheckbox },
    props: {
      title: {
        type: String,
        default: '200px'
      },
      list: {
        type: Array,
        default: () => { return [] }
      },
      selectWidth: {
        type: String,
        default: '200px'
      },
      selectBtnStyle: {
        type: String,
        default: ''
      },
      popWidth: {
        type: String,
        default: '200px'
      },
      isMiniScreen: {
        type: Boolean,
        default: mScreen
      }
    },
    data() {
      return {
        inputText: '全部'
      }
    },
    watch: {
      isMiniScreen() {
        this.resetInputText()
      }
    },
    computed: {

      emptyShowText() {
        return this.isMiniScreen ? this.title : '全部'
      },
      currentData() {
        return {
          dataList: this.list,
          model: [],
          valueFormat: (row) => {
            return `<span>${row.name}</span>`
          }
        }
      }
    },
    methods: {
      resetInputText() {
        if (!this.currentData.model.length || (this.currentData.dataList.length === this.currentData.model.length)) {
          this.inputText = this.emptyShowText
        }
        this.inputText = this.currentData.dataList.filter(t => this.currentData.model.indexOf(t.value) > -1).map(t => t.name).toString()
      },
      filterSelectClose() {
        this.$refs.changeRangeId.showMenu = false
      },
      filterSelectSuccess(val) {
        this.resetInputText()
        this.$emit('change', this.currentData.model)
        this.$refs.changeRangeId.showMenu = false
      }
    },
    mounted() {
      if (this.isMiniScreen) {
        this.resetInputText()
      }
    }
  }

</script>

<style lang="scss">
  .app-select-dropmenu-checkbox-min {
    .__mySelectWrapper {
      .el-input__inner {
        font-size: 12px;
      }
    }
  }

</style>
