<template>
  <span class="app-phone" :class="{'app-phone-old':isOld,'app-phone-login':isLog}" @mouseenter="phoneMover" @mouseleave="phoneLeave">
    <span class="iconfont icon-shouji2"></span>
    <div class="phone-qrcode" v-show="isShowPhone">
      <img src="/img/ipone-code.png?v=3" alt="">
    </div>
  </span>
</template>

<script>
  export default {
    name: 'app-phone',
    props: {
      isOld: { type: Boolean, default: false },
      isLog: { type: Boolean, default: false }
    },
    data() {
      return {
        isShowPhone: false
      }
    },
    methods: {
      phoneMover() {
        this.isShowPhone = true
      },
      phoneLeave() {
        this.isShowPhone = false
      }
    }
  }

</script>

<style lang="scss" scoped>
  .app-phone {
    position: relative;
    display: inline-flex;
    width: 28px;
    height: 28px;
    border-radius: 4px;
    align-items: center;
    justify-content: center;
    background: transparent;
    cursor: pointer;

    &:hover {
      background: #F2F8FE;

      .iconfont {
        color: #128BED;
      }
    }


    .iconfont {
      font-size: 18px;
      cursor: pointer;
      color: #666;
    }

    .phone-qrcode {
      position: absolute;
      top: 32px;
      right: 0;
      min-width: 160px;
      z-index: 100000;
      border: 1px solid #fff;
      border-radius: 4px;
      box-shadow: rgba(0, 0, 0, 0.3) 0px 5px 12px;

      img {
        width: 250px;
        vertical-align: middle;
      }
    }
  }

  .app-phone-old {
    .iconfont {
      color: #D8D8D8;
    }

    &:hover {
      background: transparent;

      .iconfont {
        color: #128BED;
      }
    }
  }

  .app-phone-login {
    .iconfont {
      color: #FFF;
    }
  }

</style>
