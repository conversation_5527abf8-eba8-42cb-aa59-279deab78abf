<template>
  <span v-if="getCompanyName" class="overrflow-with-ellipsis" v-html="getCompanyName" @click=""></span>
  <span v-else>-</span>
</template>

<script>
export default {
  name: 'app-company-link',
  props: {
    options: {}, // 该属性其它地方不要使用，只对用于表格
    name: {},
    keyno: {}

  },
  computed: {
    getCompanyName() {
      if (this.name) return this.name
      if (this.options && this.options.row && this.options.row != null) {
        if (this.options.columprop.config && this.options.columprop.config.formatText) {
          let res = this.options.columprop.config.formatText(this.options.row)
          return res || '-'
        }
        let val = this.options.row[this.options.columprop.config ? (this.options.columprop.config.compnameKey || 'corpName') : 'corpName']
        return $util.getCompanyOrPersonLinkerByOrg(val, this.getCompanyKeyNo)
      }
      return '-'
    },
    getCompanyKeyNo() {
      return this.keyno ? this.keyno : this.options.row[this.options.columprop.config ? (this.options.columprop.config.compkeynoKey || 'keyNo') : 'keyNo']
    }
  },
  methods: {

  }
}

</script>

<style scoped>

</style>
