<template>
  <div
    class="app-block"
    v-bind="$attrs"
    v-on="$listeners"
    :class="{
      'app-block--show-all': isShowAll,
      'app-block--detail': theme === 'detail'
    }"
    :style="[
      {
        '--line-height': `${lineHeight}px`,
        '--shrink-height': shrinkHeight
      }
    ]"
    @click.stop="contentExpand"
  >
    <div
      class="app-block__body"
      ref="app-block-body"
    >
      <slot />
    </div>
    <div
      class="app-block__mask"
      v-if="showMask"
      @click.stop="handleToggleShowAll()"
    >
    </div>
    <div
      class="app-block__stretch"
      v-if="showButton"
      :style="domLineHeight ? { lineHeight: domLineHeight + 'px' } : {}"
    >
      <template v-if="isShowAll">
        <a href="javascript:;" @click.stop="handleToggleShowAll()">收起</a>
      </template>
      <template v-else>
        <span> ...</span>
        <a href="javascript:;" @click.stop="handleToggleShowAll()">展开</a>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'app-auto-block',
  props: {
    theme: {
      type: String,
      default: 'table'
    },
    /**
     * 当前行的内容
     */
    row: {
      type: [Object, undefined],
      default: () => undefined
    },
    /**
     * 是否开启展开、收起
     */
    stretchEnabled: {
      type: Boolean,
      default: true
    },
    /**
     * 是否显示遮罩
     */
    mask: {
      type: Boolean,
      default: false
    },
    /**
     * 是否开启窗口监听事件
     */
    resize: {
      type: Boolean,
      default: true
    },
    /**
     * 监听到窗口改变后，延迟处理事件的时间
     * 单位为毫秒
     */
    resizeDelay: {
      type: Number,
      default: 200
    },
    /**
     * 行高
     */
    lineHeight: {
      type: Number,
      default: 19
    },
    useProplineHeight: {
      type: Boolean,
      default: false
    },
    /**
     * 行
     */
    line: {
      type: Number,
      default: 5
    },
    /**
     * 状态改变事件
     */
    onChange: {
      type: Function,
      default: () => {}
    },
    /**
     * 更新试图后延迟计算是否显示展开的时间
     */
    updateMesureDelayTime: {
      default: 0,
      type: Number
    }
  },
  data() {
    return {
      // 是否手动操作过的标记
      manual: false,
      showAll: !this.stretchEnabled,
      // dom行高
      domLineHeight: 0,
      resizeTimer: null,
      // 自动计算是否需要拉伸按钮
      stretchMarker: false
    }
  },
  computed: {
    isShowAll() {
      if (!this.row || this.row.expandFlag === undefined) {
        return this.showAll
      } else {
        if (this.row.expandFlag) {
          this.manual = true
        }
        return this.row.expandFlag
      }
    },
    /**
     * 是否显示按钮
     * @returns {*|boolean}
     */
    showButton () {
      return this.stretchEnabled && this.stretchMarker
    },
    /**
     * 是否显示遮罩
     * @returns {*|boolean}
     */
    showMask () {
      return this.stretchEnabled && this.stretchMarker && !this.isShowAll
        ? this.mask
        : false
    },
    /**
     * 伸缩高度
     * @returns {string|string}
     */
    shrinkHeight () {
      return (this.manual && this.isShowAll)
        ? 'unset'
        : (this.domLineHeight * this.line) + 'px'
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.update()
    })
    // 开启窗口监听事件，动态计算
    if (this.resize) {
      window.addEventListener('resize', this.handleResize)
    }
  },
  beforeDestroy () {
    if (this.resize) {
      window.removeEventListener('resize', this.handleResize)
    }
  },
  methods: {
    contentExpand() {
      if (!this.isShowAll && this.showButton) {
        this.handleToggleShowAll()
      }
    },
    /**
     * 切换展开收起状态
     */
    handleToggleShowAll() {
      this.manual = true
      this.showAll = !this.isShowAll
      if (this.row && typeof this.row === 'object') {
        if (this.row.expandFlag === undefined) {
          this.$set(this.row, 'expandFlag', this.showAll)
        } else {
          this.row.expandFlag = this.showAll
        }
      }
      this.$emit('change', {
        showAll: this.showAll
      })
    },
    /**
     * 计算是否需要拉伸
     */
    update () {
      setTimeout(() => {
        if (this.useProplineHeight && this.lineHeight) {
          this.domLineHeight = this.lineHeight
        } else {
          this.domLineHeight = parseFloat(getComputedStyle(this.$el).lineHeight)
          if (isNaN(this.domLineHeight)) {
            this.domLineHeight = this.lineHeight
          }
        }
        if (this.stretchEnabled) {
          const thresholdHeight = this.domLineHeight * this.line

          if (this.$refs['app-block-body']) {
            if (this.isShowAll && this.theme === 'detail') {
              this.stretchMarker = this.$refs['app-block-body'].offsetHeight > thresholdHeight
            } else {
              this.stretchMarker = this.$refs['app-block-body'].scrollHeight > thresholdHeight
            }
          }
        }
      }, this.updateMesureDelayTime)
    },
    /**
     * 窗口改变事件
     * 延时执行，解决频繁执行问题
     */
    handleResize () {
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer)
      }
      this.resizeTimer = setTimeout(() => {
        this.update()
      }, this.resizeDelay)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-block {
  position: relative;
  font-weight: initial;
  //font-size: 14px;
  &.bottom-shadow {
    &:before {
      position: absolute;
      content: ' ';
      display: block;
      width: 100%;
      left: 0;
      bottom: 0;
      height: var(--line-height, 15px);
      z-index: 1;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, #FFFFFF 83%);
    }
  }

  &__body {
    max-height: var(--shrink-height, unset);
    overflow: hidden;
    //white-space: pre-wrap;
  }

  &__mask {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.50) 0%, #FFFFFF 99%);
    position: absolute;
    height: 22px;
    bottom: 0;
    right: 0;
    left: 0;
    cursor: pointer;
  }

  &__stretch {
    position: absolute;
    bottom: 0;
    right: 0;
    //background: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 10%);
    background-color: #fff;
    padding-left: 2px;
    display: flex;
    z-index: 9;
    //line-height: 22px;
  }

  &--show-all:not(&--detail) {
    .app-block__stretch {
      position: relative;
      justify-content: flex-end;
      a {
        color: #999999;
        &:hover {
          color: #128bed;
        }
      }
    }
  }

  &--detail {
    &.app-block--show-all {
      .app-block__body {
        display: inline;
      }

      .app-block__stretch {
        position: unset;
        display: inline-flex;
        padding-left: 0;
      }
    }

    .app-block__body {
      line-height: var(--line-height);
    }
  }
}
</style>
