<template>
  <div
    class="app-block"
    v-bind="$attrs"
    v-on="$listeners"
    :class="{
      'app-block--show-all': showAll
    }"
    :style="[
      {
        '--shrink-height': showAll ? 'unset' : shrinkHeight
      }
    ]"
  >
    <div
      class="app-block__body"
      @click.stop="handleShowAll()"
    >
      <slot/>
    </div>
    <div
      class="app-block__mask"
      v-if="showMask && !showAll"
      @click.stop="handleToggleShowAll()"
    >
    </div>
    <div
      class="app-block__stretch"
      v-if="stretchEnabled"
    >
      <template v-if="showAll">
        <a href="javascript:;" @click.stop="handleToggleShowAll()">收起</a>
      </template>
      <template v-else>
        <span> ...</span>
        <a href="javascript:;" @click.stop="handleToggleShowAll()">展开</a>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'app-block',
  props: {
    /**
     * 是否开启展开、收起
     */
    stretchEnabled: {
      type: Boolean,
      default: false
    },
    /**
     * 是否显示遮罩
     */
    showMask: {
      type: Boolean,
      default: false
    },
    /**
     * 收起高度
     */
    shrinkHeight: {
      type: String,
      default: '66px'
    }
  },
  data() {
    return {
      // 展开收起标记
      showAll: !this.stretchEnabled
    }
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
    /**
     * 切换展开收起状态
     */
    handleToggleShowAll() {
      this.showAll = !this.showAll
    },
    /**
     * 显示全部
     */
    handleShowAll () {
      this.showAll = true
    }
  }
}
</script>

<style lang="scss" scoped>
.app-block {
  position: relative;
  font-size: 14px;

  &__body {
    max-height: var(--shrink-height, unset);
    overflow: hidden;
  }

  &__mask {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.50) 0%, #FFFFFF 99%);
    position: absolute;
    height: 22px;
    bottom: 0;
    right: 0;
    left: 0;
    cursor: pointer;
  }

  &__stretch {
    position: absolute;
    bottom: 0;
    right: 0;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 10%);
    padding-left: 2px;
    display: flex;
    line-height: 22px;
  }

  &--show-all {
    .app-block__stretch {
      position: relative;
      justify-content: flex-end;
      a {
        color: #999999;
        &:hover {
          color: #128bed;
        }
      }
    }
  }
}
</style>
