<template>
  <div v-if="layout === 'default'" class="date-range-wrapper">
    <div v-if="isMiniScreenShow">
      <el-date-picker ref="myDateRangeRef" v-model="dataRangeModel" @change="changeDate" class="__myDateRangeSelect" type="daterange" :align="align" unlink-panels range-separator="-" :size="size" start-placeholder="开始日期" end-placeholder="结束日期" :disabled="disabled" :clearable="clearable"
                      :default-value="timeDefaultShow" value-format="yyyy-MM-dd" :picker-options="pickerOptions"></el-date-picker>
      <span class="mini-screen-tip" @click="showSelectPannel" v-if="!dataRangeModel || dataRangeModel.length !== 2">{{emptyShowText}}</span>
    </div>
    <el-date-picker v-else ref="myDateRangeRef" v-model="dataRangeModel" @change="changeDate" class="__myDateRangeSelect" type="daterange"
                    :align="align" unlink-panels range-separator="-" :size="size" start-placeholder="开始日期" end-placeholder="结束日期"
                    :disabled="disabled" :clearable="clearable" :default-value="timeDefaultShow" value-format="yyyy-MM-dd" :picker-options="pickerOptions"></el-date-picker>
  </div>
  <div v-else-if="layout === 'dropdown'" class="dropdown-date-picker" :class="[{borderTheme: border}, `${size}`, `${dropdownClass}`]"
       @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
    <div class="dropdown-trigger" :class="[{'active': pickerVisible}, dropdownClass]">
      <span>{{ dropdownTitle }}</span>
        <span class="caret"></span>
    </div>
    <el-date-picker :offset="-5" :popper-class="['dropdown-date-picker-popper', layout === 'dropdown' && border?'mt-10':''].join(' ')"
                    ref="myDateRangeRef" v-model="dataRangeModel" @change="changeDate"
                    type="daterange" :align="align" unlink-panels range-separator="-" :size="size" start-placeholder="开始日期" end-placeholder="结束日期"
                    :disabled="disabled" :clearable="clearable" :default-value="timeDefaultShow" value-format="yyyy-MM-dd" :picker-options="pickerOptions"></el-date-picker>
  </div>
</template>

<script>
  export default {
    name: 'app-daterange',
    props: {
      size: { default: 'small' },
      _vModelProp: {},
      disabled: { default: false, type: Boolean },
      clearable: { default: false, type: Boolean },
      defaultBeforeDays: { default: 30 },
      disabledDate: {},
      disabledBeforedays: { default: '' },
      shortcuts: {},
      removeshortcuts: {},
      addshortcuts: {},
      notincludetoday: { default: false, type: Boolean },
      sameDateTriggerChange: { default: false, type: Boolean },
      align: { default: 'left', type: String },
      miniScreenAdapter: { default: false, type: Boolean },
      canMini: { default: false, type: Boolean },
      emptyShowText: { default: '', type: String },
      needClear: { default: false, type: Boolean },
      hasLongYear: { default: false, type: Boolean },
      layout: { default: 'default', type: String },
      showTimeout: { type: Number, default: 250 },
      hideTimeout: { type: Number, default: 150 },
      needPopMounse: { default: false, type: Boolean },
      border: { default: false, type: Boolean }
    },
    model: {
      prop: '_vModelProp',
      event: '_changeModelEvent'
    },
    watch: {
      _vModelProp(val) {
        this.dataRangeModel = val
        this.$emit('_changeModelEvent', val)
      }
    },
    data() {
      return {
        timeout: null,
        dataRangeModel: [],
        timeDefaultShow: '',
        disabledBegindate: undefined,
        pickerVisible: false
      }
    },
    computed: {
      isMiniScreenShow() {
        if (this.canMini) {
          return this.miniScreenAdapter || $util.isMiniScreen()
        }
        return (this.$parent.miniScreenAdapter || this.miniScreenAdapter) && this.$parent.isMiniScreen
      },
      pickerOptions() {
        var shortcuts = []
        var that = this
        var end = new Date()
        if (this.notincludetoday) {
          end.setTime(end.getTime() - 3600 * 1000 * 24)
        }
        if (this.shortcuts) {
          shortcuts = this.shortcuts
        } else {
          if (this.needClear && !(this.removeshortcuts && this.removeshortcuts.indexOf('不限') > -1)) {
            shortcuts.push({
              text: '不限',
              onClick(picker) {
                picker.$emit('pick', ['', ''])
              }
            })
          }
          if ((!this.removeshortcuts || this.removeshortcuts.indexOf('今天') === -1) && !this.notincludetoday) {
            shortcuts.push({
              text: '今天',
              onClick(picker) {
                var start = new Date()
                picker.$emit('pick', [start, start])
              }
            })
          }
          if (!this.removeshortcuts || this.removeshortcuts.indexOf('最近一周') === -1) {
            shortcuts.push({
              text: '最近一周',
              onClick(picker) {
                var start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * (that.notincludetoday ? 7 : 6))
                picker.$emit('pick', [start, end])
              }
            })
          }
          if (!this.removeshortcuts || this.removeshortcuts.indexOf('最近一个月') === -1) {
            shortcuts.push({
              text: '最近一个月',
              onClick(picker) {
                var start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * (that.notincludetoday ? 30 : 29))
                picker.$emit('pick', [start, end])
              }
            })
          }
          if (this.addshortcuts && this.addshortcuts.indexOf('最近两个月') > -1) {
            shortcuts.push({
              text: '最近两个月',
              onClick(picker) {
                var start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * (that.notincludetoday ? 60 : 59))
                picker.$emit('pick', [start, end])
              }
            })
          }
          if (!this.removeshortcuts || this.removeshortcuts.indexOf('最近三个月') === -1) {
            shortcuts.push({
              text: '最近三个月',
              onClick(picker) {
                var start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * (that.notincludetoday ? 90 : 89))
                picker.$emit('pick', [start, end])
              }
            })
          }
          if (!this.removeshortcuts || this.removeshortcuts.indexOf('最近半年') === -1) {
            shortcuts.push({
              text: '最近半年',
              onClick(picker) {
                var start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * (that.notincludetoday ? 180 : 179))
                picker.$emit('pick', [start, end])
              }
            })
          }
          if (!this.removeshortcuts || this.removeshortcuts.indexOf('最近一年') === -1) {
            shortcuts.push({
              text: '最近一年',
              onClick(picker) {
                var start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * (that.notincludetoday ? 365 : 364))
                picker.$emit('pick', [start, end])
              }
            })
          }

          if (this.hasLongYear) {
            if (!this.removeshortcuts || this.removeshortcuts.indexOf('最近三年') === -1) {
              shortcuts.push({
                text: '最近三年',
                onClick(picker) {
                  var start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 3 * (that.notincludetoday ? 365 : 364))
                  picker.$emit('pick', [start, end])
                }
              })
            }
            if (!this.removeshortcuts || this.removeshortcuts.indexOf('最近五年') === -1) {
              shortcuts.push({
                text: '最近五年',
                onClick(picker) {
                  var start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 5 * (that.notincludetoday ? 365 : 364))
                  picker.$emit('pick', [start, end])
                }
              })
            }
          }
        }

        return {
          disabledDate: this.disabledDate ? this.disabledDate : function(time) {
            if (that.disabledBegindate) {
              return time.getTime() > end.getTime() || that.disabledBegindate.getTime() > time.getTime()
            }
            return time.getTime() > end.getTime()
          },
          shortcuts: shortcuts
        }
      },
      dropdownTitle() {
        if (this._vModelProp) {
          const date = this._vModelProp.filter(item => item)
          return date.length > 0 ? date.join(' 至 ') : this.emptyShowText
        }
        return ''
      },
      dropdownClass() {
        if (this._vModelProp) {
          const date = this._vModelProp.filter(item => item)
          return date.length > 0 ? 'has-value' : ''
        }

        return ''
      }
    },
    methods: {
      resetData(list) {
        if (list && list.length) {
          this.dataRangeModel = list
        }
      },
      shortcutsPick(val) {
        let res = [$util.formatDate(val[0]), $util.formatDate(val[1])]
        if (res.toString() === this._vModelProp.toString()) {
          this.$emit('change', val)
        }
      },
      changeDate(val) {
        this.$emit('_changeModelEvent', val)
        this.$emit('change', val)
      },
      showSelectPannel() {
        this.pickerVisible = true
        if (this.$refs.myDateRangeRef) {
          this.$refs.myDateRangeRef.pickerVisible = true
        }

        if (this.layout === 'dropdown') {
          this.$nextTick(() => {
            this.$refs.myDateRangeRef.popperElm.addEventListener('mouseenter', this.dropdownDatePickerMouseEnter)
            this.$refs.myDateRangeRef.popperElm.addEventListener('mouseleave', this.dropdownDatePickerMouseLeave)
          })
        } else {
          if (this.needPopMounse) {
            this.$nextTick(() => {
              this.$refs.myDateRangeRef.popperElm.addEventListener('mouseenter', this.dropdownDatePickerMouseEnter)
              this.$refs.myDateRangeRef.popperElm.addEventListener('mouseleave', this.dropdownDatePickerMouseLeave)
            })
          }
        }
        return true
      },
      hideSelectPannel() {
        this.pickerVisible = false
        this.$refs.myDateRangeRef.pickerVisible = false

        if (this.layout === 'dropdown') {
          this.$nextTick(() => {
            this.$refs?.myDateRangeRef?.popperElm?.removeEventListener('mouseenter', this.dropdownDatePickerMouseEnter)
            this.$refs?.myDateRangeRef?.popperElm?.removeEventListener('mouseleave', this.dropdownDatePickerMouseLeave)
          })
        } else {
          if (this.needPopMounse) {
            this.$nextTick(() => {
              this.$refs?.myDateRangeRef?.popperElm?.removeEventListener('mouseenter', this.dropdownDatePickerMouseEnter)
              this.$refs?.myDateRangeRef?.popperElm?.removeEventListener('mouseleave', this.dropdownDatePickerMouseLeave)
            })
          }
        }
        return false
      },
      handleMouseEnter() {
        clearTimeout(this.timeout)
        this.timeout = setTimeout(() => {
          this.showSelectPannel()
        }, this.showTimeout)
      },
      handleMouseLeave() {
        clearTimeout(this.timeout)
        this.timeout = setTimeout(() => {
          this.hideSelectPannel()
        }, this.hideTimeout)
      },
      dropdownDatePickerMouseEnter() {
        this.$emit('popMEnter')
        this.handleMouseEnter()
      },
      dropdownDatePickerMouseLeave() {
        this.$emit('popMLeave')
        this.handleMouseLeave()
      }
    },
    created() {
      if (this.disabledBeforedays && Number(this.disabledBeforedays)) {
        var date = new Date()
        date.setTime(date.getTime() - 3600 * 1000 * 24 * (this.notincludetoday ? (Number(this.disabledBeforedays) + 1) : Number(this.disabledBeforedays)))
        this.disabledBegindate = date
      }
      this.dataRangeModel = this._vModelProp || []
      // if (!this.dataRangeModel || this.dataRangeModel.length != 2) {
      //   var date = new Date()
      //   date.setMonth(date.getMonth() - 1)
      //   this.dataRangeModel = [formatDate(date), formatDate(new Date())]
      //   this.$emit('_changeModelEvent', this.dataRangeModel)
      // }

      if (this.dataRangeModel.length === 2 && $formatter.isBlank(this.dataRangeModel[0]) && $formatter.isBlank(this.dataRangeModel[1])) {
        this.dataRangeModel = [] // 默认时间显示空之后的处理
      }

      this.timeDefaultShow = new Date()
      this.timeDefaultShow.setMonth(new Date().getMonth() - 1)
    },
    mounted() {
      if (this.sameDateTriggerChange) { // 选择和上一次相同的日期，仍然触发change事件
        const datePickerVM = this.$refs.myDateRangeRef
        datePickerVM.emitInput = (val) => {
          const formatted = datePickerVM.formatToValue(val)
          let res = [$util.formatDate(formatted[0]), $util.formatDate(formatted[1])]
          if (res.toString() === this._vModelProp.toString()) {
            this.$emit('change', res)
          } else {
            datePickerVM.$emit('input', formatted)
          }
        }
      }
      if (this.layout === 'dropdown') {
        this.$refs.myDateRangeRef.handleClose = () => {}
      }
    }
  }

</script>

<style lang="scss" scoped>
  @import "../../styles/common";

  .date-range-wrapper {
    display: inline-block;
    position: relative;
    width: 100%;

    .mini-screen-tip {
      color: $base-black-color;
      display: inline-block;
      position: absolute;
      left: 10px;
      top: 1px;
      font-size: 12px;
      cursor: pointer;
      width: calc(100% - 30px);
      height: 30px;
      line-height: 30px;
      background: white;
    }
  }

  .dropdown-date-picker {
    display: flex;
    flex-direction: column;
    padding: 0 5px;
    &.borderTheme {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #D8D8D8;
      border-radius: 2px;
      padding: 0px 12px;
      height: 32px;
      &.has-value,
      &:hover {
        border-color: #128BED;
      }
      &.mini {
        height: 28px;
      }
    }

    ::v-deep {
      .el-range-editor {
        padding: 0;
        height: 0;
        border: 0;
        width: 0;
      }

      .el-input__icon {
        display: none;
      }


      .el-range-input {
        display: none;
      }

      .el-range-separator {
        display: none;
      }
    }
  }

  .dropdown-trigger {
    cursor: pointer;
    display: flex;
    align-items: center;

    span {
      font-size: 14px;
      color: #333333;
      &:first-child {
        margin-right: 5px;
      }
    }

    &.has-value {
      span {
        color: #128BED;
      }

      .caret {
        border-top-color: #128BED;
      }
    }

    &:hover {
      span {
        color: #128BED;
      }

      .caret {
        border-top-color: #128BED;
        transform: rotate(180deg);
      }
    }

    &.active {
      span {
        color: #128BED;
      }

      .caret {
        border-top-color: #128BED;
        transform: rotate(180deg);
      }
    }
  }

  .caret {
    display: inline-block;
    width: 0;
    height: 0;
    vertical-align: middle;
    border-top: 4px dashed #666666;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
    transition: transform 0.15s linear;
  }

</style>
<style lang="scss">
  .__myDateRangeSelect {
    .el-range-separator {
      padding: 0;
      // width:auto;
      // margin-right: 5px;
    }

    .el-range-input {
      font-size: 12px;
    }

    .el-icon-date:before {
      content: "\e790";
      // display: none;
      color: #666;
    }

    .el-range__close-icon {
      position: absolute;
      right: 0;
      top: 2px;
      // color: #8f8399;
      height: 25px;
      color:#666;
    }

    .el-input__icon.el-range__icon.el-icon-date {
      position: absolute;
      right: 0;
      top: 0;
    }

    &.el-range-editor.el-input__inner {
      padding: 3px 6px;
    }

  }

  .el-date-editor .el-range-input {
    width: 45%;
    max-width: 84px;
    color:#333;
  }

  .date-range-wrapper {
    .el-date-editor--daterange.el-input__inner {
      width: 100%;
    }
  }

  .dropdown-date-picker-popper {
    //left: -5px !important;
    margin: 0;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
    border: none;
    border-radius: 4px;
    margin-top: 5px !important;

    .el-picker-panel__sidebar {
      border-bottom-left-radius: 4px;
      border-top-left-radius: 4px;
      border-right: 1px solid #eee;
    }

    .el-date-range-picker__content.is-left {
      border-right: 1px solid #eee;
    }

    .el-picker-panel__shortcut {
      &:hover {
        color: inherit;
        background: #F2F8FE;
      }

      &:focus {
        color: #128bed;
      }
    }

    &.mt-10 {
      margin-top: 10px!important;
    }
  }

</style>
