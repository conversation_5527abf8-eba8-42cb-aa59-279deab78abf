import { mount } from '@vue/test-utils'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import AppDaterange from '../index.vue'

// 模拟全局变量和方法
global.$util = {
  formatDate: (date) => {
    if (!date) return ''
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },
  isMiniScreen: () => false
}

global.$formatter = {
  isBlank: (val) => val === undefined || val === null || val === ''
}

describe('AppDaterange', () => {
  let wrapper

  beforeEach(() => {
    // 创建一个新的Vue实例
    wrapper = mount(AppDaterange, {
      propsData: {
        _vModelProp: []
      },
      stubs: {
        'el-date-picker': {
          template: '<div class="el-date-picker-stub"><slot></slot></div>',
          props: ['value', 'type', 'align', 'size', 'disabled', 'clearable', 'defaultValue', 'pickerOptions'],
          methods: {
            focus() {}
          }
        }
      }
    })
  })



  it('renders correctly with default props', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.date-range-wrapper').exists()).toBe(true)
    expect(wrapper.find('.__myDateRangeSelect').exists()).toBe(true)
  })

  it('renders dropdown layout when layout prop is dropdown', async () => {
    await wrapper.setProps({ layout: 'dropdown' })
    expect(wrapper.find('.dropdown-date-picker').exists()).toBe(true)
    expect(wrapper.find('.dropdown-trigger').exists()).toBe(true)
  })

  it('emits change event when date range changes', async () => {
    const dateRange = ['2023-01-01', '2023-01-31']
    wrapper.vm.changeDate(dateRange)

    expect(wrapper.emitted()).toHaveProperty('change')
    expect(wrapper.emitted().change[0]).toEqual([dateRange])
    expect(wrapper.emitted()).toHaveProperty('_changeModelEvent')
    expect(wrapper.emitted()._changeModelEvent[0]).toEqual([dateRange])
  })

  it('updates dataRangeModel when _vModelProp changes', async () => {
    const dateRange = ['2023-01-01', '2023-01-31']
    await wrapper.setProps({ _vModelProp: dateRange })

    expect(wrapper.vm.dataRangeModel).toEqual(dateRange)
  })

  it('shows mini screen tip when canMini is true and miniScreenAdapter is true', async () => {
    await wrapper.setProps({
      canMini: true,
      miniScreenAdapter: true,
      emptyShowText: '请选择日期范围'
    })

    expect(wrapper.find('.mini-screen-tip').exists()).toBe(true)
    expect(wrapper.find('.mini-screen-tip').text()).toBe('请选择日期范围')
  })

  it('computes correct pickerOptions with default shortcuts', () => {
    const options = wrapper.vm.pickerOptions

    expect(options).toHaveProperty('shortcuts')
    expect(options.shortcuts).toBeInstanceOf(Array)
    expect(options.shortcuts.length).toBeGreaterThan(0)

    // 检查是否包含常见的快捷选项
    const shortcutTexts = options.shortcuts.map(s => s.text)
    expect(shortcutTexts).toContain('今天')
    expect(shortcutTexts).toContain('最近一周')
    expect(shortcutTexts).toContain('最近一个月')
    expect(shortcutTexts).toContain('最近三个月')
  })

  it('computes correct dropdownTitle based on _vModelProp', async () => {
    // 空值情况
    await wrapper.setProps({
      _vModelProp: [],
      emptyShowText: '请选择日期范围'
    })
    expect(wrapper.vm.dropdownTitle).toBe('请选择日期范围')

    // 有值情况
    const dateRange = ['2023-01-01', '2023-01-31']
    await wrapper.setProps({ _vModelProp: dateRange })
    expect(wrapper.vm.dropdownTitle).toBe('2023-01-01 至 2023-01-31')
  })

  it('handles showSelectPannel method correctly', () => {
    // 模拟ref
    wrapper.vm.$refs.myDateRangeRef = {
      pickerVisible: false,
      popperElm: {
        addEventListener: vi.fn()
      }
    }

    const result = wrapper.vm.showSelectPannel()

    expect(wrapper.vm.pickerVisible).toBe(true)
    expect(wrapper.vm.$refs.myDateRangeRef.pickerVisible).toBe(true)
    expect(result).toBe(true)
  })

  it('handles hideSelectPannel method correctly', () => {
    // 模拟ref
    wrapper.vm.$refs.myDateRangeRef = {
      pickerVisible: true,
      popperElm: {
        removeEventListener: vi.fn()
      }
    }

    wrapper.vm.pickerVisible = true
    const result = wrapper.vm.hideSelectPannel()

    expect(wrapper.vm.pickerVisible).toBe(false)
    expect(wrapper.vm.$refs.myDateRangeRef.pickerVisible).toBe(false)
    expect(result).toBe(false)
  })

  it('initializes with correct default values', () => {
    expect(wrapper.vm.dataRangeModel).toEqual([])
    expect(wrapper.vm.timeDefaultShow).toBeInstanceOf(Date)
  })

  it('removes specific shortcuts when removeshortcuts prop is provided', async () => {
    await wrapper.setProps({
      removeshortcuts: ['今天', '最近一周']
    })

    const options = wrapper.vm.pickerOptions
    const shortcutTexts = options.shortcuts.map(s => s.text)

    expect(shortcutTexts).not.toContain('今天')
    expect(shortcutTexts).not.toContain('最近一周')
    expect(shortcutTexts).toContain('最近一个月')
    expect(shortcutTexts).toContain('最近三个月')
  })

  it('adds "最近两个月" shortcut when addshortcuts includes it', async () => {
    await wrapper.setProps({
      addshortcuts: ['最近两个月']
    })

    const options = wrapper.vm.pickerOptions
    const shortcutTexts = options.shortcuts.map(s => s.text)

    expect(shortcutTexts).toContain('最近两个月')
  })

  it('adds long year shortcuts when hasLongYear is true', async () => {
    await wrapper.setProps({
      hasLongYear: true
    })

    const options = wrapper.vm.pickerOptions
    const shortcutTexts = options.shortcuts.map(s => s.text)

    expect(shortcutTexts).toContain('最近三年')
    expect(shortcutTexts).toContain('最近五年')
  })

  it('excludes today from date range when notincludetoday is true', async () => {
    await wrapper.setProps({
      notincludetoday: true
    })

    const options = wrapper.vm.pickerOptions
    const shortcutTexts = options.shortcuts.map(s => s.text)

    // 当notincludetoday为true时，不应该有"今天"选项
    expect(shortcutTexts).not.toContain('今天')

    // 测试快捷选项的日期计算是否正确
    // 模拟picker对象
    const picker = {
      $emit: vi.fn()
    }

    // 找到"最近一周"的快捷选项并触发它的onClick
    const weekShortcut = options.shortcuts.find(s => s.text === '最近一周')
    weekShortcut.onClick(picker)

    // 验证$emit被调用，并且日期范围计算正确（应该是7天而不是6天）
    expect(picker.$emit).toHaveBeenCalledWith('pick', expect.any(Array))
    const emitArgs = picker.$emit.mock.calls[0][1]

    // 计算日期差异（应该是7天）
    const startDate = new Date(emitArgs[0])
    const endDate = new Date(emitArgs[1])
    const diffTime = Math.abs(endDate - startDate)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    expect(diffDays).toBe(6)
  })

  it('sets disabledBegindate when disabledBeforedays is provided', async () => {
    const disabledBeforedays = 10
    await wrapper.setProps({
      disabledBeforedays,
      notincludetoday: false
    })

    // 重新创建组件以触发created钩子
    wrapper = mount(AppDaterange, {
      propsData: {
        _vModelProp: [],
        disabledBeforedays,
        notincludetoday: false
      },
      stubs: {
        'el-date-picker': {
          template: '<div class="el-date-picker-stub"><slot></slot></div>',
          props: ['value', 'type', 'align', 'size', 'disabled', 'clearable', 'defaultValue', 'pickerOptions'],
          methods: {
            focus() {}
          }
        }
      }
    })

    // 验证disabledBegindate被正确设置
    expect(wrapper.vm.disabledBegindate).toBeInstanceOf(Date)

    // 计算预期的禁用日期
    const expectedDate = new Date()
    expectedDate.setTime(expectedDate.getTime() - 3600 * 1000 * 24 * disabledBeforedays)

    // 比较日期（忽略时分秒）
    const actualDate = wrapper.vm.disabledBegindate
    expect(actualDate.getFullYear()).toBe(expectedDate.getFullYear())
    expect(actualDate.getMonth()).toBe(expectedDate.getMonth())
    expect(actualDate.getDate()).toBe(expectedDate.getDate())

    // 测试disabledDate函数
    const options = wrapper.vm.pickerOptions

    // 测试禁用日期之前的日期（应该被禁用）
    const beforeDisabledDate = new Date(actualDate)
    beforeDisabledDate.setDate(beforeDisabledDate.getDate() - 1)
    expect(options.disabledDate(beforeDisabledDate)).toBe(true)

    // 测试禁用日期之后但今天之前的日期（应该不被禁用）
    const afterDisabledDate = new Date(actualDate)
    afterDisabledDate.setDate(afterDisabledDate.getDate() + 1)
    expect(options.disabledDate(afterDisabledDate)).toBe(false)

    // 测试未来日期（应该被禁用）
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + 1)
    expect(options.disabledDate(futureDate)).toBe(true)
  })
})
