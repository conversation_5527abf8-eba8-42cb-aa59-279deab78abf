<template>
  <div class="__app-popover-select-wrapper" :class="[{actived: showMenu}]" v-myclick-outside="closeDropMenu"
       @mousedown="mouseMoveEvent(true)" @mouseover="mouseMoveEvent(true)" @mouseenter="mouseMoveEvent(true)"
       @mouseout="mouseMoveEvent(false)">
    <div class="__filter-text" :class="{selected: currentFiltersIsSelect,'__filter-text-border':hasBorder}">
      <span style="margin-right: 5px;">
        {{text}}
        <span v-if="currentAlreadySelectFilterItem && (currentAlreadySelectFilterItem.model.length || currentAlreadySelectFilterItem.selectCount)">
          {{currentAlreadySelectFilterItem.selectCount || currentAlreadySelectFilterItem.model.length}}
        </span>
      </span>
      <span class="caret"></span>
    </div>
    <transition name="el-zoom-in-top" @before-enter="handleMenuEnter" @after-leave="doDestroy">
      <div class="__app-popover-content-wrapper" :class="[{moreLeverList,'__app-popover-content-wrapper-border':hasBorder,'__app-popover-content-wrapper-auto-width':popAutoWidth}]" v-show="showMenu" ref="dropDownWrapperId" :style="dropDownPosition">
        <div class="__drop-down-content">
          <slot name="selectContent">
            <template>
              <ul class="__select-list-wrapper" v-if="!onlyOneType">
                <li v-for="(item,index) in getAllFilterList" v-if="!item.hidden" :key="index" class="__listItem"
                    :class="[{'actived': currentHoverSelect === item.type}, {'selected': currentAlreadySelectType == item.type}]"
                    @click="clickFilterItem(item)" @mouseover="filterItemMouseover(item)">
                  {{item.name}}
                  <span class="el-icon-caret-right __arrowRight" v-if="!item.isAll && !item.noChildrenFlag"></span>
                </li>
              </ul>
              <slot name="selectContentDetails" :list="getAllFilterList" :show="currentSelectFilterItem && !currentSelectFilterItem.noChildrenFlag"
                    :currentData="currentSelectFilterItem">
                <template v-if="currentSelectFilterItem && !currentSelectFilterItem.noChildrenFlag">
                  <div class="__select-list-wrapper industryWrapper" v-if="getIsShow">
                    <template v-if="currentSelectFilterItem.selfView">
                      <component :is="currentSelectFilterItem.selfView" :options="currentSelectFilterItem" @cancelclick="resetSelect"
                                 @sureclick="filterSelectSuccessBySelfView" :height="selectAreaHeight"></component>
                    </template>
                    <template v-else>
                      <filterlist-checkbox :allText="allText" :canConfirm="canConfirm" :noSort="noSort" :currentData="currentSelectFilterItem" :data-load-finished="currentSelectFilterItem.loadEnd"
                                           :valuekey="currentSelectFilterItem.valuekey !== undefined ? currentSelectFilterItem.valuekey : 'value'"
                                           :labelkey="currentSelectFilterItem.labelkey !== undefined ? currentSelectFilterItem.labelkey : 'label'"
                                           :desc="currentSelectFilterItem.desc||currentSelectFilterItem.name" @cancelclick="resetSelect"
                                           @change="changeSelectItemsModel" :height="selectAreaHeight"
                                           :singleModel="currentSelectFilterItem.singlelMode?true:false" @sureclick="filterSelectSuccess"></filterlist-checkbox>
                    </template>
                  </div>
                </template>
              </slot>
            </template>
          </slot>
        </div>
      </div>
    </transition>
  </div>
</template>

<script src="./components.js"></script>

<style lang="scss" src="./styles.scss" scoped></style>
