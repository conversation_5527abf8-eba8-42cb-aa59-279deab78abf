const generateAreaModelFun = (dataList = [], provinceList, cityList, areaList) => {
  if (!dataList || !dataList.length || !provinceList || !provinceList.length) {
    return []
  }
  let arr = []
  dataList.forEach(element => {
    if (provinceList.includes(element.name)) {
      if (element.children && element.children.length) {
        element.children.forEach(city => {
          if (cityList.includes(city.name)) { // 城市
            if (city.children && city.children.length) {
              city.children.forEach(country => {
                if (areaList.length) {
                  if (areaList.includes(country.name)) {
                    arr.push([element.name, city.name, country.name])
                  }
                } else {
                  arr.push([element.name, city.name, country.name])
                }
              })
            } else {
              arr.push([element.name, city.name])
            }
          }
        })
      } else {
        arr.push([element.name])
      }
    }
  })
  return arr
}

export default {
  generateAreaModelFun
}
