<template>
  <span class="__filter-group">
    <company-filter ref="companyFilterRef" v-bind="$attrs"  @change="selectCompanySuccess" :removeItems="realRemoveItems" :functionTableId="functionTableId"></company-filter>
    <groups-filter v-if="!hideGroup" v-model="groupModel" ref="groupsFilterRef" v-bind="$attrs"  @change="selectGroupsSuccess" :functionTableId="functionTableId"></groups-filter>
    <filter-group-collection
      v-if="showFilterGroup"
      :options="filterGroupOptions"
      @change="handleChangeFilterGroup"
      ref="filterGroupRef"
    >
    </filter-group-collection>
  </span>
</template>

<script>

import filterGroupCollection from '../filter-group-collection/index.vue'
import { RiskTypes } from '../../../routes/dd/trace/components/config'

export default {
  name: 'filter-group',
  components: {
    filterGroupCollection,
    companyFilter: () => import('./index'),
    groupsFilter: () => import('./components/groups-filter')
  },
  props: {
    removeItems: { default: '所属主题', type: String },
    hideGroup: { default: false, type: Boolean },
    showFilterGroup: {
      type: Boolean,
      default: false
    },
    functionTableId: {
      default: 'bene_id'
    }
  },
  data() {
    let selectGroupValueList = []
    let selectRiskValueList = []
    if (this.$route.query.groupValues) {
      selectGroupValueList = (this.$route.query.groupValues || '').trim().split(',').filter(v => !!v)
    }
    if (this.$route.query.risk && this.showFilterGroup) {
      selectRiskValueList = (this.$route.query.risk || '').trim().split(',')
    }
    return {
      filterRes: {
        selCity: [],
        selDistrict: [],
        selProvince: [],
        selectGroupName: '筛选范围',
        selectGroupValueList: [],
        selectMode: '',
        selectValueList: [],
        ...(this.showFilterGroup && {
          riskTypes: selectRiskValueList
        })
      },
      groupModel: { selectGroupValueList: selectGroupValueList },
      currentIsReset: false,
      filterGroupOptions: {
        list: [
          {
            title: '风险等级',
            prop: 'riskTypes',
            sort: 1,
            type: 2, // 1：单选，2多选
            filterLists: [
              {
                name: '风险等级',
                dataList: RiskTypes,
                type: 'riskTypes',
                loadEnd: true,
                valuekey: 'name',
                labelkey: 'value',
                model: selectRiskValueList,
                lastSelectModel: [] // 上一次选择
              }
            ],
            placeholder: '',
            dropPos: ''
          }
        ]
      }
    }
  },
  computed: {
    realRemoveItems() {
      if (this.removeItems) {
        return this.removeItems + ',企业分组'
      }
      return '企业分组'
    }
  },
  methods: {
    reset() {
      this.currentIsReset = true
      this.$refs.companyFilterRef.reset()
      this.$refs.groupsFilterRef && this.$refs.groupsFilterRef?.reset()
      setTimeout(() => {
        this.currentIsReset = false
      }, 1500)
      this.filterRes = {
        selCity: [],
          selDistrict: [],
          selProvince: [],
          selectGroupName: '筛选范围',
          selectGroupValueList: [],
          selectMode: '',
          selectValueList: []
      }
      if (this.showFilterGroup) {
        this.filterRes.riskTypes = []
        this.$refs.filterGroupRef.reset()
      }
      this.$emit('input', this.filterRes)
      this.$emit('change', this.filterRes)
    },
    selectCompanySuccess(val) {
      if (this.currentIsReset) {
        return
      }
      if (val) {
        for (let prop in val) {
          if (prop !== 'selectGroupValueList') {
            this.filterRes[prop] = val[prop]
          } else {
            // console.log(prop, val[prop])
          }
        }
      }
      this.filterRes.selectGroupValueList = this.groupModel.selectGroupValueList
      this.$emit('input', this.filterRes)
      this.$emit('change', this.filterRes)
      // console.log('selectCompanySuccess', val)
    },
    selectGroupsSuccess(val) {
      if (this.currentIsReset) {
        return
      }
      this.filterRes.selectGroupValueList = this.groupModel.selectGroupValueList
      this.$emit('input', this.filterRes)
      this.$emit('change', this.filterRes)
      // selectMode=U   selectGroupName  企业分组
      // console.log('selectGroupsSuccess', val)
    },
    handleChangeFilterGroup(val) {
      this.filterRes = {
        ...this.filterRes,
        ...val
      }
      this.$emit('input', this.filterRes)
      this.$emit('change', this.filterRes)
    }
  }
}
</script>

<style scoped lang="scss">
  .__filter-group {
    display: inline-flex;
    align-items: center;

    ::v-deep .__app-popover-select-wrapper{
      margin-right: 20px;
      &:last-child {
        margin-right: 0;
      }
    }
  }

</style>
