<!--
从url中获取默认的参数  类别： comfiltertype: 对应的左侧的列表  compfilterval 对应的选择的条件
-->
<template>
  <app-popover-select :text="text" :options="companyOptions" @change="changeSelect" class="group_filter_list"
                      ref="selectDropmenu" onlyOneType :immediateSearch="false"
                      :emptyShowText="emptyShowText">
  </app-popover-select>
</template>

<script>
import codeTableService from '../../../../services/codetable'
import { typeFilterRange } from '../../../app-select-dropmenu/config'
import { postVue } from '../../../../services/common-service'
import appPopoverSelect from '../../index'
import _ from 'lodash'

export default {
  components: { appPopoverSelect },
  props: {
    functionTableId: { default: 'bene_id' },
    text: { default: '企业分组', type: String },
    isCollection: { default: false, type: <PERSON><PERSON>an }, // 是否是我的收藏
    isBidding: { default: false, type: <PERSON><PERSON><PERSON> }, // 是否是招投标
    isTerminal: { default: false, type: <PERSON><PERSON><PERSON> }, // 是否是数据终端
    isIndustrialPark: { default: false, type: Boolean }, // 是否是企业园区
    polygon4search: { default: '', type: String },
    value: {}
  },
  model: {
    prop: 'value'
  },
  data() {
    let that = this
    return {
      inputText: '',
      options: {
        dropPos: { left: 0 },
        filterLists: [
          {
            name: '企业分组',
            type: typeFilterRange.typeGroup,
            req: that.getGroupListRequest,
            valuekey: 'groupValue',
            labelkey: 'groupValue',
            model: [],
            params: () => {
              return {
                functionTableId: this.functionTableId,
                groupName: '企业分组'
              }
            }
          }
          ]
      },
      lastSelectRow: ''
    }
  },
  computed: {
    companyOptions() {
      let res = { dropPos: this.dropPos }
      res.filterLists = this.options.filterLists
      return res
    },
    emptyShowText() {
      return this.$parent.isMiniScreen ? '企业范围筛选' : '不限'
    }
  },
  watch: {
    functionTableId(val) {
      this.reset()
    }
  },
  methods: {
    reset() {
      this.options.filterLists.forEach(row => {
        if (!row.dataIsLoacal) {
          row.dataList = []
          row.loadEnd = false
        }
        row.model = []
        row.selectCount = 0
        row.lastSelectModel = Array.isArray(row.lastSelectModel) ? [] : {}
      })
      this.$refs.selectDropmenu.clearAndResetSelect()
    },
    getShowSelectText() {
      return this.$refs.selectDropmenu.inputText || '全部'
    },
    clearText: function() {
      this.$refs.selectDropmenu.clearText()
    },
    changeSelect(row, selectDatas) { // 拿到当前条件的选择项
      // console.log(row, selectDatas)
      this.lastSelectRow = row

      let res = {
        selectGroupValueList: []
      }
      if (!row) {
        this.$emit('input', _.cloneDeep(res))
        this.$emit('change', res, row, selectDatas)
        return
      }
      res.selectGroupValueList = row.model
      this.$emit('input', _.cloneDeep(res))
      this.$emit('change', res, row, selectDatas)
    },
    getGroupListRequest(params) {
      if (this.isCollection) {
        return postVue('/expan_cust/group/loadCorpGroups', {})
      } else if (this.isBidding) {
        return postVue('/expan_cust/group/tender/loadCorpGroups', {})
      } else if (this.isTerminal) {
        return postVue('/webapi/saas/corp_data/group/loadCorpDataGroup4Search', {})
      }
      return codeTableService.getCompanyGroups(params)
    }
  },
  created() {
    let res = {
      selectGroupValueList: []
    }
    if (this.$route.query.groupValues) {
      this.options.filterLists[0].model = (this.$route.query.groupValues || '').trim().split(',').filter(v => v)
      res.selectGroupValueList = this.options.filterLists[0].model
    }
    if (this.isBidding) {
      this.options.filterLists[0].name = '招投标分组'
    }
    this.$emit('input', _.cloneDeep(res))

    window.AppRuntimeContext.eventBus.$on(window.AppRuntimeContext.eventBusEvents.GROUP_HAS_CHANGED, (isBackExe) => {
      let res = this.options.filterLists.filter(v => v.type === typeFilterRange.typeGroup)
      // console.log(res, this.lastSelectRow)
      if (res && res.length) {
        res[0].model = []
        res[0].dataList = []
        res[0].loadEnd = false
        if (this.$refs.selectDropmenu && this.$refs.selectDropmenu.currentSelect === typeFilterRange.typeGroup) {
          this.$refs.selectDropmenu.currentSelect = ''
          this.$refs.selectDropmenu.inputText = '不限'
        }
        this.changeSelect(this.lastSelectRow, true)
      }
    })
  }

}

</script>

<style scoped lang="scss">
.group_filter_list ::v-deep .__myCheckboxGroupSelect{
  width: 200px;
}
.group_filter_list {
  &.__app-popover-content-wrapper .__select-list-wrapper.industryWrapper {
    border-left: none;
  }
}
</style>
