<template>
  <div class="_select-area" :class="[{_noData: options.dataList.length === 0}]" :style="getStyle">
    <el-cascader-panel class="morePopover" ref="cascader" v-model="cascaderModel" :options="options.dataList" v-if="options.dataList.length > 0"
                       :props="propMore" node-key="name" @change="sureClick(false)" @expand-change="checkNodeTestColor"></el-cascader-panel>
    <app-nodata v-else text="没有加载到筛选范围数据" imgWidth="60"></app-nodata>

    <select-btns class="btn" @cancelclick="cancelclick" @sureclick="sureClick(true)" :hasSelect="!_isEmpty(cascaderModel)"
                 v-if="options.dataList && options.dataList.length > 0"></select-btns>
  </div>
</template>

<script>
import selectBtns from '../select-btns'
import { formatSelectAreaList } from '../../app-select-dropmenu/config'
import { generateAreaModelFun } from './utils'
export default {
  name: 'select-area',
  props: ['options', 'height'],
  components: { selectBtns },
  data() {
    return {
      treeProps: {
        children: 'children',
        label: 'name'
      },
      propMore: {
        expandTrigger: 'hover',
        multiple: true,
        children: 'children',
        label: 'name',
        value: 'name'
      },
      cascaderModel: []
    }
  },
  computed: {
    getStyle() {
      if (!this.height) {
        return ''
      }
      return {
        height: this.height + 'px'
      }
    }
  },
  methods: {
    checkNodeTestColor() {
      // 级联选择器组件：由于indeterminate样式在里层，通过js控制改父级兄弟元素的文字样式
      this.$nextTick(() => {
        let elements = this.$refs.cascader.$el.getElementsByClassName('el-cascader-node')
        let arr = Array.from(elements)
        arr.forEach((item) => {
          if (item.children[0].children[0].getAttribute('class').includes('is-indeterminate')) {
            item.children[1].style.color = '#128BED'
          }
        })
      })
    },
    cancelclick() {
      this.options.lastSelectModel = []
      this.options.selectCount = 0
      this.$emit('cancelclick')
    },
    sureClick(isSure) {
      let provinceList = []
      let cityList = []
      let districtList = []
      let checkeds = this.$refs.cascader.getCheckedNodes()
      if (checkeds && checkeds.length > 0) {
        checkeds.forEach(({ data }) => {
          if (data.name === data.province) {
            if (provinceList.indexOf(data.province) === -1) provinceList.push(data.province)
          } else if (data.name === data.city) {
            if (cityList.indexOf(data.city) === -1) cityList.push(data.city)
            if (provinceList.indexOf(data.province) === -1) provinceList.push(data.province)
          } else {
            if (cityList.indexOf(data.district) === -1) districtList.push(data.district)
            if (cityList.indexOf(data.city) === -1) cityList.push(data.city)
            if (provinceList.indexOf(data.province) === -1) provinceList.push(data.province)
          }
        })
      }
      let resSelect = {
        provinceList: provinceList,
        cityList: cityList,
        districtList: districtList
      }
      // console.log(resSelect, this.cascaderModel)
      if (resSelect.provinceList.length === 0) {
        this.$message.warning('请选择所属区域')
        return
      }
      if (isSure) {
        this.options.model = resSelect
        this.options.lastSelectModel = this.cascaderModel
        this.options.inputText = formatSelectAreaList(resSelect)
        this.options.selectCount = resSelect.provinceList.length
        this.$emit('sureclick', this.options)
      }
    }
  },
  created() {
    if (Number(this.$route.query.comfiltertype) === 2 &&
      (this.$route.query.compfilterval || this.$route.query.cityValue || this.$route.query.selDistrict) &&
      !this.$route.params.areaValueNewLoaded) {
      this.$route.params.areaValueNewLoaded = true
      let model = generateAreaModelFun(this.options.dataList, (this.$route.query.compfilterval || '').split(','),
        (this.$route.query.cityValue || '').split(','), (this.$route.query.selDistrict || '').split(','))
      this.cascaderModel = model
      this.options.lastSelectModel = model
      this.options.selectCount = (this.$route.query.compfilterval || '').split(',').length || 1
      // console.log('=======', this.cascaderModel)
    }
    if (this.options.lastSelectModel && this.options.lastSelectModel.length) {
      this.cascaderModel = this.options.lastSelectModel
    }
  }
}

</script>

<style scoped lang="scss">
._select-area {
  height: 100%;
  &._noData{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 180px;
  }
  ::v-deep .morePopover.el-cascader-panel{
    .el-checkbox {
      position: absolute;
      width: calc(100% - 10px);
      left: 10px;
    }
    .el-cascader-node__label {
      margin-left: 10px;
    }
  }
  ._areaTree {
    min-width: 150px;
    height: calc(100% - 40px);
    max-height: calc(100% - 40px);
    overflow: auto;
    -ms-overflow-x: hidden;
    overflow-x: hidden;
  }
}

</style>
<style lang="scss">
._select-area {
  .morePopover .el-cascader-menu__wrap {
    height: 100%!important;
    overflow-x: auto;
  }
  .morePopover.el-cascader-panel.is-bordered {
    border: none;
    height: calc(100% - 40px);
  }
  .morePopover .el-icon-arrow-right {
    color: #999;
  }
}
</style>
