<template>
  <div class="_select-importer" :class="[{ _noData: options.dataList.length === 0 }]" :style="getStyle">
    <el-cascader-panel class="morePopover" ref="cascader" v-model="cascaderModel" :options="options.dataList"
      v-if="options.dataList.length > 0" :props="propMore" node-key="name" @change="onCascaderChange"
      @expand-change="checkNodeTestColor"></el-cascader-panel>
    <app-nodata v-else text="没有加载到筛选范围数据" imgWidth="60"></app-nodata>

    <select-btns class="btn" @cancelclick="cancelclick" @sureclick="sureClick(true)" :hasSelect="!_isEmpty(cascaderModel)"
      v-if="options.dataList && options.dataList.length > 0"></select-btns>
  </div>
</template>

<script>
import selectBtns from '../select-btns'
// import { formatSelectAreaList } from '../../app-select-dropmenu/config'
// import { generateAreaModelFun } from './utils'
const getListText = (form) => {
  if (form && form.length) { return form.map(e => e.label).join(',') }

  return ''
}
// 缓存值 上一次选中值
var lastSelected = []
var dataLevel = 0 // 级联层级， 默认0层
export default {
  name: 'select-importer',
  props: ['options', 'height'],
  components: { selectBtns },
  data() {
    return {

      treeProps: {
        children: 'children',
        label: 'label'
      },
      propMore: {
        expandTrigger: 'hover',
        multiple: true,
        children: 'children',
        label: 'label',
        value: 'value'
      },
      cascaderModel: []
    }
  },
  computed: {
    getStyle() {
      if (!this.height) {
        return ''
      }
      return {
        height: this.height + 'px'
      }
    },
    // 全选值
    cascaderAllValue() {
      var model = []
      function tree2arr(arr, str, level) {
        dataLevel = (level + 1) // 实际数据结构是几层
        arr.forEach(it => {
          let newStr = str.length ? [...str, it.value] : [it.value]
          if (it.children) {
            tree2arr(it.children, newStr, level + 1)
          } else {
            model.push(newStr)
          }
        })
      }
      tree2arr(this.options.dataList, [], 0)
      return model
    }
  },
  methods: {
    checkNodeTestColor() {
      // 级联选择器组件：由于indeterminate样式在里层，通过js控制改父级兄弟元素的文字样式
      this.$nextTick(() => {
        let elements = this.$refs.cascader.$el.getElementsByClassName('el-cascader-node')
        let arr = Array.from(elements)
        arr.forEach((item) => {
          if (item.children[0].children[0].getAttribute('class').includes('is-indeterminate')) {
            item.children[1].style.color = '#128BED'
          }
        })
      })
    },
    cancelclick() {
      this.options.lastSelectModel = []
      this.options.selectCount = 0
      this.$emit('cancelclick')
    },
    setImmediate(isInde) {
      // console.log(1111)
      let cls = isInde ? 'is-indeterminate' : ''
      setTimeout(function () {
        // let label1 = document
        //   .querySelector('._select-importer')
        //   .querySelector('.el-cascader-panel')
        //   .querySelector('.el-cascader-menu__wrap')
        //   .querySelectorAll('li')[0]
        //   .querySelectorAll('label')[0]

        let span1 = document
          .querySelector('._select-importer')
          .querySelector('.el-cascader-panel')
          .querySelector('.el-cascader-menu__wrap')
          .querySelectorAll('li')[0]
          .querySelectorAll('label')[0]
          .querySelectorAll('span')[0]
        // label1.className = 'el-checkbox'
        if (span1.className && span1.className.indexOf('is-checked') >= 0) {
        } else {
          span1.className = `el-checkbox__input ${cls}`
        }
      }, 100)
    },
    onCascaderChange(node) {
      let current = [] // 当前勾选项
      let isCheck = false // 【勾选 | 反选】
      if (node.length >= lastSelected.length) {
        let keys = lastSelected.map(item => JSON.stringify(item))
        current = node.filter(item => !keys.includes(JSON.stringify(item)))
        isCheck = true
      } else {
        let keys = node.map(item => JSON.stringify(item))
        current = lastSelected.filter(item => !keys.includes(JSON.stringify(item)))
        isCheck = false
      }
      const currentValue = current.length > 0 ? current[0][0] || '' : ''
      // debugger
      if (currentValue === '全部') {
        if (isCheck) {
          this.$set(this, 'cascaderModel', this.cascaderAllValue)
        } else {
          this.$set(this, 'cascaderModel', [])
        }
      } else {
        // 除全部外
        let listAll = JSON.parse(JSON.stringify(this.cascaderAllValue))
        let firstValue = node.length > 0 ? node[0][0] || '' : ''
        if (firstValue !== '全部') {
          listAll.shift()
        }

        if (node.length === listAll.length) {
          this.$set(this, 'cascaderModel', [['全部'], ...this.cascaderModel])
        } else {
          // 选中了全部，然后取消了某一个
          if (firstValue === '全部') {
            const ab = node.filter((item, index) => index >= 1)
            this.$set(this, 'cascaderModel', ab)
          }
        }
      }
      lastSelected = this.cascaderModel
      let checkValue = this.$refs.cascader.getCheckedNodes() || []
      // let checkAll = this.$refs.cascader.getFlattedNodes() || []



      this.$nextTick(() => {
        // 更新绑定 筛选最后一层数据值
        this.$emit('update:mulSelectedVal', checkValue.filter(item => item.level === dataLevel).map(item => item.value))
        this.$emit('change', node)
        this.setImmediate(false)

        setTimeout(() => {
          let cCheckValue = checkValue.filter(e => e.checked)
          if (cCheckValue && cCheckValue.length) {
            this.setImmediate(true)
          }
        }, 50)
      })
    },
    sureClick(isSure) {
      let checkList = []
      let checkeds = this.$refs.cascader.getCheckedNodes()
      // console.log(123123, checkeds)

      if (checkeds && checkeds.length > 0) {
        // let selectAll = checkeds.find(e => e.label === '全部')
        // if (!selectAll) {
        //   checkeds.forEach(({ data }) => {

        //   })
        // }
        checkList = checkeds.filter(e => !(e.children && e.children.length) && e.value !== '全部').map(e => e.value)
      }
      let resSelect = checkList
      // console.log(resSelect, this.cascaderModel)
      if (resSelect.length > 50) {
        this.$message.warning('导入人最多选择50个')
        return
      }
      if (isSure) {
        this.options.model = resSelect
        this.options.lastSelectModel = this.cascaderModel
        this.options.inputText = getListText(checkeds)
        this.options.selectCount = resSelect.length
        this.$emit('sureclick', this.options)
      }
    }
  },
  created() {
    // if (Number(this.$route.query.comfiltertype) === 2 &&
    //   (this.$route.query.compfilterval || this.$route.query.cityValue || this.$route.query.selDistrict) &&
    //   !this.$route.params.areaValueNewLoaded) {
    //   this.$route.params.areaValueNewLoaded = true
    //   let model = generateAreaModelFun(this.options.dataList, (this.$route.query.compfilterval || '').split(','),
    //     (this.$route.query.cityValue || '').split(','), (this.$route.query.selDistrict || '').split(','))
    //   this.cascaderModel = model
    //   this.options.lastSelectModel = model
    //   this.options.selectCount = (this.$route.query.compfilterval || '').split(',').length || 1
    //   // console.log('=======', this.cascaderModel)
    // }
    if (this.options.lastSelectModel && this.options.lastSelectModel.length) {
      this.cascaderModel = this.options.lastSelectModel
    }
  }
}

</script>

<style scoped lang="scss">
._select-importer {
  height: 100%;

  &._noData {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 180px;
  }

  ::v-deep .morePopover.el-cascader-panel {
    .el-checkbox {
      position: absolute;
      width: calc(100% - 10px);
      left: 10px;
    }

    .el-cascader-node__label {
      margin-left: 10px;
    }
  }

  ._areaTree {
    min-width: 150px;
    height: calc(100% - 40px);
    max-height: calc(100% - 40px);
    overflow: auto;
    -ms-overflow-x: hidden;
    overflow-x: hidden;
  }
}
</style>
<style lang="scss">
._select-importer {
  .morePopover .el-cascader-menu__wrap {
    height: 100% !important;
    overflow-x: auto;
  }

  .morePopover.el-cascader-panel.is-bordered {
    border: none;
    height: calc(100% - 40px);
  }

  .morePopover .el-icon-arrow-right {
    color: #999;
  }
}
</style>
