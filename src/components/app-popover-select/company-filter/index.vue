<!--
从url中获取默认的参数  类别： comfiltertype: 对应的左侧的列表  compfilterval 对应的选择的条件
-->
<template>
  <app-popover-select text="企业范围" :noSort="noSort" :size="size" :options="companyOptions" @change="changeSelect"
    ref="selectDropmenu" :select="urlParams.comfiltertype || ''" moreLeverList
    :selectModel="(urlParams.compfilterval || '').split(',')" :moreParams="urlParams" :emptyShowText="emptyShowText">
  </app-popover-select>
</template>

<script>
import codeTableService from '../../../services/codetable'
import terminalService from '../../../services/data-terminal'
import { typeFilterRange, formatSelectAreaList } from '../../app-select-dropmenu/config'
import { postVue } from '../../../services/common-service'
import { requestService } from '../../../services/broswer-service'
import appPopoverSelect from '../index'
import { RecordLabelMap } from '@/routes/cdd/nubo/detail/config'
import _ from 'lodash'

export default {
  name: 'company-filter',
  components: { appPopoverSelect },
  props: {
    functionTableId: {
      default: 'bene_id'
    },
    operatorLabel: {
      type: String,
      default: '导入人'
    },
    operateTimeLabel: {
      type: String,
      default: '导入时间'
    },
    dropPos: { left: 0 },
    removeItems: { default: '所属主题' },
    size: { default: 'small' },
    hasImporters: { default: false, type: Boolean }, // 是否导入人
    identifyResultVisible: { default: false, type: Boolean }, // 是否显示识别结果
    isCollection: { default: false, type: Boolean }, // 是否是我的收藏
    isBidding: { default: false, type: Boolean }, // 是否是招投标
    isTerminal: { default: false, type: Boolean }, // 是否是数据终端
    isIndustrialPark: { default: false, type: Boolean }, // 是否是企业园区
    polygon4search: { default: '', type: String },
    noSort: { default: false, type: Boolean } // 所属行业是否需要排序
  },
  data() {
    let that = this
    return {
      urlParams: '',
      inputText: '',
      options: {
        dropPos: { left: 0 },
        filterLists: [
          {
            name: '操作记录',
            type: typeFilterRange.typeSource,
            req: that.getResourceRequest,
            valuekey: 'label',
            labelkey: 'value',
            dataList: [],
            loadEnd: false,
            params: () => {
              return {
                functionTableId: this.functionTableId
              }
            }
          },
          {
            name: '企业分组',
            type: typeFilterRange.typeGroup,
            req: that.getGroupistRequest,
            valuekey: 'groupValue',
            labelkey: 'groupValue',
            params: () => {
              return {
                functionTableId: this.functionTableId,
                groupName: '企业分组'
              }
            }
          },
          {
            name: '所属主题',
            type: typeFilterRange.typeSubject,
            req: that.getTopicsRequest,
            valuekey: 'topicName',
            labelkey: 'id',
            params: () => {
              return {
                functionTableId: this.functionTableId,
                groupName: '所属主题'
              }
            }
          },
          ...(this.hasImporters ? [{
            name: this.operatorLabel,
            type: typeFilterRange.typeImportBy,
            req: codeTableService.getImporterCount,
            selfView: 'selectImporter',
            callData: this.getImporterList,
            valuekey: 'label',
            labelkey: 'groupValue'
          }] : [{
            name: this.operatorLabel,
            type: typeFilterRange.typeImportBy,
            valuekey: 'label',
            labelkey: 'groupValue',
            dataIsLoacal: true,
            dataList: [{ label: '本人', groupValue: 'own' }, { label: '其他', groupValue: 'others' }]
          }]),
          {
            name: this.operateTimeLabel,
            type: typeFilterRange.typeImportDate,
            req: that.getImportDateRequest,
            valuekey: 'label',
            singlelMode: true,
            dataList: [],
            loadEnd: false,
            params: () => {
              return {
                functionTableId: this.functionTableId
              }
            }
          }, /// /////////////////////////////////
          {
            name: '登记状态',
            type: typeFilterRange.typeState,
            req: that.getStatusRequest,
            valuekey: 'label',
            params: () => {
              return {
                functionTableId: this.functionTableId
              }
            }
          },
          ...(this.identifyResultVisible ? [{
            name: '识别结果',
            type: typeFilterRange.typeResult,
            req: this.getIdentifyResultRequest,
            valuekey: 'label',
            labelkey: 'value',
            params: () => {
              return {
                functionTableId: this.functionTableId
              }
            }
          },
          {
            name: '备案类型',
            type: typeFilterRange.typeRecordFlag,
            req: this.getRecordLabelStatusRequest,
            valuekey: 'label',
            labelkey: 'value',
            params: () => {
              return {
                functionTableId: this.functionTableId
              }
            }
          }] : []),
          {
            name: '成立时间',
            type: typeFilterRange.typeYear,
            req: that.getYearRequest,
            valuekey: 'label',
            params: () => {
              return {
                functionTableId: this.functionTableId
              }
            }
          },
          {
            name: '注册资本',
            type: typeFilterRange.typeCapital,
            req: that.getRegistCapitalRequest,
            valuekey: 'label',
            params: () => {
              return {
                functionTableId: this.functionTableId
              }
            }
          },
          {
            name: '所属地区',
            type: typeFilterRange.typeArea,
            req: codeTableService.getAreaList,
            // callData: this.getAreaList,
            selfView: 'selectArea',
            callData: this.getAreaList,
            formatText: formatSelectAreaList,
            selectCount: 0,
            setModel: (val, ref, allParams = {}) => {
              let resSelect = {
                provinceList: val,
                cityList: allParams.cityValue ? allParams.cityValue.split(',') : [],
                districtList: allParams.selDistrict ? allParams.selDistrict.split(',') : []
              }
              ref.model = resSelect
            }
          },
          {
            name: '所属行业',
            type: typeFilterRange.typeIndustry,
            req: that.getIndustryRequest,
            valuekey: '',
            labelkey: '',
            formatData: list => {
              for (let i = 0; i < list.length; i++) {
                if (list[i] == null || !list[i]) {
                  list[i] = '其他行业'
                }
              }
              return list
            },
            params: () => {
              return {
                functionTableId: this.functionTableId
              }
            }
          }
]
      },
      lastSelectRow: ''
    }
  },
  computed: {
    companyOptions: function () {
      let res = { dropPos: this.dropPos }
      this.options.dropPos = this.dropPos
      let removeItems = this.isJSJW || this.functionTableId === 'radar_id' || this.isBidding || this.isCollection || this.isTerminal ? ['操作记录', this.operateTimeLabel] : []

      if (this.removeItems) {
        if (typeof this.removeItems === 'string') {
          removeItems = removeItems.concat(this.removeItems.split(','))
        } else {
          removeItems = removeItems.concat(this.removeItems)
        }
      }
      if (!$util.getHasPermission('rad:mnr_subject:view')) {
        removeItems.push('所属主题')
      }
      if (removeItems) {
        res.filterLists = this.options.filterLists.filter((row) => { return removeItems.indexOf(row.name) === -1 })
      } else {
        res.filterLists = this.options.filterLists
      }
      return res
    },
    emptyShowText() {
      return this.$parent.isMiniScreen ? '企业范围筛选' : '不限'
    }
  },
  watch: {
    functionTableId(val) {
      this.reset()
    }
  },
  methods: {
    reset() {
      this.options.filterLists.forEach(row => {
        if (!row.dataIsLoacal) {
          row.dataList = []
          row.loadEnd = false
        }
        row.model = Array.isArray(row.model) ? [] : {}
        row.selectCount = 0
        row.lastSelectModel = Array.isArray(row.lastSelectModel) ? [] : {}
      })
      this.$refs.selectDropmenu.clearAndResetSelect()
    },
    getShowSelectText() {
      return this.$refs.selectDropmenu.inputText || '全部'
    },
    clearText: function () {
      this.$refs.selectDropmenu.clearText()
    },
    changeSelect(row, selectDatas) { // 拿到当前条件的选择项
      this.lastSelectRow = row

      let res = {
        selCity: [],
        selDistrict: [],
        selProvince: [],
        selectGroupName: '筛选范围',
        selectGroupValueList: [],
        selectMode: '',
        selectValueList: [],
        selectedTopicIdList: []
      }
      if (!row) {
        this.$emit('change', res, row, selectDatas)
        return
      }
      switch (row.type) {
        case typeFilterRange.typeSource:
          res.selectGroupName = '操作记录'
          res.selectMode = 'Source'
          res.selectValueList = []
          res.selectGroupValueList = []
          if (row.model.length === 0 || row.model.length === row.dataList.length) { } // "企业年份：全部";
          else {
            res.selectValueList = row.model
          }
          break
        case typeFilterRange.typeGroup:
          res.selectGroupName = '企业分组'
          res.selectMode = 'U'
          res.selectValueList = []
          if (row.model.length === 0 || row.model.length === row.dataList.length) { } // "企业分组：全部";
          else {
            res.selectGroupValueList = row.model
          }
          break
        case typeFilterRange.typeSubject:
          res.selectGroupName = '所属主题'
          res.selectMode = 'U'
          res.selectValueList = []
          res.selectGroupValueList = []
          if (row.model.length === 0 || row.model.length === row.dataList.length) { } // "企业分组：全部";
          else {
            res.selectedTopicIdList = row.model
          }
          break
        case typeFilterRange.typeImportBy:
          res.selectGroupName = this.operatorLabel
          res.selectMode = 'Focus'
          res.selectValueList = []
          res.selectGroupValueList = []
          if (row.model.length !== 0 && !$util.arraysAreEqual($util.flattenValues(row.dataList), row.model)) {
            res.selectValueList = row.model
          }
          break
        case typeFilterRange.typeImportDate:
          res.selectGroupName = this.operateTimeLabel
          res.selectMode = 'FocusDate'
          res.selectValueList = []
          res.selectGroupValueList = []
          if (row.model.length === 0 || row.model.length === row.dataList.length) { } else {
            res.selectValueList = row.model
          }
          break
        case typeFilterRange.typeState:
          res.selectGroupName = '登记状态'
          res.selectMode = 'Status'
          res.selectValueList = []
          if (row.model.length === 0 || row.model.length === row.dataList.length) { } // "企业年份：全部";
          else {
            res.selectValueList = row.model
          }
          break
        case typeFilterRange.typeResult:
          res.selectGroupName = '识别结果'
          res.selectMode = 'UboResult'
          res.selectValueList = (row.model.length && row.model.length !== row.dataList.length) ? row.model : []
          break
        case typeFilterRange.typeRecordFlag:
          res.selectGroupName = '备案类型'
          res.selectMode = 'RecordFlag'
          res.selectValueList = (row.model.length && row.model.length !== row.dataList.length) ? row.model : []
          break
        case typeFilterRange.typeYear:
          res.selectGroupName = '成立时间'
          res.selectMode = 'Year'
          res.selectValueList = []
          if (row.model.length === 0 || row.model.length === row.dataList.length) { } // "企业年份：全部";
          else {
            res.selectValueList = row.model
          }
          break
        case typeFilterRange.typeCapital:
          res.selectGroupName = '注册资本'
          res.selectMode = 'RegCapi'
          res.selectValueList = []
          if (row.model.length === 0 || row.model.length === row.dataList.length) { } // "企业年份：全部";
          else {
            res.selectValueList = row.model
          }
          break
        case typeFilterRange.typeArea:
          res.selectGroupName = '所属区域'
          res.selectMode = 'A'
          res.selectValueList = []
          if (row.model.provinceList.length === 0) { // "所属区域：全部";
          } else {
            res.selProvince = row.model.provinceList
            res.selCity = row.model.cityList
            res.selDistrict = row.model.districtList
          }
          this.$emit('change', res, row, selectDatas)
          return
        case typeFilterRange.typeIndustry:
          res.selectGroupName = '所属行业'
          res.selectMode = 'I'
          res.selectValueList = []
          if (row.model.length === 0 || row.model.length === row.dataList.length) { } // "所属行业：全部";
          else {
            res.selectValueList = row.model
          }
          break
      }
      if (!this.notReoad) {
        this.$emit('change', res, row)
      }
    },
    getAreaListOptionsByList(list) {
      let resArr = []
      if (list && list.length > 0) {
        let tmpCity = ''
        let country = ''
        list.forEach((item) => {
          tmpCity = this.getHasOrCreateTarget(resArr, item.province, item)
          country = this.getHasOrCreateTarget(tmpCity, item.city, item)
          this.getHasOrCreateTarget(country, item.district, item, true)
        })
        resArr.push({ name: '其他', province: '其他', city: '其他', district: '其他' })

        const removeEmptyProp = (list) => {
          if (Array.isArray(list)) {
            list.forEach(t => {
              if (t.children !== undefined && t.children.length === 0) {
                delete t.children
              } else {
                removeEmptyProp(t.children)
              }
            })
          }
        }
        removeEmptyProp(resArr)
        return resArr
      } else {
        // resArr.push({name: '其他', province: '其他', city: '其他', district: '其他'})
        return resArr
      }
    },
    getHasOrCreateTarget(list, name, item, notReturn) {
      if (!name || !list) {
        return ''
      }
      let isAdded = false
      for (var i = 0; i < list.length; i++) {
        if (list[i].name === name) {
          return notReturn ? '' : list[i].children
        }
      }
      if (!isAdded) {
        if (notReturn) {
          list.push({ name: name, province: item.province, city: item.city, district: item.district })
          return ''
        } else {
          let obj = { name: name, province: item.province, city: item.city, district: item.district, children: [] }
          list.push(obj)
          return obj.children
        }
      }
    },
    getAreaList(row, call) {
      if (row.dataList && row.dataList.length > 0) {
        return
      }
      this.getAreaListRequest()
        .then(res => {
          call(this.getAreaListOptionsByList(res.resultList))
        }).catch(() => {
          call([])
        })
    },

    getAreaListRequest() {
      if (this.isCollection) {
        return postVue('/expan_cust/favor/loadDistrict', {})
      } else if (this.isBidding) {
        return postVue('/expan_cust/favor/tender/loadDistrict', {})
      }
      if (this.isTerminal) {
        return terminalService.loadDistrict({})
      }
      return codeTableService.getAreaList({ functionTableId: this.functionTableId })
    },
    getImporterList(row, call) {
      if (row.dataList && row.dataList.length > 0) {
        return
      }
      this.getImporterListRequest()
        .then(res => {
          call(this.getImporterListOptionsByList(res.resultList))
        }).catch(() => {
          call([])
        })
    },
    getImporterListRequest() {
      return codeTableService.getImporterCount({ functionTableId: this.functionTableId })
    },
    getImporterListOptionsByList(list) {
      let resArr = [{ label: '全部', value: '全部' }, { label: '本人', value: 'own' }]
      // let resArr = [{ label: '本人', value: 'own' }]
      if (list && list.length > 0) {
        let otherChildren = list.map(e => ({ label: e.userName, value: e.userId }))
        resArr.push({ label: '其他', value: 'others', children: otherChildren })
      } else {
        resArr.push({ label: '其他', value: 'others' })
      }
      return resArr
    },
    getTopicsRequest() {
      return requestService.getRiskMonitorThemes({
        functionTableId: 'radar_id'
      })
    },
    getGroupistRequest(params) {
      if (this.isCollection) {
        return postVue('/expan_cust/group/loadCorpGroups', {})
      } else if (this.isBidding) {
        return postVue('/expan_cust/group/tender/loadCorpGroups', {})
      } else if (this.isTerminal) {
        return postVue('/webapi/saas/corp_data/group/loadCorpDataGroup4Search', {})
      }
      return codeTableService.getCompanyGroups(params)
    },
    getIndustryRequest(params) {
      if (this.isCollection) {
        return postVue('/expan_cust/favor/loadIndustry', {})
      } else if (this.isBidding) {
        return postVue('/expan_cust/favor/tender/loadIndustry', {})
      }
      if (this.isTerminal) {
        return terminalService.loadIndustry({})
      }
      if (this.isIndustrialPark) {
        return postVue('/expan_cust/map/getCorpInfoByPolygonMapItemList', {
          polygonSearch: this.polygon4search,
          screenType: 'corp_industry'
        })
      }
      return codeTableService.getIndustry(params)
    },
    getYearRequest(params) {
      if (this.isTerminal) {
        return terminalService.getRegYearCount(params)
      }
      return codeTableService.getCompDate(params)
    },
    getImportDateRequest(params) {
      return codeTableService.getImportDateRequest(params)
    },
    getStatusRequest(params) {
      if (this.isTerminal) {
        return terminalService.getStatusCount({})
      }
      if (this.isIndustrialPark) {
        return postVue('/expan_cust/map/getCorpInfoByPolygonMapCount', {
          polygonSearch: this.polygon4search,
          screenType: 'corp_status'
        })
      }
      return codeTableService.getOperatingState(params)
    },
    getIdentifyResultRequest() {
      return new Promise((resolve) => {
        resolve({
          status: '200',
          resultList: [
            {
              label: '简易识别',
              value: 'ubo_simple'
            },
            {
              label: '豁免识别',
              value: 'ubo_exempt'
            },
            {
              label: '正常识别',
              value: 'ubo_normal'
            }
          ]
        })
      })
    },
    getRecordLabelStatusRequest() {
      return new Promise((resolve) => {
        resolve({
          status: '200',
          resultList: _.keys(RecordLabelMap).map(value => ({
            label: value,
            value: value
          }))
        })
      })
    },
    getRegistCapitalRequest(params) {
      if (this.isTerminal) {
        return terminalService.getRegCapiCount({})
      }
      if (this.isIndustrialPark) {
        return new Promise((resolve) => {
          resolve({
            status: '200',
            resultList: [
              {
                id: null,
                isNewRecord: true,
                companyId: null,
                remarks: null,
                createDate: null,
                updateDate: null,
                version: null,
                createCompGroupId: null,
                label: '100万以下',
                count: 1
              },
              {
                id: null,
                isNewRecord: true,
                companyId: null,
                remarks: null,
                createDate: null,
                updateDate: null,
                version: null,
                createCompGroupId: null,
                label: '100-500万',
                count: 4
              },
              {
                id: null,
                isNewRecord: true,
                companyId: null,
                remarks: null,
                createDate: null,
                updateDate: null,
                version: null,
                createCompGroupId: null,
                label: '500-1000万',
                count: 0
              },
              {
                id: null,
                isNewRecord: true,
                companyId: null,
                remarks: null,
                createDate: null,
                updateDate: null,
                version: null,
                createCompGroupId: null,
                label: '1000-5000万',
                count: 5
              },
              {
                id: null,
                isNewRecord: true,
                companyId: null,
                remarks: null,
                createDate: null,
                updateDate: null,
                version: null,
                createCompGroupId: null,
                label: '5000万-1亿 ',
                count: 1
              },
              {
                id: null,
                isNewRecord: true,
                companyId: null,
                remarks: null,
                createDate: null,
                updateDate: null,
                version: null,
                createCompGroupId: null,
                label: '1亿以上 ',
                count: 18
              }
            ]
          })
        })
      }
      return codeTableService.getRegistCapital(params)
    },
    getResourceRequest(params) {
      return requestService.loadCompSourceFilter(params)
    }

  },
  created() {
    this.urlParams = this.$route.query
    if (this.isBidding) {
      this.options.filterLists[1].name = '招投标分组'
      this.options.filterLists[8].name = '项目行业'
    }

    if (!this.removeItems || !this.removeItems.includes('企业分组')) {
      window.AppRuntimeContext.eventBus.$on(window.AppRuntimeContext.eventBusEvents.GROUP_HAS_CHANGED, (isBackExe) => {
        let res = this.options.filterLists.filter(v => v.type === typeFilterRange.typeGroup)
        // console.log(res, this.lastSelectRow)
        if (res && res.length) {
          res[0].model = []
          res[0].dataList = []
          res[0].loadEnd = false
          if (this.$refs.selectDropmenu && this.$refs.selectDropmenu.currentSelect === typeFilterRange.typeGroup) {
            this.$refs.selectDropmenu.currentSelect = ''
            this.$refs.selectDropmenu.inputText = '不限'
          }
          this.changeSelect(this.lastSelectRow, true)
        }
      })
    }
  }

}

</script>

<style scoped lang="scss"></style>
