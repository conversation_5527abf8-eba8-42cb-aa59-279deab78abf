<template>
  <app-popover-select text="风险级别" :options="options" @change="changeSelect" class="group_filter_list"
                      ref="selectDropmenu" onlyOneType :immediateSearch="false">
  </app-popover-select>
</template>

<script>
import appPopoverSelect from '../../index'
import _ from 'lodash'
const riskCate = [
  { value: 'R', label: '高风险信息' },
  { value: 'L', label: '风险信息' },
  { value: 'W', label: '警示信息' },
  { value: 'A', label: '提示信息' },
  { value: 'N', label: '良好信息' },
  { value: 'none', label: '无风险信息' }
]

export default {
  components: { appPopoverSelect },
  props: {
    value: {}
  },
  model: {
    prop: 'value'
  },
  data() {
    return {
      options: {
        dropPos: { left: 0 },
        filterLists: [
          {
            name: '风险级别',
            type: 'risk-level',
            valuekey: 'label',
            labelkey: 'value',
            model: [],
            dataList: riskCate
          }
        ]
      }
    }
  },
  methods: {
    reset() {
      this.options.filterLists.forEach(row => {
        row.model = []
      })
      this.$emit('input', [])
    },
    changeSelect(row, selectDatas) { // 拿到当前条件的选择项
      this.$emit('input', _.cloneDeep(this.options.filterLists[0].model))
      this.$emit('change', this.value)
    },
    setSelectValue(value) {
      this.options.filterLists[0].model = value
    }
  },
  mounted() {
  },
  created() {
  }

}

</script>

<style scoped lang="scss">
.group_filter_list ::v-deep .__myCheckboxGroupSelect {
  width: 200px;
}

.group_filter_list {
  &.__app-popover-content-wrapper .__select-list-wrapper.industryWrapper {
    border-left: none;
  }
}
</style>
