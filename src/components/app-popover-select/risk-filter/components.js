import appPopoverSelect from '../index'
import codeTableService from '../../../services/codetable'
import filterlistCheckbox from '../filterlist-checkbox'
import _ from 'lodash'
import forbidChangeEventMixins from '../util/forbid-change-event-mixins'

/**
 * monitorTypes:[]
 * riskCategory:[]
 *
 * @type {string}
 */
const STAFF_FLAG = '1'
const DD_FLAG = '2'
const riskCate = [
  { value: 'R', label: '高风险信息' },
  { value: 'L', label: '风险信息' },
  { value: 'W', label: '警示信息' },
  { value: 'A', label: '提示信息' },
  { value: 'N', label: '良好信息' }
]
const riskStaffCate = [
  { value: 'R', label: '高风险信息' },
  { value: 'W', label: '警示信息' },
  { value: 'A', label: '提示信息' },
  { value: 'N', label: '良好信息' }
]
const riskDdCate = [
  { value: '1', label: '高风险信息' },
  { value: '2', label: '风险信息' },
  { value: '3', label: '警示信息' }
]
export default {
  name: 'risk-filter',
  mixins: [forbidChangeEventMixins],
  props: {
    riskgrade: { type: String, default: DD_FLAG }, // 1: 人员风险  2：尽调风险  ‘’：风险监控
    catergory: { type: String, default: '' },
    firstChange: { type: Boolean, default: false },
    popStyle: {
      default: () => {
        return {}
      }
    },
    investigationPage: {
      type: Boolean,
      validator: s => [true, false].includes(s),
      default: false
    },
    value: {}
  },
  model: {
    prop: 'value'
  },
  components: { filterlistCheckbox, appPopoverSelect },
  data() {
    return {
      emptyText: '没有加载到筛选范围数据',
      inputText: '',
      urlParams: {},
      noData: false,
      superRangeSelectText: '',
      secondRangeSelectText: '',
      currentForbidChange: false,
      riskOptions: {
        dropPos: { left: 0 },
        filterLists: []
      },
      categoryRemoteData: [],
      secondRangeSelect: [],
      currentSelectFilterItem: { dataList: [], model: [] },
      filterRangeCheckList: [],
      riskLevelOptions: {
        dropPos: { left: 0 },
        filterLists: [
          {
            name: '风险级别',
            type: 'risk-level',
            valuekey: 'label',
            labelkey: 'value',
            model: [],
            dataList: this.riskgrade === STAFF_FLAG ? riskStaffCate : this.riskgrade === DD_FLAG ? riskDdCate : riskCate
          }
        ]
      },
      defaultRiskSelectType: undefined, // 默认的监测类型选中
      currentModel: {}
    }
  },
  computed: {
    isDDMonitor() {
      return this.riskgrade === DD_FLAG
    },
    menus1() {
      return this.investigationPage ? '年检类型' : (this.isDDMonitor ? '监测类型' : '风险类型')
    },
    menus2() {
      return this.investigationPage ? '风险级别' : (this.isDDMonitor ? '监测级别' : '风险级别')
    }
  },
  watch: {
    'value.monitorTypes'(val) {
      // debugger
      // 若外部修改v-model的值，内部只是改变选中项
      if (val.toString() !== (this.currentModel.monitorTypes || '').toString()) {
        this.defaultRiskSelectType = ''
        if (val.includes('9') && this.riskgrade === DD_FLAG) {
          let monitorTypes = val.filter(v => v !== '9').concat(['5', '4', '7', '6', '8', '12'])
          this.currentModel = Object.assign({
            monitorTypes: monitorTypes,
            riskCategory: this.value.riskCategory
          })
          this.$emit('input', _.cloneDeep(this.currentModel))
        } else {
          this.currentModel.monitorTypes = _.cloneDeep(val)
        }

        for (let row of this.riskOptions.filterLists) {
          if (row.model === undefined) {
            row.model = []
          }
          if (this.currentModel.monitorTypes.includes(row.value)) {
            row.model = row.model.includes(row.value) ? row.model : [row.value]
            if (row.model.length) {
              this.defaultRiskSelectType = row.type
            }
          } else {
            // debugger
            if (row.dataList && row.dataList.length) {
              row.model = row.dataList.filter(v => this.currentModel.monitorTypes.includes(v.value)).map(v => v.value)
              if (row.model.length) {
                this.defaultRiskSelectType = row.type
              }
            } else {
              row.model = []
            }
          }
        }
      }
    }
  },
  created() {
    this.changeSelect = _.debounce(this._changeSelect, 80)
    this.initRiskFilter()
  },
  mounted() {
  },
  methods: {
    _reset() {
      const params = {
        monitorTypes: [],
        riskCategory: [],
        parentType: ''
      }
      this.currentModel = params
      this.$emit('input', _.cloneDeep(params))
      this.$children.forEach(el => {
        el.resetSelect && el.resetSelect()
      })
      this.defaultRiskSelectType = ''
    },
    initRiskFilter() {
      let { catergoryText, risklevel, typelist } = this.$route.query
      if (this.$route.params.catergoryText) {
        catergoryText = this.$route.params.catergoryText
      }
      if (this.$route.params.risklevel) {
        risklevel = this.$route.params.risklevel
      }
      if (this.$route.params.typelist) {
        typelist = this.$route.params.typelist
      }
      const params = {
        monitorTypes: [],
        riskCategory: [],
        parentType: ''
      }
      if (risklevel) { // 监测级别 风险监测详情-多级
        let hasSetModel = false
        this.getData(undefined, () => {
          if ((!this.riskgrade || !this.riskgrade !== STAFF_FLAG) && catergoryText) {
            for (let row of this.riskOptions.filterLists) {
              if (row.label === catergoryText) {
                this.defaultRiskSelectType = row.type
                this.$set(row, 'model', row.dataList.map(v => v.value))
                this.$set(row, 'lastSelectModel', row.dataList.map(v => v.value))
                this.changeSelect()
                hasSetModel = true
                break
              }
            }
            if (!hasSetModel) { // 如果没有获取到对应的条件，那么设置空数据的model
              this.changeSelect()
            }
          }
        })
        let rl = (`${risklevel}` || '').split(',').filter(v => !['', 'true'].includes(v))
        this.riskLevelOptions.filterLists[0].model = rl
        params.riskCategory = rl
      } else if (typelist) { // 客户年检
        let risklevel = (typelist || '').split(',').filter(v => v !== '')
        if (risklevel && risklevel.length) {
          if (risklevel.includes('9')) {
            params.monitorTypes = risklevel.filter(v => v !== '9').concat(['5', '4', '7', '6', '8', '12'])
          } else {
            params.monitorTypes = risklevel
          }
        }
        this.getData(false, () => {
          if (risklevel && risklevel.length) {
            risklevel.forEach(t => {
              for (let row of this.riskOptions.filterLists) {
                if (t === row.value) {
                  if (row.dataList && row.dataList.length) {
                    this.defaultRiskSelectType = row.type
                    row.model = [].concat(row.dataList.map(v => v.value))
                  } else {
                    row.model = [t]
                    this.defaultRiskSelectType = row.type
                  }
                  break
                }
              }
            })
          }
        })
      } else {
        this.getData(undefined, () => {
          this.changeSelect()
        })
      }
      /** 人员风险  2：尽调风险   若不是风险监控，先设置好v-model
       *  ''：风险监控, 若是风险监控，则需要先得到数据后再设置v-model
       */
      if (this.riskgrade === '2') {
        this.currentModel = params
        this.$emit('input', _.cloneDeep(params))
      }
    },
    _changeSelect(isImmediate) {
      if (this.currentForbidChange && !isImmediate) {
        return
      }
      let select = this.riskOptions.filterLists.filter(v => v.model && v.model.length)
      const params = {
        monitorTypes: select.map(v => v.model.toString()).join(',').split(',').filter(v => v !== ''),
        riskCategory: this.riskLevelOptions.filterLists[0].model,
        parentType: select.map(v => v.type || '').toString()
      }
      this.currentModel = params
      this.$emit('input', _.cloneDeep(params))
      this.$emit('change', params)
    },
    getData(selfSetTitle, call) {
      if (this.riskgrade === STAFF_FLAG) {
        codeTableService.getStaffRisk().then((data) => {
          this.categoryRemoteData = data
          this.setNew(data)
          call && call()
        })
      } else if (this.riskgrade === DD_FLAG) {
        codeTableService.getDdRisk().then((data) => {
          this.categoryRemoteData = data
          this.setNew(data, selfSetTitle, call)
        })
      } else {
        // debugger
        codeTableService.getCorpRisk().then((data) => {
          this.categoryRemoteData = data
          if (!$util.getHasPermission('rad:mnr_overview:view') && data.result && data.result.settingJsonStr) {
            data.result.mainSettingList = data.result.mainSettingList.filter(v => v.label === '工商信息')
            let settingJson = JSON.parse(data.result.settingJsonStr)
            settingJson = settingJson.filter(v => v.parentId === 'so1' && v.label === '受益人变更')
            data.result.settingJsonStr = JSON.stringify(settingJson)
          }
          this.setNew(data, selfSetTitle, call)
          this.$emit('shareTypesData', $util.tryParseJSON(data.result?.settingJsonStr) || [])
        })
      }
    },
    setNew(data, selfSetTitle, call) { // 初始化下拉框的可选菜单
      if (data.status === '200') {
        let subSettingChecks = JSON.parse(data.result.settingJsonStr)
        let unqueSetting = []
        subSettingChecks.forEach((cjk) => {
          cjk.name = cjk.label
          let isExist = false
          unqueSetting.forEach((usetting) => {
            if (usetting.id === cjk.id) {
              isExist = true
            }
          })

          if (!isExist) {
            unqueSetting.push(cjk)
          }
        })
        data.result.mainSettingList.forEach((el) => {
          el.name = el.label
          el.type = el.value
          el.dataList = []
          unqueSetting.forEach((subChk) => {
            if (el.id === subChk.parentId) {
              el.dataList.push(subChk)
            }
          })
        })
      }
      this.riskOptions.filterLists = data?.result?.mainSettingList || []
      if (this.riskgrade === DD_FLAG) {
        this.riskOptions.filterLists.forEach((sub) => {
          if (!['工商信息变更', '工商变更'].includes(sub.label)) {
            sub.notArrow = true
          }
        })
      }
      for (let row of this.riskOptions.filterLists) {
        row.noChildrenFlag = row.dataList.length === 0
        row.loadEnd = true
        row.valuekey = 'label'
        row.labelkey = 'value'
        row.model = []
        row.lastSelectModel = []
      }
      if (selfSetTitle) {
        call && call()
      } else {
        if (this.catergory || this.firstChange) {
          this.setCategoryText(this.catergory)
        }
        call && call()
      }
    },
    superRangeClick(item, index) {
      this.noData = false
      if (item.label === '不限') {
        this.$refs.changeRangeId.inputText = '不限'
        this.currentSelectFilterItem.dataList = []
        this.currentSelectFilterItem.model = []
        this.secondRangeSelect = []
        this.superRangeSelectText = ''
        this.$emit('change', { parentType: '', typeList: [], categoryList: [] })
        this.$refs.changeRangeId.closeDropMenu()
      } else if (item.label === this.menus1) {
        this.superRangeSelectText = this.menus1
        this.currentSelectFilterItem.dataList = []
        if (item.dataList && item.dataList.length > 0) {
          this.secondRangeSelect = item.dataList
        } else {
          this.noData = true
        }
      } else if (item.label === this.menus2) {
        this.superRangeSelectText = this.menus2
        this.secondRangeSelect = []
        if (this.riskgrade === STAFF_FLAG) {
          this.currentSelectFilterItem.dataList = this.riskStaffCate
        } else if (this.riskgrade === DD_FLAG) {
          this.currentSelectFilterItem.dataList = this.riskDdCate
        } else {
          this.currentSelectFilterItem.dataList = this.riskCate
        }

        this.filterModel()
      }
    },
    secondRangeClick(item, index) {
      this.secondRangeSelectText = item.label
      if (this.riskgrade === DD_FLAG) {
        this.noData = false
        // console.log(item.label, item.dataList)
        if (['工商信息变更', '工商变更'].includes(item.label)) {
          this.currentSelectFilterItem.dataList = item.dataList
        } else {
          this.currentSelectFilterItem.dataList = []
          var typeList = []
          if (item.label === '不限') {

          } else {
            typeList = [item.value]
          }
          this.$emit('change', { parentType: '', typeList: typeList, categoryList: [] })
          this.$refs.changeRangeId.inputText = item.label
          this.$refs.changeRangeId.closeDropMenu()
        }
      } else if (item.dataList && item.dataList.length > 0) {
        this.noData = false
        this.currentSelectFilterItem.dataList = item.dataList
      } else {
        this.noData = true
      }
      this.filterModel()
    },
    filterModel() {
      let that = this
      if (this.currentSelectFilterItem.model) {
        let selectedModel = []
        this.currentSelectFilterItem.model.forEach((m) => {
          that.currentSelectFilterItem.dataList.forEach((rc) => {
            if (rc.value === m) {
              selectedModel.push(m)
            }
          })
        })
        this.currentSelectFilterItem.model = selectedModel
      }
    },
    setInputText() {
      let that = this

      if (!this.currentSelectFilterItem || !this.currentSelectFilterItem.model || this.currentSelectFilterItem.model.length <= 0) {
        this.$refs.changeRangeId.inputText = '不限'
        return
      }
      let txt = ''
      this.currentSelectFilterItem.model.forEach((m) => {
        that.currentSelectFilterItem.dataList.forEach((d) => {
          if (m === d.value) {
            if (txt) {
              txt += '/' + d.label
            } else {
              txt = d.label
            }
          }
        })
      })
      this.$refs.changeRangeId.inputText = txt

      return txt
    },
    setOutText(txt) {
      if (txt) {
        this.$refs.changeRangeId.inputText = txt
      }
    },

    initCateGoryRiskLevel() {
      this.superRangeSelectText = this.menus2
      this.currentSelectFilterItem.dataList = this.riskCate
      this.setInputText()
      this.filterSelectSuccess({})
    },

    setCategoryText(categoryText) { // 选择风险类型
      this.superRangeSelectText = this.menus1
      let dataList = []
      let model = []

      this.riskOptions.filterLists.forEach((first) => {
        if (first.label === categoryText) {
          dataList = first.dataList
        }
      })
      dataList.forEach((second) => {
        model.push(second.value)
      })
      this.secondRangeSelect = this.riskOptions.filterLists
      this.secondRangeSelectText = categoryText
      this.currentSelectFilterItem.dataList = dataList
      this.currentSelectFilterItem.model = model
      this.setInputText()
      this.filterSelectSuccess({})
    },
    filterSelectSuccess(val) {
      let parentType = ''
      let typeList = []
      let categoryList = []
      if (this.currentSelectFilterItem && this.currentSelectFilterItem.model && this.currentSelectFilterItem.model.length > 0) {
        if (this.superRangeSelectText === this.menus1 && this.currentSelectFilterItem.dataList.length > 0) {
          parentType = this.currentSelectFilterItem.dataList[0].parentId
          typeList = this.currentSelectFilterItem.model
        }

        if (this.superRangeSelectText === this.menus2) {
          parentType = ''
          categoryList = this.currentSelectFilterItem.model
        }
      }
      let text = this.setInputText()
      this.$emit('change', { parentType: parentType, typeList: typeList, categoryList: categoryList, text: text })
      this.$refs.changeRangeId.closeDropMenu()
    },
    filterSelectClose() {
      this.$refs.changeRangeId.closeDropMenu()
    },
    // 手动联动
    makeDdSelectData(second) {
      this.superRangeSelectText = this.menus1
      this.currentSelectFilterItem.dataList = []
      this.secondRangeSelect = this.riskOptions.filterLists
      let children = []
      if (second.value === '9') {
        let children9 = this.riskOptions.filterLists.find(e => e.value === second.value)
        if (children9) {
          children = children9.dataList
        }
      }
      // 看不懂这个逻辑
      // if (this.riskgrade === DD_FLAG) {
      //   children = second.value === '9' ? this..dataList0].children[0].dataList : []
      // } else {
      //   children = second.value === '9' ? this..dataList0].children[this..dataList0].children.length - 1].dataList : []
      // }
      let secondItem = {
        label: second.name,
        value: second.value,
        children: children
        // children: second.value === '9' ? this..dataList0].children[this..dataList0].children.length - 1].dataList : []
      }
      // console.log(secondItem, secondItem.value)
      // 吊销或注销1、经营异常2、年报未公示3、严重违法10、失信被执行人11、工商信息变更9、受益所有人变更13
      if (['1', '2', '3', '9', '10', '11', '13'].includes(secondItem.value)) {
        this.secondRangeClick(secondItem)
        if (secondItem.value === '9') {
          this.$nextTick(() => {
            this.currentSelectFilterItem.model = secondItem.dataList.map(v => v.value)
            this.filterSelectSuccess()
          })
        }
      } else {
        // 法定代表人变更、企业名称变更、注册地址变更、注册资本变更、经营范围变更
        this.secondRangeSelectText = '工商信息变更'
        this.currentSelectFilterItem.dataList = this.riskOptions.filterLists[3].dataList
        setTimeout(() => {
          this.$refs.filtercheck.manualMakeSure([second.value])
        }, 100)
      }
    }
  }
}
