<template>
  <div class="risk-filter-container">
    <app-popover-select :text="menus1" :defaultSelectType.sync="defaultRiskSelectType" :options="riskOptions" @change="changeSelect()"
                        ref="selectDropmenu" moreLeverList :immediateSearch="false"
                        :dropListHeight="riskgrade===''? 300 : riskgrade==='1' ? 200 : undefined"
                        :class="[{'left-filter': ['1'].includes(riskgrade)}]">
    </app-popover-select>
    <app-popover-select :text="menus2" :options="riskLevelOptions" @change="changeSelect()" class="margin-l-15px"
                        ref="selectDropmenu" onlyOneType :immediateSearch="false">
    </app-popover-select>
  </div>
</template>

<script src="./components.js"></script>

<style scoped lang="scss" src="./style.scss">

</style>
