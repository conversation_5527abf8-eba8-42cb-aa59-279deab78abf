@import '../../styles/common';

$color-input-text: #333333;
$fontSize: 14px;

.__app-popover-select-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  color: $color-input-text;
  font-size: $fontSize;
  position: relative;
  min-height: 22px;
  cursor: pointer;
  padding: 0 5px;
  &.actived {
    color: #128BED;
    .caret {
      transform: rotate(180deg);
      border-top-color: #128BED;
    }
  }
  &:hover {
    color: #128BED;
    .__filter-text-border{
      border-color: #128BED;
    }
  }


  .el-icon-caret-bottom {
    font-size: 12px;
    margin-left: 4px;
    transition: all linear 0.2s;
  }

}
.__app-popover-content-wrapper-border{
  top: 34px !important;
}

.__app-popover-content-wrapper-auto-width{
  width: auto !important;
  min-width: 200px;
  .__select-list-wrapper{
    width: auto !important;
  }
}


.__filter-text {
  border-width: 0px;
  height: 22px;
  display: inline-flex;
  align-items: center;
  cursor: pointer;

  &.border-theme {
    border: 1px solid #eee;
    border-radius: 2px;
    padding: 4px 12px;
    height: 32px;
  }
  &:hover, &.hovered{
    color: #128BED;
    .__filter-text-border, &.border-theme{
      border-color: #128BED;
    }
    .caret {
      transform: rotate(180deg);
      border-top-color: #128BED;
    }
  }
  &.selected {
    color: #128BED;
    .caret {
      border-top-color: #128BED;
    }

    &.__filter-text-border, &.border-theme{
      border: 1px solid #128BED;
    }
  }
  .el-icon-caret-bottom{

  }
  .caret {
    display: inline-block;
    width: 0;
    height: 0;
    vertical-align: middle;
    border-top: 4px dashed #666666;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
    transition: all linear 0.2s;
  }
}

.__filter-text-border{
  box-sizing: border-box;
  border: 1px solid #eee;
  height: 32px !important;
  border-radius: 2px;
  padding: 5px 12px;
}


.__app-popover-content-wrapper {
  position: absolute;
  left: 5px;
  top: 27px;
  width: 100%;
  min-width: 200px;
  background: #fff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
  z-index: 33;
  max-height: 300px;
  border-radius: 4px;

  &.not-absolute {
    position: relative;
    top: 0;
    left: 0;
  }

  &.moreLeverList {
    width: auto;
    min-height: 200px;
    min-width: 180px;
    max-height: unset;
    .__select-list-wrapper {
      width: 180px;
      box-shadow: none;
      &.industryWrapper {
        width: auto;
        overflow: auto;
        border-left: 1px solid #D8d8d8;
      }
    }
  }

  .__drop-down-content {
    position: relative;
    @include flex-def;
    background: #fff;
    border-radius: 4px;
    min-height: 200px;

    >div {
      overflow: auto;
      position: relative;
      display: inline-block;
    }
  }

  .__popper__arrow {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 6px;
    -webkit-filter: drop-shadow(0 2px 12px rgba(0, 0, 0, .03));
    filter: drop-shadow(0 2px 12px rgba(0, 0, 0, .03));
    top: -6px;
    left: 95px;
    margin-right: 3px;
    border-top-width: 0;
    border-bottom-color: #ebeef5;

    &:after {
      position: absolute;
      display: block;
      width: 0;
      height: 0;
      border-color: transparent;
      border-style: solid;
      content: " ";
      border-width: 6px;
      top: 1px;
      margin-left: -6px;
      border-top-width: 0;
      border-bottom-color: #fff;
    }
  }

  .__select-list-wrapper {
    height: auto;
    max-height: 360px;
    width: 300px;
    display: inline-block;
    overflow: auto;
    border-radius: 4px;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);

    .__listItem {
      padding: 6px 15px;
      padding-right: 10px;
      font-size: 14px;
      line-height: 22px;
      cursor: pointer;
      color: #333333;
      position: relative;
      margin-bottom: 0;
      background: white;

      &:first-child {
        margin-top: 5px;
      }
      &:last-child {
        margin-bottom: 5px;
      }

      &:hover {
        background: #F2F8FE;
        color: #128bed;
      }

      .el-icon-caret-right.__arrowRight {
        position: absolute;
        font-size: 8px;
        right: 10px;
        top: 6px;
        line-height: 22px;
        color: #999999;
        //transform: rotate(-90deg);
      }

      &.selected {
        color: #128BED;
      }
      &.actived {
        color: #128BED;
        background: #F2F8FE;
        .__arrowRight {
          color: #128BED;
        }
      }
    }

    &.areaTreeWrapper {
      width: auto;
      min-width: 200px;
      box-sizing: border-box;
      border-left: 1px solid #D8d8d8;

      .el-tree-node__content:hover {
        background: #fff;
        color: #128BED;
      }

      ._areaTree {
        padding: 10px 15px;
      }

      ._areaTree {
        height: calc(100% - 50px);
        overflow: auto;
        -ms-overflow-x: hidden;
        overflow-x: hidden;
      }
    }
  }

  .__companyFilter-wrapperareaTreeWrapper {
    border-left: 1px solid #D8d8d8;
    height: 300px;

    .el-tree {
      height: 250px;
      overflow: auto;
    }
  }
}
