import myclickOutside from '../app-select-dropmenu/clickOutSide'
import filterlistCheckbox from './filterlist-checkbox'
import selectArea from './company-filter/selectArea'
import selectImporter from './company-filter/select-importer'
import selectRiskType from './account-risk-filter/select-risk-type'
import _ from 'lodash'

export default {
  name: 'app-popover-select',
  components: { filterlistCheckbox, selectArea, selectImporter, selectRiskType },
  directives: { myclickOutside: myclickOutside },
  props: {
    hasBorder: {
      type: Boolean,
      default: false
    },
    text: { default: '' },
    allText: { default: '全部' },
    canConfirm: { default: false, type: Boolean },
    popAutoWidth: { default: false, type: Boolean },
    options: {
      type: Object,
      default: () => {
        return {
          filterLists: [],
          placeholder: '',
          dropPos: ''
        }
      }
    },
    select: {},
    selectModel: {},
    moreParams: {},
    popStyle: {
      default: () => {
        return {}
      }
    },
    immediateSearch: {
      type: Boolean,
      default: true
    },
    emptyShowText: { default: '不限', type: String },
    noSort: { default: false, type: Boolean },
    moreLeverList: { default: false, type: Boolean },
    onlyOneType: { default: false, type: Boolean }, // 是否只有一类条件筛选
    defaultSelectType: { default: undefined, type: [String, Number] },
    dropListHeight: { default: 0, type: Number }
  },
  data() {
    return {
      showMenu: false,
      currentHoverSelect: '',
      currentAlreadySelectType: '',
      inputText: '不限'
    }
  },
  computed: {
    selectAreaHeight() {
      if (this.onlyOneType) {
        return ''
      }
      return this.dropListHeight
        ? this.dropListHeight
        : this.getAllFilterList.length * 34 + 10
    },
    inputShow() {
      if (!this.text && this.emptyShowText && this.inputText === '不限') {
        return this.emptyShowText
      }
      return this.text ? this.text : this.inputText
    },
    suffixIcon() {
      return this.showMenu ? 'el-icon-arrow-up' : 'el-icon-arrow-down'
    },
    getAllFilterList() {
      return [{ name: '不限', type: '000000', isAll: true }].concat(
        this.options.filterLists
      )
    },
    dropDownPosition() {
      let res = this.options.dropPos ? this.options.dropPos : { left: 5 }
      if (res.left === 0) {
        res.left = 5
      }
      return Object.assign(res, this.popStyle)
    },
    getIsShow() {
      // eslint-disable-next-line no-mixed-operators
      let res =
        this.currentSelectFilterItem &&
        (this.currentSelectFilterItem.loadEnd ||
          (this.currentSelectFilterItem.dataList &&
            this.currentSelectFilterItem.dataList.length > 0))
      this.$emit('showCheckbox', res)
      return res
    },

    currentSelectFilterItem() {
      // 当前选择的哪个条件
      if (this.onlyOneType) {
        this.currentHoverSelect =
          this.getAllFilterList[this.getAllFilterList.length - 1]
        return this.getAllFilterList[this.getAllFilterList.length - 1]
      }
      if (!this.showMenu && this.currentHoverSelect === '000000') {
        return ''
      }
      for (let i = 0; i < this.getAllFilterList.length; i++) {
        if (this.getAllFilterList[i].type === this.currentHoverSelect) {
          return this.getAllFilterList[i]
        }
      }
      return ''
    },

    currentAlreadySelectFilterItem() {
      // 当前选择的哪个条件
      if (
        this.currentAlreadySelectType === '000000' ||
        !this.currentAlreadySelectType
      ) {
        return ''
      }
      for (let i = 0; i < this.getAllFilterList.length; i++) {
        if (this.getAllFilterList[i].type === this.currentAlreadySelectType) {
          return this.getAllFilterList[i]
        }
      }
      return ''
    },
    currentFiltersIsSelect() {
      return (
        !this.notHighlightWhenSelected && this.currentAlreadySelectFilterItem &&
        (this.currentAlreadySelectFilterItem.model.length ||
          this.currentAlreadySelectFilterItem.selectCount)
      )
    }
  },
  watch: {
    defaultSelectType(val) {
      if (val !== undefined) {
        this.currentHoverSelect = val
        this.currentAlreadySelectType = val
      }
    }
  },
  created() {
    this.resetOptions()

    this.filterItemMouseover = _.debounce(this.filterItemMouseoverFunc, 100)
    this.mouseMoveEvent = _.debounce(this.operateFilterPopFun, 150)
    // this.mouseMoveEvent = (is) => {
    //   console.log('mouseMoveEvent=> ' + is)
    // }
    if (this.select && this.immediateSearch) {
      this.doSelect(this.select, this.selectModel, this.moreParams)
      if (
        `${this.select}` === '2' &&
        this.$route.query.compfilterval &&
        this.currentAlreadySelectFilterItem
      ) {
        this.currentAlreadySelectFilterItem.selectCount = 1
      }
    } else if (this.immediateSearch) {
      this.$emit('change', { type: '0000' })
    }
  },
  mounted() {
    if (this.onlyOneType) {
      this.currentHoverSelect = this.options.filterLists[0].type
      this.currentAlreadySelectType = this.options.filterLists[0].type
    }
    if (this.defaultSelectType !== undefined) {
      this.currentHoverSelect = this.defaultSelectType
      this.currentAlreadySelectType = this.defaultSelectType
    }
  },
  methods: {
    operateFilterPopFun(showMenu) {
      this.showMenu = showMenu
      if (!showMenu) {
        // this.currentHoverSelect = ''
        this.currentHoverSelect = this.currentAlreadySelectType
      } else {
        if (this.onlyOneType) {
          this.clickFilterItem(
            this.getAllFilterList[this.getAllFilterList.length - 1],
            '',
            true
          )
        }
      }
    },
    changeSelectItemsModel(val) {
      this.getAllFilterList.forEach((item) => {
        if (!item.selfView && item.type !== this.currentSelectFilterItem.type) {
          item.model = []
        }
      })
    },
    filterItemMouseoverFunc(item) {
      this.clickFilterItem(item, false, true)
    },
    clearText() {
      this.inputText = '不限'
    },
    clearAndResetSelect(emit = true) {
      this.currentHoverSelect = '000000'
      this.inputText = '不限'
      this.currentAlreadySelectType = ''
      this.currentHoverSelect = ''
      if (!emit) {
        return
      }
      if (this.defaultSelectType !== undefined) {
        this.$emit('update:defaultSelectType', '')
      }
      this.$emit('change')
    },
    handleMenuEnter() {},
    doDestroy() {},
    closeDropMenu(isReset) {
      if (!this.showMenu) return
      this.mouseMoveEvent = function () {}
      setTimeout(() => {
        this.showMenu = false
        if (isReset && !this.onlyOneType) {
          this.currentAlreadySelectType = ''
          this.currentHoverSelect = ''
        }
        setTimeout(() => {
          this.mouseMoveEvent = _.debounce(this.operateFilterPopFun, 150)
        }, 600)
      }, 200)
      this.$emit('close')
    },
    clickInput() {
      this.showMenu = !this.showMenu
    },
    clickFilterItem(item, loadedRestText, isHover) {
      // 点击左侧的条件
      if (item.isAll) {
        if (isHover) {
          this.currentHoverSelect = this.currentAlreadySelectType
          return
        }
        this.getAllFilterList.forEach((item) => {
          item.model = Array.isArray(item.model) ? [] : {}
          item.lastSelectModel = Array.isArray(item.lastSelectModel) ? [] : {}
        })
        this.$emit('change')
        this.inputText = '不限'
        this.closeDropMenu()
        this.currentHoverSelect = ''
        this.currentAlreadySelectType = ''
        this.$emit('update:defaultSelectType', '')
        return
      }
      this.currentHoverSelect = item.type
      if (!item.noChildrenFlag && item.dataList.length === 0 && !item.loadEnd) {
        if (item.callData) {
          item.callData(item, (dataList) => {
            item.dataList = dataList
            item.loadEnd = true
            if (loadedRestText) this.filterSelectSuccess()
          })
        } else if (item.req) {
          this.commonRequest(item, loadedRestText)
        }
      } else if (item.noChildrenFlag && isHover) {
      } else {
        if (loadedRestText || item.noChildrenFlag) {
          if (item.noChildrenFlag) {
            item.model = [item.value]
          }
          this.filterSelectSuccess()
        }
      }
    },
    commonRequest(item, loadedRestText) {
      // 获取数据
      item.req(item.params()).then((res) => {
        item.dataList = item.formatData
          ? item.formatData(res[item.resProp || 'resultList'])
          : res[item.resProp || 'resultList']
        // console.log(item.dataList)
        item.loadEnd = true
        if (loadedRestText) this.filterSelectSuccess()
      })
    },
    setInputText() {
      if (!this.currentHoverSelect || !this.currentSelectFilterItem) {
        this.inputText = '不限'
        return
      }
      let res = this.currentSelectFilterItem.name
      let labelkey =
        this.currentSelectFilterItem.labelkey === undefined
          ? 'label'
          : this.currentSelectFilterItem.labelkey
      // 当前选择的
      let select = this.currentSelectFilterItem.dataList.filter((row) => {
        return (
          this.currentSelectFilterItem.model.indexOf(
            labelkey ? row[labelkey] : row
          ) > -1
        )
      })
      let nameKey =
        this.currentSelectFilterItem.valuekey === undefined
          ? 'value'
          : this.currentSelectFilterItem.valuekey
      if (
        this.currentSelectFilterItem.model &&
        this.currentSelectFilterItem.model.length > 0
      ) {
        res +=
          '/' +
          select
            .map((row) => {
              return nameKey ? row[nameKey] : row
            })
            .toString()
      }
      this.inputText = res
    },
    setInputTextForOut(txt) {
      this.inputText = txt || '不限'
    },
    filterSelectSuccessBySelfView(res, needClearOtherModel = false, model) {
      this.inputText = res.inputText
      this.currentAlreadySelectType = res.type
      this.$emit('update:defaultSelectType', res.type)
      if (needClearOtherModel) {
        this.getAllFilterList.forEach((item) => {
          if (!item.selfView && item.type !== res.type) {
            item.model = Array.isArray(item.model) ? [] : {}
            item.lastSelectModel = Array.isArray(item.lastSelectModel) ? [] : {}
          }
        })
      }
      this.closeDropMenu()
      this.$emit('change', res, model)
    },
    resetSelect(triggerChange = true) {
      // this.showMenu = false
      this.mouseMoveEvent(false)
      this.getAllFilterList.forEach((item) => {
        item.model = []
        item.lastSelectModel = []
        item.selectCount = 0
      })
      this.currentHoverSelect = ''
      this.currentAlreadySelectType = ''
      triggerChange && this.$emit('change')
      this.setInputText()
      this.closeDropMenu(true)
    },
    filterSelectSuccess(val) {
      // 选择成功,将其余选项全部置空，将现有的选择赋值
      this.$emit('change', this.currentSelectFilterItem)
      this.getAllFilterList.forEach((item) => {
        if (item.type !== this.currentSelectFilterItem.type) {
          item.model = Array.isArray(item.model) ? [] : {}
          item.lastSelectModel = Array.isArray(item.lastSelectModel) ? [] : {}
        }
      })
      this.currentAlreadySelectType = this.currentSelectFilterItem.type
      this.$emit('update:defaultSelectType', this.currentSelectFilterItem.type)
      // console.log(this.currentAlreadySelectType)
      this.closeDropMenu()
    },
    resetOptions() {
      if (!this.options.filterLists) {
        return
      }
      this.options.filterLists.forEach((item) => {
        // eslint-disable-next-line eqeqeq
        if (item.model === undefined) {
          this.$set(item, 'model', [])
        }
        if (item.dataList === undefined) {
          this.$set(item, 'dataList', [])
        }
        if (item.loadEnd === undefined) {
          this.$set(item, 'loadEnd', item.dataList.length > 0)
        }
      })
    },
    doSelect(select, selectModel, moreParams, isFirst = false) {
      let selectArr = this.options.filterLists.filter((row) => {
        return row.type === Number(select)
      })
      if (selectArr && selectArr.length === 1) {
        this.currentAlreadySelectType = selectArr[0].type
        this.$emit('update:defaultSelectType', selectArr[0].type)
        if (selectModel) {
          let model =
            Number(select) === 2
              ? selectModel
              : decodeURIComponent(selectModel).split(',')
          if (selectArr[0].setModel) {
            selectArr[0].setModel(model, selectArr[0], moreParams)
            selectArr[0].inputText = selectArr[0].formatText(selectArr[0].model)
            this.filterSelectSuccessBySelfView(selectArr[0])
            // this.currentHoverSelect = selectArr[0].type
            this.clickFilterItem(selectArr[0])
          } else if (model) {
            selectArr[0].model = model
            this.clickFilterItem(selectArr[0], true)
          }
        }
      }
    }
  }
}
