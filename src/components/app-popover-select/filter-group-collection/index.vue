/**
* @Description: 该组件可以自动渲染一组条件选择选项，条件选择支持单选和多选
* <AUTHOR>
* @date 2022/10/19 11:16
*/
<template>
  <div class="filter-group-collection-body">
    <template v-for="(item, index) in options.list">
      <single-select v-if="item.type === 1" :key="`${item.prop}-${index}`" :options="item" class="margin-r-20x"
        @change="changeSelect()"></single-select>
      <slot v-else-if="item.type === -1" :name="item.slotName"></slot>
      <app-popover-select :class="isMR20 ? 'margin-r-20x' : ''" v-else ref="multipleSelectRef" onlyOneType :text="item.title"
        :options="item" :allText="item.noAllText ? '' : '全部'" @change="changeSelect()" :immediateSearch="false" />
    </template>
    <slot></slot>
  </div>
</template>

<script>
import SingleSelect from './single-select'
import appPopoverSelect from '../index'

const waitSelectList = [
  { name: '不限', value: '' },
  { name: '选项1', value: 1 },
  { name: '选项2', value: 2 },
  { name: '选项3', value: 3 },
  { name: '选项4', value: 4 },
  { name: '选项5', value: 5 },
  { name: '选项6', value: 6 }
]
export default {
  components: {
    SingleSelect,
    appPopoverSelect
  },
  props: {
    isMR20: {
      type: Boolean,
      default: false
    },
    options: {
      type: Object,
      default: () => ({
        list: [
          {
            title: '流程节点',
            prop: 'p1',
            model: '',
            lastSelectModel: '', // 上一次选择
            type: 1, // 1：单选，2多选
            namekey: 'name', //
            valuekey: 'value',
            format: undefined,
            list: waitSelectList
          },
          {
            title: '流程节点2',
            prop: 'p2',
            model: '',
            lastSelectModel: [], // 上一次选择
            type: 1, // 1：单选，2多选
            namekey: 'name', //
            valuekey: 'value',
            format: undefined,
            list: waitSelectList
          },
          {
            type: -1, // slot
            slotName: 'filter-self'
          },
          {
            title: '多选流程节点3',
            prop: 'p3',
            type: 2, // 1：单选，2多选
            filterLists: [
              {
                name: '多选流程节点3',
                dataList: waitSelectList,
                type: 'p3',
                loadEnd: true,
                valuekey: 'name',
                labelkey: 'value',
                model: [],
                lastSelectModel: [] // 上一次选择
              }
            ],
            placeholder: '',
            dropPos: ''
          }
        ]
      })
    }
  },
  data() {
    return {}
  },
  methods: {
    changeSelect(mustEmit = false) {
      if (this.currentIsReset && !mustEmit) {
        return
      }
      let res = {}
      for (let row of this.options.list) {
        if (row.prop) {
          if (row.type === 1) {
            res[row.prop] = row.model
          } else {
            res[row.prop] = row.filterLists[0].model
          }
        }
      }
      this.$emit('change', res)
    },
    reset(flg) {
      this.currentIsReset = true
      if (flg) {
        this.currentIsReset = false
      } else {
        setTimeout(() => {
          this.currentIsReset = false
        }, 1500)
      }

      if (this.$children && this.$children.length) {
        for (let child of this.$children) {
          child.reset && child.reset()
        }
      }
      if (this.$refs.multipleSelectRef && this.$refs.multipleSelectRef.length) {
        for (let child of this.$refs.multipleSelectRef) {
          child.clearAndResetSelect && child.clearAndResetSelect()
        }
      }
      for (let row of this.options.list) {
        if (row.prop) {
          if (row.type === 1) {
            row.model = ''
            row.lastSelectModel = ''
          } else {
            row.filterLists[0].lastSelectModel = []
            row.filterLists[0].model = []
          }
        }
      }
      this.changeSelect(true)
    }
  }
}
</script>

<style scoped lang="scss">
.filter-group-collection-body {
  display: inline-flex;
  align-items: center;
  justify-content: center;

  .filter-item {}
}
</style>
