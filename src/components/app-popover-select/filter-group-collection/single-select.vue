<template>
  <el-popover
    placement="bottom-start" width="400" popper-class="addcomp-pop-selectgroup"
    v-model="visiable" slot="operation" trigger="hover">
        <span class="operation-showtext" :class="[{rotate: visiable},{visiable: visiable || selectValue}]"
              @click.stop="''"
              slot="reference">
          <span :title="currentText" class="current-text overrflow-with-ellipsis">{{ currentText }}</span>
          <i class="el-icon-caret-bottom"></i>
        </span>
    <ul>
      <li v-for="(item,index) in options.list" :key="`${getItemValue(item)}-${index}`"
          @click="handleCommand(item)"
          :class="['el-dropdown-menu__item selectlist', {actived: selectValue === getItemValue(item)}]">
        {{ getItemName(item) }}
      </li>
    </ul>
  </el-popover>
</template>

<script>
export default {
  name: 'single-select',
  props: {
    options: {
      type: Object,
      default: () => ({
        title: '流程节点',
        prop: 'p1',
        model: '',
        lastModel: '', // 上一次选择
        type: 1, // 1：单选，2多选
        namekey: 'name', //
        valuekey: 'value',
        format: undefined,
        list: []
      })
    },
    value: {}
  },
  model: {
    prop: 'value'
  },
  data() {
    return {
      currentSelect: '',
      visiable: false,
      selectValue: ''
    }
  },
  computed: {
    currentText() {
      return this.currentSelect && this.selectValue ? this.currentSelect : this.options.title
    }
  },
  mounted() {
    this.selectValue = this.options?.model
    this.$emit('input', this.selectValue)
  },
  methods: {
    reset() {
      this.options.lastSelectModel = ''
      this.currentSelect = ''
      this.visiable = false
      this.selectValue = ''
    },
    getItemValue(item) {
      if (['string', 'boolean', 'number'].includes(typeof item)) {
        return item
      }
      return item[this.options.valuekey || 'value']
    },
    getItemName(item) {
      if (this.options.format) {
        return this.options.format(item)
      } else if (['string', 'boolean', 'number'].includes(typeof item)) {
        return item
      }
      return item[this.options.namekey || 'name']
    },
    handleCommand(item) {
      this.currentSelect = this.getItemName(item)
      this.selectValue = this.getItemValue(item)
      this.options.model = this.selectValue
      this.options.lastModel = this.selectValue
      this.$emit('input', this.selectValue)
      this.visiable = false
      this.$emit('change', this.selectValue)
    }
  }
}
</script>

<style scoped lang="scss">
.operation-showtext {
  font-size: 14px;
  color: #333;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  height: 19px;

  .current-text {
    display: inline-block;
    max-width: 120px;
  }
  .el-icon-caret-bottom{
    color: #666;
  }

  &.visiable,
  &:hover {
    color: #128BED;
    .el-icon-caret-bottom{
      color: #128BED;
    }
  }

  &.rotate {
    .el-icon-caret-bottom {
      transform: rotate(180deg);
    }
  }


  .icon-xinzeng {
    font-size: 13px;
    margin-right: 5px;
  }

  .el-icon-caret-bottom {
    font-size: 12px;
    margin-left: 2px;
    transition: all 0.2s linear;
  }
}

.selectlist {
  padding: 10px;
  line-height: 18px;
  color: #333;

  ::v-deep &.el-dropdown-menu__item.actived {
    color: #128BED;
  }
}

</style>
<style lang="scss">
.el-popper.addcomp-pop-selectgroup {
  padding: 5px 0 0;
  width: unset !important;
  max-width: 300px;
  max-height: 300px;
  min-width: 200px;
  overflow: auto;
  margin-top: 5px !important;
}
</style>
