<template>
  <div class="__myCheckboxGroupSelect" :class="[{hasSearch}, { maxHeight: getSortList.length > 9 }]" :style="getStyle">
    <div class="box-search-input" v-if="hasSearch">
      <app-input :prefixIcon="searchIcon" v-model="searchValue" @change="filterSearch"
                 :placeholder="inputPlaceholder" clearable></app-input>
    </div>
    <div class="_selectWrapper" :class="currentGroupNull ? 'empty' : ''">
      <template v-if="!currentGroupNull">
        <template v-if="!singleModel">
          <app-click-checkbox v-model="checkState" v-show="allText"
            v-if="(hasSearch && !searchValue) && currentData.dataList && currentData.dataList.length > 0" @click="clickAll" class="_all-select"
            :indeterminate="checkIndeterminate">
            <slot name="allSelect">{{ allText }}</slot>
          </app-click-checkbox>
          <el-checkbox-group v-model="modelSelect" v-if="!currentGroupNull" @change="changeSelect">
            <el-checkbox v-for="(item, index) in getSortList" :key="index" :label="getCheckBoxLabel(item)"
              class="overrflow-with-ellipsis" @click="changeSelect">
              <span v-html="getCheckBoxValue(item)"></span>
            </el-checkbox>
          </el-checkbox-group>
        </template>
        <template v-else>
          <el-radio-group v-model="modelSelect">
            <el-radio v-for="(item, index) in getSortList" :key="index" :label="getCheckBoxLabel(item)"
              class="overrflow-with-ellipsis" @click="changeSelect">
              <span v-html="getCheckBoxValue(item)"></span>
            </el-radio>
          </el-radio-group>
        </template>
      </template>
      <app-nodata :show="true"  :style="{marginTop: hasSearch?'0':'55px'}" :text="emptyText" :imgWidth="60"
                  v-if="currentGroupNull || (hasSearch && !getSortList.length)"></app-nodata>
    </div>
    <select-btns class="btn fixedWidth" :selectBtnStyle="selectBtnStyle" @cancelclick="cancelclick" :hasSelect="!_isEmpty(modelSelect)"
      @sureclick="sureClick" v-if="!notShowBtns && currentData.dataList && currentData.dataList.length > 0"></select-btns>
  </div>
</template>

<script>
import selectBtns from '../select-btns'
import _ from 'lodash'
const formatSearchedItem = (str, value) => {
  if (`${value}`.length) {
    return `${str}`.replace(new RegExp(`${value}`, 'g'), `<em>${value}</em>`)
  }
  return str
}
export default {
  name: 'filterlist-checkbox',
  components: { selectBtns },
  props: {
    currentData: {},
    allText: { default: '全部' },
    labelkey: { default: 'label' }, // 选择后的model
    valuekey: { default: 'value' }, // 显示的文字
    desc: { default: '相关条件' },
    emptyText: { default: '没有加载到筛选范围数据' },
    dataLoadFinished: { default: false, type: Boolean },
    singleModel: { default: false, type: Boolean },
    selectBtnStyle: {
      type: String,
      default: ''
    },
    noSort: { default: false, type: Boolean },
    height: { default: '', type: [String, Number] },
    notShowBtns: { default: false, type: Boolean },
    canConfirm: { default: false, type: Boolean },
    inputPlaceholder: { default: '请输入名称', type: String }, // 搜索框placeholder
    hasSearch: { default: false, type: Boolean }
  },
  data() {
    return {
      modelSelect: '',
      searchValue: '',
      searchIcon: { icon: 'el-icon-search', click: this.filterSearch },
      filterInputValue: ''
    }
  },
  computed: {
    getSortList() {
      let tmpList = JSON.parse(JSON.stringify(this.currentData.dataList))
      if (this.filterInputValue) {
        tmpList = tmpList.filter(v => {
          return this.getCheckBoxValue(v, true).includes(this.filterInputValue)
        })
      }
      if (!this.noSort) {
        tmpList.sort((tmp1, tmp2) => {
          return `${this.getCheckBoxValue(tmp1, true)}`.indexOf('其他') - `${this.getCheckBoxValue(tmp2, true)}`.indexOf('其他')
        })
      }
      return tmpList
    },
    getStyle() {
      if (!this.height) {
        return ''
      }
      return {
        height: (this.height > 200 ? this.height : 200) + 'px'
      }
    },
    currentGroupNull() {
      return this.dataLoadFinished && (!this.currentData.dataList || this.currentData.dataList.length === 0)
    },
    checkState: {
      get() {
        return this.modelSelect.length === this.currentData.dataList.length && this.currentData.dataList.length > 0
      },
      set() { }
    },
    checkIndeterminate: {
      get() {
        return this.modelSelect.length !== this.currentData.dataList.length && this.currentData.dataList.length > 0 && this.modelSelect.length > 0
      },
      set() { }
    }
  },
  watch: {
    'currentData.dataList'(val) {
      this.initModelData()
    },
    'currentData.model'(val) {
      this.initModelData()
    }
  },
  methods: {
    cancelclick() {
      this.modelSelect = []
      this.currentData.lastSelectModel = []
      this.currentData.model = []
      this.$emit('cancelclick')
    },
    filterSearch: _.debounce(function () {
      this.filterInputValue = this.searchValue
    }, 300),
    _initModelData() {
      let { lastSelectModel, model } = this.currentData
      let lastSelected = !_.isEmpty(model) ? model : (!_.isEmpty(lastSelectModel) ? lastSelectModel : [])
      if (!this.singleModel) {
        this.modelSelect = _.cloneDeep(lastSelected)
      } else {
        this.modelSelect = lastSelected && lastSelected.length === 1 ? lastSelected[0] : ''
      }
    },
    changeSelect() {
      // console.log(this.modelSelect)
      this.$emit('change', this.modelSelect)
    },
    sureClick() {
      if (this.canConfirm) {
        if (!this.singleModel) {
          this.currentData.lastSelectModel = this.modelSelect
          this.currentData.model = this.modelSelect
          this.$emit('sureclick', this.modelSelect)
        } else {
          this.currentData.model = [this.modelSelect]
          this.currentData.lastSelectModel = [this.modelSelect]
          this.$emit('sureclick', [this.modelSelect])
        }
        return
      }

      if (!this.singleModel && this.modelSelect.length === 0) {
        this.$message.warning('请选择' + this.desc)
        return
      }
      if (this.singleModel && this.modelSelect === '') {
        this.$message.warning('请选择' + this.desc)
        return
      }
      if (!this.singleModel) {
        this.currentData.lastSelectModel = this.modelSelect
        this.currentData.model = this.modelSelect
        this.$emit('sureclick', this.modelSelect)
      } else {
        this.currentData.model = [this.modelSelect]
        this.currentData.lastSelectModel = [this.modelSelect]
        this.$emit('sureclick', [this.modelSelect])
      }
    },
    // 手动关联
    manualMakeSure(model) {
      this.modelSelect = model
      if (!this.singleModel) {
        this.currentData.model = this.modelSelect
        this.currentData.lastSelectModel = this.modelSelect
        this.$emit('sureclick', this.modelSelect)
      } else {
        this.currentData.model = [this.modelSelect]
        this.currentData.lastSelectModel = [this.modelSelect]
        this.$emit('sureclick', [this.modelSelect])
      }
    },
    clickAll(e) {
      if (this.checkState) {
        this.modelSelect = []
      } else {
        this.currentData.dataList.forEach((item) => {
          if (this.modelSelect.indexOf(this.getCheckBoxLabel(item)) === -1) {
            this.modelSelect.push(this.getCheckBoxLabel(item))
          }
        })
      }
      this.$emit('allselected', this.checkState)
    },
    getCheckBoxLabel(row) {
      return this.labelkey ? row[this.labelkey] : row
    },
    getCheckBoxValue(row, notFormat) {
      if (notFormat) {
        // console.log(this.valuekey ? row[this.valuekey] : row)
        let res = this.valuekey ? row[this.valuekey] : row
        return formatSearchedItem(_.isObject(res) ? '' : res, `${this.searchValue}`.trim())
      }
      if (this.currentData.valueFormat) {
        return formatSearchedItem(this.currentData.valueFormat(row), `${this.searchValue}`.trim())
      }
      let res = this.valuekey ? row[this.valuekey] : row
      return _.isObject(res) ? '' : (`<span title="${res}">${`${res}`.trim() !== ''
        ? formatSearchedItem(`${res}`.trim(), `${this.searchValue}`.trim()) : '-'}</span>`)
    }
  },
  created() {
    this.initModelData = _.debounce(this._initModelData, 80)
    this.initModelData()
  }
}

</script>

<style lang="scss">
@import "../../../styles/common";

.__myCheckboxGroupSelect {


  .el-checkbox__label {
    display: inline;
  }
}
</style>
<style scoped lang="scss">
@import "../../../styles/mixins/common";
.__myCheckboxGroupSelect {
  position: relative;
  width: auto;
  height: 100%;
  min-width: 180px;
  max-width: 400px;
  &.hasSearch {
    display: flex!important;
    overflow: hidden;
    flex-direction: column;

    ._selectWrapper {
      height: calc(100% - 40px - 53px);
    }
  }
  &.maxHeight {
    height: 360px;
  }

  .box-search-input {
    padding: 10px 15px;
    border-bottom: 1px solid #D8d8d8;
    flex: 1;
    ::v-deep {
      @include baseInputPlaceHolder(#bbb);
    }
  }

  ._selectWrapper {
    width: auto;
    overflow: auto;
    position: relative;
    height: calc(100% - 40px);
    padding: 5px 0;

    ::v-deep .el-radio-group {
      width: 100%;

      .el-radio__label {
        font-weight: normal;
      }
    }

    ._all-select {
      display: flex;
      align-items: center;
      padding-right: 10px;
      height: 34px;
      width: 100%;

      &:hover {
        background: #F2F8FE;

        ::v-deep .el-checkbox__label {
          color: #128BED;
        }
      }
    }

    &.empty {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    ::v-deep .el-radio-group {
      padding-bottom: 15px;
    }

    ::v-deep .el-radio,
    ::v-deep .el-checkbox {
      display: flex;
      align-items: center;
      padding-left: 15px;
      padding-right: 5px;
      height: 34px;
      width: 100%;

      .el-checkbox__label {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: #333;
        font-weight: normal;
        line-height: 18px;
      }

      &:hover {
        background: #F2F8FE;

        .el-checkbox__label {
          color: #128bed;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }

  }

  .btn {
    background: white;
  }
}

::v-deep {
  .el-checkbox__input {
    font-size: 0;
  }

  .el-checkbox__input.is-checked+.el-checkbox__label {
    color: #128bed !important;
  }

  .el-checkbox__input.is-indeterminate+.el-checkbox__label {
    color: #128bed !important;
  }
}</style>
