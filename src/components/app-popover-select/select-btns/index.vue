<template>
  <div class="__checkbox_select-btn-wrapper" :style="selectBtnStyle">
    <span size="small" type="text" class="__sure" @click="$emit('sureclick')">确定</span>
    <span size="small" type="text" class="__reset" :class="[{__hasSelect: hasSelect}]" @click="$emit('cancelclick')">
      <span class="iconfont icon-heimingdanchexiao"/>重置
    </span>
  </div>
</template>

<script>
  export default {
    name: 'select-btns',
    props: {
      selectBtnStyle: {
        type: String,
        default: ''
      },
      hasSelect: { default: false, type: Boolean }
    }
  }

</script>

<style scoped lang="scss">
  @import '../../../styles/mixins';

  .__checkbox_select-btn-wrapper {
    box-sizing: border-box;
    height: 40px;
    border-top: 1px solid #D8d8d8;
    width: 100%;
    padding-right: 15px;

    @include flex-def;
    @include flex-cCenter;
    flex-direction: row-reverse;

    .__reset,
    .__sure {
      height: 22px;
      line-height: 22px;
      margin-left: 0;
      padding: 0 5px;
      cursor: pointer;
    }
    .__sure {
      color: #128BED;
      margin-left: 10px;
      &:hover {
        color: $color-primary-hover;
        background-color: #f2f8fe;
      }
    }
    .__reset {
      margin-left: 10px;
      color: #bbb;
      display: flex;

      &.__hasSelect {
        color: #666;
        &:hover{
          color: #128BED;
          background-color: #f2f8fe;
        }
      }
      .icon-heimingdanchexiao {
        font-size: 14px;
        margin-right: 5px;
      }
    }
  }

</style>
