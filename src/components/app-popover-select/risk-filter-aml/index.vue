<template>
  <div class="risk-filter-container" :class="[{'border-theme':borderTheme}]">
    <app-popover-select :text="menus1" :defaultSelectType="defaultRiskSelectType" :options="riskOptions" @change="changeSelect()"
                        ref="selectDropmenu" moreLeverList :immediateSearch="false"
                        :dropListHeight="riskgrade===''? 300 : undefined"
                        :class="[{'left-filter': ['1'].includes(riskgrade)}]">
    </app-popover-select>
  </div>
</template>

<script src="./components.js"></script>

<style scoped lang="scss" src="./style.scss">

</style>
