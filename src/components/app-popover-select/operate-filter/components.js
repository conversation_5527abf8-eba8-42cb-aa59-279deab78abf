import _ from 'lodash'
import appPopoverSelect from '../index'
import { treeList } from '../../../routes/riskMonitor/monitor-detail/components/company/list/components/remark-slidepop/config'
import { requestService } from '../../../services/broswer-service'

import Vue from 'vue'
import selectOperationsItem from './select-operations-item'
Vue.component('select-operations-item', selectOperationsItem)
export default {
  components: { appPopoverSelect },
  data() {
    return {
      riskOptions: {
        dropPos: { left: 0 },
        filterLists: [
          {
            name: '标签',
            type: 'risk-tag',
            valuekey: 'name',
            labelkey: 'id',
            model: [],
            dataList: [],
            params: () => ({}),
            req: this.getLabelListRequest
          },
          {
            name: '有跟进记录',
            type: 'type-has-note',
            valuekey: 'label',
            labelkey: 'value',
            value: '1',
            selfView: 'select-operations-item',
            model: [],
            dataList: treeList
          },
          {
            name: '无跟进记录',
            type: 'type-no-note',
            value: '0',
            noChildrenFlag: true
          }
        ]
      },
      defaultRiskSelectType: undefined // 默认的监测类型选中

    }
  },
  created() {
    this.changeSelect = _.debounce(this._changeSelect, 80)
  },
  methods: {
    _reset() {
      const params = {
        select: [],
        selectObj: {},
        type: ''
      }
      this.currentModel = params
      this.$emit('input', _.cloneDeep(params))
      this.$children.forEach(el => {
        el.resetSelect && el.resetSelect()
      })
    },
    _changeSelect(isImmediate) {
      if (this.currentForbidChange && !isImmediate) {
        return
      }
      const params = {
        select: [],
        selectObj: {},
        type: ''
      }
      for (let row of this.riskOptions.filterLists) {
        if (!_.isEmpty(row.model)) {
          params.type = row.type
          if (Array.isArray(row.model)) {
            params.select = row.model
          } else {
            params.selectObj = row.model
          }
        }
      }
      this.currentModel = params
      this.$emit('input', _.cloneDeep(params))
      this.$emit('change', params)
    },
    getLabelListRequest() {
      return new Promise(resolve => {
        requestService.getRiskMonitorDetailTags({})
          .then(res => {
            resolve({
              resultList: res.resultList.concat([{ name: '无标签', id: 'none' }]).map(v => ({ name: v.name, id: v.id }))
            })
          })
      })
    }
  }
}
