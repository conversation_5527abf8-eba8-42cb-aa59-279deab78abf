<template>
  <el-popover
    ref="popover"
    v-model="showMenu"
    width="auto"
    trigger="hover"
    transition="el-zoom-in-top"
    popper-class="app-dropdown-select-pop"
    content=" "
    v-bind="$attrs"
    @hide="closeDropMenu(true)"
  >
    <div :class="{selected: currentFiltersIsSelect,'__filter-text-border':hasBorder, hovered: showMenu, 'border-theme': borderTheme}"
         class="__filter-text" slot="reference">
      <span style="margin-right: 5px;">
        {{text}}
        <span v-if="!notHighlightWhenSelected && currentAlreadySelectFilterItem && (currentAlreadySelectFilterItem.model.length || currentAlreadySelectFilterItem.selectCount)">
          {{currentAlreadySelectFilterItem.selectCount || currentAlreadySelectFilterItem.model.length}}
        </span>
      </span>
      <span class="caret"></span>
    </div>

    <div class="__app-popover-content-wrapper not-absolute" :class="[{moreLeverList,'__app-popover-content-wrapper-border':hasBorder,'__app-popover-content-wrapper-auto-width':popAutoWidth}]"
         ref="dropDownWrapperId" :style="dropDownPosition">
      <div class="__drop-down-content">
        <slot name="selectContent">
          <template>
            <ul class="__select-list-wrapper" v-if="!onlyOneType">
              <li v-for="(item,index) in getAllFilterList" v-if="!item.hidden" :key="index" class="__listItem"
                  :class="[{'actived': currentHoverSelect === item.type}, {'selected': currentAlreadySelectType == item.type}]"
                  @click="clickFilterItem(item)" @mouseover="filterItemMouseover(item)">
                {{item.name}}
                <span class="el-icon-caret-right __arrowRight" v-if="!item.isAll && !item.noChildrenFlag"></span>
              </li>
            </ul>
            <slot name="selectContentDetails" :list="getAllFilterList" :show="currentSelectFilterItem && !currentSelectFilterItem.noChildrenFlag"
                  :currentData="currentSelectFilterItem">
              <template v-if="currentSelectFilterItem && !currentSelectFilterItem.noChildrenFlag">
                <div class="__select-list-wrapper industryWrapper" v-if="getIsShow">
                  <template v-if="currentSelectFilterItem.selfView">
                    <component :is="currentSelectFilterItem.selfView" :options="currentSelectFilterItem" @cancelclick="resetSelect"
                               @sureclick="filterSelectSuccessBySelfView" :height="selectAreaHeight"></component>
                  </template>
                  <template v-else>
                    <filterlist-checkbox :allText="allText" :canConfirm="canConfirm" :noSort="noSort" :currentData="currentSelectFilterItem" :data-load-finished="currentSelectFilterItem.loadEnd"
                                         :valuekey="currentSelectFilterItem.valuekey !== undefined ? currentSelectFilterItem.valuekey : 'value'"
                                         :labelkey="currentSelectFilterItem.labelkey !== undefined ? currentSelectFilterItem.labelkey : 'label'"
                                         :desc="currentSelectFilterItem.desc||currentSelectFilterItem.name" @cancelclick="resetSelect"
                                         @change="changeSelectItemsModel" :height="selectAreaHeight"
                                         :singleModel="currentSelectFilterItem.singlelMode?true:false" @sureclick="filterSelectSuccess"></filterlist-checkbox>
                  </template>
                </div>
              </template>
            </slot>
          </template>
        </slot>
      </div>
    </div>
  </el-popover>
</template>

<script>
import mixin from './mixin.js'
import _ from 'lodash'
export default {
  mixins: [mixin],
  props: {
    borderTheme: { default: false, type: Boolean },
    notHighlightWhenSelected: { default: false, type: Boolean }
  },
  updated() {
    this.$nextTick(() => {
      this.updatePopoverPlacement()
    })
  },
  mounted() {
  },
  methods: {
    updatePopoverPlacement: _.throttle(function () {
      this.$refs.popover.updatePopper()
    }, 200)
  }

}

</script>

<style lang="scss" src="./styles.scss" scoped></style>
