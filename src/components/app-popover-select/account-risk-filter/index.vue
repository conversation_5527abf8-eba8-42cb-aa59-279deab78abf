<template>
  <div class="risk-filter-container">
    <app-popover-select :text="menus1" :defaultSelectType="defaultRiskSelectType" :options="riskOptions" @change="changeSelect()"
                        ref="selectDropmenu" moreLeverList :immediateSearch="false" :dropListHeight="317">
    </app-popover-select>
    <app-popover-select :text="menus2" :options="riskLevelOptions" @change="changeSelect()" class="margin-l-15px"
                        ref="selectDropmenu" onlyOneType :immediateSearch="false">
    </app-popover-select>
  </div>
</template>

<script>
import appPopoverSelect from '../index'
import _ from 'lodash'
import forbidChangeEventMixins from '../util/forbid-change-event-mixins'
import { riskLevel, allGroupData } from './config'

/**
 * monitorTypes:[]
 * riskCategory:[]
 *
 * @type {string}
 */
const DD_FLAG = '2'

export default {
  name: 'risk-filter',
  mixins: [forbidChangeEventMixins],
  props: {
    riskgrade: { type: String, default: DD_FLAG },
    catergory: { type: String, default: '' },
    firstChange: { type: Boolean, default: false },
    popStyle: {
      default: () => {
        return {}
      }
    },
    investigationPage: {
      type: Boolean,
      validator: s => [true, false].includes(s),
      default: false
    },
    value: {}
  },
  model: {
    prop: 'value'
  },
  components: { appPopoverSelect },
  data() {
    return {
      emptyText: '没有加载到筛选范围数据',
      inputText: '',
      noData: false,
      riskOptions: {
        dropPos: { left: 0 },
        filterLists: allGroupData
      },
      categoryRemoteData: [],
      secondRangeSelect: [],
      currentSelectFilterItem: { dataList: [], model: [] },
      filterRangeCheckList: [],
      riskLevelOptions: {
        dropPos: { left: 0 },
        filterLists: [
          {
            name: '监测类型',
            type: 'risk-level',
            valuekey: 'value',
            labelkey: 'label',
            model: [],
            dataList: riskLevel
          }
        ]
      },
      defaultRiskSelectType: undefined, // 默认的监测类型选中
      currentModel: {}
    }
  },
  computed: {
    isDDMonitor() {
      return this.riskgrade === DD_FLAG
    },
    menus1() {
      return this.isDDMonitor ? '监测类型' : '风险类型'
    },
    menus2() {
      return this.isDDMonitor ? '监测级别' : '风险等级'
    }
  },
  created() {
    this.changeSelect = _.debounce(this._changeSelect, 80)
    this.initRiskFilter()
  },
  mounted() {
    if (_.isEmpty(this.$route.query)) {
      this._reset()
    }
    // console.log(11111, this.$route.query)
    // this._reset()
  },
  methods: {
    _reset() {
      const params = {
        monitorTypes: [],
        riskCategory: []
      }
      this.currentModel = params
      this.$emit('input', _.cloneDeep(params))
      // this.$emit('change', params)
      this.$children.forEach(el => {
        el.resetSelect && el.resetSelect()
      })
    },
    initRiskFilter() {
      let params = {
        level: '',
        channel: '',
        monitorRankList: [],
        riskList: [],
        channelList: [],
        ddRiskHitCodes: {
          hitCodeSet: []
        }
      }
      const { risklevel, up } = this.$route.query
      const channelNameList = ['redChannel', 'yellowChannel', 'greenChannel']
      if (risklevel) { // 监测级别 风险监测详情-多级
        if (channelNameList.includes(risklevel)) { // 通道
          let res = this.initChannelSelectArr(risklevel)
          params.ddRiskHitCodes.hitCodeSet = res.model
          if (res.model.length > 0) {
            if (risklevel === 'greenChannel') {
              res.channelList = ['greenChannel']
            } else {
              res.channelList = ['redChannel', 'yellowChannel'].includes(risklevel) && up ? [risklevel] : []
            }
          }
          this.defaultRiskSelectType = this.riskOptions.filterLists[1].type
          this.riskOptions.filterLists[1].selectCount = res.model.length
          this.riskOptions.filterLists[1].model = params
          this.riskOptions.filterLists[1].lastSelectModel = res.lastSelectModel
          params.channelList = res.channelList
        } else { // 风险等级
          let risklevelArr = this.$route.query.risklevel.split(',').filter(v => v !== '')
          if (!_.isEmpty(risklevelArr)) {
            params.level = '1'
            params.riskList = risklevelArr
            this.defaultRiskSelectType = this.riskOptions.filterLists[0].type
            this.riskOptions.filterLists[0].selectCount = risklevelArr.length
            this.riskOptions.filterLists[0].model = risklevelArr
          }
        }
      }
      if (params.channelList.length > 0) {
        params.channel = '1'
      }
      // console.log(11111, params)
      this.currentModel = params
      this.$emit('input', _.cloneDeep(params))
    },
    _changeSelect(isImmediate) {
      if (this.currentForbidChange && !isImmediate) {
        return
      }
      let params = {
        level: '',
        channel: '',
        monitorRankList: [],
        riskList: [],
        channelList: [],
        ddRiskHitCodes: {
          hitCodeSet: []
        }
      }
      for (let row of this.riskOptions.filterLists) {
        if (row.type === 'type-chanel') {
          if (!_.isEmpty(row.model)) {
            params.channel = row.model.channel
            params.channelList = row.model.channelList
            params.ddRiskHitCodes = row.model.ddRiskHitCodes
          }
        } else if (row.type === 'risk-level') {
          if (!_.isEmpty(row.model)) {
            params.level = '1'
            params.riskList = row.model
          }
        }
      }
      params.monitorRankList = this.riskLevelOptions.filterLists[0].model || []

      this.currentModel = params
      this.$emit('input', _.cloneDeep(params))
      this.$emit('change', params)
      // console.log(params)
    },
    initChannelSelectArr(channel) {
      let res = {
        channelList: [],
        model: [],
        lastSelectModel: []
      }
      let clListArr = `${this.$route.query.cList || ''}`.split(',').filter(v => !!v)
      const find = this.riskOptions.filterLists[1].dataList.find(v => v.value === channel)
      if (find) {
        if (_.isEmpty(find.children)) {
          res.model = [channel]
          res.lastSelectModel = [[channel]]
        } else {
          if (this.$route.query.cList !== undefined && (`${this.$route.query.cList}`).trim() !== '') {
            if (clListArr && clListArr.length) {
              res.lastSelectModel = clListArr.map(v => ([channel, v]))
              res.model = clListArr
            }
          } else {
            res.lastSelectModel = find.children.map(v => ([channel, v.value]))
            res.model = find.children.map(v => v.value)
          }
        }
      }
      return res
    },
    filterSelectClose() {
      this.$refs.changeRangeId.closeDropMenu()
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper-width {
  width: 201px;
}

.el-icon-arrow-right.__arrowRight {
  line-height: 19px;
}
</style>
