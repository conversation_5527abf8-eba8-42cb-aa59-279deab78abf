<template>
  <div class="_select-risk-type" :style="getStyle">
    <el-cascader-panel class="morePopover" ref="cascader" v-model="cascaderModel" :options="options.dataList"
                       v-if="options.dataList.length > 0"
                       :props="propMore" @change="sureClick(false)"
                       @expand-change="checkNodeTestColor">
<!--      <template slot-scope="{ node, data }">-->
<!--        <span @click="btnClick(node,data)" class="lable-title"> {{ data.label }} </span>-->
<!--      </template>-->
    </el-cascader-panel>
    <select-btns class="btn" @cancelclick="cancelclick" @sureclick="sureClick(true)" :hasSelect="!_isEmpty(cascaderModel)"
                 v-if="options.dataList && options.dataList.length > 0"></select-btns>
  </div>
</template>

<script>
import selectBtns from '../select-btns'
import _ from 'lodash'

export default {
  name: 'select-area',
  props: ['options', 'height'],
  components: { selectBtns },
  data() {
    return {
      propMore: {
        expandTrigger: 'hover',
        multiple: true,
        children: 'children',
        label: 'label',
        value: 'value'
      },
      cascaderModel: [],
      allSelectCount: 0
    }
  },
  computed: {
    getStyle() {
      if (!this.height) {
        return ''
      }
      return {
        height: this.height + 'px'
      }
    }
  },
  created() {
    if (this.options.lastSelectModel && this.options.lastSelectModel.length) {
      this.cascaderModel = this.options.lastSelectModel
    } else if (this.options.model && this.options.model.length) {
      this.cascaderModel = []
    }
    this.sureClick = _.debounce(this._sureClick, 80)
  },
  mounted() {
    this.allSelectCount = this.options.dataList.reduce((sum, item) => {
      return (item.children || []).length + 1 + sum
    }, 0)
    // console.log(this.allSelectCount)
  },
  methods: {
    btnClick(node, data) {
      // console.log(data)
    },
    checkNodeTestColor() {
      // 级联选择器组件：由于indeterminate样式在里层，通过js控制改父级兄弟元素的文字样式
      this.$nextTick(() => {
        let elements = this.$refs.cascader.$el.getElementsByClassName('el-cascader-node')
        let arr = Array.from(elements)
        arr.forEach((item) => {
          if (item.children[0].children[0].getAttribute('class').includes('is-indeterminate')) {
            item.children[1].style.color = '#128BED'
          }
        })
      })
    },
    cancelclick() {
      this.options.lastSelectModel = []
      this.options.selectCount = 0
      this.$emit('cancelclick')
    },
    _sureClick(isSure) {
      // console.log(this.$refs.cascader)
      let params = {
        level: '',
        channel: '',
        monitorRankList: [],
        riskList: [],
        channelList: [],
        ddRiskHitCodes: {
          hitCodeSet: []
        }
      }
      const { risklevel, up } = this.$route.query
      const channelNameList = ['redChannel', 'yellowChannel', 'greenChannel']
      const checkeds = this.$refs.cascader.getCheckedNodes()
      if (checkeds && checkeds.length > 0) {
        const selectChannelModel = checkeds.map(v => v.data.value)
        if (selectChannelModel && selectChannelModel.length) {
          if (selectChannelModel.includes('greenChannel')) {
            params.channelList = ['greenChannel']
          } else {
            params.channelList = ['redChannel', 'yellowChannel'].includes(risklevel) && up ? [risklevel] : []
          }
          params.ddRiskHitCodes.hitCodeSet = selectChannelModel.filter(i => !channelNameList.includes(i))
        }
      }

      if (params.channelList.length > 0) {
        params.channel = '1'
      }
      if (isSure) {
        this.$emit('input', _.cloneDeep(params))
        this.$emit('change', params)
        this.options.model = params
        this.options.lastSelectModel = this.cascaderModel
        // this.options.inputText = formatSelectAreaList(resSelect)
        this.options.selectCount = checkeds.length
        this.$emit('sureclick', this.options, true)
        // console.log(params, this.cascaderModel)
      }
      // console.log(params)
    }
  }
}

</script>

<style scoped lang="scss">
._select-risk-type {
  height: 100%;

  ._areaTree {
    min-width: 150px;
    height: calc(100% - 40px);
    max-height: calc(100% - 40px);
    overflow: auto;
    -ms-overflow-x: hidden;
    overflow-x: hidden;
  }

  ::v-deep .morePopover.el-cascader-panel{
    .el-checkbox {
      position: absolute;
      width: calc(100% - 10px);
      left: 10px;
    }
    .el-cascader-node__label {
      margin-left: 10px;
    }
  }
}

</style>
<style lang="scss">
._select-risk-type {
  .morePopover .el-cascader-menu__wrap {
    //height: 310px;
    height: 100%;
    overflow-x: auto;
  }

  .morePopover.el-cascader-panel.is-bordered {
    border: none;
    height: calc(100% - 40px);
  }

  .morePopover .el-icon-arrow-right {
    color: #999;
  }
}
</style>
