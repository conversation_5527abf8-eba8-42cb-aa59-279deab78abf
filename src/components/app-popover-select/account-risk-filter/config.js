import { RED_CHANNELS, YELLOW_CHANNELS } from '../../../routes/tanke/components/investigation/investigation-item/config'
import _ from 'lodash'

const riskLevel = [
  { label: 'R', value: '高风险信息', format1: '<span style="color: #B30000">高风险信息</span>' },
  { label: 'L', value: '风险信息', format1: '<span style="color: #F04040">风险信息</span>' },
  { label: 'W', value: '警示信息', format1: '<span style="color: #FFAA00">警示信息</span>' },
  { label: 'A', value: '提示信息', format1: '<span style="color: #128BED">提示信息</span>' }
  ]

const allGroupData = [
  {
    name: '风险等级',
    value: 'riskType',
    type: 'risk-level',
    labelkey: 'value',
    valuekey: 'label',
    model: [],
    selectCount: 0,
    dataList: [
      { value: '5', label: '高风险' },
      { value: '4', label: '较高风险' },
      { value: '3', label: '中风险' },
      { value: '2', label: '较低风险' },
      { value: '1', label: '低风险' }
    ]
  },
  {
    name: '尽调通道',
    value: 'channelType',
    type: 'type-chanel',
    labelkey: 'value',
    valuekey: 'label',
    selfView: 'select-risk-type',
    selectCount: 0,
    dataList: [
      // { value: 'all-value', isAll: true, label: '全部' },
      {
        value: 'redChannel',
        label: '红色通道',
        children: RED_CHANNELS.map(v => ({ label: v.name, value: `${v.code}` }))
      },
      {
        value: 'yellowChannel',
        label: '黄色通道',
        children: YELLOW_CHANNELS.map(v => ({ label: v.name, value: `${v.code}`, isSpecial: v.isSpecial }))
      },
      { value: 'greenChannel', label: '绿色通道' }
    ]
  }
]

const getAllGroupDataNoSpecial = () => {
  let data = _.cloneDeep(allGroupData)
  data[1].children[1].children[1].children = data[1].children[1].children[1].children.filter(v => !v.isSpecial)
  return data
}

export default {
  riskLevel,
  allGroupData,
  getAllGroupDataNoSpecial
}
