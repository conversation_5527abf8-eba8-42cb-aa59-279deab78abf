<template>
  <app-popover-select text="企业标签" :options="options" @change="changeSelect" class="group_filter_list"
                      ref="selectDropmenu" onlyOneType :immediateSearch="false">
  </app-popover-select>
</template>

<script>
import appPopoverSelect from '../index'
import _ from 'lodash'
import { getUsedTagsList } from '../../../routes/riskMonitor/monitor-list/company-monitor/config'
import { requestService } from '../../../services/broswer-service'

export default {
  components: { appPopoverSelect },
  props: {
    value: {},
    type: { default: 'list', type: String }// list;列表 detail 监控动态
  },
  model: {
    prop: 'value'
  },
  data() {
    return {
      options: {
        dropPos: { left: 0 },
        filterLists: [
          {
            name: '企业标签',
            type: 'company-label',
            valuekey: 'name',
            labelkey: 'id',
            dataList: [],
            model: []
          }
        ]
      },
      lastSelectRow: ''
    }
  },
  methods: {
    reset() {
      this.options.filterLists.forEach(row => {
        row.model = []
      })
      this.$emit('input', [])
    },
    changeSelect(row, selectDatas) { // 拿到当前条件的选择项
      this.$emit('input', _.cloneDeep(this.options.filterLists[0].model))
      this.$emit('change', this.value)
    },
    getLabelListRequest() {
      if (this.type === 'list') {
        getUsedTagsList('radar_id', list => {
          this.options.filterLists[0].dataList = list.concat([{ name: '无标签', id: 'none' }]).map(v => ({ name: v.name, id: v.id }))
          this.options.filterLists[0].loadEnd = true
        })
      } else if (this.type === 'detail') {
        requestService.getRiskMonitorDetailTags({})
        .then(res => {
          this.options.filterLists[0].dataList = res.resultList.concat([{ name: '无标签', id: 'none' }]).map(v => ({ name: v.name, id: v.id }))
          this.options.filterLists[0].loadEnd = true
        })
      }
    }
  },
  mounted() {
    this.getLabelListRequest()
  },
  created() {
    window.AppRuntimeContext.eventBus.$on(window.AppRuntimeContext.eventBusEvents.COMPANY_LABEL_CHANGED, (isBackExe) => {
      let res = this.options.filterLists[0]
      res.dataList = []
      res.loadEnd = false
      this.getLabelListRequest()
    })
  }

}

</script>

<style scoped lang="scss">
.group_filter_list ::v-deep .__myCheckboxGroupSelect {
  width: 200px;
}

.group_filter_list {
  &.__app-popover-content-wrapper .__select-list-wrapper.industryWrapper {
    border-left: none;
  }
}
</style>
