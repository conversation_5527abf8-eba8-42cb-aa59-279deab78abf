<template>
  <div class="app-export-process" :style="{width,marginTop}">
    <div class="image">
      <img src="../../assets/images/qcc-progress.png">
    </div>
    <div class="title">
      <slot name="title">
        <span>后台正在努力下载数据，成功后我们将第一时间提醒您</span>
      </slot>
    </div>
    <div class="footer">
      <slot name="footer">
        <span class="second">{{second}}</span>
        <span class="back" @click="backUrlClick">返回起始页</span>
      </slot>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'appExportProcess',
    props: {
      width: {
        type: String,
        default: '350px'
      },
      marginTop: {
        type: String,
        default: '-110px'
      }
    },
    data() {
      return {
        second: 5,
        timer: null
      }
    },
    mounted() {
      // this.refresh()
    },
    watch: {
      second() {
        if (this.second <= 0) {
          this.clearInterval()
          this.backUrlClick()
        }
      }
    },
    methods: {
      refresh(second) {
        this.second = second
        this.timer = null
        this.timer = setInterval(() => {
          --this.second
        }, 1000)
      },
      clearInterval(second) {
        this.second = second
        if (this.timer) { clearInterval(this.timer) }
      },
      backUrlClick() {
        this.$emit('back')
      }
    }
  }

</script>

<style lang="scss" scoped>
  .app-export-process {
    width: 325px;
    margin: 0 auto;
    position: relative;
    top: 50%;
    margin-top: -110px;

    .image {
      width: 120px;
      margin: 0 auto;

      img {
        width: 100%;
      }
    }

    .title {
      margin-top: 15px;
      text-align: center;
      font-size: 14px;
      color: #333333;
    }

    .footer {
      margin-top: 10px;
      text-align: center;
      font-size: 12px;
      color: #999999;

      .second {
        color: #FF722D;
      }

      .back {
        cursor: pointer;

        &:hover {
          color: #128bed;
        }
      }
    }
  }

</style>
