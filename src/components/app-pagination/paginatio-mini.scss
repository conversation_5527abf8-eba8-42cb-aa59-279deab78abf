@import "../../styles/baseFlexAdapter";
@import "../../styles/common";

$paginationMiniHeight: 40px;

@mixin changePageBtnStyle {
  width: 30px;
  height: 100%;
  padding: 0;
  box-sizing: border-box;
  background: #fff;
  border-color: #D8d8d8;
  color: #333333;
  font-size: 12px;

  .el-icon-arrow-right,
  .el-icon-arrow-left {
    font-weight: 700;
  }

  &:hover {
    border-color: $color-primary;
    color: $color-primary;
  }

  &:focus {
    border-color: #D8d8d8;
    color: #333333;

    &:hover {
      border-color: $color-primary;
      color: $color-primary;
    }
  }
}

._pagination-mini-wrapper {
  height: $paginationMiniHeight;
  @include inline-flex-def;
  @include flex-cCenter;
  line-height: $paginationMiniHeight;

  ._pagination_total {
    font-size: 12px;
    color: #666666;
  }

  ._preBtn {
    @include changePageBtnStyle;
    margin-left: 5px;
  }

  ._nextBtn {
    @include changePageBtnStyle;
  }

  ._page-show-container {
    @include inline-flex-def;
    @include flex-cCenter;
    position: relative;
    width: auto;
    min-width: 60px;
    height: 100%;
    vertical-align: bottom;
    margin: 0 5px;
    font-size: 12px;
    border-radius: 3px;

    ._inputPage {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }

    .showText {
      border: 1px solid #D8d8d8;
      padding: 0 8px;
      display: inline-block;
      width: 100%;
      height: 100%;
      line-height: 100%;
      text-align: center;
      background: white;
      color: #333333;
    }
  }

  ._mini_pagesize {
    width: 90px;
    margin-left: 5px;
    height: 100%;
  }
}

._table_pagination_wrapper {
  height: unset !important;
  line-height: unset !important;

  .tableOperateWrapper {
    position: absolute;
    left: 0;
    top: 10px;
    height: 30px;

    .tblbtn {
      height: 30px;
      line-height: 28px;
      padding: 0 8px;
    }

  }
}
