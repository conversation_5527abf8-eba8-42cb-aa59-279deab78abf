const myPaginationMixins = {
  props: {
    paginationProp: {
      default: () => {
        return {
          maxLimit: -1,
          pageSizes: [10, 20, 50, 80, 100],
          pageSize: 10,
          total: 0
        }
      }
    },
    disabled: { default: false }
  },
  methods: {
    handleCurrentChange(val) {
      if (this.paginationProp.handleCurrentChange) {
        this.paginationProp.handleCurrentChange(val)
      } else {
        this.$emit('handleCurrentChange', val)
      }
      this.$emit('pagationChange')
    }
  },
  created() {
    if (this.paginationProp.pageSizes === undefined) {
      this.$set(this.paginationProp, 'pageSizes', [10, 20, 50, 80, 100])
    }
    if (this.paginationProp.pageSize === undefined) {
      this.$set(this.paginationProp, 'pageSize', 10)
    }
    if (this.paginationProp.total === undefined) {
      this.$set(this.paginationProp, 'total', 0)
    }
    if (this.paginationProp.currentPage === undefined) {
      this.$set(this.paginationProp, 'currentPage', 1)
    }
    if (this.paginationProp.lastSelectPage === undefined) {
      this.$set(this.paginationProp, 'lastSelectPage', this.paginationProp.currentPage)
    }
    if (this.paginationProp.isNet === undefined) {
      this.$set(this.paginationProp, 'isNet', true)
    }
    if (this.paginationProp.pagerCount === undefined) {
      this.$set(this.paginationProp, 'pagerCount', 5)
    }
    if (this.paginationProp.noSizes === undefined) {
      this.$set(this.paginationProp, 'noSizes', false)
    }
  }

}

export default myPaginationMixins
