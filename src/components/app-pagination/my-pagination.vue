<template>
  <el-pagination ref="pagination" :class="[{'pagination-themeV2': themeV2}]"
                 @size-change="handleSizeChange" @current-change="currentChange" :current-page="paginationProp.currentPage"
                 :page-sizes="paginationProp.pageSizes" :page-size="paginationProp.pageSize" :layout="getLayout" :pager-count="paginationProp.pagerCount"
                 :total="paginationProp.maxLimit>0?(paginationProp.total>paginationProp.maxLimit?paginationProp.maxLimit:paginationProp.total):paginationProp.total"
                 :disabled="disabled"></el-pagination>
</template>

<script>
  import paginationMixin from './pagination-mixin'

  export default {
    name: 'app-pagination',
    mixins: [paginationMixin],
    data() {
      return {}
    },
    props: {
      themeV2: {
        default: false,
        type: Boolean
      }
    },
    computed: {
      getLayout() {
        let showTotal = 'total'
        if (this.paginationProp.noShowTotal) {
          showTotal = ''
        }
        if (this.paginationProp.total <= 0) {
          return showTotal
        } else {
          if (this.paginationProp.total <= this.paginationProp.pageSize) {
            return this.paginationProp.total <= (this.paginationProp.pageSizes && this.paginationProp.pageSizes.length > 0 ? this.paginationProp.pageSizes[0] : 10) ? showTotal : (this.paginationProp.noSizes ? showTotal : ((!showTotal ? '' : (showTotal + ',')) + 'sizes'))
          } else {
            if (this.paginationProp.noJumper) {
              return `${!showTotal ? '' : (showTotal + ',')}${this.paginationProp.noSizes ? '' : ' sizes,'} prev, pager, next`
            } else {
              return `${!showTotal ? '' : (showTotal + ',')}${this.paginationProp.noSizes ? '' : ' sizes,'} prev, pager, next, jumper`
            }
          }
        }
      }
    },
    methods: {
      handleSizeChange(val) {
        if (window.isNaN(val)) {
          return
        }
        this.paginationProp.pageSize = val
        if (this.$refs.pagination) {
          this.$refs.pagination.internalCurrentPage = 1
        }
        this.paginationProp.currentPage = 1
        this.handleCurrentChange(1)
      },
      currentChange(val) {
        this.paginationProp.currentPage = val
        this.handleCurrentChange(val)
      }
    },
    mounted() {}

  }

</script>

<style lang="scss">
  @import "../../styles/common";

  .el-pagination {
    padding-right: 0;
    &.pagination-themeV2 {

      padding: 0 0 15px !important;
      height: auto !important;
      margin-right: 0 !important;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      button {
        height: 32px;
        line-height: 30px;
        min-width: 32px;
        padding: 0;
        border-radius: 2px;
        background: #FFFFFF;
        border: 1px solid #EEEEEE;
        &:not([disabled]):hover {
          border-color: #128BED !important;
        }
      }
      button:disabled {
        display: none;
      }
      .btn-prev {
        color: #128BED;
        order: 1;
      }
      .el-pager {
        font-weight: normal;
        order: 2;
        li {
          padding: 0 10px;
          height: 32px;
          line-height: 30px;
          min-width: 32px;
          border-radius: 2px;
          background: #FFFFFF;
          border: 1px solid #EEEEEE;
          margin-right: 10px;
          color: #128BED;
          &:first-child {
            margin-left: 10px;
          }
          &:last-child {
            margin-right: 0;
          }
          &:hover {
            border-color: #128BED !important;
          }
          &.active {
            background: #128BED;
            border-color: #128BED;
            color: #fff;
            & + li {
              border-left: 1px solid #EEEEEE;
            }
          }
        }
      }
      .btn-next {
        color: #128BED;
        order: 3;
        margin-left: 10px;
      }
      .el-pagination__sizes {
        order: 4;
        height: 32px;
        line-height: 32px;
        min-width: 32px;
        display: flex;
        margin-left: 10px;
        .el-input {
          margin: 0;
          width: 90px;
        }
        .el-input__inner {
          height: 32px;
          line-height: 32px;
          border: 1px solid #EEEEEE;
          border-radius: 2px;
          padding-left: 10px;
          padding-right: 24px;
          color: #333;
        }
        .el-input__suffix {
          color: #666;
        }

        .el-select__caret {
          line-height: 32px;
        }
      }
      .el-pagination__jump {
        order: 5;
        margin-left: 30px;
        height: 32px;
        line-height: 32px;
        color: #333;
        .el-pagination__editor {
          height: 32px;
          margin: 0 5px;
          width: 50px;
          padding: 0;
        }
        .el-input__inner {
          height: 32px;
          line-height: 32px;
          border: 1px solid #EEEEEE;
          border-radius: 2px;
          color: #333;
        }
      }
    }

    .el-pagination__sizes {
      margin-right: 0;

      .el-select {
        .el-input {
          margin-right: 0;
        }
      }
    }

    .btn-prev {
      margin-left: 15px;
    }

    .el-pagination__total {
      margin-right: 0;
    }
  }

</style>
