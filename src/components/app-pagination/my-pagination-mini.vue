<template>
  <div class="_pagination-mini-wrapper" :style="getMiniPaginationStyle">
    <span class="_pagination_total" style="line-height: 100%">共 {{paginationProp.total}} 条</span>
    <el-button plain icon="el-icon-arrow-left" class="_preBtn" @click="goToPrePage" v-if="isShowPageChangeClick"></el-button>
    <div class="_page-show-container" v-if="isShowPageChangeClick">
      <div class="showText" @click="clickToEdit" :style="getMiniPaginationStyleLineHeight">
        {{paginationProp.currentPage}} / {{getTotalPage}}
      </div>
      <app-input ref="inputPage" size="normal" v-model="currentPage" type="number" class="_inputPage" @blur="blueInput" v-show="showInput"></app-input>
    </div>
    <el-button plain icon="el-icon-arrow-right" class="_nextBtn" @click="goToNextPage" v-if="isShowPageChangeClick"></el-button>
    <el-select v-model="paginationProp.pageSize" class="_mini_pagesize" @change="handleSizeChange" v-if="showPageSizeChange" :popper-class="popperClass">
      <el-option v-for="item in paginationProp.pageSizes" :key="item" :label="getPageSizeLabel(item)" :value="item" :disabled="disabled"></el-option>
    </el-select>
  </div>
</template>

<script>
  import paginationMixin from './pagination-mixin'
  import { Message } from 'element-ui'

  export default {
    name: 'app-pagination-mini',
    mixins: [paginationMixin],
    data() {
      return {
        currentPage: 1,
        showInput: false
      }
    },
    props: {
      height: { default: 40 },
      popperClass: { default: '' },
      themeV2: { default: false, type: Boolean }
    },
    computed: {
      isShowPageChangeClick() {
        return this.paginationProp.pageSize < this.paginationProp.total
      },
      getMiniPaginationStyle() {
        return { height: this.height + 'px' }
      },
      getMiniPaginationStyleLineHeight() {
        return { lineHeight: (this.height - 2) + 'px' }
      },
      getTotalPage() {
        let pagecount = Number(this.paginationProp.total)
        let newpagecount = pagecount % this.paginationProp.pageSize
        if (newpagecount === 0) {
          pagecount = parseInt(pagecount / this.paginationProp.pageSize)
        } else {
          pagecount = parseInt(pagecount / this.paginationProp.pageSize)
          pagecount += 1
        }
        return pagecount
      },
      showPageSizeChange() {
        return this.paginationProp.total > this.paginationProp.pageSizes[0]
      }
    },
    methods: {
      handleSizeChange(val) {
        this.paginationProp.currentPage = 1
        if (this.paginationProp.handleCurrentChange) {
          this.paginationProp.handleCurrentChange(1)
        } else {
          this.$emit('handleCurrentChange', 1)
        }
      },
      getPageSizeLabel(item) {
        return item + '条/页'
      },
      blueInput(e) {
        if (e.target.value && this.paginationProp.currentPage.toString() !== e.target.value.toString()) {
          var val = Number(e.target.value)
          if (val > 0 && val <= this.getTotalPage) {
            this.paginationProp.currentPage = Number(e.target.value)
            this.handleCurrentChange(this.paginationProp.currentPage)
          } else {
            Message.warning('您输入的页码有误')
          }
        }
        this.showInput = false
      },
      clickToEdit() {
        this.currentPage = this.paginationProp.currentPage
        this.showInput = true
        setTimeout(() => {
          this.$refs.inputPage.$refs.input.$refs.input.focus()
          this.$refs.inputPage.$refs.input.$refs.input.select()
        }, 200)
      },
      goToPrePage() {
        if (this.paginationProp.currentPage > 1) {
          this.paginationProp.currentPage = this.paginationProp.currentPage - 1
          this.handleCurrentChange(this.paginationProp.currentPage)
        } else {
          Message.warning('当前已经是第一页')
        }
      },
      goToNextPage() {
        if (this.paginationProp.currentPage < this.getTotalPage) {
          this.paginationProp.currentPage = this.paginationProp.currentPage + 1
          this.handleCurrentChange(this.paginationProp.currentPage)
        } else {
          Message.warning('当前已经是最后一页')
        }
      }
    },
    mounted() {
      var that = this
      if (that.$refs.inputPage && that.$refs.inputPage.$refs.input && that.$refs.inputPage.$refs.input.$refs.input) {
        that.$refs.inputPage.$refs.input.$refs.input.onkeydown = function(e) {
          if (e.keyCode === 13) {
            that.blueInput(e)
          }
        }
      }
    }
  }

</script>

<style scoped lang="scss" src="./paginatio-mini.scss"></style>
<style lang="scss">
  ._mini_pagesize {
    input[readonly] {
      background-color: #fff;
    }

    .el-input input:hover,
    .el-input input:focus {
      border-color: #D8d8d8;
    }

    .el-input,
    .el-input__inner {
      height: 100%;
      line-height: 100%;
      padding-left: 5px;
    }

    .el-input__inner {
      padding-right: 20px;
    }

    .el-input__suffix,
    .el-input__icon {
      right: 0;
      line-height: 100%;
    }
  }

  ._pagination-mini-wrapper ._page-show-container {
    ._inputPage .el-input__inner {
      height: 100%;
      vertical-align: top;
    }
  }

  ._mini_pagesize_pop {
    z-index: 99999 !important;
  }

</style>
