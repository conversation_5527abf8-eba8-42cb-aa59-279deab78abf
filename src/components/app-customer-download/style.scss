.download-container {
  line-height: unset;
  display: inline-block;
  //margin-right: 10px;

  @media screen and (max-height: 700px) {
    .customerDownloadContainer {
      max-height: 350px;
    }
  }

  .update-time {
    color: #999999;
    font-size: 12px;
  }

  .search {
    display: flex;
    justify-content: space-between;

    .keyword-pop {
      z-index: 100001;
      position: absolute;
      border: 1px solid #EEEEEE;
      border-radius: 4px;
      background-color: #fff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
      box-sizing: border-box;
      // top: 118px;
      top: 161px;
      left: 15px;
      width: 632px;
      max-height: 300px;
      overflow-y: auto;
    }

    .keyword-options {
      padding: 0px 10px;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      height: 50px;
      line-height: 50px;
      cursor: pointer;

      .keyword-left {
        text-align: left;
      }

      .keyword-right {
        text-align: right;

        .ed {
          color: #BBBBBB;
        }

        .ing {
          color: #128BED;
          cursor: pointer;
          display: inline-block;
        }
      }
    }

    .keyword-options-empty {
      font-size: 14px;
      height: 50px;
      line-height: 50px;
      text-align: center;
    }

    .keyword-options-nofirst {
      border-top: 1px solid #EEEEEE;
    }
  }

  .tabs-container {
    border: 1px solid #EEEEEE;
    clear: both;
    line-height: 20px;
    margin-top: 5px;

    .tabs {
      background-color: #fcfcfc;
      border-bottom: 1px solid #EEEEEE;
      line-height: 45px;

      .tab {
        padding: 0 15px;
        display: inline-block;
        font-size: 14px;
        cursor: pointer;

        border-left: 1px solid transparent;
        border-right: 1px solid transparent;
      }

      .is-active {
        color: #128BED;
        background-color: #fff;
        border-bottom-color: #EEEEEE;
      }

      .is-border {
        border-left-color: #EEEEEE;
        border-right-color: #EEEEEE;
      }

      .is-border-first {
        border-right-color: #EEEEEE;
      }
    }

    .btns {
      padding: 15px 15px 10px 15px;
    }
  }

  .download-margin {
    margin-top: 10px !important;
  }


  .download-select {
    margin-top: 20px;

    .title {
      border-left: 2px solid #128BED;
      padding-left: 5px;
      height: 18px;
      line-height: 18px;
      display: flex;
      justify-content: space-between;

      .left {
        text-align: left;
      }

      .count {
        color: #128bed;
      }

      .icon-shanchu {
        color: #F04040;
        margin-right: 1px;
      }

      .right {
        text-align: right;
        color: #333333;
        font-size: 12px;
        cursor: pointer;
        display: inline-block;
      }
    }

    .content {
      margin-top: 10px;
      border: 1px solid #EEEEEE;
      max-height: 120px;
      min-height: 80px;
      width: 100%;
      overflow-y: auto;
      padding: 10px 10px 0px 10px;
      line-height: 16px;

      .content-btn {
        border: dashed 1px #128BED;
        color: #128BED;
        font-size: 12px;
        padding: 4px;
        border-radius: 3px;
        margin-right: 10px;
        display: inline-block;
        margin-bottom: 10px;
        cursor: pointer;
      }
    }

    .content2 {
      line-height: 20px;
      padding-top: 10px;
    }

    .content3 {
      margin-top: 10px;
      line-height: 40px;
      display: flex;

      .left {
        width: 60px;
        display: block;
      }
    }
  }

}

.customer-export-tips {
  font-size: 14px;
  color: #ff8900;
  padding-left: 20px;
  height: 40px;
  line-height: 40px;
  background-color: #F9E8CE;

}
