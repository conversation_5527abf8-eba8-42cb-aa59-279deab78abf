<template>
  <div class="download-container">
    <div class="backgroud-global" v-show='downloadPop'></div>
    <el-popover popper-class='popper-class-customer' placement="bottom-end" width="350" trigger="manual" v-model='downloadPop' :disabled="downloadEnable">
      <div>
        <div class="download-poper-title">自定义导出1</div>
        <div class="download-poper-content">
          只需3步，批量导出全维度结构化数据；第一步<span class="download-orange-color">批量导入</span>，第二步<span class="download-orange-color">选择所需的维度</span>，第三步<span class="orange-color">下载数据</span>
        </div>
        <div class="download-poper-footer">
          <app-button type="primary" size="medium" @click="know">我知道了</app-button>
        </div>
      </div>
      <app-button v-if="buttonTitle" size='small' slot="reference" style="width:140px;" v-bind="buttonProps" @click="cusomterClick">{{buttonTitle}}</app-button>
      <app-icon-operations v-else-if="functionCd && !hideTitle" slot="reference" icon="iconfont icon-daochu" @clickbtn="cusomterClick">自定义导出</app-icon-operations>
      <!--      <span v-if="functionCd" size='small' type="text" plain slot="reference" class="button-rel-tip2" @click="cusomterClick"><i class="iconfont icon-daochu c-128bed"></i>&nbsp;自定义导出</span>-->
      <app-button v-else-if="!hideTitle" size='small' type="" plain slot="reference" class="button-rel-tip" @click="cusomterClick">自定义导出</app-button>
    </el-popover>
    <app-popup class="customer-download-export-popup" ref='appPopDoload' :isLazy='true' v-if="popUpControl.show" :transbg='false' :control='popUpControl' title="导出维度选择(可直接搜索或者勾选维度)" :buttonOptions="buttonOptions" :options="options" :zIndex="90001">
      <div class="customer-export-tips">注册地在中国香港、中国台湾以及境外的企业和社会组织，暂不支持数据导出</div>

      <div style="line-height: 50px; padding:20px 15px" class='customerDownloadContainer'>
        <app-loading v-if="loadingProp.show" :control="loadingProp" top="50px"></app-loading>
        <div class="search">
          <div>
            <el-input class="default-input download-input" placeholder="请输入维度关键词" v-model="keyword" @focus='keywordFocus(0)' @blur='keywordFocus(1)'>
              <i v-if="keyword" slot="suffix" class="input-clear-icon iconfont icon-shanchu3" @click="keyword=''"></i>
            </el-input>
            <div v-show="keywordPop" class="keyword-pop">
              <div v-for="(search,index) in keywordSearches" :key="index" @click="keywordClick(search)" :class="index===0?'keyword-options':'keyword-options keyword-options-nofirst'">
                <div class="keyword-left"><span v-html="setRedFocus(search.dictLabel)"></span> </div>
                <div class="keyword-right">
                  <span v-if="search.selected" class="ed">+已选择</span>
                  <span v-else class="ing">+选择</span>
                </div>
              </div>
            </div>
          </div>
          <div v-if="showRadio">
            <el-radio-group class="download-radio-option" v-model="radio">
              <el-radio label="1" @click.native.stop.prevent='radioClick' size="mini">全选</el-radio>
              <el-radio label="2" @click.native.stop.prevent='radioClick2' size="mini">反选</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div class="tabs-container">
          <div class="tabs">
            <div v-for="(item,index) in comboData" :class='currentTabStyle(item,index)' :key="item.dictValue" @click="setCurrentTab(item)">
              {{item.dictLabel}}
            </div>
          </div>
          <div class="btns">
            <el-checkbox-group v-model="checkList" class="download-checkListClass" @change='changeCheckBox'>
              <el-checkbox v-for="btn in currentComboData.childrens" :key="btn.dictValue" :label="btn.dictLabel"></el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class="download-select">
          <div class="title"><span class="left">已选维度 &nbsp;&nbsp;(<span class="count">{{selections.length}}项</span>)</span><span class="right" @click="clearAllData"><i class="iconfont icon-shanchu"></i>清空已选</span></div>
          <div class="content">
            <span v-for="selectbtn in selections" class="content-btn" @click="deleteSelectBtn(selectbtn)">{{selectbtn.dictLabel}}<i class="el-icon-close el-icon--right"></i></span>
          </div>
        </div>
        <div class="download-select">
          <div class="title"><span class="left">导出格式（<a :href="`https://pro-files.qichacha.com/open/sample/data_terminal_datasample${isPROD ? '' : '-sit'}.zip?v=20230728`">数据样例</a>&nbsp;&nbsp;<a :href="datDicUrl">数据字典_枚举说明</a>）</span></div>
          <div class="content2">
            <el-radio-group v-model="selectedExportTypes" class="download-checkListClass">
              <el-radio v-for="exType in supperTypes" :key="exType.value" :label="exType.label"></el-radio>
            </el-radio-group>
          </div>
        </div>
        <div class=" download-margin  download-select" v-show="source==='3'">
          <div class="title"><span class="left">接收邮箱</span></div>
          <div class="content3">
            <span class="left"><span style="color:red"> *</span>邮箱：</span>
            <app-input clearable style="width:100%" size='normal' placeholder="请输入邮箱" v-model="email" maxlength='100'></app-input>
          </div>
        </div>
      </div>
      <div slot="footerLeft" style="line-height: 32px;height:32px;">
        <span class="update-time"> <span class="iconfont icon-shuaxin2"></span> 数据更新于：{{updateDate}}</span>
      </div>
    </app-popup>
  </div>
</template>

<script src="./component.js">

</script>

<style lang="scss">
  .button-rel-tip {
    z-index: 200;
    position: relative;
    /*padding: 0px 10px;*/
    color: #333333
  }

  .button-rel-tip2 {
    border: none;
    display: inline-block;
    /*padding: 0px 10px;*/
    cursor: pointer;
  }

  .button-rel-tip2:hover {
    border: none;
    color: #128bed;
  }

  .button-rel-tip2:focus {
    color: #333333;
  }

  .keyword-find {
    color: #F04040;
  }

  .download-orange-color {
    color: #FF722D;
  }

  .download-poper-title {
    color: #333333;
    font-size: 18px;
  }

  .download-poper-content {
    color: #666666;
    padding: 8px 0px 20px 0px;
  }

  .download-poper-footer {
    text-align: right;
  }

  .download-input .el-input__inner {
    width: 630px
  }

  .download-radio-option .el-radio {
    margin-right: 15px;
  }

  .download-radio-option .el-radio__label {
    padding-left: 5px;
  }

  .download-radio-option .el-radio:last-child {
    margin-right: 0px;
  }

  .download-checkListClass {
    line-height: 22px;
  }

  .download-checkListClass .el-checkbox__label {
    padding-left: 4px;
  }

  .download-checkListClass .el-checkbox {
    margin-right: 20px;
    height: 20px;
    line-height: 20px;
  }

  .loading-customClass {
    z-index: 99999 !important;

  }

  .loading-customClass .el-loading-text {
    font-size: 14px !important;
  }

  .customer-download-export-popup {
    ._popup_contentWrapper {

      ._contentDefault {
        padding: 0;
      }
    }
  }

</style>


<style lang="scss" scoped src="./style.scss">

</style>
