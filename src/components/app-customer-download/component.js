import { postVue } from '../../services/common-service'
import { loadingMixins } from '../../utils/mixinsUtils'
import _ from 'lodash'
import { getDictListByType } from '../../utils/get-dict-utils'
export default {
  name: 'app-customer-download',
  mixins: [loadingMixins],
  props: {
    keyNoList: { default: [] },
    source: { default: '' },
    functionCd: { default: '' },
    count: { default: '' },
    top: { default: '' },
    checkLimit: { default: false, type: Boolean },
    buttonTitle: {
      type: String,
      default: ''
    },
    hideTitle: { default: false, type: Boolean },
    buttonProps: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isOuter: false,
      keyNoOutList: [],

      loadings: null,
      dependentCodes: [],
      dialogVisible: false,
      downloadPop: false,
      downloadEnable: false,
      radio: '',
      keyword: '',
      keywordSelectTop: '0px',
      keywordSelectLeft: '0px',
      keywordPop: false,
      options: {
        width: '960px',
        top: '3%'
      },
      popUpControl: { show: false },
      buttonOptions: [{ name: '确定导出', notZhuge: true, type: 'primary', click: this.export }],
      comboData: [],
      currentComboData: {},
      selections: [],
      checkList: [],
      supperTypes: [],
      selectedExportTypes: '',
      // datSampleUrl: '/template/数据样例.zip',
      datDicUrl: '/template/数据字典_枚举说明.xlsx?v=10',
      email: '',
      showRadio: true,
      updateDate: ''
    }
  },
  watch: {
    keyNoList: function() {
      this.isOuter = false
      this.keyNoOutList = []
    },
    top: function() {
      if (this.top) {
        this.options.top = this.top
      }
    },
    keyword() {
      this.keywordFocus()
    },
    checkList: {
      handler(newv, oldv) {
        let diffV = _.difference(newv, oldv)
        let dependentCode = ''

        this.selections = []
        this.comboData.forEach(dict => {
          dict.childrens.forEach(child => {
            let index = this.checkList.findIndex(e => e === child.dictLabel)
            if (index >= 0) {
              this.selections.push(child)
            }
            if (diffV && diffV.length > 0) {
              if (child.dictLabel === diffV[0]) {
                dependentCode = child.dependentCode
              }
            }
          })
        })

        let dependentItem = this.getDependent(dependentCode)
        if (dependentItem) {
          let index = this.checkList.findIndex(e => e === dependentItem.dictLabel)
          if (index < 0) {
            this.checkList.push(dependentItem.dictLabel)
          }
        }
      },
      deep: true
    }
  },
  computed: {
    keywordSearches: function() {
      let kws = []
      let that = this
      this.comboData.forEach(dict => {
        dict.childrens.forEach(child => {
          if (child.dictLabel.indexOf(that.keyword) >= 0) {
            child.selected = false
            that.selections.forEach((e) => {
              if (e.dictValue === child.dictValue) {
                child.selected = true
              }
            })
            kws.push(child)
          }
        })
      })

      return kws
    }
  },
  methods: {
    functionCdSetRadio() {
      if (this.functionCd === 'expand_cust_full_adv_search' ||
        this.functionCd === 'expand_cust_chg_addr_search' ||
        this.functionCd === 'expand_cust_list_reg_capi' ||
        this.functionCd === 'expand_cust_list_map' ||
        this.functionCd === 'expand_cust_list_high_tech') {
        this.showRadio = false
      }
    },
    changeCheckBox(item) {
      // console.log('xxxxxxxxxxx', item)
    },
    clearAllData() {
      this.selections = []
      this.checkList = []
    },
    currentTabStyle: function(tab, index) {
      if (tab.dictValue === this.currentComboData.dictValue) {
        if (index === 0) {
          return 'tab is-active is-border-first'
        }
        return 'tab is-active is-border'
      }
      return 'tab'
    },
    setCurrentTab(item) {
      this.currentComboData = item
    },
    getCombos() {
      postVue('/corp_data/order/getDimensions', {}).then((data) => {
        if (data.status === '200') {
          this.updateDate = data.jsonStr
          this.dependentCodes = []

          data.resultList.forEach(element => {
            element.childrens.forEach(child => {
              if (child.dependentCode) {
                this.dependentCodes.push(child.dependentCode)
              }
            })
          })

          data.resultList.forEach(element => {
            let children = []
            element.childrens.forEach(child => {
              let index = this.dependentCodes.findIndex(e => e === child.dictValue)
              if (index < 0) {
                children.push(child)
              }
            })
            element.childrens = children
          })


          this.comboData = data.resultList
          this.currentComboData = this.comboData[0]
        }
      })
      let that = this
      getDictListByType('data_support_type', [], res => {
        this.supperTypes = res
      })

      postVue('/webapi/saas/corp_data/order/getDataTermEmail', {}).then((data) => {
        if (data.status === '200' && data.result) {
          that.email = data.result
        }
      })
    },
    getDependent(dependentCode) {
      let dependentItem = null
      if (dependentCode) {
        this.comboData.forEach(current => {
          current.childrens.forEach(child => {
            if (child.dictValue === dependentCode) {
              dependentItem = child
            }
          })
        })
      }
      return dependentItem
    },
    keywordClick(item) {
      let index = this.selections.findIndex(e => e.dictValue === item.dictValue)
      if (index < 0) {
        delete item.selected
        this.selections.push(item)
        this.checkList.push(item.dictLabel)
      }

      let dependentItem = this.getDependent(item.dependentCode)
      if (dependentItem) {
        let index2 = this.selections.findIndex(e => e.dictValue === dependentItem.dictValue)
        if (index2 < 0) {
          delete dependentItem.selected
          this.selections.push(dependentItem)
          this.checkList.push(dependentItem.dictLabel)
        }
      }
    },
    deleteSelectBtn(item) {
      let index = this.selections.findIndex(e => e.dictValue === item.dictValue)
      if (index >= 0) {
        // this.selections
        this.selections.splice(index, 1)
      }
      let index2 = this.checkList.findIndex(e => e === item.dictLabel)
      if (index2 >= 0) {
        // this.selections
        this.checkList.splice(index2, 1)
      }
    },
    radioClick() {
      this.radio = '1'
      this.selectChange(1)
    },
    radioClick2() {
      this.radio = '2'
      this.selectChange(2)
    },
    selectChange(flg) {
      if (flg === 1) {
        this.checkList = []
        this.comboData.forEach(dict => {
          dict.childrens.forEach(child => {
            this.checkList.push(child.dictLabel)
          })
        })
        this.selections = []
        this.comboData.forEach(dict => {
          dict.childrens.forEach(child => {
            this.selections.push(child)
          })
        })
      } else if (flg === 2) {
        let currentChecks = []
        this.comboData.forEach(dict => {
          dict.childrens.forEach(child => {
            let index = this.checkList.findIndex(e => e === child.dictLabel)
            if (index < 0) {
              currentChecks.push(child.dictLabel)
            }
          })
        })
        this.checkList = currentChecks

        let currentSelects = []
        this.comboData.forEach(dict => {
          dict.childrens.forEach(child => {
            let index = this.checkList.findIndex(e => e.dictValue === child.dictValue)
            if (index < 0) {
              currentSelects.push(child)
            }
          })
        })
        this.selections = currentSelects
      }
    },
    setRedFocus(text) {
      let reg = new RegExp('(' + this.keyword + ')', 'g')
      let newstr = text.replace(reg, '<font color="#F04040">$1</font>')
      return newstr
    },
    keywordFocus(flg) {
      let that = this
      this.$nextTick(() => {
        if (this.keyword && !flg) {
          this.keywordPop = true
        } else {
          setTimeout(() => {
            that.keywordPop = false
          }, 400)
        }
      })
    },
    cusomterOutClick(keyNoList) {
      this.isOuter = true
      this.keyNoOutList = keyNoList
      this.loadingProp.show = false
      if (!this.keyNoOutList.length || this.keyNoOutList.length <= 0) {
        this.$message.warning('请选择企业')
        return
      }

      if (this.count && this.keyNoOutList.length > (+this.count)) {
        this.$message.warning('当前选中企业已超出最大导出量（' + this.count + '）')
      }

      this.init()
    },
    cusomterClick() {
      this.isOuter = false
      this.keyNoOutList = []
      this.loadingProp.show = false
      if (!this.keyNoList.length || this.keyNoList.length <= 0) {
        this.$message.warning('请选择企业')
        return
      }

      if (this.count && this.keyNoList.length > (+this.count)) {
        this.$message.warning('当前选中企业已超出最大导出量（' + this.count + '）')
        return
      }
      if (this.checkLimit) {
        this.checkLimitCount().then(this.init)
      } else {
        this.init()
      }
    },
    checkLimitCount() {
      let params = {
        keyNoList: [],
        dimensions: '',
        type: '',
        source: this.source,
        email: '',
        functionCd: this.functionCd,
        allCorpKeyNoFrom: ''
      }
      return postVue('/corp_data/order/validateDailyExportLimitCount', params)
    },
    init() {
      this.keyword = ''
      this.popUpControl.show = true
      this.selections = [{ dictValue: 'AA', dictLabel: '工商信息' }]
      this.checkList = ['工商信息']
      this.selectedExportTypes = 'Excel'
      this.radio = ''
      // this.email = ''
      this.currentComboData = this.comboData[0]
      postVue('/webapi/saas/corp_data/order/getDataTermEmail', {}).then((data) => {
        if (data.status === '200' && data.result) {
          this.email = data.result
        }
      })
    },
    know() {
      this.downloadPop = false
      localStorage.setItem('downloadKnow', '1')
    },
    resetKnow() {
      let downloadKnow = localStorage.getItem('downloadKnow')
      if (downloadKnow === '1') {
        this.downloadPop = false
        this.downloadEnable = true
      } else {
        this.downloadPop = true
      }

      if (this.functionCd) {
        this.downloadPop = false
      }
    },
    loading() {
      this.loadings = this.$loading({
        lock: true,
        text: '拼命加载中',
        background: 'rgba(255,255,255,0.95)',
        customClass: 'loading-customClass'
      })
    },
    export () {
      $util.zhugeTrackTwoLevel('自定义导出-确定', $util.getCurrentPageTitle(window.AppRuntimeContext.instance))
      if (this.selections.length <= 0) {
        this.$message.warning('请选择要导出的维度')
        return
      }
      if (!this.selectedExportTypes) {
        this.$message.warning('请选择要导出数据格式')
        return
      }

      if (this.source === '3') {
        if (this.email) {
          if (!$util.checkIsEmail(this.email)) {
            this.$message.warning('输入的邮箱格式不正确')
            return
          }
        } else {
          this.$message.warning('输入的邮箱格式不正确')
          return
        }
      }

      let wdValue = []
      this.selections.forEach(sel => {
        if (sel.dependentCode) {
          let deptIndex = wdValue.findIndex(e => e === sel.dependentCode)
          if (deptIndex < 0) {
            wdValue.push(sel.dependentCode)
          }
        }
        wdValue.push(sel.dictValue)
      })

      let selectType = ''
      this.supperTypes.forEach(supper => {
        if (supper.label === this.selectedExportTypes) {
          selectType = supper.value
        }
      })
      this.loadingProp.show = true
      /// source:功能来源：1-企业详情；2-企业列表；3-数据导入
      let knl = this.isOuter ? this.keyNoOutList : this.keyNoList
      let params = { keyNoList: knl, dimensions: wdValue.toString(), type: selectType, source: this.source, email: this.email, functionCd: this.functionCd }
      postVue('/corp_data/order/orderData?showError', params).then((data) => {
        this.loadingProp.show = false
        if (data.status === '200') {
          this.$refs.appPopDoload.close()
          this.popUpControl.show = false
          // this.$message.success('您的报告正在生成中，成功后我们将第一时间提醒您 单据ID:' + data.result)
          this.$message.success('后台正在努力导出中，成功后我们将第一时间提醒您')
          this.$emit('downloadProcess')
        } else {
          this.$message.error(data.msg)
        }
      }).catch(() => {
        this.$refs.appPopDoload.close()
      })
    }
  },
  created() {
    this.getCombos()
  },
  mounted() {
    this.options.top = this.top
    this.functionCdSetRadio()
    this.resetKnow()
    this.loadingProp.show = false
  }
}
