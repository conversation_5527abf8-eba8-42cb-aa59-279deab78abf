<template>
  <div class="ball-container">
    <transition-group tag="div" @before-enter='beforeEnter' @enter='enter' @after-enter='afterEnter'>
      <div class="ball" v-for="(item, index) in balls" :key="item.id" v-show="item.show" transition='drop'>
        <div class="inner inner-hook">
          1
        </div>
      </div>
    </transition-group>
  </div>
</template>

<script>
export default {
  data() {
    return {
      balls: [
        { show: false, id: 0 },
        { show: false, id: 1 },
        { show: false, id: 2 },
        { show: false, id: 3 },
        { show: false, id: 4 },
        { show: false, id: 5 },
        { show: false, id: 6 },
        { show: false, id: 7 }
      ],
      dropBalls: []
    }
  },
  methods: {
    drop(target) {
      const { balls } = this
      for (var i = 0; i < this.balls.length; i++) {
        var ball = balls[i]
        if (!ball.show) {
          ball.show = true
          ball.el = target
          this.dropBalls.push(ball)
          return
        }
      }
    },
    beforeEnter(el) {
      let count = this.balls.length
      while (count--) {
        let ball = this.balls[count]
        if (ball.show) {
          let rect = ball.el.getBoundingClientRect() // 获取小球的相对于视口的位移(小球高度)
          // let x = -(window.innerWidth - rect.left - 126)

          let x = -(window.innerWidth - rect.left - 246)

          let y = rect.top - 90 // 负数是从左上角往下的的方向, 正数是往上
          el.style.display = ''
          el.style.webkitTransform = `translate3d(0, ${y}px, 0)`
          el.style.transform = `translate3d(0, ${y}px, 0)`
          // 处理内层动画
          let inner = el.getElementsByClassName('inner-hook')[0]
          inner.style.webkitTransform = `translate3d(${x}px, 0, 0)`
          inner.style.transform = `translate3d(${x}px, 0, 0)`
        }
      }
    },
    enter(el, done) {
      /* eslint-disable no-unused-vars */
      let rf = el.offsetHeight // 触发重绘html
      this.$nextTick(() => {
        el.style.webkitTransform = 'translate3d(0, 0, 0)'
        el.style.transform = 'translate3d(0, 0, 0)'
        let inner = el.getElementsByClassName('inner-hook')[0]
        inner.style.webkitTransform = 'translate3d(0, 0, 0)'
        inner.style.transform = 'translate3d(0, 0, 0)'
        el.addEventListener('transitionend', done)
      })
    },
    afterEnter(el) {
      let ball = this.dropBalls.shift()
      if (ball) {
        ball.show = false
        el.style.display = 'none'
      }
    }
  },
  mounted() {
    window.AppRuntimeContext.eventBus.$on(window.AppRuntimeContext.eventBusEvents.TASK_BALL, (target) => {
      this.drop(target)
    })
  }
}

</script>

<style lang="scss" scoped>
  .ball-container {
    width: 20px;
    height: 20px;
    position: absolute;
    z-index: 1000;
    right: 10px;
    top: 0px;

    .ball {
      &.v-enter-active {
        // transition: all .6s cubic-bezier(0.11, 0.92, 0.98, 0.75);
        transition: all .6s cubic-bezier(0.11, 0.92, 1, 1);
      }

      .inner {
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: #E08283;
        transition: all .6s linear;
        z-index: 1000;
        position: absolute;
        text-align: center;
        line-height: 18px;
        color: #fff;

      }
    }
  }

</style>
