import echarts from 'echarts'
import { jietuChart } from '../../routes/charts/components/echarts_common.js'
import { windowResizeMixins } from '../../utils/mixinsUtils'
export default {
  name: 'app-chart',
  mixins: [windowResizeMixins],
  props: {
    title: String,
    // echart option
    option: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    this.chart = '' // 不需要监测，在此定义
    return {}
  },
  methods: {
    onWindowSizeChange() {
      if (this.option && this.chart) {
        this.chart.resize()
      }
    },
    init(option) {
      try {
        if (this.echartId) {
          if (!this.chart) {
            this.chart = echarts.init(document.getElementById(this.echartId), 'classic',
            {
              // renderer: 'svg'
            })
          } else {
            this.chart.clear()
          }
          if (option) {
            option.backgroundColor = 'rgba(255, 255, 255, 0)'
            option.textStyle = $util.commonChartsTextStyle(true)
          }
          this.chart.setOption(option)
          this.chart.resize()
        }
      } catch (error) {

      }
    },

    click(params) {
      this.$emit('chartclickfunc', params)
    },

    doSaveImg() {
      this.jietuChart(this.chart, {
        type: 'png',
        pixelRatio: 2, // 放大两倍下载，之后压缩到同等大小展示。解决生成图片在移动端模糊问题
        backgroundColor: '#fff'
      }, this.title)
    },
    jietuChart(myChart, option, fileName) { // 截图
      jietuChart(myChart, option, fileName)
    }
  },
  watch: {
    option: {
      handler (val) {
        this.init(val)
      },
      deep: true
    }
  },
  computed: {
    echartId() {
      return 'echarts' + Math.random() * 100000
    }
  },
  mounted() {
    this.init(this.option || {})
    if (this.chart) {
      this.chart.on('click', (params) => {
        this.click(params)
      })
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.clear()
      this.chart = null
    }
  }
}
