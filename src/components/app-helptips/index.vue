<template>
  <el-popover :placement='placement' :width='width' :trigger='trigger' v-if='showTipsHelp || mustShow'
              :popper-class="'default-popover-tooltip ' + className  + `${maxWidth?' tool-auto-width' + maxWidth:' '}`"
              @show="show" @hide="hide"
              :open-delay="100"
              :close-delay="150"
  >
    <div class='_my_helptips'>
      <div class='_header' v-if="!disableTitle && hasTitle">
        <div class="help-header">
          <div class="help-header__icon">
            <span class='iconfont icon-zhuyishixiang'></span>
          </div>
          <div class="help-header__title">{{ helpText }} <span class="sub-title">{{subHelpText}}</span></div>
        </div>
        <div class="help-header-time" v-if="showTme" :style="{color:'#FFFFFF'}">今日：{{ getNowTime() }}</div>
      </div>
      <div class="help-prefix" v-if="$slots.prefix">
        <slot name="prefix"></slot>
      </div>
      <div class="_list_wrapper" :class="`${!hasTitle ? '_nothastitle' : ''}`" :style='liststyle'>
        <div v-for='item in helpContentList' :key='item.title' class='_item'>
          <h3 class='_item_title' v-if='item.title'>{{ item.title }}</h3>
          <template v-if="item.slotName">
            <slot :name="item.slotName"></slot>
          </template>
          <template v-else-if="item.contentList">
            <div class="_item-content_list flex" v-for="(i,index) in item.contentList">
              <span class="_item_content_num">{{ index + 1 }}.</span>
              <p class='_item_content_p' v-html="i"></p>
            </div>
          </template>
          <template v-else-if="item.contentListNoIndex">
            <div class="_item-content-no-index-list" v-for="(i,index) in item.contentListNoIndex">
              <span class="_item_content_num" >{{ i.label }}</span>
              <p class='_item_content_p' v-html="i.value"></p>
            </div>
          </template>
          <div v-else-if="item.tagsList" class="_item-content-tags-list">
            <p v-for="(i,index) in item.tagsList">#{{ i.name }}</p>
          </div>
          <template v-else>
            <p style='margin-bottom: 0' v-html="item.content"></p>
          </template>
        </div>
      </div>
      <slot name="listBottom"></slot>
    </div>
    <slot name="reference" slot='reference' v-if="isWrapReference"></slot>
    <span v-else slot='reference' :class='iconClass' :style="iconStyle"></span>
  </el-popover>
</template>

<script>
import moment from 'moment'

export default {
  name: 'app-helptips',
  props: {
    showTme: { type: Boolean, default: false }, // hover后头部右边当前时间
    mustShow: { type: Boolean, default: false }, // 强行显示
    isWrapReference: { type: Boolean, default: false }, // 外层按钮，原来是默认图标
    disableTitle: { type: Boolean, default: false }, // 外层按钮，原来是默认图标
    iconClass: {
      type: String,
      default: 'iconfont icon-icon_zhushi tool-izhushiv2 font14'
    },
    iconStyle: {
      type: Object
    },
    placement: { default: 'bottom-start' },
    width: { default: 350 },
    maxWidth: { default: 0 },
    trigger: { default: 'hover' },
    helpText: { default: '注意事项' },
    subHelpText: { default: '' },
    helpContentList: {
      default: () => {
        return []
      }
    },
    helpCode: {},
    codeTitle: {},
    liststyle: {
      default: () => {
        return {}
      }
    },
    className: { default: '' },
    content: { default: '', type: String }

  },
  data() {
    return {
      isNewVersion: $util.isNewVersion()
    }
  },
  computed: {
    showTipsHelp: function () {
      return this.helpContentList && this.helpContentList.length > 0
    },
    hasTitle() {
      return this.helpText
    }
  },
  methods: {
    getNowTime() {
      return moment().format('MM-DD HH:mm')
    },
    show(obj) {
      this.$emit('show', obj)
    },
    hide(obj) {
      this.$emit('hide', obj)
    }
  }
}

</script>

<style scoped lang="scss">
@import "../../styles/common";


.el-icon-question._helpIcon,
._helpIcon{
  cursor: pointer;
  color: #BBBBBB;
  font-size: 14px;

  //&:hover {
  //  color: $color-primary;
  //}
}

._my_helptips {
  position: relative;
  padding: 0 3px;
  margin-top: -8px;

  ._header {
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    border-bottom: 1px solid #1B1B1B;
    font-size: 15px;
    font-weight: bold;
    color: #FFFFFF;
    height: 50px;
    line-height: 50px;

    ._icon {
      margin-right: 5px;
      color: $color-warning;
    }
  }

  ._list_wrapper {
    box-sizing: border-box;
    max-height: 320px;
    overflow: auto;
    -ms-overflow-x: hidden;
    margin: 15px 0 7px;
    padding: 0;
    &._nothastitle {
      padding: 0;
    }

    ._item {
      padding: 0;
      margin-bottom: 20px;
      > p {
        color: #E3E3E3;
        font-size: 14px;
        line-height: 22px;
      }
      ._item_title {
        font-size: 14px;
        line-height: 14px;
        padding-left: 5px;
        box-sizing: border-box;
        margin-top: 4px;
        margin-bottom: 10px;
        color: #FFFFFF;
        border-left: 3px solid #128BED;
        font-weight: 500;
      }
      &:first-child ._item_title {
        margin-top: 4px;
      }
      ._item_content {
        font-size: 14px;
        text-align: justify;
        line-height: 21px;
        white-space: pre-line;
        padding-bottom: 10px;
        color: #999999;

      }
      ._item-content_list {
        margin-bottom: 10px;
        ._item_content_num, ._item_content_p {
          color: #E3E3E3;
          font-size: 14px;
          line-height: 21px;
        }
        ._item_content_num {
          display: inline-block;
          margin-right: 5px;
        }
        ._item_content_p {
          flex: 1;
        }

        &:last-child {
          margin-bottom: 0;
        }

      }
      ._item-content-no-index-list {
        font-size: 14px;
        line-height: 22px;
        margin-bottom: 10px;
        display: flex;

        ._item_content_num {
          font-weight: bold;
          color: #FFFFFF;
          display: block;

          white-space: nowrap;
          word-break: keep-all;
        }

        ._item_content_p {
          color: #FFFFFF;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
      ._item-content-tags-list {
        display: flex;
        flex-wrap: wrap;

        p {
          color: #E3E3E3;
          padding-right: 10px;
          line-height: 22px;
        }
      }
      &:last-child {
        margin-bottom: 0;
        ._item_content {
          padding-bottom: 0;
          font-size: 14px;
          color: #999999;
          line-height: 21px;
        }
      }
    }
    ._item:last-child {
      padding: 0;
    }
  }
}

.help-prefix {
  margin: 15px 0 10px;
  font-size: 14px;
}

.help-header {
  display: flex;
  align-items: center;

  &__title {
    display: flex;
  }

  &__icon {
    background: #F04040;
    width: 18px;
    height: 18px;
    border-radius: 100%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: normal;
    margin-right: 5px;

    .icon-zhuyishixiang {
      font-size: 12px;
      color: #FFFFFF;
    }
  }
  .sub-title {
    font-size: 12px;
    font-weight: normal;
    display: flex;
    margin-left: 10px;
  }
}

.help-header-time {
  font-size: 14px;
  font-weight: normal;
}
.small-pop-scroll.el-popper {
  ._list_wrapper::-webkit-scrollbar {
    width: 4px;
    background: #141414;
  }
  ._list_wrapper::-webkit-scrollbar-thumb {
    background: #4D4D4D;
  }
}
</style>
<style lang="scss">
.el-popper._forhelptips {
  padding: 0;

  &.leftOffset62 {
    margin-left: -62px;
  }

  &.leftOffset80 {
    margin-left: -80px;
  }

  &.leftOffset90 {
    margin-left: -90px;
  }

  &.leftOffset93 {
    margin-left: -93px;
  }

  &.leftOffset100 {
    margin-left: -100px;
  }
}
.popover-table {
  thead tr th.header-row-class {
    line-height: 18px !important;
    padding: 9px 0 !important;
    .cell {
      font-size: 12px;
      line-height: 18px !important;
      height: 18px !important;
    }
  }
  .el-table tbody td {
    padding: 9px 0 !important;
    .cell {
      line-height: 18px !important;
    }
  }
}
</style>
