<template>
  <div class="app-slider-percent">
    <el-slider class="slider-con" v-model="percent" :min="min" :max="max" :format-tooltip="formatTooltip4Silder"
      @change="sliderChange"></el-slider>
    <app-input-number class="input-number-con" :class="unit ? 'input-number-con-unit' : ''" hasDefault controls
      :placeholder="placeholder" v-model="percent" size="small" @change="sliderChange"
      :options="{ min, max }"></app-input-number>
    <span class="percent-unit" v-html="unit"></span>
  </div>
</template>

<script>
export default {
  name: 'app-slider-percent',
  props: {
    placeholder: { type: String, default: '' },
    min: { type: Number, default: 0 },
    formatTooltip: { type: Function, default: undefined },
    max: { type: Number, default: 100 },
    precision: { type: Number, default: 2 },
    unit: { type: String, default: '%' },
    // 绑定的值
    value: {
      type: [Number, String, Array],
      default: 0
    }
  },
  data() {
    return {
      percent: 0
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  watch: {
    // 监听用户的值
    value(newValue) {
      this.percent = newValue
    }
  },
  computed: {

  },
  methods: {
    sliderChange() {
      if (this.precision) {
        let num = +this.percent
        if (num === parseInt(num)) {
          this.percent = parseInt(num)
        } else {
          num = parseFloat(num.toFixed(this.precision))
          this.percent = num
        }
      }
      this.$emit('change', this.percent)
    },
    formatTooltip4Silder() {
      if (this.formatTooltip) {
        return this.formatTooltip(this.percent)
      } else {
        return this.percent + this.unit
      }
    }
  }


}
</script>
<style lang="scss" scoped>
.app-slider-percent {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  .percent-unit {
    position: absolute;
    right: 18px;
    top: 8px;
  }

  .input-number-con-unit {
    ::v-deep .el-input__inner {
      padding-right: 30px;
    }
  }

  .slider-con {
    flex-grow: 1;

    ::v-deep .el-slider__runway {
      margin: 10px 0;
      background-color: #EEEEEE;

      .el-slider__button-wrapper {
        z-index: 100;

        .el-slider__button {
          width: 12px;
          height: 12px;
          border: solid 1px #128bed;
        }
      }

    }
  }

  .input-number-con {
    width: 80px !important;
    margin-left: 10px;

    ::v-deep .el-input-number {
      height: 28px;
      line-height: 28px;

      .el-input-number__decrease {
        border-radius: 0 0 2px 0;
        width: 14px;
        line-height: 1px !important;
      }

      .el-input-number__increase {
        border-radius: 0 2px 0 0;
        width: 14px;
        line-height: 1px !important;
      }

      .el-input__inner {
        border-radius: 2px;
      }
    }

  }


}
</style>
