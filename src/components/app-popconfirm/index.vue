<template>
  <el-popover
    v-model="show"
    :popper-class="'delete-tips-popup ' + popperClass"
    placement="top"
    @show="popShow"
    @hide="popHide"
    >
    <slot>
      <div class="delete-tips-content" :style="{width}"
           @mouseover="mouseEnter" @mouseleave="mouseOut">
        <div class="tips-wrapper">
          <!--                  <span class="iconfont icon-jinggao1"></span>-->
          确定删除该分组？
        </div>
        <div class="button-wrapper">
          <app-button type="normal" size="small" @click="show=false">取消</app-button>
          <app-button type="primary" size="small" @click="sure">确定</app-button>
        </div>
      </div>
    </slot>
    <slot name="reference" slot="reference"></slot>
  </el-popover>
</template>

<script>
import _ from 'lodash'
export default {
  name: 'app-popconfirm',
  props: {
    popperClass: { default: '', type: String },
    tips: { default: '', type: String },
    width: { default: '250px', type: String }
  },
  data() {
    return {
      show: false,
      autoCloseTimeId: -1
    }
  },
  created() {
  },
  methods: {
    sure() {
      this.$emit('sure')
      this.show = false
    },
    mouseEnter: _.debounce(function () {
      if (this.autoCloseTimeId !== -1) {
        clearTimeout(this.autoCloseTimeId)
        this.autoCloseTimeId = -1
      }
    }, 200),
    mouseOut: _.debounce(function () {
      this.doAutoClosePop()
    }, 200),
    popShow() {
      this.doAutoClosePop(5000)
    },
    popHide() {
      if (this.autoCloseTimeId !== -1) {
        clearTimeout(this.autoCloseTimeId)
        this.autoCloseTimeId = -1
      }
    },
    doAutoClosePop(time = 3000) {
      if (this.autoCloseTimeId !== -1) {
        clearTimeout(this.autoCloseTimeId)
        this.autoCloseTimeId = -1
      }
      this.autoCloseTimeId = setTimeout(() => {
        this.show = false
      }, time)
    }
  }
}
</script>

<style scoped lang="scss">
.delete-tips-content {
  padding: 15px;
  width: 250px;

  .tips-wrapper {
    display: flex;
    align-items: center;
  }

  .icon-jinggao1 {
    color: #FFAA00;
    font-size: 16px;
    margin-right: 10px;
  }

  .button-wrapper {
    display: flex;
    margin-top: 15px;
    justify-content: flex-end;

    ::v-deep .el-button + .el-button {
      margin-left: 15px;
    }
  }
}
</style>
<style lang="scss">
.el-popover.el-popper.delete-tips-popup {
  padding: 0px;
  width: auto;
}
</style>
