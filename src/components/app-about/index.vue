<template>
  <div class="about-box">
    <ul class="up-box">
      <li class="system-box" v-for="item in productList" :key="item.title">
        <div class="title-box" @click="skipLink(item)" style="cursor: pointer;">{{item.title}}</div>
        <div class="detail-box" v-for="detail in item.details" :key="detail.title">
          <div class="name-box">{{detail.name}}</div>
          <div class="name-box" :class="[{isactive:!detail.disable}]">
            <template v-if="detail.disable">
              {{detail.title}}
            </template>
            <template v-else-if="!detail.html">
              <a :title="detail.title || ''" class="link-theme" v-if="!detail.html" target="_self" :href="detail.url" @click="clickZhuge(detail.title)">{{detail.title}}</a>
            </template>
            <span v-else v-html="detail.html" />
          </div>
        </div>
      </li>
      <ul class="code-box">
        <li class="img-box">
          <div class='title'>微信公众号</div>
          <img class="wc-img" src="/img/wechat_pub.png?v=2">
        </li>
        <li class="img-box">
          <div class='title'>微信小程序</div>
          <img class="wc-img" src="/img/revision/xcxewm.png?v=3">
        </li>
      </ul>
    </ul>
    <div class="line"></div>
    <ul class="small-box">
      <li class="link-box" v-for="item in linkList" :key="item.title">
        <div class="title">{{item.title}}</div>
        <div class="b-link" v-for="(detail,index) in item.details" :key="detail.name">
          <template v-if="detail.url">
            <a class="link-theme" target="_self" :href="detail.url">{{detail.name}}</a>
          </template>
          <template v-else>
            {{detail.name}}
          </template>
          <!--          <div v-if="detail.url!=='#'" @click="skipOtherPage(detail.url)">{{detail.name}}</div>-->
          <div class="l-line" v-if="index !=item.details.length -1">|</div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script src="./component.js"></script>
<style scoped lang="scss" src="./style.scss"></style>
<style lang="scss">
  a.link-theme {
    font-size: 14px;
    color: #999999;

    &:hover {
      color: white;
    }
  }

</style>
