export default {
  name: 'appAbout',
  data() {
    return {
      productList: [
        {
          title: '关于我们',
          url: '/about_us',
          details: [
            {
              title: '关于我们',
              url: '/about_us?id=1',
              id: '1'
            },
            {
              title: '服务条款',
              url: '/about_us?id=4',
              id: '4'
            }
          ]
        },
        {
          title: '联系我们 ',
          url: '/about_us',
          details: [
            {
              name: '电话咨询：',
              title: '400-088-8275',
              disable: true
            },
            {
              name: '在线咨询：',
              title: '',
              html: '<a class="link-theme" onclick="window.open(\'/openOnlineChart?href=\' + encodeURIComponent(window.location.href));zhugeTrackTwoLevel(\'点击在线咨询\', window.currentPagename)"  rel="nofollow">点击在线咨询</a>'
            },
            {
              name: '合作邮箱：',
              title: '<EMAIL>',
              url: 'mailto:<EMAIL>'
            }
          ]
        },
        {
          title: '解决方案',
          url: 'javascript:;',
          details: [
            {
              title: '金融行业',
              url: '/financial'
            },
            {
              title: '制造行业',
              url: '/manufacturing'
            }
          ]
        },
        {
          title: '产品体系',
          url: '/product',
          details: [
            {
              title: '尽职调查',
              url: '/product'
            },
            {
              title: '风险监控',
              url: '/product'
            }
          ]
        }
      ],
      linkList: [
        {
          title: '友情链接：',
          details: [
            {
              name: '企查查',
              url: 'https://www.qcc.com'
            },
            {
              name: '开放平台',
              url: 'https://openapi.qcc.com?source=proqichacha'
            }
          ]
        },
        {
          title: '数据来源：',
          details: [
            {
              name: '国家企业信用信息公示系统'
            },
            {
              name: '中国裁判文书网'
            },
            {
              name: '中国执行信息公开网'
            },
            {
              name: '国家知识产权局'
            },
            {
              name: '商标局'
            },
            {
              name: '版权局'
            }
          ]
        }
      ]
    }
  },
  methods: {
    clickZhuge(name) {
      if (!window) return true
      return window.zhugeTrackTwoLevel(name, window.currentPagename)
    },
    skipLink: function(item) {
      let url = item.url
      try {
        if (item.id) {
          if (url.indexOf('about_us') >= 0) {
            localStorage.setItem('catorgate_about', item.id)
          }

          if (url.indexOf('industry') >= 0) {
            localStorage.setItem('catorgate_industry', item.id)
          }
        }
      } catch {

      }

      if ($formatter.isBlank(url)) {
        return
      }
      window.location.href = url
    },
    skipOtherPage: function(url) {
      if ($formatter.isBlank(url)) {
        return
      }
      window.open(url)
    }
  },

  components: {}
}
