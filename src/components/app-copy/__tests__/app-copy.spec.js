import appCopy from '../index.vue'
import { mount } from '@vue/test-utils'

describe('appCopy', () => {
  beforeEach(() => {

  })
  it('show button', async () => {
    const wrapper2 = mount(appCopy, {
      mocks: {
      },
      propsData: {
        isNewCopyBut: false,
        copyValue: ''
      }
    })
    await wrapper2.vm.$nextTick()
    console.log('====>', wrapper2.vm.copyValue, wrapper2.vm.isHasValue)
    // 断言 computed 属性 title 的值
    expect(!!(wrapper2.vm.isHasValue)).toBe(false)
  })
})
