@import "../../styles/common.scss";
.app-copy-box {
  display: inline-block;

  &.isTouchDevice {
    .new-copy-but,
    .copy-button-item {
      display: none;
    }
  }

  .app-copy {
    cursor: pointer;
    position: relative;
    font-weight: normal;
    &:hover:not(.copyTextHoverNoBg) {
      background-color: #f2f8fe;
    }
  }

  .copy_input {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    z-index: -10;
    width: 0px;
    height: 0px;
  }

  .base_copy {
    margin-left: 2px;
    color: #128bed;
    // font-size: 14px;
    display: inline-block;
    line-height: 19px;
    width: 30px;
    word-break: keep-all;

    .aicon {
      color: #128bed;
      margin-left: 7px;
    }
  }
}

// 复制按钮的显示隐藏
.copy-hover-item {
  .copy-button-item {
    opacity: 0;
    // display: none;
  }

  &:hover {
    .copy-button-item {
      opacity: 1;
      // display: inline-block;
    }

    .copy-value {
      background-color: #e5f2fd;
    }

    ::v-deep {
      .el-popover__reference-wrapper {
        .copy-value {
          background-color: #e5f2fd;
        }
      }
    }
  }
}

.app-copy-box {
  cursor: default;
  .new-copy-inner {
    font-size: 22px;
    line-height: 30px;
    font-weight: bold;
  }
  .new-copy-but {
    opacity: 0;
    display: inline-flex;
    align-items: center;
    padding: 4px 5px;
    font-size: 14px;
    line-height: 22px;
    vertical-align: top;
    color: #128bed;
    i {
      display: block;
    }
    div {
      padding-left: 4px;
    }
  }
  &:hover {
    .new-copy-inner {
      background-color: #e5f2fd;
    }
    .new-copy-but {
      opacity: 1;
    }
  }
}

.copy-hover-item:not(.placeholder) {
  .copy-button-item {
    display: none;
  }

  &:hover {
    .copy-button-item {
      display: inline;
    }
  }
}
