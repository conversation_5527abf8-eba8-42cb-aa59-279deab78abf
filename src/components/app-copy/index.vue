<template>
  <div v-if="canCopy" class="app-copy-box" :class="[{'copy-hover-item': !isTouchDevice},{ isTouchDevice },{'placeholder': isPlaceholderElement}]">
    <template v-if="isNewCopyBut">
      <span v-if="copyHtml" class="new-copy-inner" v-html="copyHtml" :style="innerStyle"></span>
      <slot name="newContain"></slot>
      <div class="new-copy-but" @click.stop="copy()" :style="iconStyle">
        <!-- <i class="el-icon-document-copy aicon"></i> -->
        <div>复制</div>
      </div>
    </template>
    <template v-else>
      <span class="app-copy-box-text" :class="needHover && isHasValue ? 'copy-value' : ''">
        <slot></slot>
      </span>
      <slot name="contain"></slot>
      <slot name="btn">
        <span class="app-copy copy-button-item" :class="[{copyTextHoverNoBg}]" :style="iconStyle" v-show="isHasValue">
          <div @click.stop="copy()" class="base_copy">
            <!-- <i class="el-icon-document-copy aicon"></i> -->
            <span>复制</span>
          </div>
        </span>
      </slot>
    </template>
    <textarea type="text" class="copy_input" :id="copyId" :value="copyValue"></textarea>
  </div>
  <div v-else>
    <slot></slot>
  </div>
</template>
<script src="./component.js"></script>
<style lang="scss" src="./style.scss" scoped></style>
