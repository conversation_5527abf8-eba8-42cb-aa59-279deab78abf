import { isTouchDevice } from '@/routes/companyDetail/hooks/company-detail.hook'

export default {
  name: 'app-copy',
  props: {
    copyValue: '',
    needHover: {
      default: false,
      type: Boolean
    },
    canCopy: {
      default: true,
      type: Boolean
    },
    track: {
      type: String
    },
    iconStyle: {
      type: [String, Object]
    },
    innerStyle: {
      type: [String, Object]
    },
    isNewCopyBut: {
      default: false,
      type: Boolean
    },
    copyHtml: {
      type: String
    },
    isPlaceholderElement: {
      default: true,
      type: Boolean
    },
    copyTextHoverNoBg: { // 复制 hover时不设置背景
      default: false,
      type: <PERSON>olean
    }
  },
  data() {
    return {
      copyId: 'CopyInut' + new Date().getTime(),
      isTouchDevice
    }
  },
  computed: {
    isHasValue() {
      return this.copyValue || [0, false].includes(this.copyValue)
    }
  },
  methods: {
    copy(copyValue) {
      try {
        let inputElement = document.getElementById(this.copyId)
        inputElement.value = `${(copyValue || this.copyValue)}`.replace(/<[^>]+>/g, '')
        inputElement.select()
        if (document.execCommand('Copy')) {
          this.$message.success('复制成功')
        }
      } catch (error) {
        this.$message.error('复制失败')
      }
    }
  }
}
