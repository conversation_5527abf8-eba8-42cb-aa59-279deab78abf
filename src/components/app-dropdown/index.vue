<template>
  <app-button v-if="isOnlyButton" :type="type" :plain="plain" :size="size" notZhuge @click.native="btnClick(menus[0],$event)">{{format ? format(menus[0]) : menus[0][nameKey]}}</app-button>
  <el-dropdown v-else :trigger="trigger" :class="`el-dropdown-${size}`" :placement="placement" @visible-change="handleVisibleChange">
    <app-icon-operations v-if="isNewsAdd" :class="[iconOperationClass && 'iconOperationClass',this.dropdownMenuVisible && 'active']" :icon="icon" :suffixIcon="suffixIconTriangle ? (isNewsIcon?newTriangle:triangle) : ''" :title="title" @clickbtn="$emit('click')"></app-icon-operations>
    <app-button :class="{'active': this.dropdownMenuVisible, 'hover-trigger': trigger === 'hover', 'selected': showSelectedClass}" v-else :type="type" :plain="plain" :size="size" notZhuge @click="$emit('click')"><slot name="title-left"></slot><span>{{title}}</span><span class="caret"></span></app-button>
    <el-dropdown-menu slot="dropdown" :class="dropdownMenuClass">
      <el-dropdown-item :class="{'active': activeIndex === index}" v-for="(item,index) in menus" :disabled="isDisabled ? isDisabled(item) : (item.disabled ? item.disabled : false)" :key="item[nameKey]" v-if="item.show === undefined ? true : item.show" @click.native="btnClick(item,$event, index)">{{format ? format(item) : item[nameKey]}}</el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
  export default {
    name: 'app-dropdown',
    props: {
      onlyButton: {
        type: Boolean,
        default: false
      },
      title: {},
      isNewsAdd: {
        type: Boolean,
        default: false
      },
      isNewsIcon: {
        type: Boolean,
        default: false
      },
      menus: { type: Array }, // [{name: '菜单',click: func},...]
      size: { default: 'small' },
      placement: { default: 'bottom-end' },
      format: {},
      isDisabled: {},
      nameKey: { default: 'name' },
      type: { default: 'normal' },
      plain: { default: false, type: Boolean },
      trigger: { type: String, default: 'click' },
      activeIndex: { type: Number, default: -1 },
      icon: { default: 'iconfont icon-tianjia icon-blue16 size14' },
      suffixIconTriangle: { default: false, type: Boolean },
      iconOperationClass: { default: false, type: Boolean },
      dropdownMenuClass: {
        type: String,
        default: ''
      },
      showSelectedClass: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        dropdownMenuVisible: false,
        triangle: 'triangle-down',
        newTriangle: 'el-icon-caret-bottom isNewsIcon'
      }
    },
    computed: {
      isOnlyButton() {
        if (this.onlyButton) {
          if (this.menus && this.menus.length === 1) {
            return true
          }
        }

        return false
      }
    },
    methods: {
      btnClick(item, ievent, index) {
        this.$emit('update:title', item.name)
        if (item.click) {
          item.click(ievent)
        } else {
          this.$emit('menuClick', item, index)
        }
        if (this.$route && this.$route.meta && this.$route.meta.title) {
          $util.zhugeTrackThreeoLevel(this.title.trim(), item[this.nameKey].trim(), $util.getCurrentPageTitle(this))
        }
      },
      handleVisibleChange (visible) {
        this.dropdownMenuVisible = visible
        // this.triangle = !visible ? 'triangle-down' : 'triangle-up'
        if (this.isNewsIcon) {
          this.newTriangle = !visible ? 'el-icon-caret-bottom isNewsIcon' : 'el-icon-caret-top isNewsIcon'
        } else {
          this.triangle = !visible ? 'triangle-down' : 'triangle-up'
        }
      }
    }
  }

</script>
<style lang="scss" scoped>
::v-deep {
  .el-button {
    & > span {
      display: flex;
      align-items: center;
      span:first-child {
        margin-right: 5px;
      }
    }
  }
  .el-button--primary {
    .caret {
      border-top-color: #fff;
    }
  }
}
.iconOperationClass{
  line-height: 26px;
  display: inline-block;
  padding: 0 9px;
  border: 1px solid #D8D8D8;
  border-radius: 2px;
  &:hover,&.active {
    border: 1px solid #128BED;
    ::v-deep {
      .suffixIcon.triangle-up {
        border-bottom: 4px dashed #128BED;
      }
      .suffixIcon.triangle-down {
        border-top: 4px dashed #128BED;
      }
      .title,i.iconfont {
        color: #128BED;
      }
    }
  }
  ::v-deep {
    i.iconfont {
      color: #666666;
      font-size: 14px;
    }
  }
}
</style>
<style lang="scss">
  .el-dropdown {
    .el-button {
      padding-left: 10px;
      padding-right: 10px;
    }

    &.el-dropdown-small {
      line-height: 30px;

      .el-button {
        font-size: 12px;
      }
    }

    &.el-dropdown-mini {
      line-height: 26px;

      .el-button {
        font-size: 12px;
      }
    }

    &.el-dropdown-large {
      line-height: 40px;

      .el-button {
        font-size: 14px;
      }
    }

    &.el-dropdown-medium {
      line-height: 36px;

      .el-button {
        font-size: 14px;
      }
    }
    .el-button {
      &.hover-trigger {
        &.hover-trigger:hover {
          color: #128bed;
          .caret {
            transform: rotate(180deg);
            border-top-color: #128BED;
          }
        }
        &.active {
          color: #128bed;
          border-color: #128bed;
          .caret {
            transform: rotate(180deg);
            border-top-color: #128BED;
          }
        }

        &.selected {
          color: #128bed;
          border-color: #128bed !important;

          .caret {
            border-top-color: #128BED;
          }
        }
      }
    }
  }

  .el-dropdown-menu.el-popper {
    min-width: 200px;
    .el-dropdown-menu__item {
      line-height: 17px;
      width: 100%;
      padding: 10px;
      color: #333333;

      &.is-disabled {
        color: #bbb;
        pointer-events: none;
      }
      &.active {
        color: #128bed;
      }
    }
  }

  .el-dropdown-menu__item:not(.is-disabled):hover,
  .el-dropdown-menu__item:focus {
    color: #128BED;
    background: #f2f8fe;
  }

</style>
