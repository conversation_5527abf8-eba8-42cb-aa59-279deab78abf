<template>
  <el-dropdown>
    <span class="el-dropdown-link">
      <i :class="['iconfont','margin-r-0-3x',icon]"></i>{{title}}
    </span>
    <el-dropdown-menu slot="dropdown" v-if="menu.length>0">
      <el-dropdown-item v-for="(i,index) in menu" :key="index" @click.native.stop="handleCommand(i)">{{getItemValue(i)}}</el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  name: 'app-dropdown-help',
  props: {
    // 文字内容
    title: {
      type: String,
      default: () => '企业对外投资介绍'
    },
    // 左侧图标  传入iconfont className
    icon: {
      type: String,
      default: 'icon-tishi'
    },
    // 传入下拉菜单数组
    menu: {
      type: Array,
      default: () => [1, 2]
    },
    // 如果menu是对象数组，需要传入文字对应的属性名字 {key:需要显示的文字}  meta = key
    meta: {
      type: String,
      default: ''
    },
    helpMode: {
      default: false,
      type: <PERSON><PERSON>an
    }
  },
  methods: {
    /**
       *  params e {} 返回menu中被选中的一项
       */
    handleCommand(e) {
      if (this.helpMode) {
        sessionStorage.setItem('subTitle', e.key)
        const url = this.$router.resolve({ path: '/help-center' })
        window.open(url.href, '_blank')
      } else {
        this.$emit('command', e)
      }
    },
    getItemKey() {
      return this.meta ? this.meta : (this.helpMode ? 'title' : '')
    },
    getItemValue(item) {
      return this.getItemKey() ? item[this.getItemKey()] : item
    }
  }
}

</script>
<style scoped lang="scss">
  .el-dropdown-link {
    cursor: pointer;
  }

  .el-dropdown-link:hover {
    color: #128bed;
  }

</style>
