<template>
  <div class="input-search-ctn">
    <div v-if="!searchShow" class="input-search" @click="searchChange(true)" :style="heightStyle">
      <span class="search-inner"><span class="iconfont icon-sousuo"></span>{{searchText}}</span>
    </div>
    <div v-if="searchShow">
      <div class="input-outer" :style="heightStyle">
        <input class="input" ref="input" type="text" :placeholder="placeholder" @keyup.enter="search" :maxlength="options.maxlength" v-model.trim="inputVal" :style="{'width':options.width+'px'}">
        <button type="button" class="btn btn-primary" @click="search">搜索</button>
        <span v-if="inputVal" class="iconfont icon-Shape" @click="clear"></span>
      </div>
      <span @click="cancel"><a>取消</a></span>
    </div>
  </div>
</template>
<script>
  export default ({
    name: 'appInputSearch',
    props: {
      placeholder: { type: String, default: '' },
      searchText: { type: String, default: '点击进行搜索' },
      options: {
        type: Object,
        default: () => ({
          width: 'auto',
          height: 30,
          maxlength: 30
        })
      }
    },
    data() {
      return {
        inputVal: '',
        lastInput: '',
        searchShow: false
      }
    },
    computed: {
      heightStyle() {
        return {
          height: this.options.height + 'px',
          lineHeight: (this.options.height - 2) + 'px'
        }
      }
    },
    methods: {
      searchChange() {
        this.searchShow = true
        this.$nextTick(() => {
          this.$refs.input.focus()
        })
      },
      cancel() {
        this.searchShow = false
        this.inputVal = ''
        this.lastInput = ''
        this.$emit('search', { key: 'inputVal', value: this.inputVal })
      },
      search() {
        if (this.inputVal !== this.lastInput) {
          this.$emit('search', { key: 'inputVal', value: this.inputVal })
        }
        this.lastInput = this.inputVal
      },
      clear() {
        this.inputVal = ''
        this.lastInput = ''
        this.$refs.input.focus()
        this.$emit('search', { key: 'inputVal', value: this.inputVal })
      }
    }
  })

</script>

<style lang="scss" scoped>
  .input-search-ctn {
    .input-search {
      white-space: nowrap;
      background: #FFFFFF;
      border: 1px solid #D8d8d8;
      color: #999;
      text-align: center;
      padding-left: 10px;
      padding-right: 10px;
      font-size: 12px;
      border-radius: 3px;
    }

    .iconfont {
      font-size: 13px;
      margin-right: 5px;
      color: #d4d4d4;
    }

    .input-outer {
      display: inline-flex;
      vertical-align: middle;
      margin-right: 5px;
      position: relative;
    }

    .input {
      padding-left: 8px;
      padding-right: 25px;
      outline: none;
      border: 1px solid #128bed;
      height: 100%;
      font-size: 12px;
      border-top-left-radius: 3px;
      border-bottom-left-radius: 3px;
    }

    button {
      height: 100%;
      line-height: inherit;
      color: #fff;
      background-color: #128bed;
      text-align: center;
      white-space: nowrap;
      cursor: pointer;
      border: 0 solid transparent;
      font-size: 14px;
      user-select: none;
      border-top-right-radius: 3px;
      border-bottom-right-radius: 3px;
    }

    .icon-Shape {
      position: absolute;
      right: 40px;
      top: 50%;
      transform: translateY(-47%);
      cursor: pointer;
    }
  }

</style>
