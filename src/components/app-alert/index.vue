<template>
  <base-popup ref="pop" @outerclick="bgclick" :transbg="getTransbg" :zIndex="zIndex">
    <transition name="popupSlideDown">
      <div v-if="showContent" class="__alert_contentWrapper" :class="options.contentWrapperClass || ''"
        :style="getContentWrapperStyle">
        <div v-if="title" class="__alert_contentWrapper_title">
          <div v-html="title"></div>
          <div><i class="iconfont icon-icon_guanbixx" @click="clickCancel"></i></div>
        </div>
        <div>
          <span v-if="!title && options.type" class="_icon" :class="`el-icon-${options.type}`"></span>
          <span class="_message-content" :class="!options.type ? 'fit' : ''" v-html="msg"></span>
        </div>
        <div class="_footer" :class="{ '_footer-center': options.isBtnCenter, '_footer-title': title }">
          <app-button plain type=" " size="small" class="_btn" @click="clickCancel" not-zhuge v-if="!options.hidecancel" :disabled="disabledBtn">{{
            options.cancelText || '取消' }}</app-button>
          <app-button type="primary" size="small" class="_btn" v-if="!options.hideSure" not-zhuge @click="clickSure" :disabled="disabledBtn">{{
            options.sureText || '确定' }}</app-button>
        </div>
      </div>
    </transition>
  </base-popup>
</template>

<script>
import basePopup from '../app-popup/base-popup'
import { removePopup } from '../app-popup/utils'
export default {
  name: 'app-alert',
  components: { basePopup },
  data() {
    return {
      showContent: false,
      disabledBtn: false
    }
  },
  computed: {
    getTransbg() {
      return this.transbg || false
    },
    getContentWrapperStyle: function () {
      return Object.assign({}, {
        width: this.options.width || '400px',
        left: 'calc((100% - ' + (this.options.width || '400px') + ') / 2)',
        top: this.options.top || '50px',
        height: this.options.height || 'auto'
      })
    },
    zIndex() {
      return this.options.zIndex ? this.options.zIndex : 999
    },
    title() {
      // 弹窗样式暂时使用旧的，以后更改按照这个进行整体修复即可--现暂时为AML做
      if (this.currentIsAMLAccount) {
        return this.options.title
      }
      return ''
    }
  },
  methods: {
    clickSure() {
      this.closeAlert()
      this.$promise.resolve()
    },
    clickCancel(type) {
      this.closeAlert()
      try {
        this.$promise.reject(type)
      } catch (e) { console.error(e) }
    },
    closeAlert() {
      if (!this.options.notCloseFlag) { // 不能关闭
        this.showContent = false
        removePopup(this.$refs.pop.idKey)
        this.$refs.pop.close(() => {
          this.$modal.close()
        })
      } else {
        this.disabledBtn = true
      }
    },
    bgclick() { // 半透明背景的点击事件
      if (this.options.onlyClose) {
        this.closeAlert()
      } else if (this.options.beforeClose) {
        this.options.beforeClose(this.closeAlert)
      } else {
        this.clickCancel('bgclick')
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.showContent = true
      var height = document.body.offsetHeight
      var avaiableHeight = height - 60 - 50
      this.$emit('popupMounted', { avaiableHeight: avaiableHeight, height: height })
    })
  }
}

</script>

<style scoped lang="scss" src="../app-popup/my-popup.scss"></style>
<style scoped lang="scss">
@import "../../styles/common.scss";

.__alert_contentWrapper {
  padding: 15px;
  background: white;
  position: absolute;
  border-radius: 3px;
  box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.20);

  .__alert_contentWrapper_title {
    font-weight: bold;
    font-size: 15px;
    color: #333333;
    line-height: 24px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;

    display: flex;
    justify-content: space-between;

    .icon-icon_guanbixx {
      font-size: 18px;
      color: #bbb;
      cursor: pointer;
      font-weight: normal;
    }
  }

  ._icon {
    font-size: 24px;
    vertical-align: top;
    margin-right: 5px;

    &.el-icon-success {
      color: $global-color-success;
    }

    &.el-icon-warning {
      color: $color-warning;
    }

    &.el-icon-error {
      color: $color-danger;
    }

    &.el-icon-info {
      color: $color-info;
    }
  }

  ._message-content {
    display: inline-block;
    width: calc(100% - 35px);
    font-size: 14px;
    color: $color-text;
    line-height: 22px;
    vertical-align: top;
    max-height: 50vh;
    overflow: auto;

    &.fit {
      width: 100%;
    }
  }

  ._footer {
    text-align: right;
    padding-top: 15px;

    ._btn {
      height: 32px;
      min-width: 80px;
      padding: 0 15px !important;
    }
  }

  ._footer-center {
    text-align: center !important;
  }

  ._footer-title {
    border-top: 1px solid #eee;
    margin-top: 20px;
  }
}
</style>
