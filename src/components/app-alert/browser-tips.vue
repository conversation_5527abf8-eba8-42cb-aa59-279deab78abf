<template>
  <app-popup title="浏览器模式切换提示" :buttonOptions="[{ name: '我知道了', type: 'normal' }]" @closed="closed" :options="options">
    <div style="line-height: 14px;">检测到您的浏览器当前处于“<span class="tips-warning">兼容模式</span>”，为了更好的体验，请参考以下提示将浏览器手动改为“<span class="tips-warning">极速模式</span>”。</div>
    <div class="tips-wrapper">
      <img :src="imgs.begin" width="342"><img
      :src="imgs.arrow" width="36" style="margin: auto 20px"><img
      :src="imgs.end" width="342">
    </div>
  </app-popup>
</template>

<script>
export default {
  name: 'browser-tips',
  data() {
    return {
      options: { width: '960px' },
      imgs: {
        begin: require('../../assets/images/browser-compatible.png'),
        end: require('../../assets/images/browser-rapid.png'),
        arrow: require('../../assets/images/browser-arrow.png')
      }
    }
  },
  methods: {
    closed() {
      this.$modal.close()
    }
  }
}
</script>

<style scoped lang="scss">
  @import "../../styles/common";
  .tips-warning{
    color: #FF722D;
  }
  .tips-wrapper{
    margin-top: 20px;
    @include flex-def;
    @include flex-cCenter;
    img{
      vertical-align: top;
    }
  }

</style>
