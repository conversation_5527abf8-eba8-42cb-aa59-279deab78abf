<template>
  <base-popup ref="pop" :zIndex="9999999" :bgClose="false">
    <transition name="popupSlideDown">
      <div class="_popup_contentWrapper version_popup_contentWrapper" v-show="show">
        <img :src="bgImg" width="600px">
        <div v-html="versionText" class="version-theme"></div>
        <span class="el-icon-close closeicon" @click="sure" />
        <img src="./img/find-new-version.png" class="new-version-txt" width="201px">
        <div class="tips-text">服务器版本已发生变更，请刷新页面</div>
        <app-button type="primary" size="large" not-zhuge style="width: 84px;text-align: center" @click="sure">确定</app-button>
      </div>
    </transition>
  </base-popup>
</template>

<script>
  import basePopup from '../app-popup/base-popup'
  import { clearSessionWhenCutVersion } from '@/utils/storage-operate-util'

  export default {
    components: { basePopup },
    data() {
      return {
        bgImg: require('./img/version-upgrade-tip.png'),
        show: false
      }
    },
    computed: {
      versionText() {
        return this.version || ''
      }
    },
    methods: {
      sure() {
        try {
          clearSessionWhenCutVersion()
          this.$message.success('正在刷新，请稍后...')
          setTimeout(() => {
            window.location.reload()
          }, 1500)
        } catch (e) {
          setTimeout(() => {
            window.location.reload()
          }, 1000)
        }
      }
    },
    mounted() {
      setTimeout(() => {
        this.show = true
      }, 150)
    }
  }

</script>

<style scoped lang="scss" src="../app-popup/my-popup.scss" />
<style scoped lang="scss">
  ._popup_contentWrapper.version_popup_contentWrapper{
    width: 600px;
    height: 300px;
    min-width: unset;
    background: white;
    border-radius: 3px;
    margin-left: calc(50vw - 300px);
    margin-top: calc(45vh - 170px);
    text-align: center;
    position: relative;
    .version-theme {
      position: absolute;
      left: 40px;
      top: 90px;
      font-size: 18px;
      color: #FFFFFF;
      line-height: 24px;
    }

    .new-version-txt{
      position: absolute;
      left: 40px;
      top: 45px;
    }

    img {
      border-top-left-radius: 3px;
      border-top-right-radius: 3px;
    }

    .closeicon{
      position: absolute;
      padding: 10px;
      color: #fff;
      cursor: pointer;
      top: 10px;
      right: 10px;
      &:hover {
        opacity: 0.5;
      }
    }
    .tips-text {
      font-size: 16px;
      color: #333333;
      line-height: 21px;
      margin-top: 28px;
      margin-bottom: 20px;
    }
  }
</style>
