<template>
  <div class="drop-box">
    <div class="section-box" :class="[{isactive:currentIndex === index}]" v-for="(item,index) in dropList" :data="item" :key="item.id" @mouseenter="showItem(index)" @click="menuClick(item.title,item.func)">
      <div class="line" v-if="item.title === '安全退出'"></div>
      <div class="left-box"></div>
      <div class="iconfont" :class="[item.icon]"></div>
      <div class="section-title">{{item.title}}</div>
    </div>
  </div>
</template>

<script src="./component.js"></script>
<style scoped lang="scss" src="./style.scss"></style>
