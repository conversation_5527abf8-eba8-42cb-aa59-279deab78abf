@import '../../styles//common.scss';

.drop-box {
  display: flex;
  flex-direction: column;
  width: 170px;
  height: 201px;
  // background-color: $header-bg-color;
  background-color: $header-bg-color;
  color: $header-font-color;
}

.section-box {
  position: relative;
  width: 170px;
  height: 40px;
  display: flex;
  flex-direction: row;
  align-items: center;
  cursor: pointer;
  /*background-color: red;*/

  .line {
    position: absolute;
    top: 0;
    height: 1px;
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
  }

  .left-box {
    //width: 3px;
    //height: 40px;
    // background-color: #128bed;

  }

  .section-icon {
    font-size: 14px;
    margin-left: 17px;
  }

  .section-title {
    margin-left: 15px;
    font-size: 14px;
  }
  .iconfont {
    color: white;
  }
}

.isactive {

  //background: rgba(255, 255, 255, 0.1);
  background: $sidebar-item-hover-color;
  color: white;
  .iconfont {
    color: white;
  }
  .left-box {
    // background: $sidebar-item-hover-color;
    background-color: $base-main-color;
  }

}

.aq-line {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  // opacity: 0.1;
}
