import localStorage from '../../utils/localstorage'
import userService from '../../services/user'
import { getAllAvailableMenus } from '../../data/menu/menuHelper'
import { mapState } from 'vuex'

export default {
  name: 'dropMenu',
  props: {
    data: Array
  },
  data() {
    return {
      dropList: [
        {
          icon: 'iconfont section-icon icon-gerenzhongxin',
          title: '个人中心'
        },
        // {
        //   func: 'sub/my_comp',
        //   icon: 'iconfont section-icon icon-shezhizhongxin',
        //   title: '设置中心'
        // },
        {
          icon: 'iconfont section-icon icon-wodexiazai',
          title: '我的下载'
        },
        // {
        //   icon: 'iconfont section-icon icon-bangzhuzhongxin1',
        //   title: '帮助说明'
        // },
        {
          icon: 'iconfont section-icon icon-anquantuichu',
          title: '安全退出'
        }
      ],
      currentIndex: -1
    }
  },
  computed: {
    ...mapState('appState', {
      settingMenus: state => state.settingMenus
    })
  },
  mounted() {
    if ($util.getHasPermission('main:setting_ctr')) {
      this.dropList.splice(0, 0, {
        func: 'sub/my_comp',
        icon: 'iconfont section-icon icon-shezhizhongxin1',
        title: '设置中心'
      })
    }
    if (__QCC__ && !$util.getHasNoPermission('rvrs:set:hide_prod_set') && !__PLUGIN__ && !$util.isBankIP()) {
      this.dropList.splice(this.dropList.length - 1, 0, {
        icon: 'iconfont section-icon icon-wodedingdan',
          title: '我的订单'
      })
    }
  },
  methods: {
    showItem(index) {
      this.currentIndex = index
    },

    menuClick(title, func) {
      let path = '/home'
      let target = getAllAvailableMenus().find(x => { return func && x.func === func })
      if (target) {
        path = target.path
      }
      switch (title) {
        case '个人中心':
          this.personal()
          break
        case '设置中心':
          if (this.settingMenus && this.settingMenus.length) {
            if (this.settingMenus[0].subs && this.settingMenus[0].subs.length) {
              this.$router.push({ path: this.settingMenus[0].subs[0].path })
            }
            return false
          }
          this.$router.push({ path: path })
          break
        case '我的下载':
          this.$router.push({ name: 'my-download' })
          break
        case '我的订单':
          this.$router.push({ name: 'order-manage-mine' })
          break
        case '安全退出':
          $util.clearCacheUserPerMissionList()
          this.logout()
          break
        default:
          break
      }
    },
    personal() {
      this.$router.push({ name: 'personal-info' })
    },
    logout() {
      userService.logout({})
        .then(() => {
          localStorage.removeUser()
          sessionStorage.clear()
          window.location.href = $util.getLoginTargetUrl()
        })
        .catch(() => {
          localStorage.removeUser()
          sessionStorage.clear()
          window.location.href = $util.getLoginTargetUrl()
        })
    }
  },

  components: {}
}
