<!-- 子页面有背景图片标题s组件 -->
<template>
  <div class="border-radius-4" :class="[!isNormal && !empty ? 'app-body-head-wrap' : '',isHalfBg ? 'is-half-bg' : '',isSmall?'small-head-wrap':'']">
    <template v-if="!empty">
      <img v-if="titleImgSrc && !isNormal" :src="titleImgSrc" alt="">
      <div class="tabs-check" v-if="menus && menus.length && !isNormal">
        <div v-for="(item,index) in menus" :key="index" :class="handleClassName(item)" @click="jump(item, index)">
          {{ item.name }}<i></i>
        </div>
      </div>
    </template>
    <slot></slot>
  </div>
</template>

<script>
  export default {
    name: 'appBodyHead',
    props: {
      titleImgSrc: { type: String, default: '' },
      mode: {
        type: String,
        default: 'route'
      },
      value: {
        type: String,
        default: ''
      },
      menus: {
        type: Array,
        default: () => []
      },
      isNormal: {
        type: Boolean,
        default: false
      },
      empty: {
        type: Boolean,
        default: false
      },
      isHalfBg: {
        type: Boolean,
        default: false
      },
      isSmall: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        active: this.$route.name
      }
    },
    methods: {
      handleClassName (item) {
        if (this.mode === 'route') {
          return this.active === item.routeName ? 'active' : ''
        } else if (this.mode === 'switch') {
          return this.value === item.value ? 'active' : ''
        }
      },
      jump(item, index) {
        if (this.mode === 'switch') {
          this.$emit('change', item.value, index)
          return
        }
        const { path } = this.$router.resolve({ name: item.routeName }).route
        this.$router.push({ path: path + $util.getUrlEndIndex() })
      }
    }
  }

</script>


<style lang="scss" scoped>
  .app-body-head-wrap {
    width: 100%;
    padding: 50px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    box-sizing: border-box;

    background-image: url("/img/entry-main/entry-main-bg-left.svg"), url("/img/entry-main/entry-main-bg-right.svg"),url("/img/entry-main/entry-main-bg-shadow.svg");
    background-repeat: no-repeat, no-repeat, no-repeat;
    background-position: left top, right top, left top;

    &.is-half-bg {
      background-size: auto auto,auto auto,100% 55%;
    }


    >img {
      height: 54px;
      margin-bottom: 35px;
    }

    &.small-head-wrap
    {
      padding: 23px 0;

      >img {
      margin-bottom: 0;
    }
    }

  }

  .tabs-check {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 0 15px;

    >div {
      padding: 5px 12px;
      border-radius: 2px;
      background: rgba(255, 255, 255, 0.2);
      height: 32px;
      font-size: 14px;
      line-height: 22px;
      margin: 0 15px;
      position: relative;
      cursor: pointer;
      color: #FFFFFF;
    }

    >div:hover,
    .active {
      background: #FFFFFF;
      color: #128BED;

      i {
        width: 0;
        height: 0;
        border-top: 7px solid #FFFFFF;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        position: absolute;
        bottom: -7px;
        left: 50%;
        margin-left: -6px;
      }
    }
  }

</style>
