<template>
  <div class='_charts-analysis' :class="[{hideTitle},{miniNoData},{fullWH}]" v-w-resize="selfResize">
    <template v-if="!hideTitle">
      <span class='_title'>{{title}}</span>
      <slot name='header'></slot>
      <app-icon-operations v-if="!noData" class='_hrbtn' icon='iconfont icon-daochu icon-blue16' title='保存' @clickbtn='doSaveImg'></app-icon-operations>
      <app-icon-operations v-if="exportFunc && !noData" class="_hrbtn left1" icon="iconfont icon-xiazai icon-blue16"
      title="导出数据" @clickbtn="exportData" v-permission="[{name: 'rvrs:hidden_export', isInverse: true}]"></app-icon-operations>
    </template>
    <div :id='_chartid' ref='chartid' class='_chartscontainer'></div>
    <app-nodata v-if="noData" class="nodata" style="position: absolute" :text="emptyText" :imgWidth="miniNoData ? 60 : 100"></app-nodata>
  </div>
</template>

<script>
import echarts from 'echarts/lib/echarts'
import { jietuChart } from '../../routes/charts/components/echarts_common'
import _ from 'lodash'

export default {
  name: 'app-charts',
  props: {
    chartid: { default: '', type: String },
    title: { default: '请输入图标名称', type: String },
    emptyText: { default: '暂无数据', type: String },
    hideTitle: { default: false, type: Boolean },
    exportFunc: { default: undefined, type: Function },
    chartOptions: { default: () => {}, type: Object },
    miniNoData: { default: false, type: Boolean },
    fullWH: { default: false, type: Boolean }
  },
  data() {
    // 不需要监测，在此定义
    this.myCharts = ''
    this.options = {}
    return {
      noData: false
    }
  },
  computed: {
    _chartid() {
      return this.chartid || ('echarts' + Math.random() * 100000)
    }
  },
  watch: {
    chartOptions(val) {
      this.setChartOptions(val)
    }
  },
  mounted() {
    setTimeout(() => {
      if (!_.isEmpty(this.chartOptions) || this.chartOptions === undefined) {
        this.setChartOptions(this.chartOptions)
      }
    }, 0)
  },
  methods: {
    exportData() {
      if (this.exportFunc) {
        this.exportFunc({
          title: this.title,
          options: this.options
        })
      }
    },
    setChartOptions(option, clickCall) {
      if (!option) {
        if (this.myCharts) {
          this.myCharts.clear()
        }

        this.noData = true
        return
      }
      this.noData = false
      if (option.backgroundColor) {
        option.backgroundColor = '#fff'
      }
      option.textStyle = $util.commonChartsTextStyle(true)
      this.options = option
      this.myCharts = echarts.init(document.getElementById(this._chartid))
      if (this.myCharts) {
        this.myCharts.clear()
      }
      if (this.myCharts) {
        this.myCharts.clear()
        this.myCharts.setOption(option)
        this.myCharts.off('click')
        this.myCharts.on('click', (arg1, arg2, arg3) => {
          if (clickCall) {
            clickCall({ option, arg1, arg2, arg3 })
          }
          this.$emit('chartClick', { option, arg1, arg2, arg3 })
        })
      }
    },
    doSaveImg() {
      // document.body.style.overflow = 'hidden'
      jietuChart(this.myCharts, {
        type: 'png',
        pixelRatio: 2, // 放大两倍下载，之后压缩到同等大小展示。解决生成图片在移动端模糊问题
        backgroundColor: '#fff'
      }, this.title, () => {
        // document.body.style.overflow = 'unset'
      })
    },
    beforeDestroy() {
      if (this.myCharts) {
        this.myCharts.clear() // 如果渲染了  先销毁
        this.myCharts = undefined
      }
    },
    resize() {
      // if (this.myCharts) {
      //   this.myCharts.resize()
      // }
    },
    selfResize() {
      if (this.myCharts) {
        this.myCharts.resize()
      }
    }
  }
}

</script>

<style scoped lang="scss">
._charts-analysis {
  width: 100%;
  position: relative;
  height: 100%;
  &.hideTitle {
    ._chartscontainer {
      height: calc(100% - 20px);
    }
    &.fullWH {
      ._chartscontainer {
        height: 100%;
      }
    }
  }

  ._title {
    position: absolute;
    left: 15px;
    top: 15px;
    color: #333;
    font-size: 14px;
  }

  ._hrbtn {
    position: absolute;
    right: 15px;
    top: 15px;
    line-height: 22px;
    height: 22px;
    display: flex;
    padding: 0 5px;
    ::v-deep {
      .iconfont {
        margin-right: 5px;
      }
    }
    &.left1{
      right: 80px;
    }
    &:hover {
      background: #F2F8FE;
    }
  }

  ._chartscontainer {
    width: calc(100% - 20px);
    height: calc(100% - 60px);
    position: absolute;
    left: 10px;
    bottom: 10px;
  }

  .nodata {
    position: absolute;
    left: calc(50% - 40px);
    top: calc(50% - 89px);
  }

  &.miniNoData {
    .nodata {
      top: calc(50% - 65px);
    }
  }
  &.fullWH {
    ._chartscontainer {
      width: 100%;
      height: calc(100% - 45px);
      left: 0;
      bottom: 0;
    }
  }
}

</style>
