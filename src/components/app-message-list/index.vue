<template>
  <div class="all-message-box" id="allMessageReaded">
    <div class="all-message-header">
      <div class="title">消息中心 <span class="title-count">（ <span class="title-count-num">{{totalCount}}</span> 条未读）</span></div>
      <div v-show="list && list.length" class="read-container">
        <span class="read" @mouseenter="readShow=true" @mouseleave="readShow=false" @click="readAllClick"> <span class="iconfont icon-querenruwei"></span></span>
        <div v-show="readShow" class="read-poper el-popper default-popover-tooltip">全部已读</div>
      </div>
    </div>
    <div class="all-message-table" :style="style">
      <message-section :item="item" v-for="item in list" :key="item.id"></message-section>
      <div class="no-data-box" v-if="!list || !list.length">
        <app-nodata :show="options.emptyShow && !options.showLoading" :imgtype="options.imgtype||1" :text="options.emptyText" :options="options.emptyOption" :imgWidth="options.emptyImgWidth"></app-nodata>
      </div>
    </div>
    <div class="all-message-footer">
      <div class="lookAll" @click="lookAllClick">查看全部<span class="iconfont icon-tiaozhuan"></span></div>
    </div>
  </div>
</template>

<script src="./component.js"></script>
<style scoped lang="scss" src="./style.scss"></style>
