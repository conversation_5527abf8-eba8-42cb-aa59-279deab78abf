<template>
  <div class="monitor-box">
    <div class="section b-section">
      <div class="name-box">
        <!-- <i class="iconfont" :class="icon"></i> -->
        <img :src="icon" alt="">
        <div class="title">{{title}}</div>
      </div>
      <div class="time">{{item.createDate}}</div>
    </div>
    <div class="section message-special-section height17" v-if="!!content">
      <div class="detail" v-html="content"></div>
    </div>
    <div class="section">
      <div class="down" v-if="item.previewUrl" @click="preview">
        <em class="operation-icon iconfont icon-liulanliang1"/>预览
      </div>

      <div class="down" v-if="isShowDownloadByHyperLink" @click="plDownload">
        <em class="operation-icon iconfont icon-daochu1"/>下载
      </div>
      <div class="down" v-if="isShowDownloadPDF" @click="downloadPDFClick">
        <em class="operation-icon iconfont icon-daochu1"/>下载PDF
      </div>
      <div class="down down-word" v-if="isShowDownloadWord" @click="downloadDocClick">
        <em class="operation-icon iconfont icon-daochu1"/>下载Word
      </div>

      <div class="down" v-if="isShowDownload" @click="downloadPDFClick">
        <em class="operation-icon iconfont icon-daochu1"/>下载
      </div>
      <div class="down" v-if="isShowDownloadByMessageId" @click="downloadFileByMessageIdClick">
        <em class="operation-icon iconfont icon-daochu1"/>下载
      </div>
      <a class="down" v-if="isShowDirectDownload" :href="formatTargetUrl(item.hyperLink)"  @click="read">
        <em class="operation-icon iconfont icon-daochu1"/>下载
      </a>
      <a class="down" @click="lookClick" v-if="(isShowDDDownload ||isShowKKDownload) && !isHideCheckDetail">点击查看<i class="iconfont icon-tiaozhuan"></i></a>
      <div class="down" v-if="isShowDownloadByMessageRefIds && !isShowDDDownload&& !isShowKKDownload"
           @click="downloadByMessageRefIdsClick">
        <em class="operation-icon iconfont icon-daochu1"/>{{item.messageMode === 'IVC' ? '下载电子发票' : '下载'}}
      </div>
      <div class="look" v-if="isShowLook">
        <a class="margin-r-0-6x" v-if="showCommonDownload" @click="downloadErrorImport">下载匹配结果</a>
        <a @click="lookClick" v-if="!item.notShowCheckDetailFlag && !isHideCheckDetail">点击查看<i class="iconfont icon-tiaozhuan"></i></a>
        <a style="margin-left: 10px" v-if="isShowErrorImportDownload" @click="downloadErrorImport">下载失败清单</a>
      </div>
      <div class="task-status" v-if="isShowTaskStatus">状态：
        <div class="tasking status-container" v-if='item.taskStatus == "P"'><span class="status"></span> <span>正在执行</span></div>
        <div class="fail status-container" v-if='item.taskStatus == "F"'><span class="status"></span> <span>失败</span></div>
        <div class="re-try status-container" v-if='item.taskStatus == "F" && item.supportRetriggerFlag' @click.stop="retryTaskClick"><span class="status"></span> <span>重试</span></div>
      </div>
    </div>
  </div>
</template>

<script src="./component.js"></script>
<style scoped lang="scss" src="./style.scss"></style>


<style lang="scss">
  .message-special-section {
    .detail {
      word-break: break-all;

      .corp-flag {
        display: inline-block;
        padding: 0 4px;
        font-weight: bold;
        //color: #128bed;
      }
    }

  }

</style>
