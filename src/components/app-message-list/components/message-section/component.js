import mixin from '../../../../routes/message/mixin'

export default {
  name: 'message-item',
  mixins: [mixin],

  data() {
    return {
      title: '',
      content: ''
    }
  },
  watch: {
    item() {
      this.init()
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    // clickCheck4All() {
    //   if ((this.isShowDDDownload || this.isShowKKDownload) && !this.isHideCheckDetail) {
    //     this.lookClick()
    //   } else if (this.isShowLook && (!this.item.notShowCheckDetailFlag && !this.isHideCheckDetail)) {
    //     this.lookClick()
    //   }
    // },
    initData() {
      this.title = ''
      this.content = this.item.messageContent


      var messageCategory = this.item.messageCategory

      switch (messageCategory) {
        // 企业详情 -- 企业监控详情
        case '1':
          this.title = '监控消息（企业）'
          break
          // 监控详情 -- 人员监控详情
        case '2':
          this.title = '监控消息（自然人）'
          break
          // 下载
        case '4':
          this.title = '下载通知'
          this.content = this.item.titleName
          if (this.item.messageMode === 'IVC') { // 发票的
            this.content = this.item.messageContent
            this.title = '电子发票开票成功通知'
          }
          // 公司名跳转企业详情
          this.gotoCompany()
          break
          // 新的改版提醒的页面
        case '5':
          this.title = '改版通知'
          break
          // 自然人核验页面
        case '6':
          this.title = '系统消息提醒'
          break
          // 企业列表
        case '7':
          this.title = this.item.titleName || '企业列表'
          break
          // 企业列表
        case '8':
          this.title = '关联企业添加成功'
          break
        case '9':
        case '13':
          this.title = '数据导出'
          break
        case '10':
        case '11':
        case '12':
          this.title = this.item.titleName
          break

        case 'credit_rpt':
          this.title = '报告生成失败通知'
          // 公司名跳转企业详情
          this.gotoCompany()
          break
        case '20':
          this.title = '下载通知'
          this.content = this.item.titleName
          break
        default:
          this.title = this.item.titleName
          break
      }
    },




    gotoCompany() {
      if (this.item.messageContent.indexOf('{param0}') > -1 &&
        this.item.paramMap &&
        this.item.paramMap.param0.name &&
        this.item.paramMap.param0.keyNo &&
        this.item.paramMap.param0.type === 'c') {
        this.content = this.item.messageContent.replace('{param0}', `<a href="/companyDetail?keyNo=${this.item.paramMap['param0'].keyNo}">${this.item.paramMap['param0'].name}</a>`)
      }
    }




  },
  components: {

  }
}
