@import '../../../../styles/common.scss';
@import '../../../../styles/baseFlexAdapter.scss';

.monitor-box {
  @include flex-def;
  flex-direction: column;
  width: 100%;
  background-color: #fff;
  border-bottom: 1px solid #f5f5f5;
  padding: 10px 0px;
  line-height: 22px;

  &:hover {
    background: #F5F9FF;
    //cursor: pointer;
    //.section {
    //  .name-box {
    //    .title {
    //      color: #128bed
    //    }
    //  }
    //}
  }
  .iconfont.icon-tiaozhuan {
    font-size: 14px;
  }
}

.section {
  @include flex-def;
  padding: 0px 15px;
  padding-left: 43px;

  .name-box {
    @include flex-def;
    @include flex-cCenter;

    .iconfont {
      font-size: 16px;
    }


    .title {
      margin-left: 8px;
      font-size: 14px;
      color: #333;
      text-align: left;
      font-weight: 400;
    }
  }

  .time {
    font-size: 14px;
    color: #999999;
  }

  .detail {
    margin-top: 8px;
    font-size: 14px;
    color: #666666;
    line-height: 22px;

    ::v-deep {
      .color-danger:hover {
        color: #F04040 !important;
      }
    }

  }

  .down {
    margin-top: 5px;
    font-size: 14px;
    color: #128bed;
    cursor: pointer;
    margin-right: 24px;

    .operation-icon {
      padding-right: 4px;
      color: #128BED;
      font-size: 14px;
      &.icon-liulanliang1 {
        font-size: 13px;
      }
    }

    &:hover {
      color: $color-primary-hover;
      .operation-icon {
        color: $color-primary-hover;
      }
    }
  }

  .down-word {
    margin-left: 10px;
  }

  .look {
    margin-top: 5px;
    // margin-left: 50px;
    font-size: 14px;
    color: #128bed;
    cursor: pointer;

    &:hover {
      color: $color-primary-hover;

      a {
        color: $color-primary-hover;
      }
    }
  }

  .task-status {
    margin-top: 8px;
    font-size: 14px;
    color: #999999;
    cursor: pointer;
    @include flex-def;

    .status {
      border-radius: 50%;
      width: 6px;
      height: 6px;
      display: inline-block;
      margin-right: 10px;
    }

    .status-container {
      @include flex-def;
      align-items: center;
    }

    .tasking {
      .status {
        background: #ff8900;
      }
    }

    .fail {
      .status {
        background: #d13e3b;
      }
    }

    .re-try {
      .status {
        color: #128bed;
      }

      margin-left: 5px;
    }

  }
}


.b-section {
  @include flex-zBetween;
  padding-left: 15px;
}
