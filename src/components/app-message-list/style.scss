@import '../../styles/common.scss';
@import '../../styles/baseFlexAdapter.scss';

.all-message-box {
  @include flex-def;
  flex-direction: column;
  width: 500px;
  box-shadow: rgba(0, 0, 0, 0.3) 0px 5px 12px;
  cursor: default;
  border-radius: 4px;
  opacity: 1;
  background: #FFFFFF;
  box-sizing: border-box;
  border: 1px solid #EEEEEE;


}


.all-message-header {
  @include flex-def;
  @include flex-zBetween;
  @include flex-cCenter;
  width: 100%;
  height: 50px;
  border-bottom: 1px solid #f5f5f5;
  padding: 0px 15px;

  .title {
    font-size: 15px;
    font-weight: bold;
    color: #333;
    .title-count {
      font-weight: normal;
      color: #999999;

      .title-count-num {
        color: #F04040;
      }
    }
  }

  .read-container {
    position: relative;
    color: #666666;
    .read {
      cursor: pointer;
      width: 22px;
      height: 22px;
      display: inline-flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;

      .iconfont {
        font-size: 12px;
      }

      &:hover {
        background: #F2F8FE;

        .iconfont {
          color: #128bed;
        }
      }
    }

    .read-poper {
      position: absolute;
      top: 32px;
      right: -25px;
      z-index: 1;
      width: 80px;
      border-radius: 4px;
    }
  }




}


.all-message-table {
  width: 100%;
  max-height: 397px;
  overflow-y: auto;

  .no-data-box {
    padding-top: 20px;
    padding-bottom: 20px;
  }

}

.all-message-footer {
  @include flex-def;
  @include flex-cCenter;
  @include flex-zCenter;
  width: 100%;
  height: 53px;
  border-top: 1px solid #f5f5f5;

  .lookAll {
    margin-right: 10px;
    font-size: 14px;
    color: #128bed;
    cursor: pointer;

    .iconfont {
      font-size: 14px;
      color: #128bed;
    }

    &:hover {
      color: $color-primary-hover;

      .iconfont {
        color: $color-primary-hover;
      }
    }
  }


}
