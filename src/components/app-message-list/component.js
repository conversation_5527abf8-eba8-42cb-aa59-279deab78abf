import messageSection from './components/message-section'
import messageService from '../../services/message'
import { mapState, mapActions } from 'vuex'
import { notifyModule } from '../../data/menu/cdd/config'
import store from '../../store'


export default {
  data() {
    return {
      // list: []
      options: {
        emptyText: '暂无数据',
        border: true,
        showLoading: false,
        loadingText: '加载中 . . .',
        emptyShow: true,
        emptyImgWidth: 100
      },
      style: '',
      readShow: false
    }
  },
  mounted() {
    let h = window.innerHeight - 150
    if (h && h < 460) {
      this.style = 'max-height:' + h + 'px'
    } else {
      this.style = ''
    }
  },
  computed: {
    ...mapState('messageList', ['list']),
    ...mapState('messageList', ['totalCount'])
  },
  methods: {
    ...mapActions('messageList', ['setList']),
    ...mapActions('appState', ['openMenus']),
    mEnter() {

    },
    mLeave() {
      this.$refs.mtooltip.popoverValue = false
    },
    readAllClick() {
      messageService.readAll({}).then(res => {
        if (res.result > 0) {
          this.loadUnreadMsg()
        } else {
          this.$message.warning('暂时还没有未读提醒')
        }
      })
    },

    isShow(item) {
      let show = true
      if (item.titleName === '服务到期通知' || item.titleName === '套餐续费通知') {
        if (!__QCC__) {
          show = false
        } else {
          if (__PLUGIN__) {
            show = false
          }
        }
      }
      return show
    },
    loadUnreadMsg() {
      messageService.loadUnreadMsg({}).then(res => {
        store.commit('messageList/setTotalCount', res.totalCount)
        const list = res.resultList.filter(v => v.iconName !== 'task' || (v.iconName === 'task' && v.taskStatus && v.taskStatus !== 'S'))
        this.setList(list)
      })
    },

    lookAllClick() {
      // console.log(row)
      if (notifyModule.path === this.$route.path) {
        return
      }
      $util.zhugeTrackOneLevel('查看全部提醒')
      this.$router.push('/message/all-message')
    }


  },
  components: {
    messageSection

  }
}
