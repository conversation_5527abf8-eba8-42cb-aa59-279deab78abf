<template>
  <div class="app-file-logo">
    {{ label }}<span class="hover-area" @click="showFile">
      <app-source-logo
        :file-type="fileType"
        :url="url"
        noClick
        :hover-interactive="hoverInteractive"
      />
      <span class="name">{{ name }}</span>
    </span>
  </div>
</template>

<script>
export default {
  name: 'AppFileLogo',

  props: {
    fileType: {
      type: String,
      default: ''
    },

    url: {
      type: String,
      default: ''
    },

    label: {
      type: String,
      default: '原文：'
    },

    name: {
      type: String,
      default: '查看'
    },

    /** hover 交互 */
    hoverInteractive: {
      type: Boolean,
      default: true
    }
  },

  methods: {
    showFile () {
      window.open(this.url)
    }
  }
}
</script>

<style lang="scss" scoped>
  .app-file-logo {
    color: #666;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;

    .app-source-logo {
      display: inline-block;
      font-size: 0;
      vertical-align: top;
      height: 18px;
    }

    >.hover-area {
      display: inline-block;
      color: #128bed;
      cursor: pointer;
    }

    &:hover {
      >.hover-area {
        color: #0d61a6;
      }
    }
  }
</style>

