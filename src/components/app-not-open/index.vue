<template>
  <div class="app-not-open">
    <div class="app-not-open__content">
      <img :src="emptyLogo" width="100" />
      <div class="app-not-open__message">
        <template v-if="isMainUserAccount">
          <span>暂未开启{{ functionName }}，一键开启</span>
          <app-switch :value="value" active-text="开" inactive-text="关" text-position="inside" :width="36" @change="handleSwitch"></app-switch>
          <span>或进入</span>
          <app-link @click="redirect.func">{{ redirect.pageName }}</app-link>
          <span>开启{{ functionName }}</span>
        </template>
        <template v-else>
          <span>暂未开启{{ functionName }}，请联系系统管理员开启{{ functionName }}</span>
        </template>
      </div>
      <div class="app-not-open__tip" v-if="tip">{{ tip }}</div>
    </div>
    <div class="app-not-open__example" v-if="example.show">
      <img v-if="example.img" :src="example.img" :style="getExampleStyle()" />
    </div>
  </div>
</template>

<script>
import config from '../../config/client'

export default {
  name: 'app-not-open',
  props: {
    modelValue: {
      type: Boolean
    },
    functionName: {
      type: String,
      default: ''
    },
    example: {
      type: Object,
      default: () => ({
        show: true,
        img: null,
        width: '976px',
        height: '229px'
      })
    },
    tip: {
      type: String,
      default: null
    },
    redirect: {
      type: Object,
      default: () => ({
        pageName: '尽职调查列表',
        func: null
      })
    }
  },
  model: {
    prop: 'modelValue',
    event: 'modelChange'
  },
  data () {
    return {
      isMainUserAccount: $util.getIsMainuserAccount(),
      emptyLogo: config.customize.emptyLogo,
      value: false
    }
  },
  watch: {
    modelValue (val) {
      this.value = val
    }
  },
  methods: {
    handleSwitch () {
      this.$emit('switch')
    },
    getExampleStyle () {
      const style = {}
      if (this.example.width) {
        style.width = this.example.width
      }
      if (this.example.height) {
        style.height = this.example.height
      }
      return style
    }
  }
}
</script>

<style lang="scss" scoped>
.app-not-open {
  height: 100%;
  background: #fff;
  text-align: center;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img {
      margin-bottom: 12px;
    }
  }
  &__message {
    line-height: 22px;
    justify-content: center;
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    .app-switch {
      margin: 0 5px;
    }
    .app-link {
      margin: 0 5px;
    }
  }
  &__tip {
    color: #999999;
    line-height: 22px;
  }
  &__example {
    text-align: center;
    padding: 20px;
    background: #FAFAFA;
    border-radius: 4px;
    margin: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
    }
  }
}
</style>
