<template>
  <div class="footerFloatWrapper" v-show="footerFlag">
    <span class="el-icon-close closeicon" @click="hideFooter()"></span>
  </div>
</template>

<script>
  import promote from '../../assets/images/website_footer_promote.png'
  export default {
    name: 'app-benefit',
    data() {
      return {
        footerFlag: true,
        promote
      }
    },
    methods: {
      hideFooter: function() {
        this.footerFlag = false
      }
    }
  }

</script>

<style lang="scss" scoped>
  .footerFloatWrapper {
    width: 100%;
    height: 136px;
    position: fixed;
    pointer-events: none;
    bottom: 0;
    background: url('../../assets/images/website_footer_promote.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .closeicon {
    float: right;
    margin: 15px;
    margin-top: 78px;
    font-size: 16px;
    cursor: pointer;
    color: white;
    pointer-events: all;
  }

</style>
