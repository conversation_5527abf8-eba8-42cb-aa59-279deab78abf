const typeFilterRange = {
  typeArea: 2,
  typeIndustry: 3,
  typeYear: 4,
  typeState: 5,
  typeCapital: 6,
  typeGroup: 7,
  typeImportBy: 8,
  typeImportDate: 81,
  typeSource: 9,
  typeSubject: 10,
  typeResult: 11,
  typeRecordFlag: 12 // 备案类型
}
const typeFilterRangeArrConfig = [
  { mode: 'A', type: '2' },
  { mode: 'I', type: '3' },
  { mode: 'Year', type: '4' },
  { mode: 'Status', type: '5' },
  { mode: 'RegCapi', type: '6' },
  { mode: 'U', type: '7' },
  { mode: 'Source', type: '9' }
  ]

const typeFilterForStaff = {
  typePhoneCheck: 6,
  typeGroup: 7,
  typeImportBy: 8
}

const reportFilter = {
  reportType: 1,
  reportStatus: 2,
  group: 3,
  area: 4
}

const formatSelectAreaList = (model) => {
  let res = ''
  if (model.provinceList.length === 0) {
    res = '所属区域'
  } else {
    res = '所属区域/省:' + model.provinceList.toString()
    if (model.cityList.length > 0) {
      res += ';市:' + model.cityList.toString()
    }
    if (model.districtList.length > 0) {
      res += ';县/区:' + model.districtList.toString()
    }
  }
  return res
}
export default {
  typeFilterRange,
  typeFilterForStaff,
  formatSelectAreaList,
  reportFilter,
  typeFilterRangeArrConfig
}
