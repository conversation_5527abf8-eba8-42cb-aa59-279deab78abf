/* istanbul ignore next */
import Vue from 'vue'
var onForClickOutSide = (function() {
  if (!Vue.prototype.$isServer && document.addEventListener) {
    return function(element, event, handler) {
      if (element && event && handler) {
        element.addEventListener(event, handler, false)
      }
    }
  } else {
    return function(element, event, handler) {
      if (element && event && handler) {
        element.attachEvent('on' + event, handler)
      }
    }
  }
})()
var nodeList = []
var clickOutSideCtx = '@@clickoutsideContext'

var startClick
var seed = 0

!Vue.prototype.$isServer && onForClickOutSide(document, 'mousedown', function (e) {
  startClick = e
})

!Vue.prototype.$isServer && onForClickOutSide(document, 'mouseup', function (e) {
  nodeList.forEach(function (node) {
    node[clickOutSideCtx].documentHandler(e, startClick)
  })
})

function createDocumentHandler(el, binding, vnode) {
  return function(mouseup, mousedown) {
    mouseup = mouseup || {}
    mousedown = mousedown || {}
    if (!vnode ||
            !vnode.context ||
            !mouseup.target ||
            !mousedown.target ||
            el.contains(mouseup.target) ||
            el.contains(mousedown.target) ||
            el === mouseup.target ||
            (vnode.context.popperElm &&
                (vnode.context.popperElm.contains(mouseup.target) ||
                    vnode.context.popperElm.contains(mousedown.target)))) return

    if (binding.expression &&
            el[clickOutSideCtx].methodName &&
            vnode.context[el[clickOutSideCtx].methodName]) {
      vnode.context[el[clickOutSideCtx].methodName]()
    } else {
      el[clickOutSideCtx].bindingFn && el[clickOutSideCtx].bindingFn()
    }
  }
}


var myclickOutside = {
  bind: function(el, binding, vnode) {
    nodeList.push(el)
    var id = seed++
    el[clickOutSideCtx] = {
      id: id,
      documentHandler: createDocumentHandler(el, binding, vnode),
      methodName: binding.expression,
      bindingFn: binding.value
    }
  },

  update: function(el, binding, vnode) {
    el[clickOutSideCtx].documentHandler = createDocumentHandler(el, binding, vnode)
    el[clickOutSideCtx].methodName = binding.expression
    el[clickOutSideCtx].bindingFn = binding.value
  },

  unbind: function(el) {
    var len = nodeList.length

    for (var i = 0; i < len; i++) {
      if (nodeList[i][clickOutSideCtx].id === el[clickOutSideCtx].id) {
        nodeList.splice(i, 1)
        break
      }
    }
    delete el[clickOutSideCtx]
  }
}

export default myclickOutside
