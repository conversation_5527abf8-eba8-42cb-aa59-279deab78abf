<template>
  <el-input class="default-input input-wrap"
            :class="isFocus ? 'focus-wrap' : ''"
            ref='input'
            @change='changeInput'
            @keyup.enter.native="handleSubmit"
            :size="$attrs.size||'small'"
            :placeholder="$attrs.placeholder||'请输入'"
            :prefix-icon='getIcon(prefixIcon)'
            :suffix-icon='getIcon(suffixIcon)'
            @blur='blurInput'
            @focus='focusInput'
            v-bind="$attrs"
            :clearable="false"
  >
    <slot slot='prepend' name='prepend'></slot>
    <slot slot='append' name='append'></slot>
    <slot slot='prefix' name='prefix'></slot>
    <slot slot='suffix' name='suffix'></slot>
    <i v-if="$attrs.clearable && modelBind" slot="suffix" class="input-clear-icon iconfont icon-shanchu3" @click.stop="handleClearKeyword"></i>
  </el-input>
</template>

<script>
import { isIE } from '../../utils/browser-detector'

export default {
  name: 'app-input-new',
  props: {
    prefixIcon: { type: Object }, // 输入框前面的icon {icon: 'el-icon-search', click: function(){}}
    suffixIcon: { type: Object } // 输入框后面的icon {icon: 'el-icon-search', click: function(){}}
  },
  methods: {
    handleClearKeyword() {
      this.modelBind = ''
      this.$refs.input.focus()
      this.$emit('clearValue', '')
    },
    getIcon(data) {
      return data && data.icon ? data.icon : ''
    },
    handleSubmit(val) {
      this.$emit('keyupEnter', this.modelBind)
    },
    changeInput(val) {
      if (this.ischange) {
        this.$emit('change', val)
      } else {
        this._changeModel(val)
      }
    },
    blurInput(e) {
      this.isFocus = false
      this.$emit('blur', e)
    },
    focusInput(e) {
      this.isFocus = true
      this.$emit('focus', e)
    },
    onKeyDownListener(e) {
      if (e.keyCode === 13) {
        if (e.target.value !== this._vModelProp) {
          this._changeModel(e.target.value)
        }
        this.$emit('keyEnter', e)
      }
    },
    onKeyUpListener(e) {
      // if (this.type == 'number')newsMorePop-contentwrapper {//验证格式
      //   this.modelBind = this.modelBind.replace(/[^\d\.]/g, '')
      // }
      this._changeModel(this.modelBind)
    },
    initFocus() {
      if (this.$refs.input) {
        this.$refs.input.focus()
      }
    }
  },
  data() {
    return {
      modelBind: '',
      isFocus: false
    }
  },
  mounted() {
    let elInput = this.$refs.input
    if (this.suffixIcon && this.suffixIcon.click && elInput.$vnode && elInput.$vnode.elm) { // 设置后面的icon的点击事件
      let tmp = elInput.$vnode.elm.getElementsByClassName('el-input__suffix')
      if (tmp && tmp.length > 0) {
        tmp[0].onclick = this.suffixIcon.click
      }
    }
    if (this.prefixIcon && this.prefixIcon.click && elInput.$vnode && elInput.$vnode.elm) { // 设置前面的icon的点击事件
      let tmp = elInput.$vnode.elm.getElementsByClassName('el-input__prefix')
      if (tmp && tmp.length > 0) {
        tmp[0].onclick = this.prefixIcon.click
      }
    }
    let nativeInput = elInput.$refs.input
    if (nativeInput) {
      nativeInput.onkeydown = this.onKeyDownListener
      nativeInput.onkeyup = this.onKeyUpListener
      if (isIE()) {
        nativeInput.value = this.modelBind
        nativeInput.oninput = (val) => {
          this.changeInput(val.target.value)
        }
      }
    }
  }
}

</script>

<style lang="scss" scoped>
::v-deep {
  .el-input__inner {
    border-radius: 2px;
  }
}
</style>
