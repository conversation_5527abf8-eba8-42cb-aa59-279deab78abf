import { debounce } from 'lodash'
import { jietuChart } from '../../routes/charts/components/echarts_common.js'
let echarts = require('echarts')
require('echarts-wordcloud')
require('echarts/map/js/china')
export default {
  name: 'app-echarts',
  props: {
    option: {
      type: Object
    },
    type: {
      type: String
    },
    height: {
      type: String,
      default: '300px'
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: 'auto'
    },
    rendererType: {
      type: String,
      default: 'canvas'
    }
  },
  data() {
    return {
      color: [
        '#62a5e7',
        '#7ecf52',
        '#eecb5f',
        '#9470e5',
        '#e3935c',
        '#e06757',
        '#605ef0',
        '#45c2ce',
        '#4dd593',
        '#ecb556',
        '#a877d1',
        '#bac859',
        '#da6464',
        '#5c8ce4',
        '#e899b6',
        '#4bd485',
        '#e6a75e',
        '#d6d436',
        '#58b7e8',
        '#f18b8b'
      ]
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
      this.draw()
    })
    this.$_debounceInit = debounce(this.init, 100)
    window.addEventListener('resize', this.$_debounceInit)
  },
  watch: {
    option: {
      handler(newVal, oldVal) {
        if (this.chart) {
          if (newVal) {
            if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
              this.draw()
            }
          }
        } else {
          this.initChart()
        }
      },
      immediate: false,
      deep: true
    }
  },
  beforeDestroy() {
    if (this.chart && !this.chart.isDisposed) {
      this.chart.clear()
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.$_debounceInit)
  },
  methods: {
    init() {
      try {
        this.chart && this.chart.resize()
      } catch (err) {}
    },
    initChart() {
      if (this.$refs.main) {
        this.chart = echarts.init(this.$refs.main, null, { renderer: this.rendererType })
        this.chart.on('click', (params) => {
          this.$emit('chartClick', params)
        })
      }
    },
    draw() {
      let option = this.option
      if (!option) return
      if (!this.chart) {
        return
      }
      this.chart.clear()
      this.chart.resize({ height: this.height })
      this.chart.setOption(option)
    },
    toggleCount() {
      if (this.option.series && this.option.series.length) {
        this.option.series.forEach(x => {
          x.label = x.label || {}
          x.label.show = !x.label.show
        })
      }
    },
    download(title) {
      jietuChart(this.chart, {
        type: 'png',
        pixelRatio: 2, // 放大两倍下载，之后压缩到同等大小展示。解决生成图片在移动端模糊问题
        backgroundColor: '#fff'
      }, title || this.title)
    }
  }
}
