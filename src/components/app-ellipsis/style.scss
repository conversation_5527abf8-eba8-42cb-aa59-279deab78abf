.app-ellipsis-inner {
  &.isSafari {
    display: grid;
  }

  .app-ellipsis {
    overflow: hidden;
  }

  ::v-deep .line-clamp-btn-new {
    bottom: 2px;
    display: flex;

    .toggle-close {
      color: #999999;

      &:hover {
        color: #128bed;
      }
    }

    &.line-clamp-btn-center {
      width: 100%;
      justify-content: center;
      bottom: -10px;
      z-index: 100;
      padding-top: 2px;
      padding-bottom: 0;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, #fff 30%);

      &.linear {
        bottom: -10px;
        padding-bottom: 0;
        background: unset;
      }

      i {
        color: #999;
        font-size: 12px;
        transform: scale(0.83);
      }

      &:hover i {
        color: #128bed;
      }
    }
  }

  ::v-deep .place-holder-open {
    bottom: 2px;

    &.place-holder-open-center {
      position: relative;
      margin-top: -33px;
      bottom: -10px;
      z-index: 100;
    }
  }
}
