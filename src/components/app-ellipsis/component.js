import _ from 'lodash'
import { deviceInfo } from '@/core/device-info'
import uiService from '@/routes/companyDetail/ui-service'

export default {
  name: 'app-ellipsis',
  props: {
    ellipsis: {
      type: Number,
      default: 1
    },
    // 是否支持展开收起
    // true：展开收起；false：展开
    toggle: {
      type: Boolean,
      default: true
    },
    // 同一个父级同时展开收起
    peerlinkage: {
      type: Boolean,
      default: false
    },
    // 展开之后垂直居中方式为 vertical-align: top;
    verticalTop: {
      type: Boolean,
      default: false
    },
    // 展开按钮在中间
    centerBtn: {
      type: Boolean,
      default: false
    },
    // 折叠数据
    info: {
      type: Object,
      default: () => {}
    },
    // 是否手动改变 info.expanded 来控制展开和收起
    // 当一行数据中有一列以上数据 为可展开收起的(toggle:true) 且 均为分别单独控制的(peerlinkage:false)
    // 不建议使用字段
    isManual: {
      type: Boolean,
      default: false
    },
    // 支持全域点击打开
    globalOpen: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      isSafari: false,
      currentToggle: false
    }
  },
  mounted () {
    if (deviceInfo.result?.browser?.name === 'Safari' && deviceInfo.result?.browser?.version === '16.0') {
      this.isSafari = true
    }
  },
  watch: {
    ellipsis: {
      handler () {
        if (__BROWSER__ && this.ellipsis) {
          this.$nextTick(() => {
            this.initMoreTextHide()
          })
        }
      },
      immediate: true
    },
    'info.expanded': {
      handler () {
        if (this.isManual) {
          if (this.info?.expanded === undefined) {
            this.$set(this.info, 'expanded', false)
          }
          this.expandAction(this.info.expanded)
        }
      }
    }
  },
  methods: {
    initMoreTextHide () {
      const ellipsis = this.ellipsis
      this.$clamp = $(this.$refs.ellipsis)
      if (deviceInfo.isSafari()) {
        this.lineHeight = parseInt(this.$clamp.css('line-height'))
      } else {
        this.lineHeight = parseFloat(this.$clamp.css('line-height')).toFixed(2)
      }
      this.ellipsisHeight = Math.round((this.lineHeight * ellipsis) + 2)
      if (deviceInfo.isIE()) {
        this.ellipsisHeight += 1
      }
      this.$clamp.height('auto')
      this.$clamp.parent().find('.line-clamp-btn-new').remove()
      this.$clamp.parent().find('.place-holder').remove()
      this.$clamp.parent().find('.place-holder-open').remove()
      const clampFunc = () => {
        if (this.$clamp.height() > this.ellipsisHeight || this.$clamp[0]?.scrollHeight > this.ellipsisHeight) {
          this.$expand = this.centerBtn ? '<i class=\'aicon icon-icon_zhankai2\'/>' : '...<a>展开</a>'
          this.$close = this.centerBtn ? '<i class=\'aicon icon-icon_shouqi\'/>' : '<a class="toggle-close">收起</a>'
          this.$clampBtn = $(`<div class="line-clamp-btn-new linear ${this.centerBtn ? 'line-clamp-btn-center' : ''}">${this.$expand}</div>`)
          this.$placeholderopen = $(`<div class="place-holder-open ${this.centerBtn ? 'place-holder-open-center' : ''}"></div>`)
          this.$placeholderopen.height(this.centerBtn ? 33 : this.lineHeight)
          this.$clamp.height(this.ellipsisHeight)
          this.$clamp.after(this.$clampBtn)
          this.$clamp.after(this.$placeholderopen)
          this.$clamp.parent().css('position', 'relative')
          // 支持展开收起的才会有 expanded 字段
          if (this.isManual) {
            this.$set(this.info, 'expanded', false)
          }
          this.$clampBtn.click((e) => {
            e.stopPropagation()
            if (!this.currentToggle) {
              // 手动控制的展开
              // 展开后
              if (this.isManual) {
                if (this.$clampBtn.hasClass('linear')) {
                  this.info.expanded = true
                } else {
                  this.info.expanded = false
                }
              } else {
                this.$clamp.height('auto')
                this.$clamp.siblings('.line-clamp-btn-new').hide()
                this.$clamp.siblings('.place-holder-open').hide()
                if (this.peerlinkage) {
                  let list = this.$clamp?.parent()?.parent()?.siblings() || null
                  if (list?.length > 0) {
                    list = [...list]
                    _.forEach(list, (item) => {
                      const oappellipsis = $(item)?.find('.app-ellipsis') || null
                      if (oappellipsis) {
                        oappellipsis.height('auto')
                        oappellipsis.siblings('.line-clamp-btn-new').hide()
                        oappellipsis.siblings('.place-holder-open').hide()
                      }
                    })
                  }
                }
              }
            } else {
              if (this.$clampBtn.hasClass('linear')) {
                if (this.isManual) {
                  this.info.expanded = true
                } else {
                  this.expandAction(true)
                }
              } else {
                if (this.isManual) {
                  this.info.expanded = false
                } else {
                  this.expandAction(false)
                }
                this.$nextTick(() => {
                  const top = this.$refs.ellipsis.getBoundingClientRect()?.top || 0
                  if (top < 105) {
                    uiService.scrollTo(this.$refs.ellipsis, -140)
                  }
                })
              }
            }
          })
        } else {
          if (this.$clamp.siblings('.line-clamp-btn-new')) {
            this.$clamp.siblings('.line-clamp-btn-new').hide()
          }
          if (this.$clamp.siblings('.place-holder-open')) {
            this.$clamp.siblings('.place-holder-open').hide()
          }
        }
      }
      if (this.$clamp.height()) {
        clampFunc()
      } else {
        this.$clamp.css('max-height', this.ellipsisHeight)
        setTimeout(() => {
          this.$clamp.css('max-height', '')
          clampFunc()
        }, 200)
        setTimeout(() => {
          if (!this.$clamp.parent().find('.line-clamp-btn-new')?.length) {
            clampFunc()
          }
        }, 600)
      }
    },
    expandAction (value) {
      if (value) {
        // 展开
        this.$clamp.height('auto')
        if (this.verticalTop) {
          this.$clamp?.parent().parent().css('vertical-align', 'top')
        }
        const $placeholder = $('<div class="place-holder"></div>')
        $placeholder.height(this.centerBtn ? 10 : this.lineHeight)
        if (!this.currentToggle) { // 仅支持展开时，不提供收起按钮
          this.$clampBtn?.html('')
        } else { // 支持展开收起的情况
          this.$clampBtn?.before($placeholder)
          this.$clampBtn?.removeClass('linear')
          this.$clampBtn?.html(this.$close)
        }
        if (this.$clamp.siblings('.place-holder-open')) {
          this.$clamp.siblings('.place-holder-open').remove()
        }
        if (this.peerlinkage) {
          let list = this.$clamp?.parent()?.parent()?.siblings() || null
          if (list?.length > 0) {
            list = [...list]
            _.forEach(list, (item) => {
              const olineclampbtnnew = $(item).find('.line-clamp-btn-new')
              const oappellipsis = $(item).find('.app-ellipsis')
              const oplaceholder = $('<div class="place-holder"></div>')
              oplaceholder.height(this.lineHeight)
              if (oappellipsis) {
                oappellipsis.height('auto')
                if (oappellipsis.siblings('.place-holder-open')) {
                  oappellipsis.siblings('.place-holder-open').remove()
                }
                if (olineclampbtnnew) {
                  olineclampbtnnew.before(oplaceholder)
                  olineclampbtnnew.removeClass('linear')
                  olineclampbtnnew.html(this.$close)
                }
              }
            })
          }
        }
      } else {
        // 收起
        if (this.$clamp.height() > this.ellipsisHeight || this.$clamp[0]?.scrollHeight > this.ellipsisHeight) { // 防止监听info.expanded，导致初次进入时样式错误
          this.$clamp.height(this.ellipsisHeight)
        } else {
          this.$clamp.height('auto')
        }
        if (this.verticalTop) {
          this.$clamp.parent().parent().css('vertical-align', 'middle')
        }
        if (this.$clamp?.siblings('.place-holder')) {
          this.$clamp.siblings('.place-holder').remove()
        }
        this.$clampBtn?.addClass('linear')
        this.$clampBtn?.html(this.$expand)
        this.$clamp?.after(this.$placeholderopen)
        if (this.peerlinkage) {
          let list = this.$clamp?.parent()?.parent()?.siblings() || null
          if (list?.length > 0) {
            list = [...list]
            _.forEach(list, (item) => {
              const olineclampbtnnew = $(item).find('.line-clamp-btn-new')
              const oappellipsis = $(item).find('.app-ellipsis')
              const oplaceholderopen = $('<div class="place-holder-open"></div>')
              oplaceholderopen.height(this.lineHeight)
              if (oappellipsis) {
                oappellipsis.height(this.ellipsisHeight)
                if (oappellipsis.siblings('.place-holder')) {
                  oappellipsis.siblings('.place-holder').remove()
                }
                oappellipsis.after(oplaceholderopen)
                if (olineclampbtnnew) {
                  olineclampbtnnew.addClass('linear')
                  olineclampbtnnew.html(this.$expand)
                }
              }
            })
          }
        }
      }
    },
    expand () {
      if (this.isManual) {
        this.info.expanded = true
      }
    }
  }
}
