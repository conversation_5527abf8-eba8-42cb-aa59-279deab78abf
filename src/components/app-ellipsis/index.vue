<template>
  <div :class="['app-ellipsis-inner', { isSafari }]" @click="globalOpen && expand()">
    <div class="app-ellipsis" ref="ellipsis">
      <slot></slot>
    </div>
  </div>
</template>
<script src="./component.js"></script>
<style lang="scss" src="./style.scss" scoped></style>
<style lang="scss">
  .ntable {
    .line-clamp-btn-new{
      right: 0px;
      bottom: 0px;
      border-radius: 2px 0 0 2px;
      text-align: right;
      position: absolute;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      &.linear {
        width: 72px;
        border-radius: unset;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #FFF 38%, #FFF);
      }
    }
  }
</style>
