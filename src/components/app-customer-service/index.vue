<template>
  <div v-if="showCustomer">
    <span class="shangwu-pay" @click="openShangWu" style="z-index:100"></span>
    <app-popup :control="controlSW" v-if="controlSW.show" title="企查查-客户经理" :buttonOptions="buttonQrCodeOptions" @closed="closedShang" :options="options" :zIndex="10000">
      <div style="line-height: 14px;margin-bottom: 20px">{{titleData}}</div>
      <div class="user-info">
        <img :src="info.imgurl" @error="info.imgurl = emptyImg" width="120" />
        <div class="info-desc">
          <h2 class="name">{{info.name}}</h2>
          <div class="desc">您的客户经理</div>
          <div class="phone">电话：{{info.phone}}</div>
          <div class="phone">邮箱：{{info.email}}</div>
        </div>
      </div>
      <div slot="footer">
        <div style="text-align: center;padding: 15px 0;">
          <app-button class="_footerbtn" type="primary" size="small" @click="closedShang">我知道了</app-button>
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
  import { postVue } from '../../services/common-service'
  import { requestService } from '../../services/broswer-service'
  export default {
    name: 'customerService',
    props: ['login'],
    data() {
      const emptyImg = require('../../assets/images/business_head.png') // eslint-disable-line
      return {
        showCustomer: true,
        emptyImg,
        controlSW: { show: false },
        options: { width: '450px', contentStyle: { paddingBottom: 0 } },
        buttonQrCodeOptions: [],
        info: {
          name: '小查',
          phone: '************',
          email: '<EMAIL>',
          imgurl: emptyImg
        },
        titleData: '购买企查查专业版产品套餐，更多特权，超值尊享！'
      }
    },
    created() {
      this.showCustomer = __QCC__ && !__PLUGIN__
    },
    mounted() {
      this.setVipService()
    },
    methods: {
      openShangWu(title) {
        if (title && typeof (title) === 'string') {
          this.titleData = title
        }
        this.controlSW.show = true
      },
      closedShang() {
        this.controlSW.show = false
      },
      setVipService() {
        if (this.login) {
          requestService.businessItemGetSellerInfo({})
            .then(res => {
              let time = new Date().getTime()
              if (res.result.name === '杜虎') {
                this.info.name = '小查'
                this.info.phone = '************'
                this.info.email = '<EMAIL>'
                this.info.imgurl = this.emptyImg
              } else {
                this.info.name = !$formatter.isBlank(res.result.name) ? res.result.name : '小查'
                this.info.phone = !$formatter.isBlank(res.result.loginName) ? res.result.loginName : '************'
                this.info.email = !$formatter.isBlank(res.result.email) ? res.result.email : '<EMAIL>'
                this.info.imgurl = !$formatter.isBlank(res.result.id) ? `/proapi/getAvatarById?id=${res.result.id}&v=${time}` : this.emptyImg
              }
            })

          postVue('/comp/user/getUserRemainDays', {}).then(response => {
            if (response.result) {
              if (response.result.type === 2) {
                if (response.result.category === 'SET_STAD_COVID19' || response.result.category === 'SET_STAD_API_IDEN' || response.result.category === 'SET_STAD_COMP_IDEN' || response.result.category === 'SET_STAD_TRIAL30') {

                } else {
                  this.titleData = '企查查专业版，一站式企业尽职调查、风控大数据解决方案'
                }
              }
            }
          })
        }
      }
    }
  }

</script>
<style scoped lang="scss">
  .shangwu-pay {
    width: 150px;
    height: 140px;
    position: fixed;
    bottom: 20px;
    right: 10px;
    background-size: 150px;
    background-image: url(../../assets/images/zxsw.png);
    cursor: pointer;
  }

  .user-info {
    .info-desc {
      display: inline-block;
      margin-left: 15px;
      padding-top: 20px;
      vertical-align: top;

      .name {
        font-size: 24px;
        font-weight: bold;
      }

      .desc {
        margin-top: 5px;
        line-height: 16px;
        font-size: 12px;
        color: #999999;
        margin-bottom: 20px;
      }

      .phone {
        font-size: 14px;
        margin-bottom: 10px;
        line-height: 19px;
      }
    }
  }

</style>
