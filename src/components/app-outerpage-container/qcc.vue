<template>
  <div class="app-outerpage-container" :class="[{ __v2: isNewVersion }]"
    :style="{ 'padding-bottom': showFooter && showCommonFooter ? '46px' : '0', 'background-color': mainBackground ? '' : '#fff' }">
    <div style="background-color:#000" :class="isQcc && isFixed ? 'fixed-header' : ''" v-if="showCommonHeader">
      <comheader :rediectOption='rediectOption' :companyName="companyName"></comheader>
    </div>
    <app-permission-oversea v-if="isNotPermission4OverseaUser"
      style="height: 100vh;background-color: #fff;z-index: *********;"></app-permission-oversea>
    <div v-else :class="allClass" :style="allClassStyle">
      <slot name="tip"></slot>
      <div :class="bodyClassStyle" :style="bodystyle">
        <slot></slot>
      </div>
    </div>


    <div id="backToTop" :class="[{ 'is-jw': isJSJW }]" @click="scrollToTop">
      <img :src="imgs.backUp" width="30" class="icon_back2top">
      <img :src="imgs.backUpText" width="30" class="icon_back2top_text">
    </div>
    <comfooter v-if="showCommonFooter" :showFooter.sync="showFooter" class="_footer" :style="footerstyle"></comfooter>
  </div>
</template>

<script>
import comfooter from '../../components/app-normal-footer'
import comheader from '../../components/app-normal-header'
import menuMixins from '../../routes/welcome/loadmenu-mixin'
import config from '../../config/client'

export default {
  name: 'app-outerpage-container',
  components: { comfooter, comheader },
  mixins: [menuMixins],
  props: {
    isFixedBodyWidth: { default: false, type: Boolean },
    bodyclass: { default: '' },
    mainBackground: { default: true, type: Boolean },
    bodystyle: {},
    allBodystyle: {},
    footerstyle: {},
    showSearch: { default: false, type: Boolean },
    showDataInterface: { default: true, type: Boolean },
    showTask: { default: false, type: Boolean },
    showMessage: { default: false, type: Boolean },
    showHelpCenter: { default: true, type: Boolean },
    showMenu: { default: false, type: Boolean },
    showCommonFooter: { default: true, type: Boolean },
    showCommonHeader: { default: true, type: Boolean },
    isFixed: { default: false, type: Boolean },
    isQcc: { default: false, type: Boolean },
    companyName: { default: '', type: String },
    bodyFlexStyle: { default: false, type: Boolean }
  },
  data() {
    return {
      showFooter: true,
      imgs: config.customize
    }
  },
  computed: {
    allClass() {
      let clss = ''
      if (this.isFixed && this.isQcc) {
        clss += (' fixed-body')
      }
      return clss
    },
    allClassStyle() {
      let stlv = { marginTop: this.isNewVersion ? '2px' : '' }
      if (this.allBodystyle) {
        stlv = this.allBodystyle
      }
      return stlv
    },
    bodyClassStyle() {
      let clss = ''
      if (this.bodyclass) {
        clss += (' ' + this.bodyclass)
      }
      if (this.isQcc) {
        clss += (' qcc-body')
      }

      clss += ' _body '

      if (this.bodyFlexStyle) {
        clss += ' body-flex '
      }

      if (this.isFixedBodyWidth) {
        clss += ' _body-fix '
      }

      return clss
    },
    rediectOption: function () {
      return {
        showSearch: this.showSearch,
        showDataInterface: this.showDataInterface,
        showTask: this.showTask,
        showMessage: this.showMessage,
        showHelpCenter: this.showHelpCenter,
        showMenu: true
      }
    }
  },
  mounted: function () {
    window.addEventListener('scroll', (e) => {
      this.$emit('scrollChange', e)
      if ($(window).scrollTop() > 50) {
        $('#backToTop').show()
      } else {
        $('#backToTop').hide()
      }
    })
  },
  methods: {
    scrollToTop: function () {
      window.scrollTo(0, 0)
      this.$emit('scrollToTop')
    }
  },
  created() {
    // this.showFooter = !!this.showCommonFooter
  }
}

</script>

<style scoped lang="scss">
@import "./back-top.scss";
.app-outerpage-container {
  width: 100%;
  min-height: 100vh;
  padding-bottom: 50px;
  position: relative;
  background-color: #F7F7F7;

  &.__v2 {
    .fixed-body {
      margin-top: 52px !important;
    }
  }

  .fixed-header {
    position: fixed;
    top: 0px;
    width: 100%;
    z-index: 999;
  }

  .fixed-body {
    margin-top: 50px !important;
  }

  .qcc-body {
    width: 1220px !important;
    margin: auto;
    max-width: 100% !important;
    margin-bottom: 10px;
  }

  ._body {
    margin: 0px auto;
    padding: 10px 0px;
    min-height: calc(100vh - 100px);
    width: 90%;
    max-width: 1300px;
    background-color: #F7F7F7;
  }

  .body-flex {
    display: flex;
    flex-direction: column;
  }

  ._body-fix {
    width: 1218px;
  }

  ._footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }

  @include mixBackTop()
}
</style>
