<template>
  <app-permission-oversea v-if="isNotPermission4OverseaUser" style="height:100vh"></app-permission-oversea>
  <div class="app-outerpage-container" v-else>
    <div class="_body" :class="[bodyclass, {'width-limit': widthLimit}, {'is1220': isQCCPRO_G && !$route.path.startsWith('/charts/')}]" :style="_bodystyle" id="bodyElementId">
      <slot></slot>
    </div>
    <div id="backToTop" @click="scrollToTop">
      <img :src="imgs.backUp" width="30" class="icon_back2top">
      <img :src="imgs.backUpText" width="30" class="icon_back2top_text">
    </div>
  </div>
</template>

<script>
import { getPostMessageMixin } from '@/utils/print-utils'
import config from '../../config/client'

const printMixin = getPostMessageMixin(() => {
  try {
    return document.getElementById('bodyElementId').scrollHeight + 100
  } catch (e) {
    return document.getElementById('bodyElementId').scrollHeight + 100
  }
})
  export default {
    name: 'app-outerpage-container',
    mixins: [printMixin],
    props: {
      bodyclass: { default: '' },
      bodystyle: {}
    },
    data() {
      return {
        imgs: config.customize
      }
    },
    beforeCreate() {
      if (!__QCCPRO_G__) { document.body.style.background = '#f7f7fa' }
    },
    computed: {
      withoutIframe () {
        return window.self === window.top
      },
      _bodystyle() {
        let res = {}
        if (this.$route.query.cWidth && !this.$route.path.startsWith('/charts/')) {
          res.width = (this.$route.query.cWidth || '').toLowerCase().includes('px') || (this.$route.query.cWidth || '').toLowerCase().includes('%') ? this.$route.query.cWidth : (this.$route.query.cWidth + 'px')
          res['max-width'] = '100%'
          res.margin = 'auto'
          res.minWidth = res.width
        }
        return { ...this.bodystyle || {}, ...res }
      },
      widthLimit() {
          return !this.$route.path.startsWith('/charts/')
      },
      allClassStyle() {
        let clss = ''
        if (this.isFixed && this.isQcc) {
          clss += (' fixed-body')
        }
        return clss
      }
    },
    mounted() {
      window.addEventListener('scroll', (e) => {
        this.$emit('scrollChange', e)
        if ($(window).scrollTop() > 50) {
          $('#backToTop').show()
        } else {
          $('#backToTop').hide()
        }
      })
    },
    methods: {
      scrollToTop: function () {
        window.scrollTo(0, 0)
        this.$emit('scrollToTop')
      }
    }
  }

</script>

<style scoped lang="scss">
@import "./back-top.scss";
  .app-outerpage-container {
    width: 100%;
    min-height: 100vh;
    position: relative;
    padding-bottom: 10px;

    ._body {
      background-color: transparent;
      height: 100%;
      min-height: calc(100vh - 58px);

      &.width-limit {
        margin: 0px auto;
        padding: 10px 0px;
        min-height: calc(100vh - 100px);
        min-width: 1220px;
        width: 90%;
        max-width: 1300px;
        background-color: #F7F7F7;
        position: relative;
      }

      &.is1220 {
        width: 1220px;
        margin: 0px auto;
        padding: 0px;
        background-color: #F7F7F7;
        position: relative;
      }
    }

    @include mixBackTop()
  }

</style>

<style lang="scss">
.qccpro-g  .app-outerpage-container {
  height: unset;
  min-height: 100vh;
  background-color: #f7f7f7;
  .risk-assets-all {
    margin-top: -10px;
  }
  ._body.width-limit {
    height: unset;
    min-height: 100vh;
  }
  @media print {
    min-height: unset;
    ._body {
      min-height: unset;
    }
  }
}
</style>
