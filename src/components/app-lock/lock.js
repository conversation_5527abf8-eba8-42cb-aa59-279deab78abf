import appLock from './index.vue'
export default {
  show(keyNo, companyName, isRadar = false, isBene = false, functionid) {
    // console.log(11111, keyNo, companyName, isRadar, isBene, functionid)
    // if ((isRadar || isBene) && !functionid) {
    //   // 同时是不是雷达和受益人
    //   return
    // }
    window.AppRuntimeContext.instance.$uiService.showDialog(appLock, {
      keyNo,
      companyName,
      isRadar,
      isBene,
      functionid
    })
  }
}
