.app-lock-popup{
  ::v-deep {
    ._popup_contentWrapper {
      .footerWrapper{
        .el-button--primary{
          color: #713100;
          border-width: 0px;
          height: 32px;
          background: linear-gradient(112deg, #FFECD0 0%, #F3BB6F 99%);

          &:hover{
            background: linear-gradient(112deg, #F7CE95 0%, #CC8729 100%);
          }
        }
      }
    }
  }
}

.app-lock{
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  
  .app-lock-item{
    position: relative;
    width: 225px;
    cursor: pointer;
    border-radius: 10px;
    opacity: 1;
    border: 2px solid #fff;
    // background: #D8D8D8;  
    background: linear-gradient(112deg, #EEEEEE 0%, #F3F3F3 99%);
    .duig{
      display: none;
      width: 30px;
      height: 30px;
      position: absolute;
      right: -2px;
      bottom: -1px;
      background: url("./duig.svg") no-repeat;
      background-size: 100% 100%
    }
    .header{
      padding: 0px 15px;
      height: 42px;
      font-size: 16px;
      font-weight: 500;
      line-height: 42px;
      letter-spacing: 0em;
      color: #333333;

    }
    &.active{
      background: linear-gradient(112deg, #FBE9D3 0%, #FFFAF5 99%);
      border: 2px solid #B69174;
      .header{
        color: #713100;
      }
      .duig{
        display: block;
      }
    }

    &:hover{
      background: linear-gradient(112deg, #FBE9D3 0%, #FFFAF5 99%);
      // background: linear-gradient(112deg, #F3BB6F 0%, #FFECD0 99%);
      .header{
        color: #713100;
      }
    }

 

    .body{
      border-radius: 16px;
      padding: 12px 16px 16px 16px;
      margin: 0px 6px 6px 6px;
      background-color: #fff;
      color: #713100;
      .i-wd{
        min-height: 27px;
        margin-top: 5px;
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: 0em;
        /* 0 中性/中性2-#666666 */
        /* 样式描述：副标题&内容字段 */
        color: #666666;

        &:first-child{
          margin-top: 0px;
        }

        .iconfont{
          font-size: 11px;
          color: #DDC19D;
          padding: 3px 2px 2px 2px;
        }
      }
    }
  }
}