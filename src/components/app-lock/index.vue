<template>
  <app-popup ref="pop" class="app-lock-popup" :options="options" :control="popupControl" :buttonOptions="buttonOptions"
    :title="title">
    <div class="app-lock" :style="`${currentLockList && currentLockList.length === 1 ? 'justify-content:center' : ''}`">
      <div class="app-lock-item" v-for="item in currentLockList" :key="item.type" @click="selectLock(item)"
        :class="{ active: item.active }">
        <div class="header">{{ item.name }}</div>
        <div class="body">
          <div class="i-wd" v-for="(i, index) in item.list" :key="'wd' + index">
            <i class="iconfont icon-gou" v-if="i"></i> <span>{{ i||' ' }}</span>
          </div>
        </div>
        <div class="duig"></div>
      </div>
    </div>
  </app-popup>
</template>

<script src="./component.js"></script>
<style lang="scss" src="./style.scss" scoped></style>
