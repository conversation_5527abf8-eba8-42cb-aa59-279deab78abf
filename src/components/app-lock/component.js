import commonService from '../../services/common-service'
import list from './list'
import { mapState } from 'vuex'
const msg = [
  '已将该企业添加至风控列表',
  '已将该企业添加至尽调列表',
  '已将该企业同时添加至尽职调查、风险监控列表'
]
export default {
  name: 'AppLock',
  data() {
    return {
      buttonOptions: [
        {
          name: '取消',
          type: 'normal',
          click: () => {
            this.popupControl.ref.close()
          }
        },
        {
          name: '立即添加',
          type: 'primary',
          click: () => {
            this.addFunctionList()
          }
        }
      ],
      options: {
        width: '500px'
      },
      popupControl: {
        title: '',
        options: {
          width: '500px'
        },
        show: false
      },
      title: '添加至列表',
      isRadar: false, // 风险监控
      isBene: false, //  尽职调查
      functionid: '',
      lockList: [
        { type: 2, show: true, active: true, name: '尽职调查一键解锁', functionId: 'bene_id', list: list.beneList() },
        { type: 1, show: true, active: false, name: '风险监控一键解锁', functionId: 'radar_id', list: list.radarList() }
      ],
      keyNo: '',
      companyName: ''
    }
  },
  computed: {
    ...mapState('company', ['companyInfo']),
    currentLockList() {
      let f = this.lockList.filter(e => (this.functionid ? e.functionId === this.functionid : true))
      if (f && f.length === 1) {
        f[0].active = true
        this.popupControl.options.width = '300px'
        this.options.width = '300px'
      }
      return f
    }
    // currentTitle() {
    //   let f = this.lockList.filter(e => e.active)
    //   if (f && f.length === 1) {
    //     return f[0].name
    //   }

    //   return this.title
    // }
  },
  watch: {},
  async mounted() {
    await this.$nextTick()
    if (!this.keyNo) {
      this.keyNo = this.$route.query?.keyNo
    }
    if (!this.keyNo) {
      this.keyNo = this.companyInfo?.keyNo
    }

    if (!this.companyName) {
      this.companyName = this.$route.query.name
    }

    if (!this.companyName) {
      this.companyName = this.companyInfo?.name
    }
  },
  methods: {
    addFunctionList() {
      let f = this.lockList.filter(e => e.active && (this.functionid ? e.functionId === this.functionid : true))
      if (f && f.length) {
        let functionids = f.map(e => e.functionId)
        this.toCareData(functionids)
      } else {
        this.$message.warning('请选择解锁功能')
      }
    },
    toCareData(functionIds) {
      var functionTableIdList = functionIds

      var params = {
        module: 20,
        keyNo: this.keyNo,
        corpName: this.companyName,
        functionTableIdList: functionTableIdList,
        source: 'ADDCORPDETAIL'
      }

      commonService.postVue('/corp/search/importSingleCorpFromAutocomplete4FinSetNoGroupValue?showError', params).then(res => {
        if (res.status === '200') {
          if (functionIds && functionIds.length === 1) {
            if (functionIds.includes('bene_id')) {
              this.$message.success(msg[1])
            }
            if (functionIds.includes('radar_id')) {
              this.$message.success(msg[0])
            }
          } else {
            this.$message.success(msg[2])
          }
          this.popupControl.ref.close()
          setTimeout(() => {
            location.reload()
          }, 2000)
        } else {
          let message = res.msg || '添加失败，请稍后再试'
          this.$message.error(message)
        }
      }).catch((error) => { // 115 --一个有额度的情况
        if ($util.isJSONObject2String(error.message) && JSON.parse(error.message).status === '115') {
          this.popupControl.ref.close()
          setTimeout(() => {
            location.reload()
          }, 2000)
        }
      })
    },
    selectLock(item) {
      item.active = !item.active
    //   this.lockList.forEach(lock => {
    //     if (lock.type === item.type) {
    //       lock.active = true
    //     } else {
    //       lock.active = false
    //     }
    //  })
    }
  }
}
