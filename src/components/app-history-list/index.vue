<template>
  <div class="app-history-items" v-if="historys && historys.length">
    <div class="history-item" v-for="(history, index) in historys" :key="'history' + index">
      <div class="title">
        <div class="left-title" v-if="history.name && !noTitle" v-html="history.name"></div>
        <div class="count-type" v-if="customerCategory(history) && !noTitle" v-html="customerCategory(history)"></div>
        <div class="count-c" :class="{'count-c-1': noTitle}">查询 <span class="count">{{ getMarkCount(history.list) }}</span> 个{{mark}}</div>
      </div>
      <div class="content-oper">
        <div :ref="'rhistory' + index" class=" content" v-if="history.list && history.list.length">
          <div class="content-item" v-for="(item, subindex) in history.list"
            :ref="'rhistory' + index + '-sub-' + subindex" :key="'hitem' + subindex">
            <img class="file-icon" src="../../assets/images/cdd/file.svg" alt="" v-if="item.isFile"/>
            <span class="file-name" v-if="item.isFile" v-html="getFile(item)"></span>
            <span v-else :title="item.corpName" v-html="getTitle(item.corpName)"></span>
          </div>
          <div :ref="'rhistory' + index + '-tover'" class="content-over"></div>
        </div>
        <div class="btn-check" @click="check(history)"  v-track="{text: '历史记录-查看'}">查看<i class="iconfont icon-wenzilianjiantou font14"></i></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'app-history-list',
  props: {
    // 格式：[{name:'cccc',category:'cccc',list:[{corpName:'xxxx',corpKeyNo:'XXXX',isFile:false,fileType:'excel',fileName:'xxxxxx',fileUrl:'xxxxxx.x'}]},.............]
    historys: {
      type: Array,
      default: () => []
    },
    customerCategory: {
      type: Function, default: () => { return '' }
    },
    mark: {
      type: String,
      default: '目标'
    },
    noTitle: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
    }
  },
  watch: {
    historys: {
      handler(newv, oldv) {
        this.onWindowSizeChange()
      },
      deep: true
    }
  },
  mounted() {
    this.onWindowSizeChange()
  },
  methods: {
    initWidth() {
      if (!this.$refs || !Object.keys(this.$refs)) {
        return
      }

      let arrayKeys = []
      Object.keys(this.$refs).forEach(key => {
        if (key.indexOf('rhistory') === 0 && key.indexOf('-tover') < 0 && key.indexOf('-sub-') < 0) {
          let width = 0
          if (this.$refs[key] && this.$refs[key][0] && this.$refs[key][0].offsetWidth) {
            width = this.$refs[key][0].offsetWidth
          }
          arrayKeys.push({ key: key, width: width, allWidth: 0 })
        }
      })

      arrayKeys.forEach(ay => {
        Object.keys(this.$refs).forEach(key => {
          if (key.indexOf(ay.key) === 0 && key.indexOf('-sub-') > 0) {
            let width = 0
            if (this.$refs[key] && this.$refs[key][0] && this.$refs[key][0].offsetWidth) {
              width = this.$refs[key][0].offsetWidth
            }
            ay.allWidth += width
          }
        })
      })
      arrayKeys.forEach(ay => {
        if (ay.width < ay.allWidth) {
          $(this.$refs[ay.key + '-tover'][0]).show()
        } else {
          $(this.$refs[ay.key + '-tover'][0]).hide()
        }
      })
    },
    getTitle(title) {
      return $util.truncate(title, 16)
    },
    getFile(item) {
      // item ={corpName:'xxxx',corpKeyNo:'XXXX',isFile:false,fileType:'excel',fileName:'xxxxxx',fileUrl:'xxxxxx.x'}
      let icon = 'icon-excel'
      if (item.fileType === 'word') {
        icon = 'icon-word'
      }
      let name = $util.truncate(item.fileName, 16)
      return `<span title="${item.fileName}"><i class="iconfont ${icon} iconfont-history"></i> <span>${name}</span></span>`
    },
    onWindowSizeChange() {
      this.$nextTick(() => {
        this.initWidth()
      })
    },
    check(historyItem) {
      this.$emit('check', historyItem)
    },
    getMarkCount(list) {
      if (!list || !list.length) {
        return 0
      }
      if (list[0].isFile) {
        return list[0].fileCompanyCount || 1
      }
      return list.length
    }
  }
}

</script>

<style scoped lang="scss">
.app-history-items {
  padding: 0px 15px;

  .history-item {
    margin-top: 15px;
    padding: 15px;
    border-radius: 4px;
    background: #F7F7F7;
    font-size: 14px;

    &:first-child {
      margin-top: 0px;
    }

    .title {
      margin-bottom: 10px;

      .left-title {
        font-size: 15px;
        line-height: 24px;
        font-weight: bold;
        display: inline-block;
      }

      .count-type {
        display: inline-block;
        color: #999999;
        font-size: 14px;
        // margin-left: 5px;
        // margin-right: 10px;
      }

      .count-c {
        display: inline-block;
        line-height: 22px;
        margin-left: 5px;

        .count {
          color: #F04040;
        }

        &.count-c-1 {
          color: #9E9E9E;
          font-size: 14px;
        }
      }
    }


    // background: linear-gradient(90deg, rgba(255,255,255,0.50) 0%, #FFFFFF 100%);

    .content-oper {
      display: flex;

      .btn-check {
        cursor: pointer;
        height: 42px;
        line-height: 42px;
        border-radius: 4px;
        text-align: center;
        background: #FFFFFF;
        color: #128bed;
        margin-left: 10px;
        padding: 0px 10px;
        white-space: nowrap;

        &:hover {
          background: #E2F1FD;
          color: #0069BF;
        }
      }

      .content {
        border-radius: 2px;
        padding: 10px 15px;
        background: #FFFFFF;
        white-space: nowrap;
        overflow-x: hidden;
        position: relative;

        .content-item {
          display: inline-block;
          line-height: 22px;
          padding: 0px 15px;
          border-right: 1px #eee solid;
          position: relative;

          ::v-deep {
            .iconfont-history {
              font-size: 14px;
            }
          }

          &:first-child {
            padding-left: 0px;
          }

          &:nth-last-child(2) {
            padding-right: 0px;
            border-right: none;
          }

          .file-icon
          {
            width: 14px;
            height: 17px;
            position: absolute;
            left: 0;
            top: 2px;
          }

          .file-name
          {
            margin-left: 20px;
          }
        }

        .content-over {
          width: 40px;
          right: 0;
          top: 0;
          bottom: 0;
          position: absolute;
          background: linear-gradient(90deg, rgba(255, 255, 255, 0.50) 0%, #FFFFFF 100%);
        }
      }
    }
  }
}

@media screen and (max-height: 680px) {
     .app-history-items {

      .history-item {
        padding: 10px 15px;
      }

     }
  }

</style>
