<template>
  <div
    class="app-header-search"
    :class="{
      'direct-display': directDisplaySearch,
      'app-header-search--aml': isAML,
      'app-header-search--fin-package': finPackage
    }"
    :style="{
      '--content-height': contentHeight
    }"
  >
    <app-input
      v-show="!directDisplaySearch"
      v-model="value"
      :disabled="true"
      class="input"
      :class="{ 'input-collapse': containerVisible }"
      :placeholder="inputPlaceholder"
      @click.native="handleInputClick"
    >
      <template v-slot:prefix>
        <i class="iconfont icon-sousuo"></i>
      </template>
    </app-input>
    <transition name="search-collapse">
      <div
        v-show="containerVisible"
        class="search-container"
        :class="{ 'active': containerVisible }"
        :style="{ '--function-line': Math.ceil(functions.length / 4) }"
      >
        <div class="search-container__modal" @click="handleClose()"></div>
        <div class="search-container__body">
          <div class="search-container__content">
            <template v-if="isAML">
              <div class="search-title">
                风险为本，数字化反洗钱平台
              </div>
              <div class="search-input">
                <app-search-input
                  ref="app-search-input"
                  submit-text="AML"
                  type="aml"
                  button-class="blue"
                  zhuge-page-name="顶部搜索框"
                  :input-text="value"
                  :placeholder="placeholder"
                  :direct-display-search="directDisplaySearch"
                  :clearable="true"
                  @select-company="handleSelectCompany"
                  @close-search="handleClose"
                  @submit-callback="handleSubmitCallback"
                />
              </div>
            </template>
            <template v-else-if="finPackage">
              <div class="search-title">
                尽调有道 风控有方
              </div>
              <div class="search-input">
                <app-search-input
                  ref="app-search-input"
                  :submit-text="submitText"
                  zhuge-page-name="顶部搜索框"
                  :type="currentTab"
                  :input-text="value"
                  :button-class="buttonClass"
                  :clearable="true"
                  :direct-display-search="directDisplaySearch"
                  :show-batch-search="false"
                  @select-company="handleSelectCompany"
                  @close-search="handleClose"
                  @submit-callback="handleSubmitCallback"
                />
              </div>
            </template>
            <template v-else>
              <div v-if="!isQCCPRO_G" class="search-tabs">
                <div
                  v-for="(tab) in tabs"
                  :key="tab.key"
                  class="search-tabs__item"
                  :class="{ 'active': currentTab === tab.key, 'un-sortable': !tab.sortable }"
                  @click="handleChangeTab(tab)"
                >
                  {{ tab.title }}
                  <el-popover
                    v-show="tab.sortable"
                    :open-delay="150"
                    class="search-tabs__drag-icon"
                    placement="bottom"
                    trigger="hover"
                    content="排序"
                    popper-class="default-popover-tooltip app-popover topIndex"
                  >
                    <template v-slot:reference>
                      <i class="iconfont icon-paixu1" @click="handleShowDragDialog"></i>
                    </template>
                  </el-popover>
                </div>
              </div>
              <div class="search-input">
                <app-search-input
                  ref="app-search-input"
                  :submit-text="submitText"
                  zhuge-page-name="顶部搜索框"
                  :type="currentTab"
                  :input-text="value"
                  :placeholder="placeholder"
                  :show-batch-search="currentTab === 'company' && searchAuthority"
                  :direct-display-search="directDisplaySearch"
                  :button-class="buttonClass"
                  :clearable="true"
                  @select-company="handleSelectCompany"
                  @close-search="handleClose"
                  @submit-callback="handleSubmitCallback"
                />
                <div
                  v-show="showAdvancedSearch && searchAuthority"
                  v-track="{ text: '高级搜索', pageName: directDisplaySearch ? '首页' : '顶部搜索框' }"
                  class="advanced-search"
                  @click="handleAdvancedSearchClick"
                >
                  <i class="iconfont icon-gaojisousuo1"></i><span>高级搜索</span>
                </div>
                <div v-if="financialVersion && !finPackage && !isNotDD" class="limit-instruction">
                  <span class="iconfont icon-icon_tswb"></span>
                  <span>额度说明：</span>
                  <span>准入尽调和受益人识别的企业计入尽职调查列表中扣取相应额度。</span>
                </div>
              </div>
              <common-functions
                v-if="!financialVersion && !finPackage && !isNotDD"
                :function-gutter="functionGutter"
                :function-width="functionWidth"
                :direct-display-search="directDisplaySearch"
                :functions="functions"
                :col-span="colSpan"
                @close-search="handleClose"
              />
              <div v-if="!financialVersion && !finPackage && !isNotDD" class="limit-instruction"
                   :style="{width: functionWidth ? `${functionWidth}px` : '800px'}">
                <span class="iconfont icon-icon_tswb"></span>
                <span>额度说明：</span>
                <span>准入尽调和受益人识别的企业计入尽职调查列表中扣取相应额度。</span>
              </div>
            </template>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
  import searchItem from '../../routes/welcome/components/search-item'
  import commonFunctions from './components/common-functions'
  import appSearchInput from '../app-search-input'
  import beneBreakThroughService from '../../services/through'
  import searchTabSort from './components/search-tab-sort'
  import { mapActions, mapState } from 'vuex'
  import { getQccproGMenus } from '../app-search-input/mixins/menus'

  export default {
    name: 'app-header-search',

    components: {
      searchItem,
      commonFunctions,
      appSearchInput
    },

    props: {
      /**
       * 常用功能间距
       */
      functionGutter: {
        type: Number,
        default: 15
      },

      colSpan: {
        type: Number,
        default: 48
      },

      /**
       * 常用功能狂赌
       */
      functionWidth: {
        type: Number,
        default: 800
      },

      /**
       * 是否直接显示内容，无需点击显示内容
       */
      directDisplaySearch: {
        type: Boolean,
        default: false
      },

      buttonClass: {
        type: String,
        default: ''
      },

      /**
       * 金融版标记
       */
      financialVersion: {
        type: Boolean,
        default: false
      },

      /**
       * 专业版金融套餐标识
       */
      finPackage: {
        type: Boolean,
        default: false
      }
    },

    data() {
      return {
        searchAuthority: $util.getHasPermission('expan_cust:search:view||data_term:corp_data:view||expan_cust:batch_search:view'),
        isNotDD: $util.isNotDD(),
        value: '',
        manualTab: null,
        defaultTab: null,
        functions: [],
        isAML: false // 是否是反洗钱
      }
    },

    computed: {
      ...mapState('functions', {
        tabs(state) {
          if (state.tabs && state.tabs.length > 0) {
            this.defaultTab = state.tabs[0].key
          }
          return state.tabs
        },
        searchBodyVisible: state => state.searchBodyVisible
      }),

      contentHeight() {
        if (this.isAML) {
          return '210px'
        }
        if (this.__FIN_PACKAGE__) {
          return '260px'
        }
        return '427px'
      },

      /**
       * 当前选中的标签
       * @returns {null|string}
       */
      currentTab() {
        if (__QCCPRO_G__) return 'due-diligence'
        if (this.finPackage) {
          return 'company'
        }
        return this.manualTab || this.defaultTab
      },

      containerVisible() {
        if (this.directDisplaySearch) {
          return true
        }
        return this.searchBodyVisible
      },

      inputPlaceholder() {
        if (this.containerVisible) {
          return '搜索'
        }
        return '请输入企业名称、人名、产品名等'
      },

      /**
       * 根据标签类型返回按钮文字
       * @returns {string}
       */
      submitText() {
        switch (this.currentTab) {
          case 'due-diligence':
            return '准入尽调'
          case 'beneficiary':
            return '受益人识别'
          case 'company':
            return '查一下'
          case 'function':
            return '查一下'
        }
      },

      /**
       * 根据标签类型返回文本框占位符
       * @returns {string}
       */
      placeholder() {
        switch (this.currentTab) {
          case 'due-diligence':
          case 'beneficiary':
          case 'company':
            return '请输入企业名称或统一社会信用代码'
          case 'function':
            return '请输入功能关键词'
        }
      },

      /**
       * 根据标签类型是否显示高级搜索
       * @returns {boolean}
       */
      showAdvancedSearch() {
        if (this.finPackage) {
          return false
        }
        switch (this.currentTab) {
          case 'due-diligence':
          case 'beneficiary':
          case 'function':
            return false
          case 'company':
            return true
        }
      }
    },

    watch: {
      $route() {
        this.setInitialValue()
      },

      containerVisible(val) {
        // 判断是否是反洗钱账号
        if (!this.isAML) {
          this.isAML = this.currentIsAMLAccount
        }
        if (val) {
          this.handleInputFocus()
        }
        this.handleWatchContainerVisible()
      }
      // '$store.state.appState.currentIsAMLAccount' (val) {
      //   this.isAML = val
      // }
    },

    async created() {
      // 判断是否是反洗钱账号
      // this.isAML = $util.getHasPermission('rvrs:hidden_export')
      this.setInitialValue()
      await this.handleWatchContainerVisible()
      await this.getTabs()
      const unWatchTabsOrder = this.$watch('$store.state.functions.tabsOrder', (newVal) => {
        if (newVal) {
          this.getTabs()
          unWatchTabsOrder()
        }
      })
      this.handleInputFocus()
      window.AppRuntimeContext.eventBus.$on(window.AppRuntimeContext.eventBusEvents.CHANGE_SEARCH_KEYWORD, (value) => {
        this.value = value
      })
    },

    methods: {
      ...mapActions(
        'functions',
        ['getTabs', 'setTabs', 'setTabsOrder', 'getCommonFunctions', 'setSearchBodyVisible', 'toggleSearchBodyVisible']
      ),

      async handleWatchContainerVisible() {
        if (this.containerVisible) {
          this.functions = this.isQCCPRO_G ? getQccproGMenus() : await this.getCommonFunctions()
          !this.directDisplaySearch && document.querySelector('body').classList.add('hidden-scroll')
        } else {
          !this.directDisplaySearch && document.querySelector('body').classList.remove('hidden-scroll')
        }
      },

      handleInputClick() {
        this.toggleSearchBodyVisible()
        $util.zhugeTrackTwoLevel('点击', '顶部搜索框')
      },

      /**
       * 关闭下拉内容
       * @param callback
       */
      handleClose(callback) {
        if (!this.directDisplaySearch) {
          this.setSearchBodyVisible(false)
        }
        if (callback && !this.directDisplaySearch) {
          setTimeout(callback, 300)
        } else if (callback) {
          callback()
        }
      },

      /**
       * 切换标签
       * @param tab
       */
      handleChangeTab(tab) {
        this.manualTab = tab.key
        this.handleInputFocus()
        $util.zhugeTrackOneLevel(`搜索企业类型-${tab.title}`, '顶部搜索框')
      },

      handleRedirectFunctions() {
      },

      /**
       * 选择企业列表下拉框事件
       * @param corpName
       */
      handleSelectCompany({ corpName }) {
        this.value = corpName
      },

      /**
       * 按钮提交事件
       * 文本框回车事件
       * @param corpName
       */
      handleSubmitCallback({ corpName }) {
        this.value = corpName
      },

      /**
       * 高级搜索按钮事件
       */
      handleAdvancedSearchClick() {
        this.handleClose(() => {
          this.$router.push({ path: '/customer/customer-advance' })
        })
      },

      /**
       * 设置组件初始数据
       * 根据URL中的KeyNo，设置文本框的企业名称
       * 根据URL设置当前选中的标签
       */
      setInitialValue() {
        this.value = this.$route.query.searchkey || ''
        if (this.$route.query.keyNo && !this.$route.query.keyNo.startsWith('p')) {
          beneBreakThroughService.getLatestNameByKeyNo({ keyNo: this.$route.query.keyNo }).then(res => {
            if (res.status === '200' && res.result) {
              this.value = res.result
            }
          })
        }
        switch (true) {
          case ['account-search', 'dd-open', 'account-detail'].includes(this.$route.name):
            this.manualTab = 'due-diligence'
            break
          case ['ubo-single', 'beneficiary-search'].includes(this.$route.name):
            this.manualTab = 'beneficiary'
            break
          case ['search', 'companyDetail'].includes(this.$route.name):
            this.manualTab = 'company'
            break
          case this.$route.name === 'function-search':
            this.manualTab = 'function'
            break
          default:
            this.manualTab = this.defaultTab
            break
        }
      },

      /**
       * 显示排序弹出框事件
       */
      handleShowDragDialog() {
        $util.zhugeTrackTwoLevel('快捷搜索功能排序', '顶部搜索框')
        this.$uiService.showDialog(searchTabSort, {
          options: {
            list: [...this.tabs],
            saveFunc: (list) => {
              this.setTabs(list)
              this.setTabsOrder(list)
              return true
            },
            dragMoveFunc: (e) => {
              return e.relatedContext.element.key !== 'function'
            }
          }
        })
      },

      /**
       * 文本框设置焦点方法
       */
      handleInputFocus() {
        this.$nextTick(() => {
          if (
            this.$refs &&
            this.$refs['app-search-input'] &&
            this.$refs['app-search-input'].$refs &&
            this.$refs['app-search-input'].$refs.input
          ) {
            this.$refs['app-search-input'].$refs.input.focus()
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import "../../styles/common";

  .app-header-search {
    color: #333333;

    &--aml {
      .search-container__content {
        padding: 50px 0 20px;
      }
    }

    &.direct-display {
      height: 100%;

      .search-container {
        position: relative;
        top: auto;
        right: auto;
        bottom: auto;
        left: auto;
        z-index: 1;
        height: 100%;
        overflow: visible;
      }

      .search-container__modal {
        display: none;
      }

      .search-container__content {
        padding: 0;
      }

      .search-container__body {
        height: 100% !important;
        padding-top: calc(100vh * 0.2);
        box-shadow: none;

        @media screen and (max-height: 600px) {
          padding-top: calc(100vh * 0.1);
        }
      }

      .search-input {
        margin: 0 auto 50px;
      }

      .un-sortable {
        display: none;
      }
    }

    &--fin-package {
      .search-title {
        font-size: 54px;
        line-height: 62px;
        font-weight: bold;
        margin-bottom: 50px;
      }

      .search-sub-title {
        font-size: 20px;
        line-height: 28px;
        text-align: center;
        margin-bottom: 25px;
      }

      &.direct-display {
        color: #FFFFFF;

        .search-input {
          margin: 0 auto 73px;
        }
      }

      ::v-deep {
        .el-input__inner {
          border-color: #eee;

          &:focus {
            border-color: #eee;
          }
        }

        .search-dropdown {
          border-top: none;
        }
      }
    }
  }

  .input {
    width: 260px;
    transition: width 0.3s ease 0s;
    cursor: pointer;

    ::v-deep {
      input::-webkit-input-placeholder {
        color: #bbb !important;
      }

      .el-input__prefix {
        @include flex-center();
        left: 13px;
      }

      .el-input__inner {
        padding-right: 18px;
        padding-left: 32px;
        background-color: #FAFAFA;
        border-color: #EEEEEE;
        border-radius: 2px;
        color: #333333;
        pointer-events: none;
      }

      .iconfont {
        font-size: 14px !important;
        color: #999;
      }
    }
  }

  .input-collapse {
    width: 80px;
  }

  .search-container {
    position: fixed;
    top: 52px;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
    margin: 0;
    z-index: 1000000; // 避免被内容页面的loading遮挡
    line-height: 1.5;
    font-size: 16px;
    --function-line: 2;

    &__modal {
      opacity: 0.5;
      background: #000;
      position: absolute;
      top: 0;
      bottom: 0;
      right: 0;
      left: 0;
      transition: opacity 0.2s linear 0s;
    }

    &__body {
      background: #fff;
      position: relative;
      z-index: 2;
      overflow: hidden;
      height: 0;
      opacity: 0.5;
      box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.08) inset;
    }

    &__content {
      padding: 50px 0 0;
    }

    &.active {
      .search-container__body {
        height: var(--content-height, 427px);
        //height: calc(270px + var(--function-line) * 71px);
        opacity: 1;
        overflow: visible;
      }
    }
  }

  .search-collapse-enter {
    .search-container__body {
      overflow: hidden !important;
    }
  }

  .search-collapse-enter-to {
    .search-container__body {
      overflow: hidden !important;
    }
  }

  .search-collapse-leave {
    .search-container__body {
      overflow: hidden !important;
    }
  }

  .search-collapse-leave-to {
    .search-container__body {
      overflow: hidden !important;
    }
  }

  .search-collapse-enter-active {
    animation: 0.3s ease;

    .search-container__body {
      animation: searchOpen 0.3s ease 0s;
      overflow: hidden !important;
    }
  }

  .search-collapse-leave-active {
    animation: 0.3s ease;

    .search-container__modal {
      opacity: 0.1;
    }

    .search-container__body {
      animation: searchOpen 0.3s ease reverse;
      overflow: hidden !important;
    }
  }

  .search-tabs {
    @include flex-center();
    width: 800px;
    margin: 0 auto 25px;

    &__item {
      font-size: 16px;
      padding: 6px 20px;
      margin: 0 5px;
      border-radius: 2px;
      cursor: pointer;
      position: relative;

      &.active {
        background: #128BED;
        color: #fff;

        &:before {
          content: '';
          width: 0;
          height: 0;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
          border-top: 8px solid #128BED;
          position: absolute;
          top: calc(100% - 1px);
          left: calc(50% - 8px);
        }

        &:hover {
          color: #fff;

          .search-tabs__drag-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      &:hover {
        color: #128BED;
      }
    }

    &__drag-icon {
      position: absolute;
      top: 6px;
      right: 4px;
      display: none;
      transition: all 0.2s ease;
      height: 22px;

      .iconfont {
        font-size: 14px;
      }
    }
  }

  .search-title {
    font-size: 30px;
    line-height: 30px;
    text-align: center;
    font-weight: bold;
    margin-bottom: 30px;
  }

  .search-input {
    width: 800px;
    margin: 0 auto 30px;
    position: relative;
  }

  .advanced-search {
    display: flex;
    position: absolute;
    font-size: 14px;
    align-items: center;
    top: calc(50% - 12px);
    left: calc(100% + 20px);
    width: 80px;
    cursor: pointer;
    line-height: 24px;

    .iconfont {
      font-size: 16px;
      color: #128BED;
      margin-right: 5px;
    }

    &:hover {
      color: $color-primary
    }
  }

  // 额度说明
  .limit-instruction {
    width: 800px;
    color: #999999;
    display: flex;
    line-height: 22px;
    font-size: 14px;
    margin: 0 auto;
    padding: 15px 0;

    .iconfont {
      color: #bbbbbb;
      font-size: 14px;
      margin-right: 6px;
    }
  }

  @keyframes searchOpen {
    from {
      height: 0;
      opacity: 0.5;
    }

    to {
      height: var(--content-height, 427px);
      //height: calc(270px + var(--function-line) * 71px);
      opacity: 1;
    }
  }
</style>
