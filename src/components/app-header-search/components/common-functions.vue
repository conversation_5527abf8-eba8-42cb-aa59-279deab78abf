<template>
  <div class="function" :style="{width: `${functionWidth}px`}">
    <div class="function__header">
      <div class="function__title">
        常用功能 <!--<span style="color: #999999;font-weight: normal;padding-left: 10px">点击“自定义”添加常用功能到首页</span>-->
      </div>
      <div class="function__extra" @click="handleRedirectAllFunctions" v-track="{text: '更多功能',pageName:directDisplaySearch?'首页':'顶部搜索框'}" v-if="!isQCCPRO_G">
        <i class="iconfont icon-icon_shouyeyingyong font14" style="padding-right: 5px;color:#128BED"></i><span style="padding-right: 2px">全部功能</span><i class="iconfont icon-wenzilianjiantou font14"></i>
      </div>
    </div>
    <div class="function__body" :class="[{noBottomPadding:isQCCPRO_G}]">
      <el-row :gutter="functionGutter">
        <el-col :span="colSpan" v-for="(item, index) in functions" :key="index">
          <function-card :mdl="item" @click="handleRedirectFunction(item)"/>
        </el-col>
        <el-col :span="colSpan">
          <function-card isCustom :mdl="customItem" @click="handleRedirectCustomFunction(customItem)"/>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import functionCard from './function-card'

export default {
  name: 'search-functions',
  components: {
    functionCard
  },
  props: {
    functionGutter: {
      type: Number,
      default: 15
    },
    functionWidth: {
      type: Number,
      default: 800
    },
    colSpan: {
      type: Number,
      default: 6
    },
    directDisplaySearch: {
      type: Boolean,
      default: false
    },
    functions: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      customItem: { title: '自定义', isCustom: true, icon: 'icon-xinzengqiye', key: 'normal' }
    }
  },
  watch: {},
  created() {
  },
  methods: {
    handleRedirectAllFunctions() {
      this.$emit('close-search', () => {
        // this.$router.push({ name: this.directDisplaySearch ? 'home-function-all' : 'function-all' })
        this.$router.push({ name: 'function-all' })
      })
    },
    handleRedirectCustomFunction() {
      this.$emit('close-search', () => {
        this.$router.push({ name: 'function-all', query: { isEdit: 1 } })
      })
    },
    handleRedirectFunction(item) {
      this.$emit('close-search', () => {
        this.$router.push({ path: item.path })
      })

      $util.zhugeTrackTwoLevel('常用功能点击', this.directDisplaySearch ? '首页' : '顶部搜索框')
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../../styles/common";

.function {
  width: 800px;
  margin: 0 auto;

  &__header {
    @include flex-vertical-center();
    justify-content: space-between;
    height: 50px;
    line-height: 50px;
  }

  &__title {
    font-size: 15px;
    font-weight: bold;
  }

  &__extra {
    color: #666666;
    display: flex;
    cursor: pointer;
    line-height: 24px;
    padding: 0 2px 0 5px;
    border-radius: 2px;

    span {
      font-size: 14px;
      //margin-right: 5px;
    }

    .iconfont {
      color: #BBBBBB;
      //font-size: 12px;
    }

    &:hover {
      color: $color-primary;
      background: #F2F8FE;

      .iconfont {
        color: $color-primary;
      }
    }
  }

  &__body {
    &.noBottomPadding {
      .el-col {
        padding-bottom: 0;
      }
    }

    .el-row {
      height: calc(100% - 15px);
    }

    .el-col {
      padding-bottom: 15px;
    }
  }
}
</style>
<style lang="scss">
.el-col-48 {
  width: 20%;
}
</style>
