<template>
  <app-popup ref="popup" :options="popOptions" :buttonOptions="[]" class="sort-app-popup" close-icon-class="iconfont icon-icon_guanbixx">
    <span slot="title" class="sort-dialog__title">
      <span>{{ headerTitle || '搜索项排序' }}</span>
      <span class="sort-dialog__sub-title">长按鼠标左键，上下拖动进行排序</span>
    </span>
    <div class="tab" :class="{'dragging': dragging}">
      <draggable
        v-model="tabs"
        animation="300"
        :forceFallback="true"
        @start="dragging = true"
        @end="dragging = false"
        :move="handleDragMove"
        filter=".un-sortable"
      >
        <div class="tab__item" v-for="item in tabs" :key="item[keyPropName]" :class="{'un-sortable': isBoolean(item.sortable) ? !item.sortable : false}">
          <div class="tab__text">{{ item[labelPropName] }}</div>
          <div class="tab__extra">
            <i class="iconfont icon-tuodong"></i>
          </div>
        </div>
      </draggable>
    </div>
    <div class="sort-dialog__footer">
      <app-button type="normal" size="small" not-zhuge @click="handleCancel">取消</app-button>
      <app-button type="primary" size="small" class="v2" not-zhuge @click="handleSubmit">确定</app-button>
    </div>
  </app-popup>
</template>

<script>
import { mapActions } from 'vuex'
import draggable from 'vuedraggable'
import { isBoolean } from 'lodash'

export default {
  name: 'search-tab-sort',
  components: {
    draggable
  },
  data() {
    return {
      popOptions: {
        width: '400px',
        contentStyle: { padding: '0' }
      },
      tabs: [],
      dragging: false,
      keyPropName: 'key',
      labelPropName: 'title',
      headerTitle: ''
    }
  },
  computed: {
  },
  async created() {
    this.tabs = this.options.list
    if (this.options.keyPropName) {
      this.keyPropName = this.options.keyPropName
    }
    if (this.options.labelPropName) {
      this.labelPropName = this.options.labelPropName
    }
  },
  methods: {
    ...mapActions('functions', ['getTabs', 'setTabs']),
    isBoolean,
    handleCancel () {
      this.$refs.popup.close()
    },
    async handleSubmit() {
      let success = true
      if (this.options.saveFunc) {
        success = await this.options.saveFunc(this.tabs)
      }
      if (success) {
        this.$refs.popup.close()
      }
    },
    handleDragMove (e) {
      if (this.options.dragMoveFunc) {
        return this.options.dragMoveFunc(e)
      }
      return true
    }
  }
}

</script>

<style lang="scss" scoped>
@import "../../../styles/common";
.tab {
  font-size: 14px;
  &__item {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    cursor: pointer;
    &:hover {
      background-color: #F2F8FE;
      .tab__extra {
        color: $color-primary;
      }
    }
    &.sortable-chosen {
      background-color: #F2F8FE !important;
      opacity: 1 !important;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
      .tab__extra {
        color: $color-primary;
      }
    }
    &.sortable-ghost {
      visibility: hidden;
    }
    &.un-sortable {
      display: none;
    }
  }
  &__text {}
  &__extra {
    color: #BBBBBB;
    .iconfont {
      font-size: 14px;
    }
  }
  &.dragging {
    .tab__item {
      border-color: transparent;
    }
    .tab__item:hover {
      background-color: transparent;
      .tab__extra {
        color: #BBBBBB;
      }
    }
  }
}
.sort-dialog__title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}
.sort-dialog__sub-title {
  font-size: 14px;
  font-weight: normal;
  color: #999999;
  margin-left: 8px;
}
.sort-dialog__footer {
  border-top: 1px solid #EEEEEE;
  margin: 0 15px;
  padding: 15px 0;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  .el-button {
    height: 32px;
    line-height: 30px;
    font-size: 14px;
    padding: 0 25px;
  }
}
</style>
