<template>
  <div class="select-download-container">
    <el-dropdown placement="bottom-end" trigger="click" v-if="!hiddenButton">
      <app-button type="" plain size="small">自定义导出<i class="el-icon-arrow-down el-icon--right"></i></app-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-for="item in exportList" :key="item.name" @click.native="exportClick(item)">{{item.name}}</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <app-popup class="customer-export-popup" ref='appPopDoload' :isLazy="true" v-if="popUpControl.show" :transbg="false" :control="popUpControl"
               title="导出维度选择(可直接搜索或者勾选维度)" :buttonOptions="buttonOptions" :options="options" :zIndex="90001">
      <div class="customer-export-tips">注册地在中国香港、中国台湾以及境外的企业和社会组织，暂不支持数据导出</div>

      <div style="line-height: 50px; padding:20px 15px" class='customerDownloadContainer'>
        <app-loading v-if="loadingProp.show" :control="loadingProp" top="50px"></app-loading>
        <div class="search">
          <div>
            <el-input class="download-input" placeholder="请输入维度关键词" v-model="keyword" @focus='keywordFocus(0)' @blur='keywordFocus(1)'></el-input>
            <div v-show="keywordPop" class="keyword-pop">
              <div v-for="(search,index) in keywordSearches" :key="index" @click="keywordClick(search)" :class="index===0?'keyword-options':'keyword-options keyword-options-nofirst'">
                <div class="keyword-left"><span v-html="setRedFocus(search.dictLabel)"></span> </div>
                <div class="keyword-right">
                  <span v-if="search.selected" class="ed">+已选择</span>
                  <span v-else class="ing">+选择</span>
                </div>
              </div>
            </div>
          </div>
          <div v-if="showRadio">
            <el-radio-group class="download-radio-option" v-model="radio">
              <el-radio label="1" @click.native.stop.prevent='radioClick' size="mini">全选</el-radio>
              <el-radio label="2" @click.native.stop.prevent='radioClick2' size="mini">反选</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div class="tabs-container">
          <div class="tabs">
            <div v-for="(item,index) in comboData" :class='currentTabStyle(item,index)' :key="item.dictValue" @click="setCurrentTab(item)">
              {{item.dictLabel}}
            </div>
          </div>
          <div class="btns">
            <el-checkbox-group v-model="checkList" class="download-checkListClass" @change='changeCheckBox'>
              <el-checkbox v-for="btn in currentComboData.childrens" :key="btn.dictValue" :label="btn.dictLabel"></el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class="download-select">
          <div class="title"><span class="left">已选维度 &nbsp;&nbsp;(<span class="count">{{selections.length}}项</span>)</span><span class="right" @click="clearAllData"><i class="iconfont icon-shanchu"></i>清空已选</span></div>
          <div class="content">
            <span v-for="selectbtn in selections" class="content-btn" @click="deleteSelectBtn(selectbtn)">{{selectbtn.dictLabel}}<i class="el-icon-close el-icon--right"></i></span>
          </div>
        </div>
        <div class="download-select">
          <div class="title"><span class="left">导出格式（<a :href="`https://pro-files.qichacha.com/open/sample/data_terminal_datasample${isPROD ? '' : '-sit'}.zip?v=20230728`">数据样例</a>&nbsp;&nbsp;<a :href="datDicUrl">数据字典_枚举说明</a>）</span></div>
          <div class="content2">
            <el-radio-group v-model="selectedExportTypes" class="download-checkListClass">
              <el-radio v-for="exType in supperTypes" :key="exType.value" :label="exType.label"></el-radio>
            </el-radio-group>
          </div>
        </div>
        <div class=" download-margin  download-select" v-show="source==='3' || isAll">
          <div class="title"><span class="left">接收邮箱</span></div>
          <div class="content3">
            <span class="left"><span style="color:red"> *</span>邮箱：</span>
            <app-input style="width:100%" size='normal' placeholder="请输入邮箱" v-model="email" maxlength='100'></app-input>
          </div>
        </div>
      </div>
      <div slot="footerLeft" style="line-height: 32px">
        <span class="update-time"> <span class="iconfont icon-shuaxin2"></span> 数据更新于：{{updateDate}}</span>
      </div>
    </app-popup>
  </div>
</template>

<script src="./component.js">

</script>

<style lang="scss" scoped src="./style.scss">

</style>

<style lang="scss">
.select-download-container .el-dropdown {
  line-height: 30px;
}
  .customer-export-popup {
    ._popup_contentWrapper {

      ._contentDefault {
        padding: 0;
      }
    }
  }
.download-checkListClass .el-checkbox__label {
  padding-left: 4px;
}
</style>
