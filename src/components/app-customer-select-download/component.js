import { postVue } from '../../services/common-service'
import { loadingMixins } from '../../utils/mixinsUtils'
import dictUtil, { getDictListByType } from '../../utils/get-dict-utils'
import _ from 'lodash'
export default {
  name: 'app-customer-select-download',
  mixins: [loadingMixins],
  props: {
    keyNoList: { default: [] },
    source: { default: '' },
    functionCd: { default: '' },
    count: { default: '' },
    top: { default: '' },
    buttonTitle: {
      type: String,
      default: ''
    },
    // 导出所有企业KeyNo来源
    keyNoFrom: {
      type: String,
      default: 'data_terminal_list'
      // radar_list:风险监控列表, dd_list:尽职调查列表, data_terminal_list:数据终端列表
    },
    allCount: {
      default: ''
    },
    cropName: {
      type: String,
      default: ''
    },
    fromDate: {
      type: String,
      default: ''
    },
    endDate: {
      type: String,
      default: ''
    },
    companyFilter: {
      type: Object,
      default: function() {
        return {}
      }
    },
    riskCategory: {
      type: Array,
      default: function() {
        return []
      }
    },
    corpTagIds: {
      type: Array,
      default: function() {
        return []
      }
    },
    hiddenButton: {
      type: Boolean,
      default: false
    },
    historyFlag: {
      type: String,
      default: ''
    },
    getLimitCountFun: {
      type: Function, // Promise function
      default: undefined
    }
  },
  data() {
    return {
      isOuter: false,
      keyNoOutList: [],
      loadings: null,
      dependentCodes: [],
      dialogVisible: false,
      downloadPop: false,
      downloadEnable: false,
      radio: '',
      keyword: '',
      keywordSelectTop: '0px',
      keywordSelectLeft: '0px',
      keywordPop: false,
      options: {
        width: '960px',
        top: '3%'
      },
      popUpControl: { show: false },
      buttonOptions: [
        { name: '确定导出', notZhuge: true, type: 'primary', click: this.export }
      ],
      comboData: [],
      currentComboData: {},
      selections: [],
      checkList: [],
      supperTypes: [],
      selectedExportTypes: '',
      // datSampleUrl: '/template/数据样例.zip',
      datDicUrl: '/template/数据字典_枚举说明.xlsx?v=10',
      email: '',
      showRadio: true,
      updateDate: '',
      exportList: [
        {
          name: '导出选中（A）',
          value: 'selected'
        },
        {
          name: '导出全部（B）',
          value: 'all'
          }
      ],
      isAll: false,
      exportCount: 10000
    }
  },
  watch: {
    keyNoList() {
      this.isOuter = false
      this.keyNoOutList = []
    },
    top() {
      if (this.top) {
        this.options.top = this.top
      }
    },
    keyword() {
      this.keywordFocus()
    },
    checkList: {
      handler(newv, oldv) {
        let diffV = _.difference(newv, oldv)
        let dependentCode = ''

        this.selections = []
        this.comboData.forEach(dict => {
          dict.childrens.forEach(child => {
            let index = this.checkList.findIndex(e => e === child.dictLabel)
            if (index >= 0) {
              this.selections.push(child)
            }
            if (diffV && diffV.length > 0) {
              if (child.dictLabel === diffV[0]) {
                dependentCode = child.dependentCode
              }
            }
          })
        })

        let dependentItem = this.getDependent(dependentCode)
        if (dependentItem) {
          let index = this.checkList.findIndex(e => e === dependentItem.dictLabel)
          if (index < 0) {
            this.checkList.push(dependentItem.dictLabel)
          }
        }
      },
      deep: true
    }
  },
  computed: {
    keywordSearches() {
      let kws = []
      let that = this
      this.comboData.forEach(dict => {
        dict.childrens.forEach(child => {
          if (child.dictLabel.indexOf(that.keyword) >= 0) {
            child.selected = false
            that.selections.forEach((e) => {
              if (e.dictValue === child.dictValue) {
                child.selected = true
              }
            })
            kws.push(child)
          }
        })
      })
      return kws
    }
  },
  methods: {
    getExportCount() {
      if (this.getLimitCountFun && typeof this.getLimitCountFun === 'function') {
        this.getLimitCountFun().then(count => {
          this.exportCount = count
        })
      } else {
        dictUtil.getDictSystemConifg('max_export_corp_data_num', 10000, (data) => {
          this.exportCount = +data
        })
      }
    },
    functionCdSetRadio() {
      if (this.functionCd === 'expand_cust_full_adv_search' ||
        this.functionCd === 'expand_cust_chg_addr_search' ||
        this.functionCd === 'expand_cust_list_reg_capi' ||
        this.functionCd === 'expand_cust_list_map' ||
        this.functionCd === 'expand_cust_list_high_tech') {
        this.showRadio = false
      }
    },
    changeCheckBox(item) {
      // console.log('xxxxxxxxxxx', item)
    },
    clearAllData() {
      this.selections = []
      this.checkList = []
    },
    currentTabStyle(tab, index) {
      if (tab.dictValue === this.currentComboData.dictValue) {
        if (index === 0) {
          return 'tab is-active is-border-first'
        }
        return 'tab is-active is-border'
      }
      return 'tab'
    },
    setCurrentTab(item) {
      this.currentComboData = item
    },
    getCombos() {
      postVue('/corp_data/order/getDimensions', {}).then((data) => {
        if (data.status === '200') {
          this.updateDate = data.jsonStr
          this.dependentCodes = []

          data.resultList.forEach(element => {
            element.childrens.forEach(child => {
              if (child.dependentCode) {
                this.dependentCodes.push(child.dependentCode)
              }
            })
          })

          data.resultList.forEach(element => {
            let children = []
            element.childrens.forEach(child => {
              let index = this.dependentCodes.findIndex(e => e === child.dictValue)
              if (index < 0) {
                children.push(child)
              }
            })
            element.childrens = children
          })

          this.comboData = data.resultList
          this.currentComboData = this.comboData[0]
        }
      })
      let that = this
      getDictListByType('data_support_type', [], res => {
        this.supperTypes = res
      })

      postVue('/webapi/saas/corp_data/order/getDataTermEmail', {}).then((data) => {
        if (data.status === '200' && data.result) {
          that.email = data.result
        }
      })
    },
    getDependent(dependentCode) {
      let dependentItem = null
      if (dependentCode) {
        this.comboData.forEach(current => {
          current.childrens.forEach(child => {
            if (child.dictValue === dependentCode) {
              dependentItem = child
            }
          })
        })
      }
      return dependentItem
    },
    keywordClick(item) {
      let index = this.selections.findIndex(e => e.dictValue === item.dictValue)
      if (index < 0) {
        delete item.selected
        this.selections.push(item)
        this.checkList.push(item.dictLabel)
      }

      let dependentItem = this.getDependent(item.dependentCode)
      if (dependentItem) {
        let index2 = this.selections.findIndex(e => e.dictValue === dependentItem.dictValue)
        if (index2 < 0) {
          delete dependentItem.selected
          this.selections.push(dependentItem)
          this.checkList.push(dependentItem.dictLabel)
        }
      }
    },
    deleteSelectBtn(item) {
      let index = this.selections.findIndex(e => e.dictValue === item.dictValue)
      if (index >= 0) {
        // this.selections
        this.selections.splice(index, 1)
      }
      let index2 = this.checkList.findIndex(e => e === item.dictLabel)
      if (index2 >= 0) {
        // this.selections
        this.checkList.splice(index2, 1)
      }
    },
    radioClick() {
      this.radio = '1'
      this.selectChange(1)
    },
    radioClick2() {
      this.radio = '2'
      this.selectChange(2)
    },
    selectChange(flg) {
      if (flg === 1) {
        this.checkList = []
        this.comboData.forEach(dict => {
          dict.childrens.forEach(child => {
            this.checkList.push(child.dictLabel)
          })
        })
        this.selections = []
        this.comboData.forEach(dict => {
          dict.childrens.forEach(child => {
            this.selections.push(child)
          })
        })
      } else if (flg === 2) {
        let currentChecks = []
        this.comboData.forEach(dict => {
          dict.childrens.forEach(child => {
            let index = this.checkList.findIndex(e => e === child.dictLabel)
            if (index < 0) {
              currentChecks.push(child.dictLabel)
            }
          })
        })
        this.checkList = currentChecks

        let currentSelects = []
        this.comboData.forEach(dict => {
          dict.childrens.forEach(child => {
            let index = this.checkList.findIndex(e => e.dictValue === child.dictValue)
            if (index < 0) {
              currentSelects.push(child)
            }
          })
        })
        this.selections = currentSelects
      }
    },
    setRedFocus(text) {
      let reg = new RegExp('(' + this.keyword + ')', 'g')
      let newstr = text.replace(reg, '<font color="#F04040">$1</font>')
      return newstr
    },
    keywordFocus(flg) {
      let that = this
      this.$nextTick(() => {
        if (this.keyword && !flg) {
          this.keywordPop = true
        } else {
          setTimeout(() => {
            that.keywordPop = false
          }, 400)
        }
      })
    },
    exportClick(item) {
      this.isAll = item.value === 'all'
      this.cusomterClick()
      try {
        $util.zhugeTrackTwoLevel(`自定义导出-${item.name}`, window.AppRuntimeContext.instance.$route.meta?.title)
      } catch (e) { }
    },

    cusomterOutClick(keyNoList) {
      this.isOuter = true
      this.keyNoOutList = keyNoList
      this.loadingProp.show = false
      if (!this.keyNoOutList.length || this.keyNoOutList.length <= 0) {
        this.$message.warning('请选择企业')
        return
      }

      if (this.count && this.keyNoOutList.length > (+this.count)) {
        this.$message.warning('当前选中企业已超出最大导出量（' + this.count + '）')
      }
      this.checkLimitCount().then(this.init)
    },
    checkLimitCount() {
      let params = {
        keyNoList: [],
        dimensions: '',
        type: '',
        source: this.source,
        email: '',
        functionCd: this.functionCd,
        allCorpKeyNoFrom: this.isAll ? this.keyNoFrom : ''
      }
      return postVue('/corp_data/order/validateDailyExportLimitCount', params)
    },
    cusomterClick() {
      this.isOuter = false
      this.keyNoOutList = []
      this.loadingProp.show = false

      if (this.isAll) {
        if (+this.allCount > this.exportCount) {
          this.$message.warning('您列表的企业数量已超出最大导出量（' + this.exportCount + '），可先企业筛选再进行导出')
          return
        }
      } else {
        if (!this.keyNoList.length || this.keyNoList.length <= 0) {
          this.$message.warning('请选择企业')
          return
        }
        if (this.count && this.keyNoList.length > (+this.count)) {
          this.$message.warning('当前选中企业已超出最大导出量（' + this.count + '）')
          return
        }
      }
      this.checkLimitCount().then(this.init)
    },
    init() {
      this.keyword = ''
      this.popUpControl.show = true
      this.selections = [{ dictValue: 'AA', dictLabel: '工商信息' }]
      this.checkList = ['工商信息']
      this.selectedExportTypes = 'Excel'
      this.radio = ''
      // this.email = ''
      this.currentComboData = this.comboData[0]
    },
    know() {
      this.downloadPop = false
      localStorage.setItem('downloadKnow', '1')
    },
    resetKnow() {
      let downloadKnow = localStorage.getItem('downloadKnow')
      if (downloadKnow === '1') {
        this.downloadPop = false
        this.downloadEnable = true
      } else {
        this.downloadPop = true
      }

      if (this.functionCd) {
        this.downloadPop = false
      }
    },
    loading() {
      this.loadings = this.$loading({
        lock: true,
        text: '拼命加载中',
        background: 'rgba(255,255,255,0.95)',
        customClass: 'loading-customClass'
      })
    },
    export () {
      $util.zhugeTrackTwoLevel('自定义导出-确定', $util.getCurrentPageTitle(window.AppRuntimeContext.instance))
      if (this.selections.length <= 0) {
        this.$message.warning('请选择要导出的维度')
        return
      }
      if (!this.selectedExportTypes) {
        this.$message.warning('请选择要导出数据格式')
        return
      }

      if (this.source === '3' || this.isAll) {
        if (this.email) {
          if (!$util.checkIsEmail(this.email)) {
            this.$message.warning('输入的邮箱格式不正确')
            return
          }
        } else {
          this.$message.warning('输入的邮箱格式不正确')
          return
        }
      }

      let wdValue = []
      this.selections.forEach(sel => {
        if (sel.dependentCode) {
          let deptIndex = wdValue.findIndex(e => e === sel.dependentCode)
          if (deptIndex < 0) {
            wdValue.push(sel.dependentCode)
          }
        }
        wdValue.push(sel.dictValue)
      })

      let selectType = ''
      this.supperTypes.forEach(supper => {
        if (supper.label === this.selectedExportTypes) {
          selectType = supper.value
        }
      })
      // 导出全部
      if (this.isAll) {
        this.exportAll(wdValue, selectType)
        return
      }
      this.loadingProp.show = true
      /// source:功能来源：1-企业详情；2-企业列表；3-数据导入
      let knl = this.isOuter ? this.keyNoOutList : this.keyNoList

      let params = {
        keyNoList: this.isAll ? [] : knl,
        dimensions: wdValue.toString(),
        type: selectType,
        source: this.source,
        email: this.email,
        functionCd: this.functionCd,
        allCorpKeyNoFrom: this.isAll ? this.keyNoFrom : '',
        historyFlag: this.historyFlag ? this.historyFlag : ''
      }
      postVue('/corp_data/order/orderData?showError', params).then((data) => {
        this.loadingProp.show = false
        if (data.status === '200') {
          this.$refs.appPopDoload.close()
          this.popUpControl.show = false
          // this.$message.success('您的报告正在生成中，成功后我们将第一时间提醒您 单据ID:' + data.result)
          this.$message.success('后台正在努力导出中，成功后我们将第一时间提醒您')
          this.$emit('downloadProcess')
        } else {
          this.$message.error(data.msg)
          this.loadingProp.show = false
        }
      }).catch(() => {
        this.loadingProp.show = false
        })
    },
    exportAll(wdValue, selectType) {
      this.loadingProp.show = true
      let params = {
        dimensions: wdValue.toString(),
        type: selectType,
        source: this.source,
        email: this.email,
        allCorpKeyNoFrom: this.keyNoFrom,
        condition: {
          corpName: this.cropName,
          selCity: this.companyFilter.selCity,
          selDistrict: this.companyFilter.selDistrict,
          selProvince: this.companyFilter.selProvince,
          selectGroupName: this.companyFilter.selectGroupName,
          selectGroupValue: this.companyFilter.selectGroupValue,
          selectGroupValueList: this.companyFilter.selectGroupValueList,
          ...this.companyFilter.selectedTopicIdList ? {
            selectedTopicIdList: this.companyFilter.selectedTopicIdList
          } : {},
          selectMode: this.companyFilter.selectMode,
          selectValue: this.companyFilter.selectValue,
          selectValueList: this.companyFilter.selectValueList
          // selectNewGroupValueList: this.companyFilter.selectGroupValueList,
          // selectNewGroupName: '企业分组'
        }
      }

      // 下单-数据终端导出所有条件
      var url = '/webapi/saas/corp_data/order/orderData4ExportAllDataTerminal'
      if (this.keyNoFrom === 'radar_list' || this.keyNoFrom === 'dd_list') {
        url = '/webapi/saas/corp_data/order/orderData4ExportAllCorpList'

        params = {
          dimensions: wdValue.toString(),
          type: selectType,
          source: this.source,
          email: this.email,
          allCorpKeyNoFrom: this.keyNoFrom,
          condition: {
            from: this.keyNoFrom === 'radar_list' ? 'monitor' : '',
            riskCategory: this.riskCategory,
            corpTagIds: this.corpTagIds,
            corpName: this.cropName,
            functionTableId: this.keyNoFrom === 'radar_list' ? 'radar_id' : 'bene_id',
            selCity: this.companyFilter.selCity,
            selDistrict: this.companyFilter.selDistrict,
            selProvince: this.companyFilter.selProvince,
            selectGroupName: this.companyFilter.selectGroupName,
            selectGroupValue: this.companyFilter.selectGroupValue,
            // selectGroupValueList: this.companyFilter.selectGroupValueList,
            ...this.companyFilter.selectedTopicIdList ? {
              selectedTopicIdList: this.companyFilter.selectedTopicIdList
            } : {},
            ...(this.companyFilter.riskTypes && {
              rygGradeList: this.companyFilter.riskTypes
            }),
            selectMode: this.companyFilter.selectMode,
            selectValue: this.companyFilter.selectValue,
            selectValueList: this.companyFilter.selectValueList,
            fromDate: this.fromDate,
            endDate: this.endDate,
            selectNewGroupValueList: this.companyFilter.selectGroupValueList,
            selectNewGroupName: '企业分组'
          }
        }
      }
      postVue(url, params).then((data) => {
        this.loadingProp.show = false
        if (data.status === '200') {
          this.$refs.appPopDoload.close()
          this.popUpControl.show = false
          this.$message.success('后台正在努力导出中，成功后我们将第一时间提醒您')
          this.$emit('downloadProcess')
        } else {
          this.$message.error(data.msg)
        }
      }).catch(() => {
        this.loadingProp.show = false
      })
    }
  },
  created() {
    this.getCombos()
  },
  mounted() {
    this.options.top = this.top
    this.getExportCount()
    this.functionCdSetRadio()
    this.resetKnow()
    this.loadingProp.show = false
  }
}
