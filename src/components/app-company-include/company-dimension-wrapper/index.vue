<template>
  <div :class="['company-dimension-wrapper', {'subtitle': sub}]" :id="id">
    <div class="_header-line" v-if="title">
      <h4 class="title">
        {{title}}
        <template v-if="!hidefirstLevelCountMore">
          <span class="count">{{weiduCount}}</span>
          <span class="check-more" @click="$emit('clickMore')" v-if="moreCount && `${moreCount}` !== '0'">
            (查看更多 <span class="moretext">{{moreText}} {{moreCount}} </span>)
          </span>
        </template>
      </h4>

      <div class="right-filter">
        <slot name="filter"></slot>
      </div>
    </div>

    <slot name="topContent"></slot>

    <div v-if="tab && tab.length" :class="['_tab', {'none-border': tab.length === 1}]">
      <span v-for="(item,index) in tab" :index="`tab-${index}`" :class="['_tab-item',{active: activatedIndex === index}]" @click="clickTabMenu(item,index)">
        <span v-html="getTabName(item)" />
      </span>
      <slot name="tab-desc"></slot>
      <div class="tab-right-filter">
        <slot name="tab-filter"></slot>
      </div>
    </div>
    <slot></slot>
  </div>

</template>

<script>
  export default {
    name: 'company-dimension-wrapper',
    props: {
      id: { default: '', type: String },
      title: { default: '', type: String },
      sub: { defaut: false, type: Boolean },
      tab: { default: undefined, type: Array },
      tabActived: { default: 0, type: [String, Number] },
      moreCount: { default: '', type: [Number, String] },
      moreText: { default: '', type: String },
      hidefirstLevelCountMore: { default: false, type: Boolean },
      count: { default: '', type: [String, Number] }
    },
    data() {
      return {
        activatedIndex: 0
      }
    },
    watch: {
      tabActived(val) {
        this.activatedIndex = val
      }
    },
    computed: {
      weiduCount() {
        let res = 0
        if (this.count !== '') {
          return this.count
        }
        if (this.$parent.getPartCount) {
          res = this.$parent.getPartCount(this.title)
        } else if (this.$parent.$parent.getPartCount) {
          res = this.$parent.$parent.getPartCount(this.title)
        }
        return res
      }
    },
    methods: {
      getTabName(item) {
        if (item.getTitle) {
          return item.getTitle()
        } else {
          if (item.count) {
            return `${item.title} ${item.count}`
          }
          return item.title
        }
      },
      clickTabMenu(item, index) {
        if (this.tab.length === 1) {
          return
        }
        this.activatedIndex = index
        this.$emit('update:tabActived', index)
        this.$emit('tabChange', item, index)
      }
    }
  }

</script>

<style scoped lang="scss" src="./style.scss"></style>
