@import "../../../styles/common";

.company-dimension-wrapper {
  &.subtitle {
    ._header-line .title {
      font-size: 14px;
      font-weight: normal;
      border-left: none;
      padding-left: 0;
    }
  }

  ._header-line {
    padding-top: 30px;
    position: relative;
    margin-bottom: 15px;

    .title {
      box-sizing: border-box;
      display: inline-block;
      font-size: 16px;
      color: #666666;
      font-weight: 600;
      border-left: $color-primary 3px solid;
      padding-left: 5px;
      line-height: 21px;
      height: 21px;

      .check-more {
        font-size: 14px;
        color: rgb(153, 153, 153);
        cursor: pointer;
        font-weight: normal;

        .moretext {
          color: rgb(245, 166, 35);
        }
      }
    }

    .count {
      color: $color-primary;
      margin-left: 5px;
      font-weight: bold;
      font-size: 16px;
    }

    .right-filter {
      position: absolute;
      right: 0;
      bottom: -4px;
      display: inline-flex;
      width: auto;
    }
  }

  ._tab {
    // height: 43px;
    // padding-top: 15px;
    box-sizing: border-box;
    margin-bottom: 15px;
    position: relative;

    &.none-border {
      margin-bottom: 10px;

      ._tab-item.active {
        border: none;
        color: rgb(96, 98, 102);
        font-weight: normal;
      }

      ._tab-item {
        padding-bottom: 0px;
        line-height: normal;
      }
    }

    ._tab-item {
      border-bottom: 2px solid transparent;
      color: #666666;
      //font-weight: bold;
      line-height: 28px;
      padding-bottom: 5px;
      margin-left: 20px;
      cursor: pointer;
      font-size: 14px;

      &.active {
        border-color: $color-primary;
        color: $color-primary;
      }
    }

    ._tab-item:first-child {
      margin-left: 0;
    }

    .tab-right-filter {
      position: absolute;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
    }
  }
}
