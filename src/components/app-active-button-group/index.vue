<template>
  <div class="app-active-button-group">
    <app-active-button class="app-active-button-item" v-for="(item, index) in value" :key="'active-btn-' + index"
      :color="item.color" :backgroundColor="item.backgroundColor" :text="item.label" :actived="item.actived"
      @click="activeButtonClick(item, index)"></app-active-button>
  </div>
</template>

<script>
export default {
  name: 'app-active-button-group',
  props: {
    // [{label:'',value:'',color:'',backgroundColor:''}]
    // 绑定的值
    value: {
      type: Array,
      default: () => []
    }
  },
  model: {
    prop: 'value',
    event: 'click'
  },
  watch: {
    // 监听用户的值
    value(newValue) {

    }
  },
  methods: {
    activeButtonClick(item, index) {
      this.value.forEach(elm => {
        if (elm.label === item.label) {
          elm.actived = true
        } else {
          elm.actived = false
        }
      })
      this.$forceUpdate()
      this.$emit('click', this.value)
      this.$emit('change', item)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-active-button-group {
  .app-active-button-item {
    margin-left: 20px;

    &:first-child {
      margin-left: 0px;
    }
  }
}
</style>


