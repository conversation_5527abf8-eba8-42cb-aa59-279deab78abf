<template>
  <el-popover class="app-glossary-info"
    :popper-class="` app-popover  ${popoverClass ? (popoverClass + ' small-popover-20 ') : 'small-popover'}   ` + (isBlack ? ' default-popover-tooltip' : '')"
    :placement="placement"
    :trigger="tigger"
    :open-delay="100"
    :close-delay="150"
    :append-to-body="appendToBody">
    <slot name="reference" slot="reference">
      <span :class="['iconfont', iconClass]" :style="iconStyle"></span>
    </slot>
    <slot name="content">
      <div v-for="(item, index) in explain" :key="`glossary-content_${index}`">
        <div v-if="item.title" class="title" v-html="item.title"></div>
        <div v-if="item.text" class="text" :class="{'need-pre-wrap': needPreWrap}" v-html="item.text"></div>
      </div>
    </slot>
  </el-popover>
</template>

<script>
import glossaryMapData from './glossary-map'
import { requestService } from '@/services/broswer-service'
import { isTouchDevice } from '@/routes/companyDetail/hooks/company-detail.hook'
let glossaryIds = []
let glossaryMap = {}

export default {
  name: 'app-glossary-info',
  props: {
    infoId: {
      type: String
    },
    placement: {
      type: String,
      default: 'bottom'
    },
    iconClass: {
      type: String, default: 'icon-shuoming1'
    },
    iconStyle: {
      type: Object
    },
    isBlack: {
      type: Boolean,
      default: true
    },
    popoverClass: {
      type: String
    },
    needPreWrap: {
      type: Boolean,
      default: false
    },
    appendToBody: { type: Boolean, default: true }
  },
  data() {
    return {
      explain: null,
      tigger: __PLUGIN__ && isTouchDevice ? 'click' : 'hover'
    }
  },

  created() {
    if (this.infoId && !glossaryIds.includes(this.infoId)) {
      glossaryIds.push(this.infoId)
      this.getExplainById()
    }
  },
  mounted() {
    this.getGlossary()
  },
  methods: {
    getGlossary() {
      if (!glossaryMap[this.infoId]) {
        setTimeout(() => {
          this.getGlossary()
        }, 100)
      } else {
        this.explain = glossaryMap[this.infoId]
      }
    },
    getExplainById() {
      let findData = glossaryMapData.find(item => item.glossaryId === this.infoId)
      if (findData) {
        try {
          glossaryMap[findData.glossaryId] = JSON.parse(findData.glossaryExplain)
        } catch { }
        this.explain = glossaryMap[this.infoId]
        return
      }


      requestService.getGlossaryInfo({
        glossaryId: this.infoId,
        terminalType: 2
      }).then(data => {
        let list = []
        try { list = JSON.parse(data.result) } catch (e) { }

        list.forEach(item => {
          if (!glossaryMap[item.glossaryId]) {
            try {
              glossaryMap[item.glossaryId] = JSON.parse(item.glossaryExplain)
            } catch { }
          }
        })
        this.explain = glossaryMap[this.infoId]
      })
    }
  }
}
</script>

<style scoped lang="scss">
.anticon {
  display: inline-block;
  color: #d8d8d8;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  outline: none;
}

.app-glossary-info {
  position: relative;
  margin-left: 4px;
  top: 1px;
  font-size: 16px;
  ::v-deep {
    b {
      font-weight: bold;
    }
  }
}

.iconfont {
  display: inline-block;
  font-size: 16px;
  cursor: auto;
  font-weight: normal;
  color: #d8d8d8;
  &.font-14 {
    font-size: 14px;
  }
  &.font-12 {
    font-size: 12px;
  }
}

.need-pre-wrap {
  white-space: pre-wrap;
}

.text {
  white-space: pre-wrap;
}
</style>
