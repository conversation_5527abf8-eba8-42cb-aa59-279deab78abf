<template>
  <el-upload class="upload-img-wrapper" :class="[{notAllowSelect: fileList.length >= 4}]" action="/app-upload" :limit="4" :file-list="fileList" accept="image/*" :before-upload="beforeUpload" list-type="picture-card" :onSuccess="onSuccess">
    <i slot="default" class="el-icon-plus"></i>
    <div slot="file" slot-scope="{file}">
      <img class="el-upload-list__item-thumbnail" :src="file.url" alt="">
      <span class="el-upload-list__item-actions">
        <span class="el-upload-list__item-delete" @click="handleRemove(file)">
          <i class="el-icon-delete"></i>
        </span>
      </span>
    </div>
  </el-upload>
</template>

<script>
  export default {
    name: 'upload-img',
    data() {
      return {
        fileList: []
      }
    },
    methods: {
      handleRemove(file) {
        this.fileList.splice(this.fileList.indexOf(file), 1)
      },
      onSuccess(response, file, fileList) {
        this.fileList = fileList
      },
      getUploadedPic() {
        return this.fileList.map(v => v.response)
      },
      beforeUpload(file) {
        const isIMG =
          file.type === 'image/jpg' ||
          file.type === 'image/jpeg' ||
          file.type === 'image/png'
        const isLt = file.size / 1024 / 1024 / 5 <= 1
        if (!isIMG) { this.$message.error('图片只支持jpg、jpeg、png格式!') }
        if (!isLt) { this.$message.error('上传图片大小不能超过5MB!') }
        return isLt && isIMG
      }
    }
  }

</script>

<style lang="scss">
  .upload-img-wrapper {
    max-height: 110px;
    overflow: hidden;

    &.notAllowSelect {
      .el-upload.el-upload--picture-card {
        display: none;
      }
    }

    .el-upload.el-upload--picture-card {
      width: 110px;
      height: 110px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 0px;
      background: white;
      border: 1px solid #D8d8d8;

      &:hover {
        border-color: #128bed;

        .el-icon-plus {
          color: #128bed;
        }
      }

      .el-icon-plus {
        color: #128bed;
      }
    }

    .el-upload-list--picture-card .el-upload-list__item {
      width: 110px;
      height: 110px;
      margin-right: 10px;
      margin-bottom: 0px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      vertical-align: top;
      border-color: #D8d8d8;
    }

    .el-upload-list__item.is-success {
      &:nth-child(4n) {
        margin-right: 0;
      }
    }
  }

</style>
