<template>
  <div class="app-consult flex" :class="[{animate:show}, {'is-jw': isJSJW}]" @mouseenter="enter" @mouseleave="leave">
    <div class="left">
      咨<br />询<br />·<br />反<br />馈
    </div>
    <div class="right">
      <template v-if="!isJW">
        <div class="info-item padding-1x" style="cursor: pointer;"
             onclick="window.open('/openOnlineChart?href=' + encodeURIComponent(window.location.href));zhugeTrackTwoLevel('点击在线咨询', window.currentPagename)">
          <img src="/img/online-chart-icon.png" alt="在线咨询" style="width: 15px;vertical-align: middle;" />
          <span class="cslt-title">在线咨询</span>
        </div>
        <div class="info-item padding-1x" @click='showBusiness' style="cursor: pointer;">
          <img src="../../assets/images/app-consult/business.png" style="width: 14px;" />
          <span class="cslt-title">客户经理</span>
        </div>
        <div class="info-item padding-1x" style="cursor: pointer;" @click="suggest">
          <img src="../../assets/images/app-consult/suggestion.png" />
          <span class="cslt-title pointer">意见反馈</span>
        </div>
      </template>
      <template v-else>
        <div class="info-item padding-1x" style="cursor: pointer;" @click="suggest">
          <img src="../../assets/images/app-consult/suggestion.png" />
          <span class="cslt-title pointer">意见反馈</span>
        </div>

        <div class="info-item padding-1x" @click='showBusiness' style="cursor: pointer;">
          <img src="../../assets/images/app-consult/business.png" style="width: 14px;" />
          <span class="cslt-title">专属顾问</span>
        </div>
        <div class="info-item padding-1x" @click="showMini(true, 2)">
          <img src="../../assets/images/app-consult/wechart.png" style="width: 15px;top: 2px;" class="img" />
          <span class="cslt-title">技术支持</span>
        </div>
      </template>

      <div class="info-item padding-1x">
        <img src="../../assets/images/app-consult/email.png" />
        <span class="cslt-title">合作邮箱</span>
        <span class="cslt-detail">
          <a href="mailto:<EMAIL>"><EMAIL></a>
        </span>
      </div>
      <div class="info-item padding-1x" @click="showMini(true, 1)">
        <img src="../../assets/images/app-consult/relation.png" />
        <span class="cslt-title pointer">微信小程序</span>
      </div>
      <div class="info-item padding-1x" @click="showModal(true)" style="border-bottom: none;" v-if="!isJSJW">
        <img src="../../assets/images/app-consult/wechart.png" />
        <span class="cslt-title pointer">微信公众号</span>
      </div>
      <div class="wechat-detail" v-if="modal" @click="showModal(false)">
        <img src="/img/wechat_pub.png?v=2" width="140px" />
      </div>
      <div class="wechat-detail" v-show="modal1" @click="showMini(false)">
        <img :src="qrcodeSrc" width="140px" />
      </div>
    </div>
  </div>
</template>

<script>
  import feedback from './feedback'
  import _ from 'lodash'
  const jwImg = __JSJW__ ? '/img/app-consult/qrcode-jsjw.png' : '/img/app-consult/qrcode-jw.png'
  export default {
    name: 'app-consult',
    components: { feedback },
    data() {
      return {
        modal: false,
        modal1: false,
        show: false,
        lock: false,
        showPop: false,
        isJW: $util.isJSJW(),
        qrcodeSrc: ''
      }
    },
    methods: {
      enter: _.debounce(
        function() {
          this.show = true
        },
        1000, { leading: true }
      ),

      leave: _.debounce(
        function() {
          this.show = false
        },
        1000, { trailing: true }
      ),
      showModal(i) {
        this.modal = i
      },
      showMini(i, type) { // type  1:小程序码  2：客服
        if (i) {
          if (type === 1) {
            this.qrcodeSrc = $util.isJSJW() ? jwImg : '/img/revision/xcxewm.png?v=3'
          } else {
            this.qrcodeSrc = '/img/app-consult/jw-customer.png'
          }
        }
        this.modal1 = i
      },
      suggest() {
        this.leave()
        this.$uiService.showDialog(feedback, {})
      },
      submit() {
        this.$refs.feedback.submit()
      },
      showBusiness() {
        if (this.$route.name === 'login' || this.$route.name === 'forgetPwd') {
          $util.enterCustomerService(true)
        } else {
          $util.enterCustomerService()
        }
      }
    }
  }

</script>

<style lang="scss" scoped>
  .app-consult {
    width: 200px;
    height: 325px;
    position: fixed;
    right: -200px;
    bottom: 128px;
    transition: right 1s;
    z-index: 111;

    &.is-jw {
      height: 276px;
      //.left {
      //  margin-top: 70px;
      //}
    }

    &:hover {
      right: 0;
    }

    .left {
      height: 125px;
      width: 32px;
      line-height: 20px;
      padding: 15px 5px 10px 5px;
      background: #128BED;
      color: #FFFFFF;
      font-size: 14px;
      border-radius: 8px 0 0 8px;
      cursor: pointer;
      text-align: center;
      position: absolute;
      left: -32px;
      top: calc(50% - 63px)
    }

    .right {
      background: #FFFFFF;
      border: 1px solid #F5F5F5;
      box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.10);
      height: 100%;
      width: 202px;
      position: relative;

      .info-item {
        border-bottom: 1px solid #F5F5F5;
        line-height: 19px;

        .cslt-title {
          color: #666666;
          font-size: 14px;
        }

        .cslt-detail {
          display: block;
          font-size: 14px;
          color: #999999;
          margin-left: 18px;
          line-height: 22px;

          a {
            font-size: 12px;
            border-bottom: none;
            cursor: pointer;
            color: #999999;
          }
        }

        a {
          font-size: 14px;
          border-bottom: none;
          cursor: pointer;
        }

        img {
          width: 14px;
          margin-top: -1px;
        }

        span {
          img {
            width: 9px;
            margin-top: -1px;
          }
        }

        .img {
          position: relative;
          top: 3px;
        }
      }

      .wechat-detail {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        background: rgba(0, 0, 0, 0.40);

        img {
          position: absolute;
          transform: translate(-50%, -50%);
          top: 50%;
          left: 50%;
          border-radius: 5px;
        }
      }
    }
  }

  .animate {
    right: 0;
  }


  @keyframes mymove {
    from {
      right: -205px;
    }

    to {
      right: 0px;
    }
  }

  @-webkit-keyframes mymove

  /*Safari and Chrome*/
    {
    from {
      right: -205px;
    }

    to {
      right: 0px;
    }
  }

</style>
<style lang="scss">
  .app-consult {
    ._footer-right {
      right: 30px !important;
    }
  }

</style>
