<template>
  <app-popup ref="pop" :title="title" class="popup" :control="popUpControl" :options="popOptions" :buttonOptions="buttonOptions">
    <div class='feedback'>
      <div class='group flex flex-end'>
        <div class='left'>姓名：</div>
        <div class='right'>
          <app-input size='medium' placeholder="请输入您的姓名" v-model="name"></app-input>
        </div>
      </div>
      <div class='group flex flex-end'>
        <div class='left'><span class="required" v-if="!isJSJW">*</span>联系方式：</div>
        <div class='right'>
          <app-input size='medium' placeholder="请输入手机号码或者邮箱" v-model="mobile"></app-input>

        </div>
      </div>
      <div class='group flex flex-end'>
        <div class='left' style="line-height: 50px;"><span class="required">*</span>{{ feedback_label }}</div>
          <div class='right' style='padding-bottom:0'>
            <el-input type='textarea' :rows='5' :placeholder='feedback_placeholder' v-model='textarea' :autosize='autosize'></el-input>
          </div>
        </div>
        <div class='group flex flex-end' style="margin-top: 10px" v-if="isLogin">
          <div class='left'>上传图片：</div>
          <div class='right' style='padding-bottom:0'>
            <upload-img ref="uploadImgRef" />
          </div>
        </div>
        <p class='tip'>{{isJSJW ? '' : '非常感谢您对企查查专业版的关注，'}}欢迎提出宝贵的意见和建议，我们将积极采纳，及时为您解答问题，更好的改善我们的服务，为您提供更贴心的服务</p>
      </div>
  </app-popup>
</template>


<script>
  import feedbackServices from '../../services/feedback'
  import { postVue } from '../../services/common-service'
  import localstorage, { fetchUser } from '../../utils/localstorage'

  export default {
    components: {
      uploadImg: () => import('./upload-img')
    },
    data() {
      let isLogin = JSON.stringify(fetchUser() || {}) !== '{}'
      return {
        isLogin,
        popUpControl: { show: true },
        popOptions: {
          contentStyle: {
            paddingTop: '5px'
          }
        },
        buttonOptions: [
          { name: '提交反馈', type: 'primary', click: this.submit }
        ],
        title: '意见反馈',
        feedback_label: '反馈内容：',
        feedback_placeholder: '请在这里直接填写您遇到的问题或意见建议，您的意见是我们前进的动力',
        feedback_warning: '请输入反馈内容',
        saveMethod: 'save2',
        name: '',
        mobile: '',
        textarea: '',
        autosize: { minRows: 5, maxRows: 10 }
      }
    },
    methods: {
      close() {
        this.$refs.pop.close()
      },
      submit() {
        const { name, mobile, textarea } = this
        if (!mobile && !this.isJSJW) {
          this.$message({
            message: '请输入联系方式',
            type: 'warning'
          })
          return
        }


        if (!textarea.trim()) {
          this.$message({
            message: this.feedback_warning,
            type: 'warning'
          })
          return
        }

        if (mobile && !$util.checkIsEmail(mobile) && !$util.checkIsMobile(mobile)) {
          this.$message({
            message: '联系方式不正确，请输入正确的手机号或者邮箱',
            type: 'warning'
          })
          return
        }
        const params = {
          dataEntity: {
            name,
            mobile,
            content: textarea,
            imagePath: this.$refs.uploadImgRef ? this.$refs.uploadImgRef.getUploadedPic().join(',') : '',
            category: this.category
          }
        }
        feedbackServices[this.saveMethod](params).then(res => {
          if (res.status !== '200') {
            this.$message({
              message: res.msg,
              type: 'error'
            })
            return
          }
          this.$message({
            message: '反馈提交成功',
            type: 'success'
          })
          this.close()
        })
      },
      getUserInfo() {
        postVue('/sys/userinfo/init/getUserInfo', {}).then((data) => {
          if (data.status === '200') {
            if (data.result && data.result.user) {
              this.name = data.result.user.name
              if (!this.isJSJW) {
                this.mobile = data.result.user.mobile
              }
            }
          }
        })
      }
    },
    mounted() {
      if (localstorage.fetchUser().accessToken) {
        this.getUserInfo()
      }
      if (this.category === 'CE') {
        this.title = '纠错'
        this.feedback_label = '具体描述：'
        this.feedback_placeholder = '亲爱的用户，请在这里描述具体的错误信息，后续我们将予以跟踪并及时反馈您'
        this.buttonOptions[0].name = '提交'
        this.feedback_warning = '请具体描述错误信息'
        this.saveMethod = 'ddSave'
      }
      if (this.category === 'CCHART') {
        this.textarea = '我有意向通过额外付费方式采购此数据。'
      }
    }
  }

</script>




<style lang="scss" scoped>
  .feedback {
    width: 100%;

    .group {
      margin: 0 auto;

      .left {
        line-height: 56px;
        font-size: 14px;
        color: #666666;
        width: 81px;
        text-align: right;

        .required {
          font-size: 14px;
          color: #F04040;
        }
      }

      .right {
        // width: 398.5px;
        width: 479px;
        padding: 10px 0px 10px 0px;
      }

    }

    .tip {
      float: right;
      width: 478px;
      font-size: 12px;
      color: #999999;
      letter-spacing: 0;
      text-align: justify;
      line-height: 18px;
      padding-top: 5px;
      //padding-left: 5px;
    }
  }

</style>
<style lang="scss">
  .feedback {
    .el-textarea {
      .el-textarea__inner {
        // resize: none;
        max-height: 350px;
        padding-left: 10px;
        padding-right: 10px;
      }
    }


  }

</style>
