<template>
  <div class="_loading-wrapper" :style="getWrapperStyle">
    <div class="_default-content" :class="[{horizontal: isShowQCCImg && !penguinAnimation}]">
      <div class="el-loading-spinner" style="position: relative;">
        <div class="penguin-animation" v-if="penguinAnimation && isShowQCCImg"></div>
        <template v-else-if="isShowQCCImg">
          <qcc-logo-gif></qcc-logo-gif>
        </template>
        <svg v-else viewBox="25 25 50 50" class="circular" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <circle cx="50" cy="50" r="20" fill="none" class="path"></circle>
        </svg>
      </div>
      <div v-if="!isShowQCCImg && !hideText" id="loadingTxtId" class="txt" :class="isShowQCCImg ? '__qcc-txt' : ''">{{ getLoadingText() }}</div>
    </div>
  </div>
</template>

<script>
import { isIE } from '../../utils/function'
import { windowResizeMixins } from '../../utils/mixinsUtils'
import qccLogoGif from './qcc-logo-gif'

export default {
  name: 'app-loading',
  mixins: [windowResizeMixins],
  components: {
    qccLogoGif
  },
  props: {
    control: {
      type: Object
    },
    top: {
      default: 0
    },
    left: {
      default: 0
    },
    zIndex: {
      default: 999999,
      type: [Number, String]
    },
    text: {
      default: '拼命加载中',
      type: String
    },
    isProgressLoading: {
      default: false,
      type: Boolean
    },
    fullText: {
      default: '',
      type: String
    },
    importTitle: {
      type: String,
      default: '正在导入'
    },
    isTextCenter: {
      type: Boolean,
      default: false
    },
    hideText: {
      type: Boolean,
      default: !__PLUGIN__
    }

  },
  data () {
    return {
      penguinAnimation: false,
      bodyWidth: document.body.offsetWidth
    }
  },
  computed: {
    isShowQCCImg () {
      return __QCC__ && !__PLUGIN__
    },
    getTextStyle () {
      return isIE() ? 'margin-top: calc(50% - 15px)' : ''
    },
    getWrapperStyle () {
      let top = this.top_ ? this.top_ : (this.top ? this.top : 0)
      let left = this.left_ ? this.left_ : (this.left ? this.left : 0)
      let res = `top:${top};left:${left};`
      if (top.toString() !== '0') {
        res += `height:calc(100${this.alertLoading ? 'vh' : '%'} - ${top});`
      }
      if (left.toString() !== '0') {
        res += `width:calc(100% - ${left});`
        if (this.alertLoading && window.innerWidth < 1300) {
          res += `min-width:calc(${this.bodyWidth}px - ${left});`
        }
      }
      res += `z-index:${this.zIndex};`
      return res
    },
    getFullText () {
      return this.fullText || this.fullText_ || ''
    }
  },
  methods: {
    onWindowSizeChange () {
      this.bodyWidth = document.body.offsetWidth
    },
    getImportTitle () {
      if (this.importTitle_) {
        return this.importTitle_
      } else {
        return this.importTitle
      }
    },
    getLoadingText () {
      if (this.text_) {
        return this.text_
      } else if (this.text) {
        return this.text
      } else {
        return '拼命加载中'
      }
    }
  }

}

</script>

<style scoped lang="scss">
._loading-wrapper {
  height: 100%;
  width: 100%;
  background: rgb(255, 255, 255);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 99999;
  display: -webkit-box;
  /* 老版本语法: Safari, iOS, Android browser, older WebKit browsers. */
  display: -moz-box;
  /* 老版本语法: Firefox (buggy) */
  display: -ms-flexbox;
  /* 混合版本语法: IE 10 */
  display: -webkit-flex;
  /* 新版本语法: Chrome 21+ */
  display: flex;
  /* 新版本语法: Opera 12.1, Firefox 22+ */
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-direction: normal;
  -webkit-box-orient: vertical;
  flex-direction: column;
  min-height: 200px;
  font-size: 14px;

  ._default-content {
    position: relative;
    text-align: center;

    &.horizontal {
      display: flex;
      align-items: center;

      .el-loading-spinner {
        width: auto;
        height: 60px;

        .loading-img {
          width: 60px;
          height: 60px;
          margin-left: 0;
        }
      }

      .txt.__qcc-txt {
        margin-top: 0;
        margin-left: 15px;
      }
    }
  }

  .txt {
    color: #999999;
    font-size: 14px;
    margin-top: 15px;
    text-align: center;

    &.__qcc-txt {
      margin-top: 15px;
    }
  }
}

.el-loading-spinner {
  top: 0;
  margin-top: 0;
}

.penguin-animation {
  height: 80px;
  background: url('../../assets/images/loading/loading.gif') no-repeat;
  background-size: 100%;

  @keyframes penguin-animation {
    0% {
      opacity: 1;
    }

    11% {
      opacity: 0.6;
    }

    22% {
      background-image: url('../../assets/images/loading/img-2.png');
    }

    33% {
      opacity: 1;
    }

    44% {
      opacity: 0.6;
    }

    55% {
      background-image: url('../../assets/images/loading/img-3.png');
    }

    66% {
      opacity: 1;
    }

    77% {
      opacity: 0.6;
    }

    90% {
      background-image: url('../../assets/images/loading/img-1.png');
    }

    100% {
      opacity: 1;
    }
  }

}

.qcc-loading-img {
  width: 171px;
  height: 44px;
}


</style>
