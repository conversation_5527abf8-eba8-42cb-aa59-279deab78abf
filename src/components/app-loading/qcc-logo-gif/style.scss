// 无logo动图
$size-sm: 50px;
$size-default: 80px;
$size-lg: 200px;

// logo动图
$logo-width-default: 44px;
$logo-height-default: 44px;

$logo-width-small: 120px;
$logo-height-small: 30px;


// 中圈
@keyframes rotate-clockwise {
  from {
    transform: rotate(0);
  }

  to {
    transform: rotate(-360deg);
  }
}

// 小圈
@keyframes rotate-counterclockwise {
  from {
    transform: rotate(0);
  }

  to {
    transform: rotate(360deg);
  }
}

// 闪动
@keyframes bulb-on {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.4;
  }

  100% {
    opacity: 1;
  }
}


.ui-loading-all-logo {
  display: inline-block;
  //width: $logo-width-default;
  height: $logo-height-default;

  .qcc-icon {
    position: relative;
    display: inline-block;

    width: $logo-height-default;
    height: $logo-height-default;

    img {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      transform-origin: center;
      animation: 1s linear infinite;

      &:nth-child(2) {
        width: 75%;
        height: 75%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }

      &:nth-child(3) {
        width: 45%;
        height: 45%;
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        right: 0;
        margin: auto;
        -webkit-animation: rotate-clockwise 1.33s ease infinite;
        animation: rotate-clockwise 1.33s ease infinite;
      }

      &:nth-child(4) {
        width: 18%;
        height: 18%;
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        right: 0;
        margin: auto;
        -webkit-animation: rotate-counterclockwise 1.33s ease infinite;
        animation: rotate-counterclockwise 1.33s ease infinite;
      }
    }
  }

  .qcc-logo {
    display: inline-block;
    width: $logo-width-default - $logo-height-default - 10px;
    height: $logo-height-default;
    line-height: $logo-height-default;
    vertical-align: baseline;

    -webkit-animation: bulb-on 1.33s linear infinite;
    animation: bulb-on 1.33s linear infinite;

    margin-left: 10px;
  }

  &.small {
    width: $logo-width-small;
    height: $logo-height-small;

    .qcc-icon {
      width: $logo-height-small;
      height: $logo-height-small;
    }

    .qcc-logo {
      width: $logo-width-small - $logo-height-small - 10px;
      height: $logo-height-small;
      line-height: $logo-height-small;
    }
  }
}

:global(.ant-spin-nested-loading)>div {
  &> :global(.ant-spin) {
    .root {
      margin: -$size-default / 2;
    }
  }

  &> :global(.ant-spin-sm) {
    .root {
      margin: -$size-sm / 2;
    }
  }

  &> :global(.ant-spin-lg) {
    .root {
      margin: -$size-lg / 2;
    }
  }
}

:global(.ant-spin-sm) {
  .root {
    width: $size-sm;
    height: $size-sm;
  }
}

:global(.ant-spin-lg) {
  .root {
    width: $size-lg;
    height: $size-lg;
  }
}
