<template>
  <el-autocomplete
    class="app-general-autocomplete"
    ref="autocomplete"
    v-model="inputValue"
    popper-class="app-general-autocomplete-popper"
    :fetch-suggestions="handleFetchSuggestions"
    @select="handleSelect"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <template slot-scope="{ item }">
      <slot v-if="$scopedSlots.suggestion" name="suggestion" :item="item"></slot>
      <template v-else>
        <template v-if="item.isEmptyPlaceholder">
          <app-nodata class="no-date" imgWidth="60" text="暂时相关选项"></app-nodata>
        </template>
        <div class="suggestion" v-else>
          <span class="suggestion-name" v-html="handleNameHighlight(item.name)"></span>
          <span class="suggestion-desc">{{ item.desc }}</span>
        </div>
      </template>
    </template>
  </el-autocomplete>
</template>

<script>
import _ from 'lodash'

export default {
  name: 'app-general-autocomplete',
  directives: {},
  components: {},
  props: {
    suggestions: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      inputValue: ''
    }
  },
  watch: {},
  computed: {},
  created() {
  },
  mounted() {
  },
  methods: {
    clearInputValue () {
      this.inputValue = ''
    },
    handleNameHighlight(name) {
      return $util.wholeWordHighlight(name, this.inputValue)
    },
    handleFetchSuggestions(queryString, cb) {
      let result = []
      if (_.isEmpty(queryString)) {
        cb(result)
        return
      }
      result = this.suggestions.filter((item) => item.name.includes(queryString))
      if (_.isEmpty(result)) {
        result.push({ isEmptyPlaceholder: true })
      }
      cb(result)
    },
    handleSelect(item) {
      if (!item.isEmptyPlaceholder) {
        this.inputValue = item.name
        this.$emit('select', item)
      }
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .el-input__inner {
    height: 32px;
    line-height: 32px;
    border-radius: 2px;
  }
}

.suggestion {
  display: flex;
}

.suggestion-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.suggestion-desc {
  color: #999999;
  margin-left: 4px;
  white-space: nowrap;
}

.no-date._app-nodata-wrapper {
  color: #bbb;

  ::v-deep {
    .content-wrapper {
      padding: 30px 0 !important;
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 14px !important;
    }

    .emptyIcon {
      margin-bottom: 10px;
    }

    ._empty-text-wrapper {
      height: 22px !important;
      line-height: 22px !important;
    }

    ._empty-text {
      display: flex !important;
      height: 22px !important;
      line-height: 22px !important;
    }
  }
}
</style>
<style lang="scss">
.app-general-autocomplete-popper {
  border: 0;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
  border-radius: 4px !important;

  .el-autocomplete-suggestion__wrap {
    padding: 5px 0;
  }

  .el-autocomplete-suggestion__list {
    li {
      line-height: 32px;
      padding: 0 15px;

      &:hover {
        background: #F2F8FE !important;
        color: #128BED;
      }
    }
  }

  &.el-popper[x-placement^="bottom"]:not(.noReset) {
    margin-top: 0;
  }
}
</style>
