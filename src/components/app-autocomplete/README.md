
### autocomplete：
#### 本地或异步

### 参数传递
| props参数        | 类型    |  介绍  |    |
    | --------   | -----:   | :----: |  :----: |
    | searchFromLists        | Array    | 从本地选择的数组| 
    | searchFromNet    | Boolean    | 是否异步加载数组 |
    | options    | Object    | 表格的其它一些配置,例如数据为空样式的显示 （常用）||
    | querySearchData    | Function    | 异步加载数据的回调|
    | valueKey    | String    | 默认 value |
    | nameKey    | String    | 下拉列表的显示文字属性，默认 name |
    | notHighlightText    | Boolean    | 匹配到的文字是否需要高亮 默认 false | |
    | icon    | Function    | 输入框的图标 ||
    | placeholder    | String    | - ||
    | size    | String    | - ||
    | disabled    | Boolean    | - ||
    
### events
| 名称        | 类型    |  介绍  |    |
    | --------   | -----:   | :----: |  :----: |
    | change        | -    | 选择某一选项触发| 
    | keyEnter    | -    | 输入框回车触发 |
    