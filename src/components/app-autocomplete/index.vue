<!--
keyEnter: 回车事件,返回输入框的值

-->
<template>
  <div class="app-auto-complete" v-click-outside="handleBlur"  :class="[`closeIconSize-${closeIconSize}`, hasIconSlot ? 'hasIconSlot' : '']">
    <el-autocomplete class="default-input auto-complete" :maxlength="300" v-bind="$attrs" style="width: 100%" ref="inputSearch" v-model="inputText" :size="size"
      @keyup.enter.native="sureInput" :disabled="disabled" :placeholder="placeholder" :trigger-on-focus="triggerOnFocus" :fetch-suggestions="getRequestDatas" :value-key="nameKey"
      popper-class="app-auto-complete-search-list" @select="handleSelect" @focus="focusInput">
      <template slot-scope="slotValue">
        <div class="search-item">
          <search-item-avatar :mdl="slotValue.item" v-if="slotValue.item.imgUrl" />
          <div class="text" :title="getFormatTitle(slotValue.item)">
            <slot v-if="$scopedSlots.suggestion" name="suggestion" :item="slotValue.item"></slot>
            <template v-else>
              <span class="main-text" v-html="slotValue.item.originName || slotValue.item.name || slotValue.item.corpName || slotValue.item.title"></span>
              <span class="ntag text-blue-violet" v-if="slotValue.item.country">{{slotValue.item.country}}</span>
            </template>
          </div>
          <div class="extend" v-if="slotValue.item.reason">
            <div class="match">
              {{ slotValue.item.reason }}匹配
            </div>
          </div>
        </div>
      </template>
      <i v-if="clearable && inputText" slot="suffix" class="input-clear-icon iconfont icon-shanchu3" @click="clearInputContent"></i>
      <slot>
        <i :class="icon" slot="suffix" @click="iconClick"></i>
      </slot>
    </el-autocomplete>
  </div>
</template>

<script>
import { myModelMixins } from '../../utils/mixinsUtils'
import clickOutside from '../app-select-dropmenu/clickOutSide'
import searchItemAvatar from '../app-search-input/search-item-avatar.vue'
import { isIE } from '../../utils/browser-detector'

export default {
  name: 'app-autocomplete',
  directives: { clickOutside },
  mixins: [myModelMixins],
  components: {
    searchItemAvatar
  },
  props: {
    options: {
      // 配置没有匹配到数据的提示文字
      default: () => {
        return { emptyText: '没有匹配到数据' }
      },
      type: Object
    },
    fromName: { default: '', type: String },
    size: { default: 'small', type: String },
    icon: { default: 'el-icon-search', type: String },
    placeholder: { default: '请输入内容', type: String },
    triggerOnFocus: { defult: false, type: Boolean },
    disabled: { default: false, type: Boolean },
    searchFromLists: { default: () => [], type: Array }, // 如果从已有的列表中取出其中一项，将已有的list传入
    searchFromNet: { default: false, type: Boolean }, //  是否搜索远程的数据
    querySearchData: {}, // 远程搜索数据时触发请求的回调
    valueKey: { default: 'value', type: String },
    nameKey: { default: 'name', type: String },
    selectedShowKey: { default: '', type: String },
    notHighlightText: { default: false, type: Boolean }, // 匹配到的文字是否需要高亮
    notAutoTiggerInput: { default: false, type: Boolean },
    clearable: { default: false, type: Boolean },
    hasIconSlot: { default: false, type: Boolean },
    closeIconSize: { default: '14', type: String }// 14, 12
  },
  data() {
    return {
      queryTextObj: {},
      inputText: '',
      lastKeyInput: ''
    }
  },
  watch: {
    inputText(newValue) {
      this.$emit('inputValue', newValue)
    }
  },
  methods: {
    getFormatTitle(item) {
      return $util.replaceHTMLLabel(item.name || item.originName || item.corpName || item.title)
    },
    handleBlur() {
      this.$emit('inputBlur', this.inputText)
    },
    _changeModel(data) {
      //  重写v-model的设置方法
      // this.$emit('_changeModelEvent', data)
      if (data === undefined) {
        return
      }
      this.$emit('change', data)
    },
    getAutoItem(slotValue) {
      if (this.notHighlightText) {
        return slotValue[this.nameKey]
      }
      if (slotValue[this.nameKey] && slotValue[this.nameKey].indexOf(this.inputText) > -1) {
        var tmp = slotValue[this.nameKey]
        tmp = tmp.replace(this.inputText, '<span class="color-danger">' + this.inputText + '</span>')
        return tmp
      } else {
        return slotValue[this.nameKey]
      }
    },
    focusInput() {
      this.lastKeyInput = this.inputText
      this.$emit('focus', this.$refs.inputSearch, this.inputText)
    },
    blueInput() {
      setTimeout(() => {
        if (this.$refs.inputSearch) {
          this.$refs.inputSearch.focus()
        } else {
          return ''
        }
      }, 500)
    },
    sureInput() {
      this.$emit('sureInput')
    },
    getRequestDatas(queryString, cb) {
      if (this.searchFromNet) {
        this.querySearchAsync(queryString, cb)
      } else { // 如果全部来自本地的数据
        this.querySearch(queryString, cb)
      }
    },
    querySearch(queryString, cb) {
      // 搜索返回下拉的显示数据
      let tmp = this.searchFromLists ? this.searchFromLists : [{ name: this.options.emptyText, isNull: true }]
      let result = queryString ? tmp.filter((item, index, arr) => {
            item.query = queryString
            return item[this.nameKey].indexOf(queryString) > -1
          })
        : tmp
      if (queryString === '') {
        result.forEach(function (item) {
          item.query = ''
        })
      }
      if (result === undefined || result.length === 0) {
        // 如果搜索结果为空，显示空数据
        result.push({ name: this.options.emptyText, value: this.options.emptyText, isNull: true }) // '没有搜索到数据'
      }
      cb(result)
    },
    querySearchAsync(queryString, cb) {
      // 执行网络请求
      let that = this
      if (this.querySearchData) {
        this.querySearchData(
          queryString,
          function (resData) {
            if (resData === undefined || resData.length === 0) {
              // 如果搜索结果为空，显示空数据
              resData = [{
                name: that.options.emptyText,
                value: that.options.emptyText,
                [that.nameKey]: that.options.emptyText,
                [that.valueKey]: that.options.emptyText,
                isNull: true
              }] // '没有搜索到数据'
            }
            cb(resData)
          },
          cb
        )
      }
    },
    iconClick() {
      this.$emit('iconClick', this.inputText)
    },
    setInputContent(val) {
      this.inputText = val
      if (val === '') {
        if (this.$refs.inputSearch && this.$refs.inputSearch.suggestions) {
          this.$refs.inputSearch.suggestions = []
        }
      }
      if (isIE !== undefined && isIE()) {
        try {
          this.$refs.inputSearch.$refs.input.$refs.input.value = val
        } catch (err) {}
      }
    },
    getInputContent() {
      return this.inputText?.trim?.() || this.inputText
    },
    clearInputContent() {
      // this.$refs.inputSearch.handleClear()
      this.$refs.inputSearch.handleInput('')
      this.modelBind = ''
      this.inputText = ''
      this._changeModel('')
      this.initFocus()
    },
    handleSelect(item) {
      this.showList = false
      let data = JSON.parse(JSON.stringify(item))
      data[this.selectedShowKey] = $util.replaceHTMLLabel(data[this.selectedShowKey])
      if (data.isNull) {
        this.clearInputContent('')
      } else {
        if (this.selectedShowKey) {
          this.setInputContent(data[this.selectedShowKey])
        }
        this._changeModel(data)
      }
    },
    onKeyDownListener(e) {
      if (+e.keyCode === 13) {
        this.$emit('keyEnter', this.inputText)
      }
    },
    onKeyUpListener(e) {},
    initFocus() {
      if (this.$refs.inputSearch) {
        this.$refs.inputSearch.focus()
      }
    }
  },
  created() {
    this.inputText = this.fromName
    if (this.options.emptyText === undefined) { this.$set(this.options, 'emptyText', '没有匹配到数据') }
  },
  mounted() {
    var elInput = this.$refs.inputSearch
    var nativeInput = elInput.$refs.input.$refs.input
    if (nativeInput) {
      nativeInput.onkeydown = this.onKeyDownListener
      nativeInput.onkeyup = this.onKeyUpListener
      if (isIE()) {
        nativeInput.value = this.modelBind
      }
    }
  }
}
</script>

<style scoped lang="scss">
@mixin buttonHeight($height) {
  height: $height;
  line-height: 50px;
  // line-height: $height - 2px;
}

.app-auto-complete {
  width: 100%;
  line-height: 1.5;
  position: relative;
  box-sizing: border-box;

  &.hasIconSlot {
    ::v-deep {
      .el-input__suffix {
        right: 33px;
      }
    }
  }
}

.el-input--large {
  .buttonHeight {
    @include buttonHeight(50px);
  }
}

.search-item {
  line-height: 22px;
  font-size: 14px;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 10px;
  background: #F8FBFE;
  &:hover {
    color: #128BED;
    .match {
      color: #128BED;
      background: #E5F2FD;
    }
    .iconfont {
      color: #128BED;
    }
  }
}

.avatar {
  margin-right: 8px;
  display: flex;
  .iconfont {
    color: #BBBBBB;
  }
}

.logo {
  width: 22px;
  height: 22px;
  border-radius: 4px;
  border: 1px solid #EEEEEE;
}

// .text {
//   flex: 1;
//   margin-right: 8px;
// }


.text {
  flex: 1;
  margin-right: 8px;
  // display: flex;
  // align-items: center;
  display: table-cell;


  .ntag {
    padding: 2px 6px;
    margin-left: 5px;
    line-height: 18px;

    &.text-blue-violet {
      position: relative;
      // top: -1px;
      flex-shrink: 0;
    }
  }
}

.extend
{
  flex-shrink: 0;
}

.match {
  padding: 2px 6px;
  border-radius: 2px;
  background: #F3F3F3;
  font-size: 12px;
  color: #999999;
  line-height: 18px;
}
</style>

<style lang="scss">
.custom-reset-overseas-report-search-auto {
  min-width: 380px;
}
.app-auto-complete-search-list{
  margin-top: 0px !important;
  background-color: #fff;
  .el-autocomplete-suggestion__wrap{
    padding: 15px 0px !important;
  }
  li.highlighted {
    background-color: transparent;
    .search-item {
      background-color: #F2F8FE;
      color: #128BED;

      .match {
        color: #128BED;
        background: #E5F2FD;
      }

      .iconfont {
        color: #128BED;
      }
    }
  }
}
.el-autocomplete-suggestion{
  border-radius: 0 !important;
}
.el-autocomplete-suggestion li {
  white-space:  normal;
  text-overflow: initial;
}
.el-autocomplete-suggestion li:hover {
  background-color: initial !important;
}
</style>
