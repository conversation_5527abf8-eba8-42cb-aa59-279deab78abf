<template>
  <div
    class="app-paragraph"
    :class="[{
      'app-paragraph--stretch': modelValue
    }]"
  >
    <div
      class="app-paragraph__body"
    >
      <p>{{ content }}</p>
    </div>
    <div
      @click="handleChangeStatus"
      class="app-paragraph__operation"
    >
      {{ modelValue ? '收起' : '展开' }}
    </div>
    <app-copy v-if="modelValue" :copyValue="content"></app-copy>
  </div>
</template>

<script>
export default {
  name: 'app-paragraph',
  props: {
    modelValue: {
      type: Boolean
    },
    content: {
      type: String,
      default: ''
    }
  },
  model: {
    prop: 'modelValue',
    event: 'modelChange'
  },
  data() {
    return {
      value: false
    }
  },
  watch: {
    modelValue(val) {
      this.value = val
    }
  },
  created () {
    this.value = this.modelValue
  },
  methods: {
    handleChangeStatus () {
      this.value = !this.value
      this.$emit('modelChange', this.value)
      this.$emit('change', this.value)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-paragraph {
  display: flex;
  line-height: 24px;

  &--stretch {
    display: block;

    .app-paragraph__body {
      display: inline;

      p {
        overflow: visible;
        white-space: initial;
        text-overflow: initial;
        display: inline;
      }
    }

    .app-paragraph__operation {
      display: inline;
    }
  }

  &__body {
    overflow: hidden;
    flex: 1 1 auto;
    display: table;
    width: 100%;
    table-layout: fixed;

    p {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  &__operation {
    flex: 0 0 28px;
    color: #128BED;
    cursor: pointer;

    &:hover {
      color: $color-primary-hover;
    }
  }
}
</style>
