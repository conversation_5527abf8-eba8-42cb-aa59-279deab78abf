export default {
  data() {
    return {
      hoticon: require('../../assets/images/hot.png'),
      tabMenus: [],
      provideVisiable: {
        show: true
      },
      includePage: ['listCompanyMonitor', 'companyMonitorList'],
      isHideMenus: this.$route.query.hideOtherMenuFlag === 'Y'
    }
  },
  provide() {
    return {
      menuTabVisiable: this.provideVisiable,
      setMenuTabVisiable: this.setMenuTabVisiable,
      setMenuTabCount: this.setMenuTabCount
    }
  },
  watch: {
    '$route.meta.menus'(val) {
      this.refreshRoute()
    }
  },
  computed: {
    activedName() {
      return this.isNotFinancial ? (this.$route.nfName || this.$route.name) : this.$route.name
    }
  },
  created() {
    window.AppRuntimeContext.eventBus.$on(window.AppRuntimeContext.eventBusEvents.NOTIFICATION_TAB_MENU_REFRESH, () => {
      this.refreshRoute()
    })
    this.refreshRoute()
  },
  mounted() {
    window.AppRuntimeContext.eventBus.$on(window.AppRuntimeContext.eventBusEvents.RISK_PAGE_CACHE_ADD, (name) => {
      if (!this.includePage.includes(name)) {
        this.includePage.push(name)
      }
    })
    window.AppRuntimeContext.eventBus.$on(window.AppRuntimeContext.eventBusEvents.RISK_PAGE_CACHE_REMOVE, (name) => {
      if (this.includePage.includes(name)) {
        this.includePage.splice(this.includePage.indexOf(name), 1)
      }
    })
  },
  methods: {
    getIsActived(routeNames) {
      if (!this.tabMenus || !this.tabMenus.length || this.tabMenus.length <= 1) {
        return false
      }

      if (typeof routeNames === 'string') {
        return this.activedName === routeNames
      } else {
        return routeNames.includes(this.activedName)
      }
    },
    setMenuTabCount(key, count) {
      for (let row of this.tabMenus) {
        if (row.routeName === key) {
          this.$set(row, 'count', count)
          break
        }
      }
    },
    setMenuTabVisiable(is = true) {
      this.provideVisiable.show = is
    },
    changePage(item) {
      $util.zhugeTrackThreeoLevel(item.trackName || item.name, '横向菜单', '点击菜单')
      let query = {}
      if (__PLUGIN__ && this.$route.query.hideBackFlag) {
        query.hideBackFlag = this.$route.query.hideBackFlag
      }
      if (this.$route.query.hideOtherMenuFlag !== undefined) {
        query.hideOtherMenuFlag = this.$route.query.hideOtherMenuFlag
      }
      query.pagev = `${Date.now()}`.substring(9, 13)
      let routerName = typeof item.routeName === 'string' ? item.routeName : item.routeName[0]
      const { path } = this.$router.resolve({ name: routerName }).route
      if (path) {
        this.$router.push({ path: path + $util.getUrlEndIndex(), query })
      } else {
        throw new Error('未匹配到对应的路由，请检查路由配置')
      }
    },
    refreshRoute() {
      this.isHideMenus = this.$route.query.hideOtherMenuFlag === 'Y'
      let arr = (this.isNewVersion ? this.$route.meta.menusV2 : this.$route.meta.menus) || []
      if (this.isNotFinancial) {
        for (let t of arr) {
          t.name = t.nfName || t.name
        }
      }
      this.tabMenus = arr.filter(v => !v.hidden)
      if (this.setMenuTabVisiable) this.setMenuTabVisiable(this.tabMenus.length > 0)
    }
  }

}
