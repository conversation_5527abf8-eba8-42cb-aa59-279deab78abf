<template>
  <div style="height: 100%;position: relative;">
    <keep-alive style="height: 100%" :include="includePage">
      <router-view style="height: 100%"></router-view>
    </keep-alive>
    <div class="tabmenus-layout" :class="[{isNewVersion}]" v-if="provideVisiable.show && !$route.meta.menuTabV1Hidden && !isHideMenus">
      <span class="app-menu-item" v-for="(item,index) in tabMenus" :key="`${item.name}-${index}`" :class="[{active: getIsActived(item.routeName)}]" @click="item.routeName === activedName ? '' : changePage(item)">
        {{item.name}}
        <template v-if="item.badgeFlag && item.count">
          <span class="tabitem-badge__content">{{item.count > 99 ? '99+' : item.count}}</span>
        </template>
        <img :src="hoticon" v-if="item.hot" class="icon" />
      </span>
    </div>
  </div>
</template>
<script>
  import mixins from './mixins'
  export default {
    name: 'app-menutab-layout',
    mixins: [mixins]

  }

</script>
<style lang="scss" scoped>
  @import '../../styles/common.scss';

  .tabmenus-layout {
    position: absolute;
    left: 0;
    top: 0;
    height: 49px;

    &.isNewVersion {
      left: 12px;
    }

    .app-menu-item {
      display: inline-block;
      font-size: 14px;
      padding: 0 15px;
      cursor: pointer;
      line-height: 49px;
      background: white;
      position: relative;

      &.active {
        color: $color-primary;
        border-bottom: 2px solid $color-primary;
      }

      .icon {
        position: absolute;
        right: -10px;
        top: 5px;
        height: 15px;
        z-index: 2;
      }

      .tabitem-badge__content {
        position: absolute;
        left: calc(100% - 16px);
        top: 5px;
        z-index: 10;
        background: #E08283;
        font-size: 12px;
        height: 18px;
        line-height: 18px;
        border-radius: 10px;
        padding: 0 6px;
        color: #fff;
      }
    }
  }

  @media screen and (max-width: 1600px) {
    .tabmenus-layout {
      .app-menu-item {
        padding-left: 10px !important;
        padding-right: 10px !important;

        .tabitem-badge__content {
          font-family: "iconfont" !important;
          font-size: 0;
          font-style: normal;
          -webkit-font-smoothing: antialiased;
          background: transparent;
          color: #e08283;
          padding: 0;

          &:before {
            content: "\ee06";
            font-size: 16px;
          }
        }
      }
    }
  }

  @media screen and (max-width: 1340px) {
    .tabmenus-layout {
      .app-menu-item {
        padding-left: 8px !important;
        padding-right: 8px !important;

        .tabitem-badge__content {
          font-family: "iconfont" !important;
          font-size: 0 !important;
          font-style: normal;
          -webkit-font-smoothing: antialiased;
          background: transparent;
          color: #e08283;
          padding: 0;

          &:before {
            content: "\ee06";
            font-size: 16px;
          }
        }
      }
    }
  }

</style>
