<template>
  <app-popup ref="pop" :control="control" :button-options="selectStuffButtonOptions" :options="optionsCompanySelect" title="人员列表">
    <div>
      <app-input style="margin: 10px 0;" v-model="searchStuffkey" size="normal" placeholder="请输入自然人名称" :suffix-icon="suffixForStuffIcon" :key-enter="selectStuffRequestData"></app-input>
      <app-table :control-data="selectStuffControlTableData" tableclass="notAuto" :pagination-prop="selectStaffPaginationProp" v-if="selectStuffControlTableData.showDatas.length > 0" :colums-prop="selectStuffColumsProp" :selection="control.options.selection" :request-page-data="selectStuffRequestData" :options="selectStuffOptions"></app-table>
      <app-loading v-if="loadingProp.show" :control="loadingProp" top="50px"></app-loading>
    </div>
    <app-pagination-mini height="32" v-if="!selectStuffOptions.showLoading && selectStuffControlTableData.showDatas.length > 0" :pagination-prop="selectStaffPaginationProp" slot="footerLeft">
    </app-pagination-mini>
  </app-popup>
</template>

<script>
import { loadingMixins } from '../../utils/mixinsUtils'
import { postVue } from '../../services/common-service'

export default {
  name: 'app-select-staff',
  mixins: [loadingMixins],
  props: {
    control: {
      default: () => {
        return {
          show: false,
          options: {
            url: '/setting/staff/loadStaffForBbt', // 获取数据的接口
            selection: { show: true, singleChoice: false } // 是否显示选择， 多选还是单选
          }
        }
      }
    },
    onlySelectCompany: { default: true }
  },
  data() {
    return {
      controlSelectStuffPop: { show: false },
      searchStuffkey: '',
      suffixForStuffIcon: { icon: 'el-icon-search', iconClick: this.selectStuffRequestData },
      selectStuffControlTableData: {
        showDatas: [],
        tableHeight: 400
      },
      selectStuffOptions: {
        emptyText: '暂未找到该账号',
        emptyShow: false,
        showLoading: false
      },
      optionsCompanySelect: {
        width: '600px',
        contentStyle: { 'padding-top': 0 }
      },
      selectStaffPaginationProp: {
        show: false,
        pageSize: 10,
        currentPage: 1,
        total: 0,
        handleCurrentChange: this.selectStuffRequestData
      },
      selectStuffColumsProp: [
        { colName: '人员姓名', prop: 'staffName', width: 100, emptyStr: '-', isTitle: true },
        { colName: 'ID号', prop: 'staffId', minWidth: 130, emptyStr: '-' },
        { colName: '标识符', prop: 'staffKeyNo', minWidth: 200, singleLineLayout: true }]
    }
  },
  computed: {
    selectStuffButtonOptions() {
      if (this.selectStuffControlTableData.showDatas.length === 0) {
        return []
      }
      return [{ name: '选择', type: 'primary', click: this.selectStuffChooseClick }]
    }
  },
  methods: {
    selectStuffChooseClick() {
      if (this.selectStuffControlTableData.selections.length === 0) {
        this.$message.warning('您还没有选择自然人')
        return
      }
      this.$emit('handleSelect', this.selectStuffControlTableData.selections)
      this.control.ref.close()
    },
    selectStuffRequestData() {
      var params = $util.formatPaginationRequest({}, this.selectStaffPaginationProp)
      params.condition.searchKey = this.searchStuffkey
      params.condition.displayMode = '1'
      this.showLoadding()
      postVue('/setting/staff/loadStaffForBbt', params)
        .then(res => {
          this.selectStuffControlTableData.showDatas = res.resultList
          this.selectStaffPaginationProp.total = res.totalCount
          this.selectStuffOptions.emptyShow = true
          this.hideLoadding()
        })
    }
  },
  created() {
    if (this.control.options === undefined) {
      this.$set(this.control, 'options', {})
    }
    if (this.control.options.selection === undefined) {
      this.$set(this.control.options, 'selection', { show: true })
    }
    if (this.control.options.selection.show === undefined) {
      this.$set(this.control.options.selection, 'show', true)
    }
    if (this.control.options.selection.singleChoice === undefined) {
      this.$set(this.control.options.selection, 'singleChoice', false)
    }
  },
  mounted() {
    this.selectStuffControlTableData.tableHeight = 400
    this.selectStuffRequestData(true)
  }
}

</script>

<style scoped>

</style>
