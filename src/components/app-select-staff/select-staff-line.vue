<template>
  <cell-item title="人名：" :width='labelWidth'>
    <app-input class="select-company-line-input" :size="size" v-model="selectCompResult.compsSelectText" ref="inputref" placeholder="请选择自然人姓名" disabled></app-input>
    <app-button type="text" size="small" class="cityChoose" @click="control.show = true">
      <img src="../../assets/images/icon-building.png" style="margin-right: 3px;" width="12px">选择人名
    </app-button>
    <app-select-staff v-if="control.show" :control="control" @handleSelect="handleSelect"></app-select-staff>
  </cell-item>
</template>

<script>
import cellItem from '../app-cell-item'

export default {
  name: 'app-select-staff-line',
  props: {
    size: { default: 'large' },
    labelWidth: { default: '100' }

  },
  components: { cellItem },
  data() {
    return {
      selectCompResult: {
        compsSelectText: '',
        compsSelect: []
      },
      control: { show: false },
      selectCompFromList: false
    }
  },
  methods: {
    clear() {
      this.selectCompResult.compsSelect = []
      this.selectCompResult.compsSelectText = ''
    },
    handleSelect(val) {
      this.selectCompResult.compsSelect = val
      this.$emit('selectdone', val)
      this.selectCompResult.compsSelectText = val.map((item) => { return item.staffName }).toString()
    }
  }
}

</script>

<style scoped lang="scss">
  @import '../../styles/common.scss';

  .cityChoose {
    position: absolute;
    right: 10px;
    top: 0px;
    height: 100%;
    line-height: calc(100% - 2px);
  }

</style>
