import commonService from '../../../services/common-service'

export default {
  name: 'AppRelatCompany',
  data() {
    return {
      buttonOptions: [
        // {
        //   name: '关闭',
        //   type: 'primary'
        // }
      ],
      options: {
        width: '960px'
      },
      popupControl: {
        title: '',
        options: {
          width: '960px'
        },
        show: false
      },

      name: '',
      keyNo: '',
      loadingshow: true,
      controlData: {
        showDatas: []
      },
      paginationProp: {
        total: 0,
        show: true,
        pageSize: 5,
        currentPage: 1,
        pageSizes: [5, 10, 25, 50, 100]
      },
      columsProp: [
        { colName: '序号', prop: 's', width: 60, type: 'sortNumber' },
        {
          colName: '企业名称',
          prop: '',
          type: 'autoHeight',
          minWidth: 100,
          isTitle: false,
          // showWarLink: true,
          componentName: 'app-table-column-1',
          format(row) {
            return { keyNo: row.keyNo, name: row.name, imageUrl: row.imageUrl }
          }
        },
        {
          colName: '状态',
          prop: '',
          type: 'autoHeight',
          width: 90,
          tagType: 'line-fill',
          componentName: 'app-table-column-status',
          format(row) {
            return { status: row.status }
          }
        },
        {
          colName: '法定代表人等',
          prop: '',
          type: 'autoHeight',
          width: 105,
          format(row) {
            return $util.getCompanyOrPersonLinkerByOrg(
              row.operName,
              row.operPersonId
            )
          }
        },
        {
          colName: '注册资本',
          prop: 'regCap',
          type: 'autoHeight',
          width: 125,
          format(row) {
            return $util.rmRMB(row.regCap) || '-'
          }
        },
        {
          colName: '地区',
          prop: 'province',
          type: 'autoHeight',
          width: 80
        },
        {
          colName: '持股比例',
          prop: 'radior',
          type: 'autoHeight',
          width: 95
        },
        {
          colName: '职位',
          prop: 'postionr',
          type: 'autoHeight',
          width: 120
        }
      ]
    }
  },
  watch: {},
  async mounted() {
    await this.$nextTick()
    this.doSearch()
  },
  methods: {
    doSearch() {
      commonService
        .postVue('/webapi/saas/person/dtl/getBossDJGDataNew', {
          personId: this.keyNo,
          pageSize: this.paginationProp.pageSize,
          pageIndex: this.paginationProp.currentPage || 1
        })
        .then((res) => {
          this.controlData.showDatas = this.formatData(res.resultList)
          this.paginationProp.total = res.totalCount
          this.loadingshow = false
        })
      // peopleService.getRelatCompany(this.params).then((data) => {
      //   if (data?.data) {
      //     this.list = this.formatData(data.data)
      //     this.pageInfo = data.pageInfo
      //   } else {
      //     this.list = []
      //     this.pageInfo = null
      //   }
      // })
    },
    formatData(list) {
      return list.map((item) => {
        item.province = item.area?.province
        if (item.status === '存续（在营、开业、在册）') {
          item.status = '存续'
        }
        if (item.relation?.length) {
          item.relation.forEach((r) => {
            if (r.type === '1') {
              item.radior = r.value
            } else if (r.type === '2') {
              item.postionr = r.value
            }
          })
        }
        return item
      })
    }
  }
}
