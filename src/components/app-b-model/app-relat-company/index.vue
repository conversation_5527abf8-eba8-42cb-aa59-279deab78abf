<template>
  <div class="app-relat-company">
    <app-popup ref="pop" :options="options" :control="popupControl" :buttonOptions="buttonOptions"
      :title="`${name} 关联企业`">
      <app-table autoheight :control-data.sync="controlData" :pagination-prop.sync="paginationProp"
        :colums-prop.sync="columsProp" :request-page-data="doSearch" :options="{ emptyShow: false }"></app-table>
      <app-loading v-if="loadingshow" top="50px"></app-loading>
    </app-popup>
  </div>
</template>

<script src="./component.js"></script>
<style lang="scss" src="./style.scss" scoped></style>
