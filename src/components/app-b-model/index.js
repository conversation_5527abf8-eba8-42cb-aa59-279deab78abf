import appRelatCompany from './app-relat-company'
import appInvestment from './app-investment'
export default {
  getRelateCompany(keyNo, name) {
    window.AppRuntimeContext.instance.$uiService.showDialog(appRelatCompany, {
      keyNo,
      name
    })
  },
  getInvestment(groupId, keyNo, name, type, count) {
    window.AppRuntimeContext.instance.$uiService.showDialog(appInvestment, {
      keyNo,
      groupId,
      name,
      type,
      count
    })
  }
}
