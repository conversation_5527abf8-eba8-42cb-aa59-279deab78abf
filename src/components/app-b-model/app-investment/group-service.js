import commonService from '../../../services/common-service'

export default {
  GetPartnerDetailList(params) {
    return new Promise((resolve, reject) => {
      commonService
        .postVue(
          '/webapi/saas/corp/common/groupInfo/getPartnerDetailList',
          params
        )
        .then((res) => {
          let result = JSON.parse(res.result)
          resolve(result)
        })
        .catch((e) => {
          reject(e)
        })
    })
  },
  GetInvestmentDetailList(params) {
    return new Promise((resolve, reject) => {
      commonService
        .postVue(
          '/webapi/saas/corp/common/groupInfo/getInvestmentDetailList',
          params
        )
        .then((res) => {
          let result = JSON.parse(res.result)
          resolve(result)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
