import groupService from './group-service'
import { formatPaginationRequest } from '../../../utils/function'
export default {
  name: 'AppInvestment',
  data() {
    return {
      buttonOptions: [
        // {
        //   name: '关闭',
        //   type: 'primary'
        // }
      ],
      options: {
        width: '960px'
      },
      popupControl: {
        title: '',
        options: {
          width: '960px'
        },
        show: false
      },

      loadingshow: true,
      controlData: {
        showDatas: []
      },
      paginationProp: {
        total: 0,
        show: true,
        pageSize: 5,
        currentPage: 1,
        pageSizes: [5, 10, 25, 50, 100]
      },
      keyNo: '',
      visible: false,
      name: '',
      params: {},
      list: [],

      type: ''
      // investmentCount: 0,
      // investorCount: 0
    }
  },
  watch: {},
  computed: {
    title() {
      if (this.type === 'investor') {
        return `${this.name} 投资${
          this.count || this.paginationProp.total
        }家企业`
      } else if (this.type === 'investment') {
        return `${this.name} ${
          this.count || this.paginationProp.total
        }家成员企业参与投资`
      }
    },
    columsProp() {
      const base = [
        { colName: '序号', prop: 's', width: 60, type: 'sortNumber' },
        {
          colName: this.type === 'investor' ? '被投资企业' : '参与投资企业',
          prop: 'name',
          type: 'autoHeight',
          minWidth: 450,
          componentName: 'app-table-column-1',
          format(row) {
            return { keyNo: row.KeyNo, name: row.KeyName, imageUrl: row.Logo }
          }
        },
        {
          colName: '持股比例',
          prop: 'StockPercent',
          type: 'autoHeight',
          width: 100,
          isTitle: true
        },
        {
          colName: '认缴出资额/持股数',
          prop: 'ShouldCapi',
          type: 'autoHeight',
          width: 150,
          isTitle: true,
          format(row) {
            let d = $util.rmRMB(row.ShouldCapi)
            return `<span title="${d}">${d || '-'}</span>`
          }
        },
        {
          colName: '认缴出资日期',
          prop: 'ShouldDate',
          type: 'autoHeight',
          width: 120,
          isTitle: true,
          format(row) {
            var val = row.ShouldDate || row.ShoudDate || '-'
            val = val.replace(/,/g, '<br>')
            return val
          }
        }
      ]

      return base
    }
  },
  async mounted() {
    await this.$nextTick()
    this.doSearch()
  },
  methods: {
    // show({ name, keyNo, groupId, type, investmentCount, investorCount }) {
    //   this.name = name
    //   this.keyNo = keyNo
    //   this.type = type || 'investor'
    //   this.params.keyNo = keyNo
    //   this.params.groupId = groupId
    //   // this.params.pageIndex = 1
    //   // this.params.pageSize = 5
    //   // this.investmentCount = investmentCount
    //   // this.investorCount = investorCount
    //   this.visible = true
    //   this.getData()
    // },
    getParams() {
      return formatPaginationRequest(
        {
          groupId: this.groupId,
          keyNo: this.keyNo,
          isV2: true
        },
        this.paginationProp
      )
    },
    getData() {
      let service = groupService.GetPartnerDetailList
      if (this.type === 'investment') {
        service = groupService.GetInvestmentDetailList
      }
      service(this.getParams()).then((data) => {
        if (data.Result) {
          this.controlData.showDatas = data.Result
          this.paginationProp.pageSize = data.Paging.PageSize
          this.paginationProp.total = data.Paging.TotalRecords
        }
      })
    },
    doSearch() {
      this.getData()
    }
  }
}
