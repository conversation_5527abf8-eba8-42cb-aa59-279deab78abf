<template>
  <div class="app-invesment">
    <app-popup :title="title" ref="pop" :options="options" :control="popupControl" :buttonOptions="buttonOptions">
      <app-table autoheight :control-data.sync="controlData" :pagination-prop.sync="paginationProp"
        :colums-prop.sync="columsProp" :request-page-data="doSearch" :options="{ emptyShow: false }"></app-table>
    </app-popup>
  </div>
</template>

<script src="./component.js"></script>
<style lang="scss" src="./style.scss" scoped></style>
