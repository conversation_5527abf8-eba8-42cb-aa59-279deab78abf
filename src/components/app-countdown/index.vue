<template>
  <span class="__countdown-wrapper" :style="{width}" :class="[size,{fullHeight},{disabled: currentCountDownState}]" @click="clickGetCode">
    <slot :data="row">
      {{showText}}
    </slot>
  </span>
</template>

<script>
  export default {
    name: 'app-countdown',
    props: {
      text: { default: '获取验证码', type: String },
      min: { default: 1, type: Number },
      size: { default: 'large', type: String },
      fullHeight: { default: false, type: Boolean },
      width: { default: '98px', type: String },
      textTmpStr: { default: () => ['{seconds}秒', '{minute}分'], type: Array }
    },
    data() {
      return {
        currentCountDownState: false,
        timer: null,
        count: ''
      }
    },
    computed: {
      showText() {
        if (this.timer) {
          let textTmpStr = this.textTmpStr || ['{seconds}秒', '{minute}分']
          let m = Math.floor(this.count / 60)
          let s = this.count % 60
          if (m > 0) {
            return `${textTmpStr[1].replace(/{minute}/g, m)}${textTmpStr[0].replace(/{seconds}/g, s)}`
          }
          return textTmpStr[0].replace(/{seconds}/g, this.count)
        }
        return this.text
      },
      row() {
        return {
          status: this.currentCountDownState,
          count: this.count
        }
      }
    },
    methods: {
      clickGetCode() {
        if (this.currentCountDownState) {
          return
        }
        this.$emit('click', true, () => {
          this.getCode()
        })
      },
      getCode() {
        if (!this.timer) {
          this.count = this.min * 60 - 1
          this.currentCountDownState = true
          this.timer = setInterval(() => {
            if (this.count > 0 && this.count <= (this.min * 60)) {
              this.count--
            } else {
              this.currentCountDownState = false
              clearInterval(this.timer)
              this.timer = null
              this.$emit('timeOut')
            }
          }, 1000)
        }
      }
    },
    beforeDestroy() {
      if (this.timer) { clearInterval(this.timer) }
    }
  }

</script>

<style scoped lang="scss">
  .__countdown-wrapper {
    display: inline-block;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 13px;
    color: #128bed;
    cursor: pointer;
    &:hover:not(.disabled) {
      color: #0069BF;
    }

    &.disabled {
      cursor: unset;
      color: #BBBBBB;
    }

    &.fullHeight {
      height: 38px;
      line-height: 38px;

      &.medium {
        height: 34px;
        line-height: 34px;
      }

      &.small {
        height: 30px;
        line-height: 30px;
        font-size: 12px;
      }
    }

    &.medium {
      height: 36px;
      line-height: 36px;
    }

    &.small {
      height: 32px;
      line-height: 32px;
      font-size: 12px;
    }
  }

</style>
