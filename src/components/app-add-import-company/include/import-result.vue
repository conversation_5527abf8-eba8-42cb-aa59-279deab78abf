<template>
  <div class="import-success-wrapper">
    <div class="result-text">
      成功导入 <span class="color-primary">{{ successCount }}</span> 家企业，失败 <span
      class="color-danger">{{ faildCount }}</span> 家企业
      <a class="color-primary" @click="downloadErrorDetail" v-if="faildCount>0">下载失败明细</a>
      <a class="color-primary" v-if="successCount||faildCount" @click="exportResult" v-permission="[{name: 'rvrs:hidden_export', isInverse: true}]">导出结果</a>
    </div>
    <app-table :controlData="tableControlData" :columsProp="columsProp" :paginationProp="tablePaginationProp"
               :options="selectCompGroupsOptions" style="margin-top: 10px" autoheight>
      <template slot="header-importResult">
        导入结果
        <colum-header-filter :options="resultFilterOptions" v-model="filterModel" @change="doFilter"></colum-header-filter>
      </template>
      <template slot="content-modifiedColum" slot-scope='scope'>
        <div class="result-company-text">
          <div class="comp-text" :title="scope.row.corpName">{{ scope.row.corpName }}</div>
          <span class="modify-note margin-l-0-3x" v-if='!scope.row.status'>
            <i class="iconfont icon-icon_bii" @click.stop="editRecord(scope.row)"/>
          </span>
        </div>
      </template>
    </app-table>
  </div>
</template>

<script>
import { tableDataMixins } from '../../../utils/mixinsUtils'
import updateRecordDialog from '../../app-update-ename'
import columHeaderFilter from './colum-header-filter'
import { exportExcelBase } from '../../../utils/exportexcel/export-excel-utils'
import userstorage from '../../../utils/localstorage'

export default {
  name: 'import-result',
  mixins: [tableDataMixins],
  components: {
    columHeaderFilter
  },
  props: {
    dataList: { default: () => [], type: Array },
    functionTableId: { default: 'bene_id', type: String },
    showGroup: {
      type: Boolean,
      default: true
    }
  },
  data() {
    const user = userstorage.fetchUser()
    return {
      mainUserFlag: user.mainUserFlag,
      selectCompGroupsOptions: {
        emptyShow: true,
        isNet: false
      },
      filterModel: '',
      columsProp: [
        { colName: '企业名称/统一社会信用代码/注册号', type: 'autoHeight', prop: 'name', minWidth: 230, contentSlotName: 'content-modifiedColum' },
        {
          colName: '导入结果',
          prop: 'statusDesc',
          type: 'autoHeight',
          width: 95,
          headerSlotName: 'header-importResult',
          format(row) {
            return row.status ? '<span class="color-success">成功</span>' : '<span class="color-danger">失败</span>'
          }
        },
        {
          colName: '失败原因',
          prop: 'errMsg',
          type: 'autoHeight',
          minWidth: 200,
          format(row) {
            return !row.status ? `<span class="color-danger" title="${row.errMsg}">${row.errMsg}</span>` : '-'
          }
        }
      ],
      resultFilterOptions: {
        list: []
      }
    }
  },
  created() {
    if (this.showGroup) {
      var customerCol = { colName: '客户编号', prop: 'corpCode', width: 80, type: 'autoHeight' }
      var groupCol = { colName: '企业分组', prop: 'userGroup', width: 80, type: 'autoHeight' }
      this.columsProp.splice(1, 0, customerCol, groupCol)
    }
    // 专业版主账号导入结果需要添加关注账号、未匹配或账号错误
    if (this.showFollowAccount) {
      // this.columsProp.forEach(item => {
      //   item.type = 'autoHeight'
      // })
      this.columsProp.splice(
        this.columsProp.length - 2,
        0,
  {
          colName: '关注账号',
          prop: 'successFollowAccounts',
          minWidth: 110,
          type: 'autoHeight',
          stretch: true,
          format(row) {
            const result = row.successFollowAccounts?.join(',<br>')
            return result || '-'
          }
        },
        {
          colName: '未匹配或账号错误',
          prop: 'failedFollowAccounts',
          minWidth: 125,
          type: 'autoHeight',
          stretch: true,
          format(row) {
            const result = row.failedFollowAccounts?.join(',<br>')
            return result ? `<div class="color-danger">${result}</div>` : '-'
          }
        }
      )
    }
    this.tablePaginationProp.isNet = false
    this.tablePaginationProp.pageSize = 10
    this.tablePaginationProp.themeV2 = true
    this.tablePaginationProp.showMini = true
  },
  mounted() {
    this.doFilter()
  },
  computed: {
    successCount() {
      return (this.dataList || []).filter(v => v.status).length || 0
    },
    faildCount() {
      return (this.dataList || []).length - this.successCount
    },
    isOnlyCsv() {
      return $util.isBankIP() || $util.isCentralBanking() || this.isJSJW
    },
    /**
     * 是否显示关注账号内容
     * @returns {false}
     */
    showFollowAccount () {
      return !this.isOnlyCsv && this.mainUserFlag && ['benebreakthrough-list', 'list-company-monitor'].includes(this.$route.name)
    }
  },
  methods: {
    generateOptions() {
      let arr = [
        { name: '全部', value: '' }
      ]
      if (this.successCount > 0) {
        arr.push({ name: '成功', value: true })
      }
      if (this.faildCount > 0) {
        arr.push({ name: '失败', value: false })
      }
      this.resultFilterOptions.list = arr
    },
    downloadErrorDetail() {
      let errorList = this.dataList.filter(v => (!v.status || v.failedFollowAccounts?.length > 0))
      if (!errorList.length) {
        this.$message.warning('暂无导入失败的数据')
        return
      }
      let header = ['企业名称/统一社会信用代码/注册号', '导入结果', '失败原因']

      let data = errorList.map(x => [
        x.corpName || '-',
        x.status ? '成功' : '失败',
        x.errMsg || '-'
      ])
      if (this.showGroup) {
        header = ['企业名称/统一社会信用代码/注册号', '客户编号', '企业分组', '导入结果', '失败原因']

        // 专业版主账号导入结果需要添加关注账号、未匹配或账号错误
        if (this.showFollowAccount) {
          header.splice(3, 0, '关注账号', '未匹配或账号错误')
        }

        data = errorList.map(x => [
          x.corpName || '-',
          x.corpCode || '-',
          x.userGroup || '-',
          ...(this.showFollowAccount ? [
            (x.successFollowAccounts?.join(',\n')) || '-',
            (x.failedFollowAccounts?.join(',\n')) || '-'
          ] : []),
          x.status ? '成功' : '失败',
          x.errMsg || '-'
        ])
      }
      exportExcelBase({
        header,
        data
      }, '导入失败明细', this.showFollowAccount ? {
        sourceCellAlignment: {
          wrapText: true,
          vertical: 'top'
        }
      } : undefined)
    },
    exportResult() {
      let header = ['企业名称/统一社会信用代码/注册号', '导入结果', '失败原因']

      let data = this.dataList.map(x => [
        x.corpName || '-',
        x.status ? '成功' : '失败',
        x.errMsg || '-'
      ])

      if (this.showGroup) {
        header = ['企业名称/统一社会信用代码/注册号', '客户编号', '企业分组', '导入结果', '失败原因']

        // 专业版主账号导入结果需要添加关注账号、未匹配或账号错误
        if (this.showFollowAccount) {
          header.splice(3, 0, '关注账号', '未匹配或账号错误')
        }

        data = this.dataList.map(x => [
          x.corpName || '-',
          x.corpCode || '-',
          x.userGroup || '-',
          ...(this.showFollowAccount ? [
            (x.successFollowAccounts?.join(',\n')) || '-',
            (x.failedFollowAccounts?.join(',\n')) || '-'
          ] : []),
          x.status ? '成功' : '失败',
          x.errMsg || '-'
        ])
      }

      exportExcelBase({
        header,
        data
      }, '批量操作结果', this.showFollowAccount ? {
        sourceCellAlignment: {
          wrapText: true,
          vertical: 'top'
        }
      } : undefined)
    },
    doFilter(val) {
      this.generateOptions()
      let data = []
      if ([undefined, ''].includes(val)) {
        data = this.dataList || []
      } else {
        data = this.dataList.filter(v => v.status === val)
      }
      data.forEach(item => {
        this.$set(item, 'expandFlag', false)
      })
      this.tableControlData.allDatas = data
      this.tablePaginationProp.total = this.tableControlData.allDatas.length
    },
    editRecord(row) {
      this.$uiService
        .showDialog(updateRecordDialog, {
          formModel: {
            corpName: row.corpName || '',
            corpKeyNo: row.corpKeyNo || '',
            userGroup: row.userGroup || '',
            functionTableId: this.functionTableId
          }
        }).then(data => {
          row.errorRsn = '-'
          if (data.updated) {
            this.$emit('importSuccess')
            row.status = true
            row.corpName = data.corpName
            row.searchKey = data.corpName
            row.corpKeyNo = data.corpKeyNo
            row.errMsg = ''
            row.filterCode = 'S'
            this.$nextTick(() => {
              if (this.faildCount === 0) {
                this.filterModel = ''
              }
              this.doFilter(this.filterModel)
            })
          }
        })
    }
  }
}
</script>

<style scoped lang="scss">
.result-text {
  background: #F2F8FE;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;

  > span {
    margin: 0 5px;
  }

  > a {
    margin-left: 10px;
  }
}

.result-company-text {
  display: flex;
  //height: 20px;
  align-items: center;
  .comp-text {
    flex: 1;
  }

  .modify-note {
    cursor: pointer;
    color: #bbb;
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;

    &:hover {
      color: #128BED;
      background: #E2F1FD;
    }

    .iconfont.icon-icon_bii {
      font-size: 13px;
    }
  }
}
</style>
