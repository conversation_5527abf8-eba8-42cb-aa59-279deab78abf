<template>
  <el-dropdown placement="bottom-start" class="table-colum-filter-wrapper">
    <span class="el-icon-caret-bottom"></span>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item v-for="(item,index) in options.list" :key="item[nameKey]" @click.native="selectFilter(item)"
                        :class="['filter-item', {actived: myValue!=='' && myValue===item[modelKey]}]">{{item[nameKey]}}</el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  name: 'table-colum-filter',
  props: {
    modelKey: { default: 'value', type: String },
    nameKey: { default: 'name', type: String },
    value: { type: String, default: '' },
    options: { default: () => ({ list: [] }), type: Object }
  },
  data() {
    return {
      myValue: this.value
    }
  },
  watch: {
    value(newValue) {
      this.myValue = newValue
    },
    myValue(newValue) {
      this.$emit('input', newValue)
      this.$emit('change', newValue)
    }
  },
  methods: {
    selectFilter(item) {
      this.myValue = item[this.modelKey]
    }
  }
}
</script>

<style lang="scss">

  .table-colum-filter-wrapper.el-dropdown {
    padding-left: 0;
    .el-icon-caret-bottom {
      color: #999999;
      cursor: pointer;
    }

    .filter-item {
      &.el-dropdown-menu__item {
        width: unset;
        min-width: 78px;
        &.actived {
          color: #128BED;
        }
      }
    }
  }
</style>
