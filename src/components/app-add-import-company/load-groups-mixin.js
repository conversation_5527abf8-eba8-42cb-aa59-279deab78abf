import groupSetService from '../../services/enterprise-center/group-set'

export default {
  data() {
    return {
      groupList: []
    }
  },
  props: {
    functionTableId: { default: 'bene_id' },
    showGroup: {
      type: Boolean,
      default: true
    }
  },
  created() {
    if (this.showGroup) {
      this.initGroupLists()
    }
  },
  methods: {
    initGroupLists() {
      const params = {
        functionTableId: this.functionTableId
      }
      groupSetService.loadAllGroups(params).then(res => {
        this.groupList = [{ groupName: '企业分组', groupValue: '未分组', isEmptyType: true }].concat(res.resultList)
      })
    }
  }
}
