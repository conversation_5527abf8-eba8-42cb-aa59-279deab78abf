import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import ElDropdown from 'element-ui/lib/dropdown'
import ElDropdownMenu from 'element-ui/lib/dropdown-menu'
import ElDropdownItem from 'element-ui/lib/dropdown-item'
import ColumHeaderFilter from '../../include/colum-header-filter.vue'

// Mock Element UI components
// vi.mock('element-ui', async () => {
//   const actual = await vi.importActual('element-ui')
//   return {
//     ...actual,
//     ElDropdown: { template: '<div><span class="el-dropdown-link" @click="$emit(\'click\')"><slot></slot></span><slot name="dropdown"></slot></div>' },
//     ElDropdownMenu: { template: '<div><slot></slot></div>' },
//     ElDropdownItem: { template: '<div :class="{ \'el-dropdown-menu__item\': true, \'active\': active }" @click.native="$emit(\'click\')"><slot></slot></div>', props: ['active'] }
//   }
// })

describe('app-add-import-company/include/colum-header-filter.vue', () => {
  let wrapper

  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    wrapper = mount(ColumHeaderFilter, {
      global: {
      },
      components: {
        ElDropdown,
        ElDropdownMenu,
        ElDropdownItem
      },
      propsData: {
        options: {
          list: [
            { label: '全部', value: '' },
            { label: '成功', value: 'success' },
            { label: '失败', value: 'fail' }
          ]
        },
        modelKey: 'value',
        nameKey: 'label',
        value: ''
      }
    })
  })

  /**
   * 测试组件渲染
   */
  it('renders the component correctly', async () => {
    expect(wrapper.exists()).toBe(true)
    // 检查下拉选项是否正确渲染
    // 点击下拉触发器以展开菜单
    const dropdownTrigger = wrapper.find('.el-dropdown')
    await dropdownTrigger.trigger('click')

    const dropdownItems = wrapper.findAll('.el-dropdown-menu__item')
    expect(dropdownItems.length).toBe(3)
    expect(dropdownItems.wrappers[0].element.textContent).toBe('全部')
    expect(dropdownItems.wrappers[1].element.textContent).toBe('成功')
    expect(dropdownItems.wrappers[2].element.textContent).toBe('失败')
  })

  /**
   * 测试选项选择功能
   */
  it('should select filter option correctly', async () => {
    // 选择"成功"选项
    // 点击下拉触发器以展开菜单
    const dropdownTrigger = wrapper.find('.el-dropdown')
    await dropdownTrigger.trigger('click')

    const successItem = wrapper.findAll('.el-dropdown-menu__item').wrappers[1]
    await successItem.trigger('click')

    // 验证model是否更新
    expect(wrapper.vm.myValue).toBe('success')
    // 验证input事件是否触发
    expect(wrapper.emitted('input')).toBeTruthy()
    expect(wrapper.emitted('input')[0]).toEqual(['success'])
    // 验证change事件是否触发
    expect(wrapper.emitted('change')).toBeTruthy()
    expect(wrapper.emitted('change')[0]).toEqual(['success'])
  })

  /**
   * 测试value prop更新
   */
  it('should update when value prop changes', async () => {
    // 外部更新value prop
    await wrapper.setProps({ value: false })

    // 验证内部状态是否同步更新
    expect(wrapper.vm.myValue).toBe(false)
  })

  /**
   * 测试选中状态样式
   */
  it('should have active class for selected option', async () => {
    // 选择"失败"选项
    // 点击下拉触发器以展开菜单
    const dropdownTrigger = wrapper.find('.el-dropdown')
    await dropdownTrigger.trigger('click')

    const failItem = wrapper.findAll('.el-dropdown-menu__item').wrappers[2]
    await failItem.trigger('click')

    // 验证选中样式是否应用
    expect(failItem.classes()).toContain('actived')
    const itemWrappers = wrapper.findAll('.el-dropdown-menu__item')
    // 验证其他选项没有选中样式
    expect(wrapper.findAll('.el-dropdown-menu__item').wrappers[0].classes()).not.toContain('actived')
    expect(wrapper.findAll('.el-dropdown-menu__item').wrappers[1].classes()).not.toContain('actived')
  })
})
