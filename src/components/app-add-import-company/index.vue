<template>
  <app-popup ref="popRef" title="重新发送企业年检结果" :control="{show:true}" :buttonOptions="buttonOptions" :options="popOptions" @closed="close" :bgClose="false"  :footShow="footShowConfirm">
    <div slot="title" class="title-wrapper">
      <template v-if="importResultPage">
        导入完成
      </template>
      <template v-else>
        <span v-for="(item,index) in tabs" :key="item.type" @click="changeTab(item)"
              :class="['title-item', {actived: currentSelect === item.type}]">
        {{item.name}}
      </span>
      </template>
    </div>
    <template v-if="importResultPage">
      <import-result :functionTableId="_functionTableId" :data-list="resultList" :showGroup="showGroup" @importSuccess="importSuccess"></import-result>
    </template>
    <template v-else-if="currentSelect === 'add'">
      <add ref="addRef" :functionTableId="_functionTableId" :source="source"></add>
    </template>
    <template v-else-if="currentSelect === 'paste'">
      <batch-paste ref="pasteRef" :functionTableId="_functionTableId" :uploadsuccess="uploadsuccess" :showGroup="showGroup" :limit="pastMaxCount"  @change="changeData">
        <div slot="footerLeft" style="color: #999999;font-size: 14px;position: absolute;bottom: 10px;bottom: 1px;left: 15px; width: calc(50% - 70px); height: 34px; line-height: 32px; background: #fff;">
          文本粘贴导入单次至多可匹配{{pastMaxCount}}行
        </div>
      </batch-paste>
      <!-- <div slot="footerLeft" style="color: #999999;font-size: 12px;">
        注：文本粘贴导入单次至多可匹配<strong>{{pastMaxCount}}</strong>行
      </div> -->
    </template>
    <template v-else-if="currentSelect === 'import'">
      <batch-import
        :uploadsuccess="uploadsuccess"
        :limitCount="maxCount"
        :showGroup="showGroup"
        :showLimitCount="!showGroup"
        :functionTableId="functionTableId"
      ></batch-import>
    </template>
    <app-loading :control="loadingProp" v-if="loadingProp.show"></app-loading>
  </app-popup>
</template>

<script>
import companyListService from '../../services/enterprise-center/company-list'
import { loadingMixins } from '../../utils/mixinsUtils'
import getConfigService from '../../services/codetable'
import commonService from '../../services/common-service'

/**
 * 隐形可传入参数
 * @param {String} defaultType 默认选择tab
 * @param {Array} removeItems  需要移除的tab中的type值
 * @param {String} functionTableId  类型
 * @param {Number} limitCount  最大企业数量
 */
export default {
  name: 'app-add-import-company',
  mixins: [loadingMixins],
  components: {
    add: () => import('./include/add'),
    batchPaste: () => import('./include/batch-paste'),
    batchImport: () => import('./include/batch-import'),
    importResult: () => import('./include/import-result')
  },
  data() {
    return {
      tabs: [
        { name: '添加企业', type: 'add' },
        { name: '批量粘贴', type: 'paste' },
        { name: '批量上传', type: 'import' }
      ],
      currentSelect: 'add',
      importResultPage: false,
      resultList: [],
      needUpdateGroupName: false,
      maxCount: 100,
      pastMaxCount: 100,
      PFootShowConfirm: false,
      source: 'ADDIMPORT'
    }
  },
  computed: {
    footShowConfirm() {
      if (this.currentSelect === 'paste') {
        return this.PFootShowConfirm
      }
      return true
    },
    buttonOptions() {
      let arr = [
        { name: '取消', type: 'normal' },
        { name: '确定', type: 'primary', click: this.sureClick }
      ]
      if (this.importResultPage) {
        arr.splice(0, 1)
      }
      return arr
    },
    popOptions() {
      return {
        width: this.currentSelect === 'add' ? '600px' : '960px',
        contentStyle: {
          'padding-bottom': this.importResultPage ? '0' : '15px'
        }
      }
    },
    _functionTableId() {
      return this.functionTableId || 'bene_id'
    },
    showGroup() {
      return !(this.functionTableId === 'expand_cust_list_high_tech' || this.functionTableId === 'bidding_id' || this.functionTableId === 'expand_cust_list_science' || this.functionTableId === 'expand_cust_list_manufactur' || this.functionTableId === 'expand_cust_list_honor' || this.functionTableId === 'finance_proposed_list_export')
    }
  },
  created() {
    if (this.removeItems) {
        this.tabs = this.tabs.filter(row => !this.removeItems.includes(row.type))
    }
    if (this.defaultType && this.tabs.map(v => v.type).includes(this.defaultType)) {
      this.currentSelect = this.defaultType
    }

    if (this.functionTableId === 'finance_proposed_list_export') {
        this.getLimitCount('max_import_finance_proposed_list_num')
    } else if (this.functionTableId === 'expand_cust_list_high_tech') {
        this.getLimitCount('max_import_high_tech_corp_num')
    } else if (this.functionTableId === 'expand_cust_list_science') {
        this.getLimitCount('max_import_science_corp_num')
    } else if (this.functionTableId === 'expand_cust_list_manufactur') {
        this.getLimitCount('max_import_manufactur_corp_num')
    } else if (this.functionTableId === 'expand_cust_list_honor') {
        this.getLimitCount('max_import_honor_corp_num')
    } else if (this.functionTableId === 'bidding_id') {
      commonService.postVue('/webapi/saas/expan_cust/tender/getTenderUploadCorpMaxCount', {}).then(res => {
        if (res.result) {
          this.maxCount = Number(res.result)
        }
      })
    } else if (this.limitCount) {
        this.maxCount = this.limitCount
    }
  },
  methods: {
    changeData(list) {
        // console.log(11111, list)
        if (list && list.length) {
          this.PFootShowConfirm = true
        } else {
          this.PFootShowConfirm = false
        }
        // console.log(222, this.PFootShowConfirm)
    },
    getLimitCount(label) {
      let params = {
       label: label
      }
      getConfigService.getDictValue4SystemConifg(params)
        .then(res => {
          if (res.result) {
            this.maxCount = Number(res.result)
          }
        })
    },
    importSuccess() {
      this.addCompanyFlag = 'Y'
    },
    importCompany(fileUrl, fileName, uploadCompanyList, groupValue) {
      // $util.zhugeTrackTwoLevel('企业列表上传', '批量操作')
      this.showLoadding()
      let params = {
        functionTableId: this._functionTableId
      }
      if (!this.uploadNotSource) {
        params.source = this.source
      }
      if (fileUrl) {
        params.url = fileUrl
        this.needUpdateGroupName = true
      } else {
        params.importMode = 'fuzzy'
        params.importList = uploadCompanyList
        params.groupValue = groupValue
      }
      if (!this.showGroup) {
        if (!fileUrl) {
          params = {
            functionTableId: this._functionTableId,
            importMode: 'fuzzy',
            importList: uploadCompanyList
          }
        }

        return new Promise(resolve => {
          companyListService.uploadCompanyLatent(params, { showLoading: false })
            .then(res => {
              if (+res.status === 500) {
                this.hideLoadding()
                this.$message.error(res.msg)
                return
              }
              this.resultList = res.result.companyList
              this.importResultPage = true
              this.addCompanyFlag = 'Y'
              this.hideLoadding()
              resolve()
            }).catch(e => {
            this.catchData(e)
            this.hideLoadding()
            resolve(e)
          })
        })
      } else {
        return new Promise(resolve => {
          companyListService.uploadCompany(params, { showLoading: false })
            .then(res => {
              if (+res.status === 500) {
                this.hideLoadding()
                this.$message.error(res.msg)
                return
              }
              this.resultList = res.result.companyList
              this.importResultPage = true
              this.addCompanyFlag = 'Y'
              this.hideLoadding()
              resolve()
            }).catch(e => {
            this.catchData(e)
            this.hideLoadding()
            resolve(e)
          })
        })
      }
    },
    uploadsuccess({ url, fileName, uploadCompanyList, str, shortStr, groupValue }) {
      this.uploadFileName = fileName || shortStr
      return new Promise((resolve, reject) => {
        this.importCompany(url, fileName, uploadCompanyList, groupValue)
      })
    },
    catchData(e) {
      try {
        let err = JSON.parse(e.toString().replace('Error:', ''))
        if (['115'].includes(`${err.status}`)) { // '114', '112',
          setTimeout(() => {
            this.$refs.popRef.close()
            this.$promise.resolve(true)
          }, 150)
        }
      } catch (e) {
      }
    },
    changeTab(item) {
      this.currentSelect = item.type
    },
    sureClick() {
      if (this.importResultPage) {
        this.$refs.popRef.close()

        // 去重同样的企业
        let map = new Map()
        for (let item of this.resultList) {
          map.set(item.corpKeyNo, item)
        }
        this.resultList = [...map.values()]

        this.$promise.resolve({ addCompanyFlag: this.addCompanyFlag === 'Y', resultList: this.resultList })
      } else {
        if (this.currentSelect === 'add') {
          this.$refs.addRef.doAddCompany().then(res => {
            this.$refs.popRef.close()
            this.$promise.resolve({ addCompanyFlag: this.addCompanyFlag === 'Y' })
          })
        } else if (this.currentSelect === 'paste') {
          this.$refs.pasteRef.sureSuccess().then((res) => {
            // this.$refs.popRef.close()
            // this.$message.success('新增企业成功')
            // this.$promise.resolve(true)
          })
        } else if (this.currentSelect === 'import') {
          this.$refs.popRef.close()
          this.$promise.resolve({ addCompanyFlag: this.addCompanyFlag === 'Y' })
        }
      }
    },
    close() {
      if (this.needUpdateGroupName) {
        window.AppRuntimeContext.eventBus.$emit(window.AppRuntimeContext.eventBusEvents.GROUP_HAS_CHANGED)
      }
      this.$promise.resolve(this.addCompanyFlag === 'Y')
    }
  }
}
</script>

<style scoped lang="scss">
.title-wrapper {
  height: 50px;
  display: flex;
  align-items: center;
  .title-item {
    line-height: 49px;
    margin-right: 30px;
    color: #999999;
    font-size: 15px;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    &:hover {
      color: #999999;
    }
    &.actived {
      color: #333;
      border-bottom: 3px solid #128BED;
    }

  }

}

</style>
