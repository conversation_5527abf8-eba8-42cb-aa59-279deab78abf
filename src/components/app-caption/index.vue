<template>
  <div
    class="app-caption"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <h3 class="title">
      <slot v-if="$slots.title" name="title"></slot>
      <template v-else>{{ title }}</template>
    </h3>
    <div class="right">
      <slot name="filter" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'app-caption',
  props: {
    title: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang="scss">
.app-caption {
  height: 32px;
  margin-bottom: 10px;

  .title {
    font-size: 14px;
    color: #666;
    font-weight: bold;
    margin-top: 0;
    margin-bottom: 0;
    display: inline;
    line-height: 32px;
  }

  .right {
    float: right;
  }
}
</style>
