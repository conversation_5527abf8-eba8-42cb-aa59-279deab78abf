<template>
  <div class="multiple-input-section" :class="isActive || (dropShow && dropList.length) ?'isActive':''" ref="mySection">
    <div class="selected-cell" ref="mySelectedCell" :style="selectedStyle" v-if="selects.length">
      <span class="sub-cell" v-for="(item,index) in selects">
        <span class="sub-label">{{item.label}}</span>
        <i class="iconfont icon-icon_guanbixx" @click="remove(item,index)"></i>
      </span>
    </div>
    <i class="iconfont icon-sousuo3" :class="selects.length?'m-l-5':'m-l-10'"></i>
    <input type="text" class="text-input" :style="inputStyle" :placeholder="placeholder" ref="key" @keyup.enter="handleEnter" />
    <div class="drop-content-section" :style="dropStyle" v-show="dropShow && dropList.length">
      <div class="drop-cell" v-for="(item,index) in dropList" @click="selectClick(item)">
        <span class="drop-cell-label" v-html="item.richLabel || item.label"></span>
      </div>
    </div>
  </div>
</template>

<script src="./component.js"></script>
<style scoped lang="scss" src="./style.scss"></style>
