export default {
  props: {
    placeholder: {
      type: String,
      default: '请输入已合作开户行'
    },
    searchList: {
      type: Array,
      default: () => ([])
    },
    selectLimitCount: {
      type: Number,
      default: 5
    },
    limitMsg: {
      type: String,
      default: '已合作开户行最多可选择5家'
    }
  },
  data() {
    return {
      selectedStyle: '',
      inputStyle: '',
      dropStyle: 'top:37px',
      selects: [],
      dropList: [],
      dropShow: false,
      isActive: false,
      inputValue: '',
      typing: false
    }
  },
  watch: {
    searchList: {
      handler(val1, val2) {
        if (this.inputValue) {
          this.dropList = val1
        } else {
          this.dropList = []
        }
        if (val1.length) {
          this.dropShow = true
        } else {
          this.dropShow = false
        }
      },
      deep: true,
      immediate: false
    }
  },
  mounted() {
    this.onResize()
    this.addEventListener2()
  },
  methods: {
    onResize() {
      if (this.$refs.mySection) {
        const height = this.$refs.mySection.clientHeight
        let dropTop = height + 5 + 'px'
        this.dropStyle = 'top:' + dropTop
      }

      // this.selectedStyle = ''
      if (this.$refs.mySelectedCell) {
        const width = this.$refs.mySelectedCell.clientWidth
        // console.log('width')
        // console.log(width)
        // console.log(this.$refs.mySelectedCell)
        // if (width < 300) {
        //   this.selectedStyle = 'max-width:' + width + 'px'
        // }
        // console.log('selectedStyle')
        // console.log(this.selectedStyle)
        let inputWidth = 462 - width - 30
        // console.log('input width')
        // console.log(inputWidth)
        this.inputStyle = 'width:' + inputWidth + 'px'
      } else {
        this.inputStyle = 'width:430px'
      }
    },
    // 计算宽度
    calcWidth() {

     },
    addEventListener2() {
      document.addEventListener('click', (e) => {
        // console.log('lister click')
        // console.log(e)
        let className = e.target.className
        if (
          className !== 'multiple-input-section' &&
          className !== 'text-input' &&
          className !== 'drop-cell' &&
          className !== 'drop-cell-label'
        ) {
          this.dropShow = false
        } else {
          this.dropShow = true
        }

        if (
          className !== 'multiple-input-section' &&
          className !== 'text-input' &&
          className !== 'selected-cell' &&
          className !== 'sub-cell'
        ) {
          this.isActive = false
        } else {
          this.isActive = true
        }
      })
      var that = this
      $('.text-input').on('compositionstart', function() {
        // console.log('compositionstart')
        that.typing = true
      })
      $('.text-input').on('compositionend', function() {
        // console.log('compositionend')
        that.typing = false
      })
      $('.text-input').on('input', function () {
        setTimeout(function() {
          if (!that.typing) {
            var inputContent = $('.text-input').val()
            // console.log('实时获取输入的内容-----' + inputContent)
            if (inputContent.length < 2 || $.trim(inputContent) === '') {
              // 输入框没有值
              that.inputValue = ''
              that.dropList = []
              that.dropShow = false
            } else {
              // 输入框有值，就发起网络请求获取搜索内容
              that.inputValue = inputContent
              that.$emit('requestData', inputContent)
            }
          }
        }, 1)
      })

      // $('.text-input').on('focus', function () {
      //   console.log('focus')
      //   that.focusFunc()
      // })
      // $('.text-input').on('blur', function () {
      //   console.log('blur')
      //   // that.focusFunc()
      // })
    },
    handleEnter() {
       // console.log('kk')
    },
    remove(item, index) {
      this.selects.splice(index, 1)
      setTimeout(() => {
        this.onResize()
      }, 100)
      this.$emit('change', this.selects)
    },
    selectClick(item) {
      // console.log('selectClick')
      // console.log(item)
      if (this.selects.length > this.selectLimitCount || this.selects.length === this.selectLimitCount) {
        this.$message.warning(this.limitMsg)
        return
      }
      if (!this.selects.length) {
        this.selects.push(item)
      } else {
        let find = this.selects.find(
          (v) => JSON.stringify(v) === JSON.stringify(item)
        )
        if (find) {
          this.$message.warning('已选中，请勿重复添加')
          return
        } else {
          this.selects.push(item)
        }
      }
      setTimeout(() => {
        this.onResize()
      }, 100)
      this.$emit('change', this.selects)
    },
    // 给初始化用的
    makeInitSelects(selects) {
      this.selects = selects || []
      setTimeout(() => {
        this.onResize()
      }, 100)
    },
    focusFunc() {
      // console.log('focus===>>>222')
    }
  }
}
