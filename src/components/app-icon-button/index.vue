<template>
  <div v-if="common" class="qcc-app-icon-button" v-bind="$attrs" v-on="$listeners">
    <span :class="icon"></span>
    <slot></slot>
  </div>
  <el-popover v-else-if="!!content && !disabled" placement="bottom" title="" :width="width" popper-class="default-popover-tooltip app-popover topIndex" trigger="hover" :content="content" :append-to-body="appendToBody" :offset="offset">
    <div v-bind="$attrs" v-on="$listeners" class="fun-icon" slot="reference"
         :class="[{left20}, {left0}, {'mini': miniTheme}, {'size22': size22}, {disabled}]" >
      <span :class="icon"></span>
      <slot></slot>
    </div>
  </el-popover>
  <div v-else v-bind="$attrs" v-on="$listeners" class="fun-icon"
       :class="[{left20}, {left0}, {'mini': miniTheme}, {'size22': size22}, {disabled}]">
    <span :class="icon"></span>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'app-icon-button',
  props: {
    content: { default: '', type: String },
    icon: { default: '', type: String },
    width: { default: '', type: String },
    left20: { default: false, type: Boolean },
    left0: { default: false, type: Boolean },
    miniTheme: { default: false, type: Boolean }, // 长宽 20  图标字号13
    size22: { default: false, type: Boolean }, // 长宽22 图标字号14
    common: { default: false, type: Boolean },
    disabled: { default: false, type: Boolean },
    appendToBody: {
      type: Boolean,
      default: true
    },
    offset: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {}
  }
}
</script>

<style scoped lang="scss">
.qcc-app-icon-button {
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  height: 22px;
  color: #333;
  cursor: pointer;
  padding: 0 5px;
  border-radius: 2px;
  &.color-primary {
    color: #128bed;
  }

  &:hover {
    background: #F2F8FE;
    color: #128BED;
  }
  ::v-deep .iconfont {
    font-size: 14px;
    color: #128BED;
    margin-right: 5px;
  }
}

.fun-icon {
  display: inline-flex;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 10px;
  position: relative;
  &.left0 {
    margin-left: 0px;
  }
  &.left20 {
    margin-left: 20px;
  }
  &:hover:not(.disabled) {
    background: #F2F8FE;
    ::v-deep .iconfont {
      color: #128BED!important;
    }
  }
  &.disabled,
  &.disabled.mini {
    cursor: not-allowed;
    ::v-deep .iconfont {
      &.primary {
        color: #B8DCFA;
      }
    }
  }
  &.mini {
    width: 20px;
    height: 20px;
    &.size22 {
      width: 22px;
      height: 22px;
      .iconfont {
        font-size: 14px;
      }
    }
    .iconfont {
      font-size: 13px;
      color: #bbb;
      &.primary {
        color: #128bed;
      }
    }
    &:hover:not(.disabled) {
      background: #E5F2FD;
    }
  }
  ::v-deep .iconfont {
    font-size: 18px;
  }
}

</style>
