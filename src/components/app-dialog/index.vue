<template>
  <!-- <div class="modal fade" ref="dialog">
    <div :class="['modal-dialog']">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span></button>
          <slot class="modal-title" name="title">
          </slot>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-xs-24">
              <slot name="body"></slot>
            </div>
          </div>
        </div>
        <div class="text-center modal-footer">
          <slot name="footer"></slot>
        </div>
      </div>
    </div>
  </div> -->
</template>

<script>
export default {
  name: 'app-dialog',
  props: {
    size: {
      type: String,
      default: ''
    }
  }
}

</script>

<style lang="scss" scoped>

</style>
