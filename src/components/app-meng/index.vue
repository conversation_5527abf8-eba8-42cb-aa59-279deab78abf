<template>
  <div v-if="lock2" class="mengbang2"  @click.stop="unlock">
    <slot>
      <div class="mengban-txt-2">
        <span class="iconfont icon-icon_suok"></span>
        <span class="text" v-html="text"></span>
        <app-button class="app-button" type="normal" size="mini"  @click.native.stop="unlock" >立即添加</app-button>
      </div>
    </slot>
  </div>
  <div v-else class="mengbang" :class="{ 'no-bg-img': noBgImg }"  @click.stop="unlock">
    <slot>
      <div class="mengban-txt" :class="{ margin: nomargin }"><span
          class="iconfont icon-icon_suok"></span><span class="text" v-html="text"></span></div>
    </slot>
  </div>
</template>

<script>
import lock from '../app-lock/lock.js'
export default {
  name: 'app-meng',
  props: {
    title: {
      type: String,
      default: '添加至列表解锁'
    },
    keyNo: {
      type: String,
      default: ''
    },
    name: {
      type: String,
      default: ''
    },
    isLock: {
      type: Boolean,
      default: true
    },
    isRadar: {
      type: Boolean,
      default: true
    }, // 风险监控
    isBene: {
      type: Boolean,
      default: true
    }, //  尽职调查
    functionid: {
      type: String,
      default: ''
    },
    nomargin: {
      type: Boolean,
      default: false
    },
    noBgImg: {
      type: Boolean,
      default: false
    },
    lock2: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    text() {
      if (this.functionid === 'bene_id') {
        return '添加尽调解锁'
      } else if (this.functionid === 'radar_id') {
        return '添加风控解锁'
      }
      return this.title
    }
  },
  methods: {
    unlock() {
      this.$emit('click')
      lock.show(this.keyNo, this.name, this.isRadar, this.isBene, this.functionid)
    }
  }
}
</script>
<style lang="scss">
.mengbang2 {
  width: 100%;
  height: 60px;
  cursor: pointer;
  background: #FFF9F2;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: #FFF2E0;
  }

  .mengban-txt-2 {
    font-size: 14px;
    color: #B96C30;
    display: flex;
    align-items: center;
    line-height: 24px;
    .iconfont {
      font-size: 14px;
      margin-right: 5px;
    }

    .text {
      margin-right: 15px;
    }

    .app-button {
      border-width: 0px;
      color: #713100;
      border-radius: 2px;
      opacity: 1;
      padding-left: 5px;
      padding-right: 5px;
      /* 会员/VIP/按钮背景-#F6CDA0 */
      background: #F6CDA0;
      height: 24px;
      line-height: 24px;
      &:hover {
        color: #713100;
        background: #F2B879;
      }
    }
  }
}

.mengbang {
  cursor: pointer;
  width: 100%;
  background-image: url(./mengban.png);
  background-size: 130%;
  text-align: center;
  // height: 22px;
  background-repeat: no-repeat;
  background-position: center;

  &.no-bg-img {
    background-image: none;
  }

  .mengban-txt {
    // cursor: pointer;
    margin: 16px auto;
    display: inline-flex;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    height: 22px;
    letter-spacing: 0em;
    /* 6标签色/会员金（会员权益）/会员金1常规-#BB833D */
    color: #B96C30;

    &.margin {
      .iconfont {
        background-color: transparent;
        padding-left: 0px;
        padding-right: 0px;
      }

      .text {
        margin-left: 2px;
      }

      &:hover {
        .iconfont {
          background-color: transparent;
        }
      }
    }

    .iconfont {
      font-size: 14px;
      display: inline-block;
      background-color: #F9EEE2;
      color: #B96C30;
      padding: 0px 24px;
    }

    .text {
      margin-left: 20px;
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      letter-spacing: 0em;
      /* 6标签色/会员金（会员权益）/会员金1常规-#BB833D */
      color: #B96C30;
    }

  }

    &:hover {
      .iconfont {
        background-color: #FFF0E0;
      }
    }
}
</style>
