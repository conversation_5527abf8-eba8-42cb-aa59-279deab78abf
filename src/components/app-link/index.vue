<template>
  <div class="app-link">
    <el-link :underline="false" type="primary" v-bind="$attrs" v-on="$listeners">
      <template v-for="(_, slot) of $slots">
        <slot :name="slot" />
      </template>
    </el-link>
  </div>
</template>

<script>
export default {
  name: 'app-link'
}
</script>

<style lang="scss" scoped>
.app-link {
  display: inline-flex;
  line-height: 22px;
  ::v-deep {
    .el-link--inner {
      display: flex;
    }
    .el-link--primary {
      &:hover {
        color: $color-primary-hover;
        span {
          color: inherit;
        }
      }
    }
  }
}
</style>
