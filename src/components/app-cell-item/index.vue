<template>
  <div class="app-cell-item" :style="itemStyle">
    <div v-if="!hideLeft" :class="[$attrs.labelClass,labelPos === 'left' ? 'app-cell-item-left-text' : 'app-cell-item-left']" :style="style">{{ title }} </div>
    <div class="app-cell-item-right" :class="buttonPos === 'right' ? 'button-right' : ''" :style="itemStyle">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'app-cell-item',
  props: {
    // 左侧文字内容
    title: {
      type: String,
      default: () => ' '
    },
    labelPos: {
      type: String,
      default: 'right'
    },
    buttonPos: {
      type: String,
      default: 'left'
    },
    hideLeft: {
      type: Boolean,
      default: false
    },
    // 左侧文字宽度
    width: {
      type: [String, Number],
      default: () => ''
    },
    height: {
      default: 50
    }
  },
  inheritAttrs: false,
  computed: {
    itemStyle() {
      return {
        height: `${this.height}px`
      }
    },
    style() {
      let realWidth = this.width ? this.width : (this.$parent.getLabelWidth || '')
      let title = this.title
      if (title) {
        return {
          width: realWidth === 'auto' ? 'auto' : `${realWidth}px`,
          minWidth: realWidth === 'auto' ? 'auto' : `${realWidth}px`,
          lineHeight: `${this.height}px`
        }
      } else {
        return {
          width: 'auto',
          minWidth: 'auto'
        }
      }
    }
  }
}

</script>
<style scoped lang="scss">
@import '../../styles/common.scss';

.text-center {
  text-align: center !important;
}
.button-right{
  justify-content: flex-end;
}

.app-cell-item {
  @include flex-def;
  height: 50px;

  &.height40 {
    height: 40px;
  }

  .app-cell-item-left {
    text-align: right;
    // padding-right: 5px;
    font-size: 14px;
    color: #333;
    padding-right: 10px;
  }
  .app-cell-item-left-text {
    text-align: left;
    font-size: 14px;
    color: #333;
    padding-right: 5px;
  }

  .app-cell-item-right {
    @include flex-item;
    @include flex-def;
    @include flex-cCenter;

    // padding-left: 5px;
    height: 36px;
    position: relative;

    .el-input {
      height: 36px;
    }

  }

  &.autoHeight {
    min-height: 40px !important;
    height: auto !important;

    &.lineheight40 {
      line-height: 40px;
    }

    .app-cell-item-left {
      @include flex_align_self-start;
    }

    .app-cell-item-right {
      height: auto !important;
    }
  }

}

</style>
