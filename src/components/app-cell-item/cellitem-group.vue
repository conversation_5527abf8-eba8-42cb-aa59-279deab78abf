<template>
  <div class="_cellitem-group border-radius-4" ref="rootRef" :class="`${options.isLine ? 'inline' : ''}`">
    <slot></slot>
  </div>

</template>

<script>
export default {
  name: 'app-cellitem-group',
  props: {
    labelWidth: {
      default: 120
    },
    options: {
      default: () => {
        return {
          isLine: false // 是否显示一行
        }
      }
    }
  },
  computed: {
    getLabelWidth() {
      return this.options.isLine ? 'auto' : this.labelWidth
    }
  }

}

</script>

<style scoped lang="scss">
  @import "../../styles/common";

  ._cellitem-group {
    width: 100%;
    background: white;
    > *{
      white-space: nowrap;
    }

    &.inline {
      @include inline-flex-def;
      @include flex-cCenter;

      .app-cell-item {
        margin-left: 20px;
        .app-cell-item-left{
          font-size: 12px;
        }

        &:first-child {
          margin-left: 0;
        }

        &.margin-l-15 {
          margin-left: 15px;
        }
        &.margin-l-10 {
          margin-left: 10px;
        }
      }
    }

  }

</style>
