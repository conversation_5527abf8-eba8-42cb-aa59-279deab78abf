<template>
  <div class="app-desc">
    <div class="desc-sub-title" v-if="subTitle" v-html="subTitle"></div>
    <template v-if="hasdesc">
      <div class="desc-desc">
        <slot name="desc">
          <div v-html="desc"></div>
        </slot>
        <div v-if="hasMark">
          <slot name="export"></slot>
        </div>
      </div>
      <div class="desc-table">
        <slot></slot>
      </div>
    </template>
    <template v-if="hasdesc2">
      <div class="desc-desc" :class="hasdesc && hasdesc2?'desc-desc2':''">
        <slot name="desc2">
          <div v-html="desc2"></div>
        </slot>
        <div v-if="hasMark2">
          <slot name="export2"></slot>
        </div>
      </div>
      <div class="desc-table">
        <slot name="table2"></slot>
      </div>
    </template>
  </div>
</template>

<script>
  export default {
    name: 'app-desc',
    props: {
      desc: { type: String, default: '' },
      desc2: { type: String, default: '' },
      hasdesc: { type: Boolean, default: true },
      hasdesc2: { type: Boolean, default: false },
      hasMark: { type: Boolean, default: false },
      hasMark2: { type: Boolean, default: false },
      subTitle: { type: String, default: '' }
    },
    data() {
      return {}
    },
    mounted() {

    },

    methods: {
      // this.row
    }

  }

</script>

<style lang="scss" scoped>
  .app-desc {

    .desc-sub-title {
      font-size: 14px;
      line-height: 32px;
      color: #999;
      font-weight: bold;
    }

    .desc-desc {
      font-size: 14px;
      height: 32px;
      line-height: 32px;
      color: #999;

      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .desc-desc2 {
      margin-top: 10px;
    }

    .desc-table {
      margin-top: 10px;
    }
  }

</style>
