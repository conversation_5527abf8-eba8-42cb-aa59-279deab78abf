import { requestService } from '../../services/broswer-service'
export default {
  getList(inputValue, selectedObject, selectList) {
    const params = {
      page: { pageNo: 1, pageSize: 10, orderBy: '' },
      isRedirect: true,
      searchKey: inputValue
    }
    selectedObject = selectedObject || {}
    selectList = selectList || []
    return new Promise((resolve, reject) => {
      requestService
        .searchMultiList({ condition: params })
        .then((res) => {
          if (res.resultList && res.resultList.length) {
            res.resultList.forEach((item) => {
              item.corpKeyNo = item.keyNo
              item.corpName = item.nameEm || item.name
              item.type = null
              if (selectedObject.corpKeyNo === item.corpKeyNo) {
                item.isSelected = true
              }
              if (selectList.find((e) => e.corpKeyNo === item.corpKeyNo)) {
                item.isSelected = true
              }
              if (item.operInfo) {
                try {
                  const operInfo = JSON.parse(item.operInfo)
                  if (operInfo.k) {
                    item.operInfoAll = {
                      corpName: item.operName,
                      corpKeyNo: operInfo.k || '',
                      imageUrl: item.operImageUrl,
                      type: $util.operTypeName(+operInfo.t),
                      isSelected:
                        selectedObject.corpKeyNo === operInfo.k ||
                        selectList.find((e) => e.corpKeyNo === operInfo.k)
                    }
                  } else {
                    item.operInfoAll = null
                  }
                } catch {
                  item.operInfoAll = null
                }
              }

              if (item.hitReasons && item.hitReasons.length) {
                item.hitReasonsAll = []
                item.hitReasons.forEach((reason) => {
                  if (
                    reason.value &&
                    (reason.field === '历史股东' ||
                      reason.field === '投资人' ||
                      reason.field === '主要人员')
                  ) {
                    let iekeyNo = reason.eI ? reason.eI.keyNo : ''
                    item.hitReasonsAll.push({
                      corpName: reason.value,
                      corpKeyNo: iekeyNo,
                      type: reason.field,
                      isSelected:
                        selectedObject.corpKeyNo === iekeyNo ||
                        selectList.find((e) => e.corpKeyNo === iekeyNo)
                    })
                  }
                })
              }
            })
          }
          resolve(res.resultList)
        })
        .catch(() => {})
    })
  }
}
