<template>
  <el-popover visible-arrow="false" placement="bottom" popper-class="advance-pop-up" :width="popWidth" :offset="offset"
    trigger="manual" v-model="isShowPop">
    <pop-content class="advance-pop-content" :searchList="resultList" :loading="loading" :showOper="showOper" :follow="follow"
      @selected="popSelected"></pop-content>
    <div slot="reference" class="advance-pop-wrap" v-click-outside="handleBlur">
      <el-input size="small" ref="inputFocus" :placeholder="placeholder" v-model="inputValue" @focus="inputFocus"
        @input="inputChangeSelected" :class="selectedObject && selectedObject.corpKeyNo ? 'selected-active' : ''">
        <i v-if="showClear" slot="suffix" class="iconfont icon-shanchu3" @click.stop="clear"></i>
      </el-input>
      <div v-show="showMok && inputValue" @click="showMokF" class="advance-pop-wrap-mok">
        <span>{{ inputValue }}</span>
        <i class="iconfont icon-shanchu3" @click.stop="clear"></i>
      </div>
    </div>
  </el-popover>
</template>

<script>
import { batchImportSingleCorpFromAutocomplete } from '../../services/through'
import clickOutside from '../app-select-dropmenu/clickOutSide'
import servicePop from './service-pop'
import popContent from './pop'
export default {
  name: 'app-advance-search',
  components: { popContent },
  directives: { clickOutside },
  props: {
    from: { type: String, default: 'DEEPCHECK' },
    functionTableId: { type: String, default: 'bene_id' },
    offset: { type: Number, default: 46 },
    popWidth: { type: Number, default: 370 },
    placeholder: { type: String, default: '请输入企业/人员名称' },
    // 绑定的值
    value: {
      type: Object,
      default: () => { }
    },
    list: {
      type: Array,
      default: () => []
    },
    showOper: {
      type: Boolean,
      default: true
    },
    follow:
    {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      showMok: false,
      resultList: [],
      inputValue: '',
      timeout: null,
      selectedObject: {},
      isShowPop: false,
      loading: false
    }
  },
  // 创建组件的时候设置默认值
  created() {
    this.setSelectedObject(this.value)
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  watch: {
    // 监听用户的值
    value(newValue) {
      this.setSelectedObject(newValue)
    }
  },
  computed: {
    showClear() {
      return !!this.inputValue
    }
  },
  methods: {
    // 失去焦点-隐藏弹框
    handleBlur() {
      this.isShowPop = false
      this.showMok = true
    },
    showMokF() {
      if (this.$refs.inputFocus) { this.$refs.inputFocus.focus() }
    },
    // 获取焦点
    inputFocus() {
      this.showMok = false
      if (this.resultList && this.resultList.length && this.inputValue) {
        this.isShowPop = true
      } else {
        this.inputChange()
      }
    },

    // 初始化赋值
    setSelectedObject(newValue) {
      this.selectedObject = newValue || {}
      this.inputValue = ''
      if (this.selectedObject && this.selectedObject.corpKeyNo) {
        this.inputValue = $util.replaceHTMLLabel(this.selectedObject.corpName || '')
      }
    },

    // 清除
    cleat4SelectObject() {
      this.selectedObject = {}
      this.resultList = null
      this.isShowPop = false
      this.loading = false
    },
    reset() {
      this.cleat4SelectObject()
      this.inputValue = ''
      this.$emit('change', this.selectedObject)
      this.$emit('select', this.selectedObject)
    },
    clear() {
      this.cleat4SelectObject()
      this.inputValue = ''
      this.$emit('change', this.selectedObject)
      this.$emit('clear')
    },
    // 输入-及其改变
    inputChangeSelected() {
      this.cleat4SelectObject()
      this.inputChange()
    },
    inputChange(noNeedShow) {
      if (this.inputValue) {
        clearTimeout(this.timeout)
        let vtr = this.inputValue.trim()
        if (vtr.length >= 2) {
          // 实现input连续输入，只发一次请求
          this.timeout = setTimeout(() => {
            this.loading = true
            servicePop
              .getList(this.inputValue, this.selectedObject, this.list)
              .then((resultList) => {
                this.resultList = resultList
                if (!noNeedShow) {
                  this.isShowPop = !!(resultList && resultList.length)
                } else {
                  this.isShowPop = false
                }
                this.loading = false
              })
          }, 300)
        }
      }
    },
    // 弹框选择
    popSelected(item) {
      this.isShowPop = false
      if (item.corpKeyNo) {
        if (item.corpKeyNo[0] === 'p' || !this.follow) {
          this.selectedObject = {
            corpKeyNo: item.corpKeyNo,
            corpName: $util.replaceHTMLLabel(item.corpName || ''),
            matchedMode: '',
            isSelected: true
          }
          this.inputValue = $util.replaceHTMLLabel(item.corpName || '')
          this.inputChange(true)
          this.$emit('change', this.selectedObject)
          this.$emit('select', this.selectedObject)
        } else {
          this.clickToCare(item).then(() => {
            this.selectedObject = {
              corpKeyNo: item.corpKeyNo,
              corpName: $util.replaceHTMLLabel(item.corpName || ''),
              matchedMode: '',
              isSelected: true
            }
            this.inputValue = $util.replaceHTMLLabel(item.corpName || '')
            this.inputChange(true)
            this.$emit('change', this.selectedObject)
            this.$emit('select', this.selectedObject)
          })
        }
      }
    },
    clickToCare(item) {
      let corpList = [{ corpKeyNo: item.corpKeyNo, corpName: $util.replaceHTMLLabel(item.corpName || '') }]
      return batchImportSingleCorpFromAutocomplete({
        corpList: JSON.stringify(corpList),
        functionTableIdList: [this.functionTableId],
        source: this.from
      })
    }
  }
}
</script>
<style lang="scss">
.advance-pop-up {
  padding: 0px;
}

.advance-pop-wrap
{
  input::-webkit-input-placeholder { /* WebKit browsers 适配谷歌 */
    color: #bbb !important;
  }
  input:-moz-placeholder { /* Mozilla Firefox 4 to 18 适配火狐 */
    color: #bbb !important;
  }
  input::-moz-placeholder { /* Mozilla Firefox 19+ 适配火狐 */
    color: #bbb !important;
  }
  input:-ms-input-placeholder { /* Internet Explorer 10+  适配ie*/
    color: #bbb !important;
  }
}


</style>
<style lang="scss" scoped>
.advance-pop-wrap {
  width: 100%;
  position: relative;

  ::v-deep {
    .el-input__suffix-inner {
      cursor: pointer;
      line-height: 32px;
      margin-right: 5px;
      color: #d8d8d8;

      &:hover {
        color: #999;
      }

      .iconfont {
        font-size: 12px;
      }
    }
    .el-input__inner {
      border-radius: 2px;
    }

  }

  .advance-pop-wrap-mok {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    height: 32px;
    line-height: 30px;
    padding: 0px 10px;
    padding-right: 15px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow-x: hidden;
    overflow-y: hidden;
    background-color: #FFFFFF;
    border-radius: 2px;
    border: 1px solid #D8d8d8;
    cursor: pointer;

    &:hover {
      border-color: #BBBBBB;
    }

    .icon-shanchu3 {
      font-size: 12px;
      color: #bbb;
      // margin-top: 1px;
      line-height: 32px;
      position: absolute;
      right: 9px;

      &:hover {
        color: #999;
      }
    }
  }

  .selected-active {
    ::v-deep {
      .el-input__inner {
        // color: #128bed;
        color: #333;
      }
    }
  }

  .advance-pop-content {
    position: absolute;
    top: 32px;
    left: 0px;
    z-index: 1000;
  }

  .batch-lv {
    position: absolute;
    top: 6px;
    left: 6px;
    bottom: 6px;
    right: 35px;
    padding: 1px 4px;
    border-radius: 2px;
    background: #eeeeee;

    cursor: pointer;

    &:hover {
      background: #e2f1fd;
      color: #128bed;
    }
  }
}
</style>
