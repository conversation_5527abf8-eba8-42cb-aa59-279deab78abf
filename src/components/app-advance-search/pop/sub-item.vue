<template>
  <div v-if="item" :class="item.corpKeyNo && item.corpKeyNo[0] === 'p' ? 'search-item search-item-p' : 'search-item'"
    @click="choose">
    <app-auto-logo size="22px" font-size="14px" :name="delEm(item.corpName)" :logo="item.imageUrl"></app-auto-logo>
    <span class="name" v-html="item.corpName"></span>
    <span class="tag" v-if="item.type" v-html="item.type"></span>
    <div class="choose" v-if="item.corpKeyNo">
      <span v-if="!item.isSelected" class="choose1">
        <i class="el-icon-plus"></i>{{ item.corpKeyNo[0] === 'p' || !follow ? '添加' : '关注并添加' }}
      </span>
      <span v-else class="chosen"> <i class="el-icon-check"></i>{{ item.corpKeyNo[0] === 'p' || !follow  ? '已添加' : '已关注并添加' }}</span>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'
export default {
  name: 'sub-item',
  props: {
    item: { type: Object, default: () => { } },
    follow:
    {
      type: Boolean,
      default: true
    }
  },
  methods: {
    choose() {
      this.item.isSelected = true
      this.$emit('click', this.item)
    },
    delEm(emName) {
      let entName = _.cloneDeep(emName)
      if (/<[^>]+>/.test(entName)) {
        entName = entName.replace(/<[^>]+>/g, '')
      }
      if (/\s+/.test(entName)) {
        entName = entName.replace(/\s+/g, '')
      }
      return entName
    }
  }
}
</script>
<style lang="scss" scoped>
.search-item-p {
  padding: 10px 75px 10px 42px !important;
}

.search-item {
  padding: 10px 110px 10px 42px;
  position: relative;
  font-size: 0;

  &:hover {
    background-color: #f3f9fd;
    cursor: pointer;

    .name {
      // color: #128bed;
    }

    .choose {
      .choose1 {
        color: #128bed;
        border: 1px solid #128bed;

        i {
          color: #128bed;
        }
      }
    }
  }

  .app-auto-logo {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translate(0, -50%);

    >span {
      line-height: 22px !important;
    }
  }

  .name {
    font-size: 14px;
    line-height: 22px;
    margin-right: 10px;
    display: inline;
  }

  .tag {
    display: inline-block;
    border-radius: 2px;
    background-color: #f6f6f6;
    padding: 0 5px;
    height: 22px;
    line-height: 22px;
    font-size: 12px;
    color: #999;
    vertical-align: 1px;
  }

  .choose {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translate(0, -50%);
    font-size: 12px;

    .choose1 {
      color: #333333;
      border: 1px solid #d8d8d8;
      border-radius: 2px;
      padding: 0px 5px;
      line-height: 20px;
      display: inline-block;

      i {
        font-size: 10px;
        color: #333333;
        margin-right: 5px;
      }

      &:hover {
        color: #128bed;
        border: 1px solid #128bed;

        i {
          color: #128bed;
        }
      }
    }

    .chosen {
      color: #999;
      line-height: 20px;
      display: inline-block;

      i {
        font-size: 10px;
        margin-right: 5px;
        color: #999;
      }
    }
  }
}
</style>
