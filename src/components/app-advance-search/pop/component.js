import subItem from './sub-item.vue'

export default {
  components: { [subItem.name]: subItem },
  props: {
    searchList: { default: () => [], type: Array },
    showOper: {
      type: Boolean,
      default: true
    },
    follow:
    {
      type: Boolean,
      default: true
    },
    loading: { default: false, type: Boolean }
  },
  computed: {

  },
  methods: {
    choose(item) {
      this.$emit('selected', item, this.searchList)
    }
  }
}
