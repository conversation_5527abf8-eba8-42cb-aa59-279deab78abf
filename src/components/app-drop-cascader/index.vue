<template>
  <el-popover :disabled="disabled" ref="popover" :placement="placement" v-model="isShow" :offset='offset' :trigger="trigger" :popper-class="'app-drop-cascader '+ popperClass" @show='showHandler' @hide='hideHandler' :open-delay="100" :close-delay="150">
    <div>
      <el-cascader-panel class="cascader-wrap" :options="menus" v-if="isShow" :props="props" @expand-change="handleExpandChange">
        <template slot-scope="{ node, data }">
          <span v-if="!node.hasChildren" @click="btnClick(node,data)" class="lable-title" :class="[selectedName === data.label ? 'active-label' : '']"> {{ data.label }} <span class="sub-label-text">{{data.subLabel}}</span></span>
          <span v-else class="lable-title">{{ data.label }}</span>
        </template>
      </el-cascader-panel>
    </div>
    <app-button slot="reference" :type="type" :size="size" :class="{'hover-trigger': trigger === 'hover', 'active': isShow, 'disabled': disabled}"><span>{{selectedName ? selectedName : title}}</span><span class="caret"></span></app-button>
  </el-popover>
</template>

<script>
  export default {
    name: 'app-drop-cascader',
    props: {
      title: { default: '导出' },
      menus: { type: Array }, // [{name: '菜单',click: func},...]
      size: { default: 'small' },
      placement: { default: 'bottom-end' },
      trigger: { default: 'click' },
      type: { default: 'normal' },
      offset: { default: 0 },
      popperClass: { default: '' },
      selectedName: { default: '' },
      zhugeTrack: { default: false, type: Boolean },
      overflowHiddenWhenExpand: {
        default: false,
        type: Boolean
      },
      disabled: {
        default: false,
        type: Boolean
      }
    },
    data() {
      return {
        isShow: false,
        props: {
          expandTrigger: 'hover'
        }
      }
    },
    methods: {
      btnClick(node, item) {
        this.$emit('update:selectedName', item.label)
        if (node.data.onclick) {
          node.data.onclick(this.hideHandler)
        } else {
          this.$emit('menuClick', item, this.hideHandler)
        }
        if (this.selectedName) {
          this.isShow = false
        }
        if (this.zhugeTrack && item.label) {
         $util.zhugeTrackTwoLevel(`${this.title ? (this.title + '-') : ''}` + item.label, this.$route.meta.title)
        }
      },
      showHandler() {
        this.overflowHiddenWhenExpand && document.body.classList.add('overflow-hidden')
        this.isShow = true
        this.$nextTick(() => {
          this.overflowHiddenWhenExpand && document.body.classList.remove('overflow-hidden')
        })
      },
      hideHandler() {
        this.isShow = false
      },
      handleExpandChange () {
        this.$nextTick(() => {
          const popperStyle = getComputedStyle(this.$refs.popover.popperJS._popper)
          if (parseFloat(popperStyle.right) < 0) {
            this.overflowHiddenWhenExpand && document.body.classList.add('overflow-hidden')
            this.$refs.popover.updatePopper()
            this.overflowHiddenWhenExpand && document.body.classList.remove('overflow-hidden')
          }
        })
      }
    }
  }

</script>
<style lang="scss" scoped>
.cascader-wrap {
  ::v-deep {
    .is-disabled:hover span{
      color: #BBBBBB;
    }

  }
}
.el-button {
  &.hover-trigger {
    &:hover {
      color: #128bed;
      .caret {
        transform: rotate(180deg);
        border-top-color: #128BED;
      }
    }
    &.active {
      color: #128bed;
      border-color: #128bed;
      .caret {
        transform: rotate(180deg);
        border-top-color: #128BED;
      }
    }
    &.disabled{
      cursor: not-allowed;
      span {
        color: #999999 !important;
      }
      .caret {
        transform: unset !important;
        border-top-color: #999999 !important;
      }
    }
  }
  ::v-deep {
    & > span {
      display: flex;
      align-items: center;
      span:first-child {
        margin-right: 5px;
      }
    }
  }
}
</style>
<style lang="scss">

  .app-drop-cascader {
    padding: 0px;

    .el-cascader-node__label {
      margin: 0px;
      padding: 0px;
    }

    .lable-title {
      width: 100%;
      display: inline-block;
      //padding-left: 10px;
      &.active-label{
        color: #128BED;
      }
    }

    .el-cascader-menu__wrap {
      height: auto;
      min-height: 27px;
      overflow-x: hidden;
      margin-bottom: 0 !important;
    }

    .el-cascader-menu {
      // min-width: 130px;
      min-width: 146px;
    }
  }

  .sub-label-text{
    color: #999999;
    font-size: 12px;
    font-weight: normal;
    padding-left: 4px;
  }
</style>
