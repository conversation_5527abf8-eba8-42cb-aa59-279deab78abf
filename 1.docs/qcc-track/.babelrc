{"presets": ["@babel/preset-env"], "plugins": ["add-module-exports", "@babel/plugin-transform-modules-umd", "@babel/plugin-transform-modules-amd", ["@babel/plugin-transform-modules-commonjs", {"allowTopLevelThis": true}], "@babel/plugin-syntax-dynamic-import", ["@babel/plugin-proposal-decorators", {"legacy": true}], "@babel/plugin-syntax-import-meta", "@babel/plugin-proposal-class-properties", "@babel/plugin-proposal-json-strings", "@babel/plugin-proposal-function-sent", "@babel/plugin-proposal-export-namespace-from", "@babel/plugin-proposal-numeric-separator", "@babel/plugin-proposal-throw-expressions", "@babel/plugin-proposal-export-default-from", "@babel/plugin-proposal-logical-assignment-operators", "@babel/plugin-proposal-optional-chaining", ["@babel/plugin-proposal-pipeline-operator", {"proposal": "minimal"}], "@babel/plugin-proposal-nullish-coalescing-operator", "@babel/plugin-proposal-do-expressions", "@babel/plugin-proposal-function-bind", "@babel/plugin-transform-runtime"]}