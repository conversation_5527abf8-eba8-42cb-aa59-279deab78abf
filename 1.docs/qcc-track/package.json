{"name": "webpack-demo1", "version": "1.0.0", "main": "main.js", "scripts": {"dev": "webpack-dev-server --open", "build": "webpack"}, "license": "MIT", "devDependencies": {"js-cookie": "^3.0.1", "qs": "^6.10.3", "webpack": "^5.72.0", "webpack-cli": "^4.9.2"}, "dependencies": {"@babel/cli": "^7.7.7", "@babel/core": "^7.7.7", "@babel/node": "^7.7.7", "@babel/plugin-proposal-class-properties": "^7.7.4", "@babel/plugin-proposal-decorators": "^7.7.4", "@babel/plugin-proposal-do-expressions": "^7.7.4", "@babel/plugin-proposal-export-default-from": "^7.7.4", "@babel/plugin-proposal-export-namespace-from": "^7.7.4", "@babel/plugin-proposal-function-bind": "^7.7.4", "@babel/plugin-proposal-function-sent": "^7.7.4", "@babel/plugin-proposal-json-strings": "^7.7.4", "@babel/plugin-proposal-logical-assignment-operators": "^7.7.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.7.4", "@babel/plugin-proposal-numeric-separator": "^7.7.4", "@babel/plugin-proposal-optional-chaining": "^7.7.5", "@babel/plugin-proposal-pipeline-operator": "^7.7.7", "@babel/plugin-proposal-throw-expressions": "^7.7.4", "@babel/plugin-syntax-dynamic-import": "^7.7.4", "@babel/plugin-syntax-import-meta": "^7.7.4", "@babel/plugin-transform-runtime": "^7.7.6", "@babel/polyfill": "^7.7.0", "@babel/preset-env": "^7.7.7", "@babel/register": "^7.7.7", "babel-loader": "^8.0.0", "babel-plugin-add-module-exports": "^1.0.0", "webpack-dev-server": "^4.8.1"}}