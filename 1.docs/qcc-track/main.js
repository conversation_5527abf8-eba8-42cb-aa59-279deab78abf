import cookieStorage from './src/cookie-storage'
import {stringify} from './src/query-string'

function generateUUID() {
    var s = []
    var hexDigits = '0123456789abcdef'
    for (var i = 0; i < 32; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
    }
    
    s[12] = '4' // bits 12-15 of the time_hi_and_version field to 0010
    s[16] = hexDigits.substr((s[19] & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
    
    var uuid = s.join('')
    return uuid
}

const setDeviceId = () => {
    const deviceId = cookieStorage.get('qcc_did')
    if (!deviceId) {
        cookieStorage.set('qcc_did', generateUUID(), {
            expires: 1000, // 7 days in seconds
            path: '/',
            domain: window.location.href.indexOf('.greatld.com') > -1 ? '.greatld.com' : '.qcc.com'
        })
    }
}

function qccTrack(name = 'qccpro-pc-web', trackUrl = 'https://www.qcc.com/web') {
    this.platformName = name
    this.trackUrl = trackUrl || ''
    this.track = function (pageName, obj = {}, uid = '') {
        try {
            obj = obj || {}
            let event = ['page_view', 'page_exposure', 'page_button_click', 'login_success', 'search_words_click', 'button_detail'].indexOf(obj.event) > -1 ? obj.event : 'page_button_click'
            setDeviceId()
            if (!pageName || !(pageName || '').trim()) {
                return
            }
            obj.page_name = pageName
            if (obj.event) {
                delete obj.event
            }
            let data = {
                appName: this.platformName,
                entity: JSON.stringify(obj),
                originUrl: window.location.href,
                ref: document.referrer || '',
                event,
                uid: uid || ''
            }
            const img = new Image()
            img.src = `${this.trackUrl}/s.gif?${stringify(data)}&d=${new Date().getTime()}`
        } catch (e) {
            console.error(e.message)
        }
    }
}

if (window && !window.qccTrack) {
    window.qccTrack = qccTrack
}
/**
 if (window && !window.qccZhuge) {
     window.qccZhuge = new qccTrack('qccpro-pc-web', '')
 }
 */
