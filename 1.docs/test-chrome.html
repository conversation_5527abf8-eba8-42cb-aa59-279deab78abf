<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Title</title>

  <style>
    .tab{
      display: flex;
      background-color: #EFC0B8;
      padding: 0px 15px 0 15px;
      font-size: 14px;
    }
    .tab-item{
      position: relative;
      background-color: transparent;
      padding: 0px 15px;
      height: 50px;
      cursor: pointer;
      border-radius: 8px 8px 0 0;
      transition: .2s;
    }
    .tab-item::before,.tab-item::after{
      position: absolute;
      bottom: 0;
      content: '';
      width: 20px;
      height: 15px;
      border-radius: 100%;
      box-shadow: 0 0 0 40px transparent;
      transition: .2s;
    }
    .tab-item::before{
      left: -20px;
      clip-path: inset(50% -10px 0 50%);
    }
    .tab-item::after{
      right: -20px;
      clip-path: inset(50% 50% 0 -10px);
    }

    .tab-item:hover{
      background-color: #F2D0CA;
      /* color: #000; */
    }
    .tab-item:hover::before,
    .tab-item:hover::after{
      box-shadow: 0 0 0 30px #F2D0CA;
    }
    .tab-item.active{
      background-color: #F8EAE7;
      z-index: 1;
      /* color: #000; */
    }
    .tab-item.active::before,
    .tab-item.active::after{
      box-shadow: 0 0 0 30px #F8EAE7;
    }

  </style>
</head>
<body>
<nav class="tab fix">
  <a class="tab-item">Svelte API Svelte API Svelte API</a>
  <a class="tab-item active">Svelte API</a>
  <a class="tab-item">Svelte API</a>
  <a class="tab-item">Svelte API</a>
</nav>
</body>
</html>
