<html>
<head>
  <meta charset="UTF-8">
  <title>报告发送</title>
</head>
<body style="font-family:微软雅黑,sans-serif;padding: 0;margin: 0;background: white">
<table style="margin: 0 auto" cellpadding="0" style="border: 1px solid #e6e6e6;width: 900px;" cellspacing="0"
       align="center">
  <tbody>
  <tr>
    <td style="width: 900px;height: 104px;padding-bottom: 50px;">
      <img src="https://pro-files.qichacha.com/open/pic/email/header.png" alt="" style="width: 900px;" width="900px">
    </td>
  </tr>
  <tr>
    <td style="padding-left: 15px;">
      <div style="background:white;font-size:14px;color:#333333;">
        <p>尊敬的用户，您好：
        <p>
        <p>感谢您订阅企查查专业版风险监控消息通知，企查查专业版实时扫描您关注的企业/人员/舆情全维度信息，重要信息多渠道快速送达。</p>
        <p>平台监测到您关注的企业/人员/舆情有以下信息变动，敬请留意！</p>
      </div>
    </td>
  </tr>
  <tr>
    <td style="height: 15px;width: 900px;"></td>
  </tr>
  <#if countPartList?exists &&(countPartList?size gt 0) >
  <tr>
    <td style="padding-left: 15px;">
      <div style="font-weight: bold;font-size: 16px;display:inline-block;color:#999999;">
        <span style="width:5px;background: #118BED;display: inline-block;">&nbsp;</span>
        企业监控概览
      </div>
    </td>
  </tr>
  <tr>
    <td style="padding-top: 15px;">
      <table style="margin-left: 15px; border-collapse: collapse;width: 885px;">
        <tbody style="font-size:12px">
        <#if countPartList?exists &&(countPartList?size gt 0) >
        <#list countPartList as objPartList>
        <tr style="width:100%">
          <#list objPartList as obj>
          <td style="width:226px;padding-top: 15px;">
            <table
              style="width:206px; border-collapse: collapse; padding: 8px;background:#F8F8F8;">
              <tbody style="font-size:12px">
              <tr style="width:100%; ">
                <td
                  style="padding: 8px;width:100%;min-height:35px;padding-left:25px;padding-top:15px;font-size: 17px;color:#666666;font-weight: bold; padding-bottom:15px">
                  ${obj.type}
                </td>
              </tr>
              <tr style="width:100%">
                <td
                  style="padding: 8px;width:100%;color: #E08283;padding-left:30px;font-size: 30px;font-weight: bold;padding-bottom:10px; padding-top:12px;">
                  ${obj.count}
                </td>
              </tr>
              </tbody>
            </table>
          </td>
        </#list>
        <#if objPartList_index == 0>
        <td style=""></td>
        </#if>
        </tr>
      </#list>
    </#if>
  </tbody>
</table>
</td>
</tr>
<tr>
  <td style="padding-top: 30px"></td>
</tr>
</#if>

<#if countStaffPartList?exists &&(countStaffPartList?size gt 0) >
<tr>
  <td style="padding-left: 15px;padding-top: 15px">
    <div style="font-weight: bold;font-size: 16px;display:inline-block;color:#999999;">
      <span style="width:5px;background: #118BED;display: inline-block;">&nbsp;</span>
      人员监控概览
    </div>
  </td>
</tr>
<tr>
  <td style="padding-top: 15px;">
    <table style="margin-left: 15px; border-collapse: collapse;width: 885px;">
      <tbody style="font-size:12px">
      <#if countStaffPartList?exists && (countStaffPartList?size gt 0) >
      <#list countStaffPartList as objStaffPartList>
      <tr>
        <#list objStaffPartList as obj>
        <td style="width:226px;padding-top: 15px;">
          <table
            style="width:206px; border-collapse: collapse; padding: 8px;background:#F8F8F8;">
            <tbody style="font-size:12px">
            <tr style="width:100%; ">
              <td
                style="padding: 8px;width:100%;min-height:35px;padding-left:25px;padding-top:15px;font-size: 17px;color:#666666;font-weight: bold; padding-bottom:15px">
                ${obj.type}
              </td>
            </tr>
            <tr style="width:100%">
              <td
                style="padding: 8px;width:100%;color: #E08283;padding-left:30px;font-size: 30px;font-weight: bold;padding-bottom:10px; padding-top:12px;">
                ${obj.count}
              </td>
            </tr>
            </tbody>
          </table>
        </td>
      </#list>
      <#if objStaffPartList_index == 0>
      <td style=""></td>
      </#if>
      </tr>
    </#list>
  </#if>
  </tbody>
  </table>
  </td>
</tr>
<tr>
  <td style="width: 900px;">
    <div style="padding-top: 30px"></div>
  </td>
</tr>
</#if>

<#if newsCountList?exists &&(newsCountList?size gt 0) >
<tr>
  <td style="padding-left: 15px;padding-top: 15px;">
    <div style="font-weight: bold;font-size: 16px;display:inline-block;color:#999999;">
      <span style="width:5px;background: #118BED;display: inline-block;">&nbsp;</span>
      舆情推送概览
    </div>
  </td>
</tr>
<tr>
  <td style="padding-top: 15px;">
    <table style="margin-left: 15px; border-collapse: collapse;width: 885px;">
      <tbody style="font-size:12px">
      <#if newsCountList?exists &&(newsCountList?size gt 0) >
      <tr style="width:100%">
        <#list newsCountList as obj>
        <td style="width:226px;padding-top: 15px;">
          <table
            style="width:206px; border-collapse: collapse; padding: 8px;background:#F8F8F8;">
            <tbody style="font-size:12px">
            <tr style="width:100%; ">
              <td
                style="padding: 8px;width:100%;min-height:35px;padding-left:25px;padding-top:15px;font-size: 17px;color:#666666;font-weight: bold; padding-bottom:15px">
                ${obj.type}
              </td>
            </tr>
            <tr style="width:100%">
              <td
                style="padding: 8px;width:100%;color: #E08283;padding-left:30px;font-size: 30px;font-weight: bold;padding-bottom:10px; padding-top:12px;">
                ${obj.count}
              </td>
            </tr>
            </tbody>
          </table>
        </td>
      </#list>
      <td style="">
      </td>
      </tr>
      </#if>
      </tbody>
    </table>
  </td>
</tr>
</#if>

<#if monitorList?exists &&(monitorList?size gt 0) >
<tr>
  <td
    style="padding-left:15px;padding-top: 30px;font-weight: bold;font-size: 16px;display:inline-block;color:#999999;">
    <span style="width:5px;background: #118BED;display: inline-block;">&nbsp;</span>
    企业监控详情(最多显示${corpDetailCount4EmailLimit!}条详情)
  </td>
</tr>
<tr>
  <td style="padding-left: 15px;padding-top: 15px;">
    <table style="padding: 8px;border: 1px solid #e7ecf1;border-collapse: collapse;width: 870px;">
      <thead>
      <tr style="font-size:14px; font-weight:bold; background: #F4F7FA;color:#333333;text-align:left;">
        <th style="padding: 8px;border: 1px solid #e7ecf1;box-sizing: border-box;width: 80px;">
          序号
        </th>
        <th style="padding: 8px;border: 1px solid #e7ecf1;box-sizing: border-box;width: 220px;">
          企业名称
        </th>
        <th style="padding: 8px;border: 1px solid #e7ecf1;box-sizing: border-box;width: 140px;">
          风险级别
        </th>
        <th style="padding: 8px;border: 1px solid #e7ecf1;box-sizing: border-box;width: 140px;">
          风险类型
        </th>
        <th style="padding: 8px;border: 1px solid #e7ecf1;box-sizing: border-box;width: 180px;">
          风险内容
        </th>
        <th style="padding: 8px;border: 1px solid #e7ecf1;box-sizing: border-box;width: 110px;">
          变动日期
        </th>
      </tr>
      </thead>
      <tbody style="font-size:12px">
      <#list monitorList as obj>
      <tr style="color:#666666">
        <td style="padding: 8px;border: 1px solid #e7ecf1;">
          ${obj.no}
        </td>
        <td style="padding: 8px;border: 1px solid #e7ecf1;"
            title="${obj.corpName}">${obj.corpName}
        </td>
        <td style="padding: 8px;border: 1px solid #e7ecf1; ">
          ${obj.riskLevel}
        </td>
        <td style="padding: 8px;border: 1px solid #e7ecf1;">
          ${obj.riskType}
        </td>
        <td style="padding: 8px;border: 1px solid #e7ecf1;"
            title="${obj.riskCnt}">${obj.riskCnt}
        </td>
        <td style="padding: 8px;border: 1px solid #e7ecf1;">
          ${obj.monitorDate}
        </td>
      </tr>
      </#list>
      </tbody>
    </table>
  </td>
</tr>
</#if>

<#if monitorStaffList?exists &&(monitorStaffList?size gt 0) >
<tr>
  <td style="padding-left: 15px;padding-top: 30px">
    <div style="font-weight: bold;font-size: 16px;display:inline-block;color:#999999;">
      <span style="width:5px;background: #118BED;display: inline-block;">&nbsp;</span>
      人员监控详情(最多显示${persDetailCount4EmailLimit!}条详情)
    </div>
  </td>
</tr>
<tr>
  <td style="padding-left: 15px;padding-top: 15px;">
    <table style="padding: 8px;border: 1px solid #e7ecf1;border-collapse: collapse;width: 870px;">
      <thead>
      <tr style="font-size:14px; font-weight:bold; background: #F4F7FA;color:#333333;text-align:left;">
        <th style="padding: 8px;border: 1px solid #e7ecf1;width: 80px;">
          序号
        </th>
        <th style="padding: 8px;border: 1px solid #e7ecf1;width: 220px;">
          人员名称
        </th>
        <th style="padding: 8px;border: 1px solid #e7ecf1;width: 140px;">
          风险级别
        </th>
        <th style="padding: 8px;border: 1px solid #e7ecf1; width: 140px;">
          风险类型
        </th>
        <th style="padding: 8px;border: 1px solid #e7ecf1; width: 180px;">
          风险内容
        </th>
        <th style="padding: 8px;border: 1px solid #e7ecf1; width:110px;">
          变动日期
        </th>
      </tr>
      </thead>
      <tbody style="font-size:12px">
      <#list monitorStaffList as obj>
      <tr style="color:#666666">
        <td
          style="padding: 8px;border: 1px solid #e7ecf1;">
          ${obj.no}
        </td>
        <td
          style="padding: 8px;border: 1px solid #e7ecf1;"
          title="${obj.corpName}">${obj.corpName}
        </td>
        <td
          style="padding: 8px;border: 1px solid #e7ecf1; ">
          ${obj.riskLevel}
        </td>
        <td
          style="padding: 8px;border: 1px solid #e7ecf1; ">
          ${obj.riskType}
        </td>
        <td
          style="padding: 8px;border: 1px solid #e7ecf1;"
          title="${obj.riskCnt}">${obj.riskCnt}
        </td>
        <td
          style="padding: 8px;border: 1px solid #e7ecf1; ">
          ${obj.monitorDate}
        </td>
      </tr>
      </#list>
      </tbody>
    </table>
  </td>
</tr>
</#if>

<#if newsList?exists &&(newsList?size gt 0) >
<tr>
  <td style="padding-left: 15px;padding-top: 30px">
    <div style="font-weight: bold;font-size: 16px;display:inline-block;color:#999999;">
      <span style="width:5px;background: #118BED;display: inline-block;">&nbsp;</span>
      舆情推送详情
    </div>
  </td>
</tr>
<tr>
  <td style="padding-top: 15px">
    <table style="margin-left: 15px;border: 1px solid #e7ecf1;border-collapse: collapse;width: 870px;">
      <thead>
      <tr style="font-size:14px; font-weight:bold; background: #F4F7FA;color:#333333;text-align:left;">
        <th style="padding: 8px;box-sizing:border-box;border: 1px solid #e7ecf1;width: 80px;">序号</th>
        <th style="padding: 8px;box-sizing:border-box;border: 1px solid #e7ecf1;width: 220px;">公司名称</th>
        <th style="padding: 8px;box-sizing:border-box;border: 1px solid #e7ecf1;width: 140px;">情感类别</th>
        <th style="padding: 8px;box-sizing:border-box;border: 1px solid #e7ecf1;width: 140px;">新闻标题</th>
        <th style="padding: 8px;box-sizing:border-box;border: 1px solid #e7ecf1;width:180px;" >新闻类别</th>
        <th style="padding: 8px;box-sizing:border-box;border: 1px solid #e7ecf1;width:110px;">发布日期</th>
      </tr>
      </thead>
      <tbody style="font-size:12px">
      <#list newsList as obj>
      <tr style="color:#666666">
        <td style="padding: 8px;border: 1px solid #e7ecf1;">${obj_index+1}</td>
        <td style="padding: 8px;box-sizing:border-box;border: 1px solid #e7ecf1;" title="${obj.corpName!}">
          ${obj.corpName}
        </td>
        <td style="padding: 8px;box-sizing:border-box;border: 1px solid #e7ecf1;">${obj.impactDesc!}</td>
        <td style="padding: 8px;box-sizing:border-box;border: 1px solid #e7ecf1;" title="${obj.title!}">
          <a href="${obj.url!}" style="color:#128bed">${obj.title!}</a>
        </td>
        <td style="padding: 8px;box-sizing:border-box;border: 1px solid #e7ecf1;">
          ${obj.newsTypeDesc!}
        </td>
        <td style="padding: 8px;box-sizing:border-box;border: 1px solid #e7ecf1;">${obj.publishTime!}</td>
      </tr>
      </#list>
      </tbody>
    </table>
  </td>
</tr>
</#if>

<tr>
  <td>
    <table style="padding-bottom: 30px;padding-top: 30px;width: 900px;">
      <tbody>
      <td style="width: 15px"></td>
      <td style="width: 885px;">
        <div style="font-size: 12px;">查看<a
          href="${domainUrl}/login?path=monitor/detail/company/list"
          style="color:#128bed;text-decoration: none">更多详情</a></div>
      </td>
      </tbody>
    </table>
  </td>
</tr>
<tr>
  <td>
    <a style="text-decoration: unset;cursor:pointer;" href="https://pro.qcc.com">
      <img src="https://qfk-files.oss-cn-hangzhou.aliyuncs.com/open/pic/email/img-email-bottom.png" width="900px" style="width: 900px;">
    </a>
  </td>
</tr>
</tbody>
</table>
</body>
</html>
