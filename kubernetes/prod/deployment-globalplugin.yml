apiVersion: apps/v1
kind: Deployment
metadata:
  name: qichacha-globalplugin-front
  namespace: qifengkong
  labels:
    app: qichacha-globalplugin-front
spec:
  minReadySeconds: 5
  revisionHistoryLimit: 5
  progressDeadlineSeconds: 60
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  selector:
    matchLabels:
      app: qichacha-globalplugin-front
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
      labels:
        app: qichacha-globalplugin-front
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference: {}
              weight: 100
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - pro
      containers:
        - name: qichacha-globalplugin-front
          image: IMAGE_NAME:IMAGE_TAG
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 9000
          livenessProbe:
            httpGet:
              path: /
              port: 9000
            initialDelaySeconds: 10
            timeoutSeconds: 10
            periodSeconds: 30
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /
              port: 9000
            initialDelaySeconds: 5
            timeoutSeconds: 5
            periodSeconds: 10
            failureThreshold: 3
          resources:
            limits:
              cpu: 1000m
              memory: 1000Mi
            requests:
              cpu: 100m
              memory: 256Mi
          volumeMounts:
            - name: beichacha-web-logs
              mountPath: /beichacha/data/log
            - name: timezone
              mountPath: /etc/localtime
      imagePullSecrets:
        - name: new-registry
      volumes:
        - name: beichacha-web-logs
          hostPath:
            path: /var/log/pro/web
        - name: timezone
          hostPath:
            path: /usr/share/zoneinfo/Asia/Shanghai

---
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  name: qichacha-globalplugin-front
  namespace: qifengkong
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: qichacha-globalplugin-front
  minReplicas: 1
  maxReplicas: 2
  metrics:
    - type: Resource
      resource:
        name: cpu
        # scale up if usage is above
        # 99% of the requested CPU (100m)
        targetAverageUtilization: 99
