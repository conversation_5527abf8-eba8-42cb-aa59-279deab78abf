apiVersion: apps/v1
kind: Deployment
metadata:
  name: POD_NAME
  namespace: qifengkong
  labels:
    app: POD_NAME
    version: v1
spec:
  minReadySeconds: 5
  revisionHistoryLimit: 5
  progressDeadlineSeconds: 60
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 0
    type: RollingUpdate
  selector:
    matchLabels:
      app: POD_NAME
      version: v1
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
      labels:
        app: POD_NAME
        version: v1
    spec:
      # affinity:
      #   nodeAffinity:
      #     preferredDuringSchedulingIgnoredDuringExecution:
      #       - preference: {}
      #         weight: 100
      #     requiredDuringSchedulingIgnoredDuringExecution:
      #       nodeSelectorTerms:
      #         - matchExpressions:
      #             - key: kubernetes.io/hostname
      #               operator: In
      #               values:
      #                 - wuss-qifengkong-k8s-2.novalocal
      containers:
        - name: POD_NAME
          image: IMAGE_NAME:IMAGE_TAG
          imagePullPolicy: Always
          ports:
            - containerPort: 9000
          livenessProbe:
            httpGet:
              path: /
              port: 9000
            initialDelaySeconds: 15
            timeoutSeconds: 10
            periodSeconds: 30
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /
              port: 9000
            initialDelaySeconds: 10
            timeoutSeconds: 5
            periodSeconds: 15
            failureThreshold: 3
          resources:
            limits:
              cpu: 1000m
              memory: 1000Mi
            requests:
              cpu: 100m
              memory: 256Mi
          volumeMounts:
            # - name: beichacha-web-logs
            #   mountPath: /beichacha/data/log
            - name: timezone
              mountPath: /etc/localtime
      imagePullSecrets:
        - name: docker-registry
      volumes:
        # - name: beichacha-web-logs
        #   hostPath:
        #     path: /var/log/pro/web
        - name: timezone
          hostPath:
            path: /usr/share/zoneinfo/Asia/Shanghai

---
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  name: POD_NAME
  namespace: qifengkong
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: POD_NAME
  minReplicas: 1
  maxReplicas: 2
  metrics:
    - type: Resource
      resource:
        name: cpu
        # scale up if usage is above
        # 99% of the requested CPU (100m)
        targetAverageUtilization: 90
