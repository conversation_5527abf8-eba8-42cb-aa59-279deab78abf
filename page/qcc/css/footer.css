/*滚动条 start*/
::-webkit-scrollbar {
  width: 9px;
  height: 9px;
  background-color: transparent;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0);
  border-radius: 10px;
  background-color: transparent;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0);
  background-color: #DCE1E8;
}

::-webkit-scrollbar-thumb:hover {
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0);
  background-color: #CDD3DC;
}

a {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

.footer-container {
  width: 100%;
  background-color: #1A2334;
  padding-top: 15px;
  line-height: 1.5;
}

.footer-content {
  width: 1200px;
  margin: 0 auto;
  text-align: left !important;
}

.footer-item {
  display: inline-block;
  vertical-align: top;
  margin-right: 100px;
}

.footer-title {
  padding-bottom: 10px;
}

.footer-title a {
  font-size: 14px;
  color: #ffffff !important;
  display: inline-block;
  height: 22px;
}

.footer-item p {
  margin-bottom: 5px;
  color: rgba(255, 255, 255, 0.4) !important;
  display: flex;
  font-size: 12px;
}

.footer-item p a {
  color: rgba(255, 255, 255, 0.4) !important;
  font-size: 12px;
}

.footer-item p a:hover {
  color: #ffffff !important;
}

.footer-info {
  display: flex;
  float: right;
}

.footer-info .footer-title {
  font-size: 14px;
  color: #ffffff !important;
  display: none;
}

.footer-info-item {
  display: inline-block;
  margin-right: 30px;
}

.footer-info-item:last-child {
  margin-right: 0;
}

.footer-info-item__title {
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 10px;
  color: #FFFFFF;
}

.footer-link {
  border-top: 1px solid #666666;
  margin-top: 51px;
  padding-top: 15px;
  padding-bottom: 15px;
}

.link-title {
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
}

.link-item {
  display: flex;
  align-items: center;
}

.link-item2 {
  margin-top: 5px !important;
  display: flex;
  align-items: center;
}

.link-item a {
  color: rgba(255, 255, 255, 0.4) !important;
  font-size: 12px;
}

.link-item span {
  color: rgba(255, 255, 255, 0.4) !important;
  font-size: 12px;
}

.link-item2 a {
  color: rgba(255, 255, 255, 0.4) !important;
  font-size: 12px;
}

.link-item a:hover {
  color: #ffffff !important;
}

/*.link-item span:hover {*/
/*  color: #ffffff !important;*/
/*}*/

.link-item2 a:hover {
  color: rgba(255, 255, 255, 0.4) !important;
}

.footer-bottom {
  width: 100%;
  height: 48px;
  line-height: 48px;
  background-color: #111826;
}

.bottom-class {
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
  display: flex;
}

.bottom-class a {
  color: rgba(255, 255, 255, 0.4);
}

.bottom-class .national-emblem-img {
  display: inline-block;
  margin: 0 8px;
  position: relative;
  top: -2px;
}

.bottom-class a:hover {
  color: #ffffff !important;
}

em {
  border-right: 1px solid rgba(255, 255, 255, 0.4);
  height: 12px;
  margin-left: 5px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 5px;
}
