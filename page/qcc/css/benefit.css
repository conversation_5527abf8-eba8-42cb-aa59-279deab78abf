body {
  font-family: "微软雅黑", "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, Helvetica;
  font-size: 14px;
  line-height: 1.8;
  color: #333;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  min-width: 1024px;
}

body {
  margin: 0;
}

.qfkwebsite .bannerWrapper {
  height: 500px;
  width: 100%;
  text-align: center;
  color: white;
  background-repeat: no-repeat;
}

.qfkwebsite .bannerWrapper .title {
  font-size: 45px;
  display: inline-block;
  color: white;
  padding-top: 115px;
}

.qfkwebsite .bannerWrapper .line {
  display: inline-block;
  width: 50px;
  height: 2px;
  background: white;
  margin-bottom: 30px;
  margin-top: 39px;
  border-radius: 3px;
}

.qfkwebsite .bannerWrapper .describition {
  display: inline-block;
  line-height: 2.33em;
  font-size: 18px;
  width: 860px;
  margin: 2px 0px 0px 0px;
}

.qfkwebsite .bannerWrapper .playVideo {
  font-size: 16px;
  line-height: 24px;
  margin-top: 32px;
  cursor: pointer;
}

.flex-cCenter {
  -webkit-box-align: center;
  -moz-align-items: center;
  -webkit-align-items: center;
  align-items: center;
}

.flex-zCenter {
  -webkit-box-pack: center;
  -moz-justify-content: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.flex-def {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

.qfkwebsite .bannerWrapper .playVideo .trailer_play {
  height: 24px;
  width: auto;
  margin-right: 10px;
}

.container-fluid {
  position: relative;
}

.container,
.container-fluid {
  padding-left: 15px;
  padding-right: 15px;
}

.container,
.container-fluid {
  margin-right: auto;
  margin-left: auto;
}

.container.maxLimit {
  max-width: 1250px;
}

.container {
  width: 100%;
}

.home-box>h1 {
  font-size: 28px;
  text-align: center;
  margin: 0;
  padding: 100px 0px 10px 0px;
}

.fontColor1 {
  color: #333333;
}

.fontColor2 {
  color: #666666;
}

.assistant {
  font-size: 18px;
  width: 860px;
  text-align: center;
  width: 100%;
}

.row-flex {
  display: flex;
}

.col-flex {
  flex: 1;
}

.notice {
  width: 366px;
  height: 283px;
  background-repeat: no-repeat;
  color: white;
}

.notice>h3 {
  margin-top: 30px;
}

.tc {
  text-align: center;
}

h3 {
  font-size: 24px;
}

.h1,
.h2,
.h3,
h1,
h2,
h3 {
  margin-bottom: 10px;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: inherit;
}

.notice>p {
  margin: 20px 20px 0 20px;
  text-align: left;
}

a {
  cursor: pointer;
}

a {
  color: #3c4144;
}

.box {
  background-color: #F7FAFC;
  height: 281px;
  width: 200px;
}

.aic {
  align-items: center;
}

.box-header {
  background-color: #BED8EC;
  padding: 5px 20px 10px 20px;
  margin: 0;
  height: 96px;
  line-height: 28px;
}

.aic {
  align-items: center;
}

.box-header h4 {
  line-height: 28px;
  font-size: 18px;
}

.box>p {
  margin-top: 15px;
}

.box-body {
  padding: 0 20px;
}

.fontColor2 {
  color: #666666;
}

.box-margin {
  margin-left: 40px;
}

.question-answer {
  margin-top: 20px;
}

.question {
  font-size: 16px;
  color: #333333;
}

.answer {
  margin-top: 11px;
  font-size: 14px;
  color: #999999;
  line-height: 22px;
}

.more-questions {
  border: 1px solid #D8d8d8;
  border-radius: 3px;
  height: 40px;
  width: 126px;
  background: #F7FAFC;
}
