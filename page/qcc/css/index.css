body {
  font-family: "微软雅黑", "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, Helvetica;
  font-size: 14px;
  line-height: 1.42857;
  color: #333;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  min-width: 1300px;
}

body {
  margin: 0;
}

.qfkwebsite {
  position: relative;
  width: 100%;
  font-size: 12px;
}

.qfkwebsite .describitionContent {
  position: relative;
  width: 100%;
  height: 800px;
  color: white;
  // text-align: center;
}

.qfkwebsite .describitionContent .contentNormal {
  width: 85%;
  text-align: center;
  max-width: 1350px;
  min-width: 1200px;
  position: relative;
  margin: auto;
}

.qfkwebsite .describitionContent .describitionTitle {
  margin: 0;
  font-size: 54px;
  padding-top: 20vh;
}

.qfkwebsite .describitionContent .describitionSubTitle {
  font-size: 22px;
  margin-top: 40px;
  display: inline-block;
}

.qfkwebsite .describitionContent .line {
  margin-top: 50px;
  display: inline-block;
  width: 50px;
  height: 2px;
  background: white;
  border-radius: 3px;
  margin-bottom: 5vh;
}

.qfkwebsite .describitionContent .playVideo {
  font-size: 1.33em;
  cursor: pointer;
  margin-top: 5px;
}

.qfkwebsite .describitionContent .playVideo .trailer_play {
  width: 24px;
  height: 24px;
  margin-right: 6px;
}

.trailer_play {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

img {
  vertical-align: middle;
}

hr,
img {
  border: 0;
}

.qfkwebsite .describitionContent .shengqingBtn {
  height: 50px;
  margin-top: 30px;
  font-size: 1.28em;
  background: #128BED !important;
  border-color: #128BED !important;
}

.qfkwebsite .describitionContent .dropDownIconContainer {
  position: absolute;
  height: 36px;
  width: 36px;
  margin: auto;
  cursor: pointer;
  bottom: 80px;
  animation: dropdownIconAnima 2s infinite;
  -webkit-animation: dropdownIconAnima 2s infinite;
  -webkit-animation-fill-mode: both;
  left: calc((100% - 36px) / 2);
}

.qfkwebsite .describitionContent .dropDownIconContainer:hover .dropDownIconHover {
  visibility: visible;
}

.qfkwebsite .describitionContent .dropDownIconContainer:hover {
  animation: none;
}

.qfkwebsite .describitionContent .dropDownIconContainer .dropDownIconHover {
  visibility: hidden;
  position: absolute;
  left: 0;
  top: 0;
}

.qfkwebsite .describitionContent .newsWrapper {
  width: 100%;
  height: 60px;
  background: #F7FAFC;
  position: absolute;
  overflow: hidden;
  bottom: 0;
  left: 0;
  line-height: 60px;
  text-align: left;
}

.qfkwebsite .describitionContent .newsWrapper .rightMoreNews {
  display: inline-block;
  font-size: 1.17em;
  line-height: 1.17em;
  height: auto;
  width: 100px;
  box-sizing: border-box;
  text-align: right;
  padding-left: 15px;
  cursor: pointer;
}

.qfkwebsite .describitionContent .newsWrapper .rightMoreNews a {
  color: #999999 !important;
}

.qfkwebsite .describitionContent .newsWrapper .rightMoreNews a :hover {
  color: #666666;
}

.flex-cCenter {
  -webkit-box-align: center;
  -moz-align-items: center;
  -webkit-align-items: center;
  align-items: center;
}

.flex-def {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

.qfkwebsite .describitionContent .newsWrapper .leftTitle {
  display: inline-block;
  font-size: 1.5em;
  line-height: 1.5em;
  height: auto;
  color: #666666;
  width: 100px;
  box-sizing: border-box;
  line-height: unset;
  text-align: left;
  padding-right: 21px;
  border-right: 1px solid #D8d8d8;
}

.qfkwebsite .describitionContent .newsWrapper .newsTitlesWrapper {
  display: inline-block;
  font-size: 1.17em;
  line-height: 1.17em;
  height: auto;
  color: #333333;
  width: 1000px;
  box-sizing: border-box;
  text-align: left;
  padding-left: 15px;
  cursor: pointer;
  line-height: unset;
}

ul {
  padding-inline-start: 0px;
}

.function-point {
  width: 100%;
  margin-top: 80px;
}

.function-title {
  font-size: 28px;
  color: #333333;
  text-align: center;
}

.function-subTitle {
  font-size: 14px;
  color: #999999;
  text-align: center;
  margin-top: 20px;
}

.function-container {
  width: 1200px;
  margin: 0 auto;
}

.function-content {
  display: inline-block;
  width: 282px;
  height: 285px;
  padding: 30px 0px 0px 20px;
  vertical-align: top;
}

.small-title {
  color: #333333;
  font-size: 20px;
  font-weight: bold;
}

.small-title>a {
  color: #128bed;
}

.small-title>a:hover {
  color: #0069BF;
}

.small-sunTitle {
  font-size: 14px;
  line-height: 19px;
  color: #666666;
  margin-top: 5px;
}

.frist {
  margin-top: 15px;
}

.two {
  margin-top: 20px;
}

.data-info {
  margin-top: 80px;
  width: 100%;
  height: 680px;
}

.info-one {
  width: 280px;
  margin-top: 130px;
}

.info-one p {
  width: 280px;
  color: #ffffff;
  font-size: 28px;
}

.info-one div {
  width: 280px;
  color: #ffffff;
  font-size: 14px;
  margin-top: 20px;
}

.info-two {
  margin-top: 80px;
  padding: 50px;
  background-color: #ffffff;
  display: inline-block;
  margin-left: -80px;
}

.info-item {
  display: inline-block;
  width: 360px;
}

.info-item p {
  font-size: 20px;
  color: #333333;
  margin-top: 20px;
}

.info-item .item-desc {
  font-size: 14px;
  color: #999999;
  margin-top: 10px;
}

.service-partner {
  width: 100%;
  margin-top: 80px;
  margin-bottom: 80px;
}

.authoritative-certification {
  width: 100%;
  padding: 50px 0;
  background: #F7F7F7;
}

.partner-contaniner {
  width: 1200px;
  font-size: 0;
}

img.customer-logo {
  display: inline-block;
  margin-top: 30px;
  margin-right: calc((100% - 959px) / 6);
  margin-right: calc((100% - 959px) / 6.7)\9;
  width: 137px;
  height: 53px;
}

img.customer-logo:nth-child(7n) {
  margin-right: 0;
}

.qfkwebsite .home-box-01 {
  width: 100%;
  padding: 120px 0;
  background: white;
  text-align: center;
  background-attachment: fixed;
  background-position: center center;
}

.qfkwebsite .home-box-01 .title {
  display: inline-block;
  font-size: 2.33em;
  color: #333333;
  margin: 0;
}

.qfkwebsite .home-box-01 .line {
  margin-top: 20px;
  display: inline-block;
  width: auto;
  width: 50px;
  height: 3px;
  background: #D8d8d8;
  border-radius: 3px;
  margin-bottom: 20px;
}

.qfkwebsite .home-box-01 .subTitle {
  display: inline-block;
  width: 86%;
  max-width: 860px;
  font-size: 1.17em;
  line-height: 1.75em;
  color: #999999;
  margin: 0;
}

.qfkwebsite .home-box-01 .operationsWrapper {
  width: 100%;
  margin-top: 80px;
  text-align: center;
}

.qfkwebsite .home-box-01 .operationsWrapper .operationsItem {
  display: inline-block;
  width: 15%;
  min-width: 200px;
  max-width: 250px;
  border: 1px solid #efefef;
}

.qfkwebsite .home-box-01 .operationsWrapper .operationsItem .imgWrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.qfkwebsite .home-box-01 .operationsWrapper .operationsItem .imgTitle {
  font-size: 1.5em;
  color: #666666;
}

.qfkwebsite .home-box-01 .operationsWrapper .operationsItem .imgSubTitle {
  margin-top: 10px;
  font-size: 1.17em;
  color: #999999;
}

.qfkwebsite .home-box-01 .clientIconWrapper {
  font-size: 0;
  width: 1210px;
}

.qfkwebsite .home-box-01 .clientIconWrapper .operationsItem {
  width: 138px;
  display: inline-flex;
}


.qfkwebsite .home-box-01 .clientIconWrapper .operationsItem .clientImgStyle {
  width: 137px;
  height: 50px;
}

.oper-margin {
  margin-right: 40px;
}


.qfkwebsite .home-box-02 {
  width: 100%;
  height: 700px;
  position: relative;
  color: white;
  background-attachment: fixed;
  background-position: center;
  background-position: 50% 0;
  background-repeat: no-repeat;
}

.qfkwebsite .home-box-02 .box2-describtions {
  margin-left: 2.5vw;
  width: 700px;
  height: 540px;
  box-sizing: border-box;
  padding-left: 200px;
  padding-top: 98px;
  padding-right: 50px;
  background: #128BED;
}

.qfkwebsite .home-box-02 .title {
  font-size: 3em;
  margin: 0;
}

.qfkwebsite .home-box-02 .box2-acticle {
  margin-top: 12px;
  display: inline-block;
  font-size: 1.17em;
  letter-spacing: 0;
  line-height: 24px;
}

.qfkwebsite .home-box-02 .playVideo {
  font-size: 1.33em;
  line-height: 24px;
  cursor: pointer;
  margin-top: 32px;
}

.qfkwebsite .home-box-02 .playVideo .trailer_play {
  width: 24px;
  height: 24px;
  width: auto;
  margin-right: 10px;
}

.qfkwebsite .home-box-02 .btnShenqing {
  margin-top: 40px;
  background: transparent;
  color: white;
}

.qfkwebsite .home-box-01 .operationsWrapper .operationsSecondItem {
  overflow: hidden;
  position: relative;
  width: 22%;
  min-width: 240px;
  max-width: 330px;
  height: 400px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
}

.qfkwebsite .home-box-01 .operationsWrapper .operationsSecondItem .hoverShow {
  position: absolute;
  width: 100%;
  height: 100%;
  display: block;
  text-align: center;
  box-sizing: border-box;
  padding-top: 24px;
  top: 0;
  left: 0;
  transform: translateY(100%);
  opacity: 0;
  transition: 0.3s all linear;
}

.qfkwebsite .home-box-01 .operationsWrapper .operationsSecondItem .hoverShow .imgSubTitle {
  display: inline-block;
  font-size: 1.17em;
  line-height: 2em;
  margin: 0 6%;
  text-align: left;
}

.qfkwebsite .home-box-02 .box2-describtions.layoutRight {
  float: right;
  margin-right: 30px;
  padding-left: 50px;
}

.qfkwebsite .home-box-02 .box2-describtions {
  margin-left: 2.5vw;
  width: 700px;
  height: 540px;
  box-sizing: border-box;
  padding-left: 200px;
  padding-top: 98px;
  padding-right: 50px;
  background: #128BED;
}

.qfkwebsite .home-box-02 .box2-describtions.layoutRight .box2-acticle {
  width: 65%;
}



.qfkwebsite .home-box-01 .showMore:hover {
  color: #128BED;
}

.qfkwebsite .home-box-01 .showMore {
  width: 87px;
  margin: 30px auto 0 auto;
  text-align: center;
  padding: 8px 0;
  font-size: 1em;
}

.el-button:hover {
  color: #409EFF;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}

.el-button--mini,
.el-button--mini.is-round {
  padding: 7px 15px;
}

.el-button--mini,
.el-button--small {
  font-size: 12px;
  border-radius: 3px;
}

.qfkwebsite .home-box-01 .selectOperationsWrapper {
  margin: 0 auto;
  text-align: center;
}

.qfkwebsite .home-box-01 .selectOperationsWrapper .contentWrapper {
  max-width: 1240px;
  display: inline-block;
  margin: 0 auto;
  text-align: left;
}

.operations-item-box {
  display: inline-block;
  box-sizing: border-box;
  padding: 13.5px;
  vertical-align: bottom;
}

.qfkwebsite .home-box-01 .selectOperationsWrapper .operationsItem {
  text-align: center;
  display: inline-block;
  width: 278px;
  height: 234px;
  border: 1px solid #D8d8d8;
  border-radius: 3px;
  font-size: 1.2em;
  cursor: pointer;
  color: #333333;
  position: relative;
}

.qfkwebsite .home-box-01 .selectOperationsWrapper .operationsItem .icon {
  font-size: 32px;
  line-height: 32px;
  margin-top: 30px;
  margin-bottom: 15px;
  color: #128BED;
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.qfkwebsite .home-box-01 .selectOperationsWrapper .operationsItem .optionTitle {
  font-size: 16px;
  line-height: 16px;
  color: #333333;
}

.qfkwebsite .home-box-01 .selectOperationsWrapper .operationsItem .describition {
  text-align: left;
  box-sizing: border-box;
  padding: 20px;
  padding-bottom: 0;
  line-height: 24px;
  color: #999999;
}

.qfkwebsite .home-box-01 .selectOperationsWrapper .operationsItem .selectIcon {
  position: absolute;
  z-index: 2;
  right: 10px;
  top: 10px;
  display: none;
}

.el-checkbox {
  color: #666666;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  user-select: none;
}

.el-checkbox,
.el-checkbox__input {
  display: inline-block;
  position: relative;
  white-space: nowrap;
}

.el-checkbox__input {
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

.el-checkbox,
.el-checkbox__input {
  display: inline-block;
  position: relative;
  white-space: nowrap;
}

.el-checkbox__inner {
  display: inline-block;
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 2px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 14px;
  height: 14px;
  background-color: #fff;
  z-index: 1;
  -webkit-transition: border-color .25s cubic-bezier(.71, -.46, .29, 1.46), background-color .25s cubic-bezier(.71, -.46, .29, 1.46);
  transition: border-color .25s cubic-bezier(.71, -.46, .29, 1.46), background-color .25s cubic-bezier(.71, -.46, .29, 1.46);
}

input[type=checkbox],
input[type=radio] {
  margin: 4px 0 0;
  margin-top: 1px\9;
  line-height: normal;
}

input[type=checkbox],
input[type=radio] {
  box-sizing: border-box;
  padding: 0;
}

.el-checkbox__original {
  opacity: 0;
  outline: 0;
  position: absolute;
  margin: 0;
  width: 0;
  height: 0;
  z-index: -1;
}

.qfkwebsite .home-box-01 .btnShenqing {
  margin-top: 30px;
  width: 200px;
}

.videoPop {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100vh;
  text-align: center;
  background: rgba(0, 0, 0, 0.6);
}

.videoPop .videoWrapper {
  width: 700px;
  height: 394px;
  margin-top: calc((100vh - 394px) / 2);
  margin-left: calc((100% - 700px) / 2);
  position: relative;
}

.videoPop .videoWrapper .trailer_screen {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  object-fit: fill;
}

video {
  display: inline-block;
  vertical-align: baseline;
}

.videoPop .videoWrapper .trailer_close {
  width: 32px;
  height: 32px;
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 100;
}

.el-icon-arrow-down:before {
  content: "\e603";
}

[class*=" el-icon-"],
[class^=el-icon-] {
  font-family: element-icons !important;
  speak: none;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: baseline;
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.qfkwebsite .describitionContent .dropDownIconContainer:hover {
  -webkit-animation: none;
  animation: none;
}

.qfkwebsite .describitionContent .dropDownIconContainer:hover .dropDownIcon {
  visibility: hidden;
}

.qfkwebsite .describitionContent .dropDownIconContainer:hover .dropDownIconHover {
  visibility: visible;
}

.qfkwebsite .describitionContent .dropDownIconContainer .dropDownIconHover {
  visibility: hidden;
  position: absolute;
  left: 0;
  top: 0;
}

.qfkwebsite .home-box-01 .operationsWrapper .operationsSecondItem .line {
  width: 20px;
}

.qfkwebsite .home-box-01 .operationsWrapper .operationsSecondItem .imgTitle {
  font-size: 1.3em;
}

.qfkwebsite .home-box-01 .selectOperationsWrapper .operationsItem.selected .qfkwebsite .home-box-01 .selectOperationsWrapper .operationsItem:hover {
  border-color: #128BED;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.qfkwebsite .home-box-01 .selectOperationsWrapper .operationsItem.selected {
  border-color: #128BED;
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #409EFF;
  border-color: #409EFF;
}

.qfkwebsite .describitionContent .newsWrapper .newsTitlesWrapper .newsItem {
  width: 100%;
  display: flex;
  align-items: center;
  white-space: pre-line;
  font-size: 1.17em;
}

.qfkwebsite .describitionContent .newsWrapper .newsTitlesWrapper .newsItem a {
  color: #333333;
  text-decoration: none;
}

.qfkwebsite .describitionContent .newsWrapper .newsTitlesWrapper .newsItem a:hover {
  color: #128BED;
}

.qfkwebsite .describitionContent .newsWrapper .newsTitlesWrapper .newsItem .date {
  color: #999999;
}

@-webkit-keyframes dropdownIconAnima {
  0% {
    bottom: 85px;
  }

  50% {
    bottom: 75px;
  }

  100% {
    bottom: 85px;
  }
}

@-moz-keyframes dropdownIconAnima {
  0% {
    bottom: 85px;
  }

  50% {
    bottom: 80px;
  }

  100% {
    bottom: 85px;
  }
}

@-o-keyframes dropdownIconAnima {
  0% {
    bottom: 85px;
  }

  50% {
    bottom: 80px;
  }

  100% {
    bottom: 85px;
  }
}

@keyframes dropdownIconAnima {
  0% {
    bottom: 85px;
  }

  50% {
    bottom: 80px;
  }

  100% {
    bottom: 85px;
  }
}

.qfkwebsite .home-box-01 .operationsWrapper .operationsSecondItem:hover .hoverShow {
  transform: translateY(0);
  opacity: 1;
}

.qfkwebsite .home-box-01 .operationsWrapper .operationsSecondItem .hoverShow .imgTitle {
  font-size: 1.3em;
  margin-top: 15px;
  margin-bottom: 10px;
}

.qfkwebsite .home-box-01 .selectOperationsWrapper .operationsItem:hover {
  border-color: #128BED;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.qfkwebsite .home-box-01 .selectOperationsWrapper .operationsItem:hover .selectIcon {
  display: inline-block;
}

.qfkwebsite .home-box-01 .selectOperationsWrapper .operationsItem:hover .el-checkbox__inner {
  border-color: #409EFF;
}

.qfkwebsite .home-box-01 .selectOperationsWrapper .operationsItem.selected {
  border-color: #128BED;
}

.qfkwebsite .home-box-01 .selectOperationsWrapper .operationsItem.selected .selectIcon {
  display: inline-block;
}

.qfkwebsite .describitionContent .contentNormal.proTrial {
  text-align: left;
  padding-top: 100px;
}

.qfkwebsite .describitionContent .contentNormal.proTrial .title-01 {
  font-size: 36px;
  line-height: 47px;
  color: #FFFFFF;
  letter-spacing: 0;
  text-shadow: 0 2px 8px rgba(9, 11, 29, 0.8);
  font-weight: bold;
}

.qfkwebsite .describitionContent .trialPop {
  position: absolute;
  top: 106px;
  padding: 30px 30px;
  background: white;
  right: 0;
}

.qfkwebsite .describitionContent .trialPop .trialPop-title {
  font-size: 24px;
  color: #333333;
  margin-top: 0px;
  text-align: center;
}

.qfkwebsite .describitionContent .trialPop .trialPop-subTitle {
  font-size: 12px;
  color: #999999;
  line-height: 18px;
  display: inline-block;
  text-align: center;
  width: 100%;
  margin-bottom: 20px;
}

.qfkwebsite .describitionContent .contentNormal.proTrial .title-02 {
  margin-top: 10px;
  margin-bottom: 50px;
  font-size: 64px;
  line-height: 85px;
  font-weight: bold;
  color: #FFFFFF;
  letter-spacing: 0;
  text-shadow: 0 2px 8px rgba(9, 11, 29, 0.8);
}

.qfkwebsite .describitionContent .contentNormal.proTrial .playVideo {
  margin-top: 80px;
  display: inline-block;
}

.qfkwebsite .describitionContent .trialPop .el-form-item {
  margin-bottom: 20px;
}

.qfkwebsite .describitionContent .trialPop .el-form-item__error {
  padding-top: 2px;
}

.screenForm .iconfont {
  display: none;
}

.flex-cCenter {
  -webkit-box-align: center;
  -moz-align-items: center;
  -webkit-align-items: center;
  align-items: center;
}

.qfkwebsite .describitionContent .trialPop .yzmInput .el-input__inner {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}

.qfkwebsite .describitionContent .trialPop .el-input__inner {
  width: 100%;
}

.el-form-item__error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  left: 0;
}

/*意见反馈弹框*/
.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}

.fade.in {
  opacity: 1;
}

.modal {
  display: none;
  position: fixed;
  z-index: 10050;
  -webkit-overflow-scrolling: touch;
  outline: 0;
}

.modal,
.modal-backdrop {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}


.btn-foucs {
  outline: none;
}
.authoritative-item {
  display: inline-block;
  margin-right: 20px;
}
.authoritative-item img {
  width: 183px;
  margin-bottom: 15px;
}
.authoritative-item p {
  font-size: 14px;
  line-height: 22px;
  color: #333333;
  text-align: center;
}
