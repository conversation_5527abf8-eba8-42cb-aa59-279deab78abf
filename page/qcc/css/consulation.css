img {
  vertical-align: middle;
  border: 0;
}

a:hover,
a:focus {
  text-decoration: none;
}

#consulation {
  position: fixed;
  bottom: 60px;
  right: -2px;
  height: 313px;
  z-index: 999999;
  line-height: normal;
}

#consulation .nav {
  float: right;
  height: auto;
  width: 33px;
  line-height: 20px;
  margin-top: 94px;
  padding: 15px 0px 10px 10px;
  background: #128BED;
  color: #FFFFFF;
  font-size: 14px;
  border-radius: 8px 0 0 8px;
  cursor: pointer;
  vertical-align: middle;
}

#consulation .main {
  float: right;
  height: 315px;
  min-height: 315px;
  width: 0px;
  background: #FFFFFF;
  border: 1px solid #F5F5F5;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.10);
}

#consulation .info-item {
  padding: 15px;
  border-bottom: 1px solid #F5F5F5;
  line-height: 17px;
}

#consulation .info-item:last-child {
  border-bottom: none;
}

#consulation .info-item a {
  font-size: 13px;
}

#consulation .info-item a:hover {
  text-decoration: none !important;
}

#consulation .info-item>img {
  width: 14px;
  margin-top: -1px;
}

#consulation .info-item span img {
  width: 9px;
  margin-top: -1px;
}

#consulation .cslt-title {
  color: #666666;
  font-size: 13px;
}

#consulation .cslt-detail {
  display: block;
  font-size: 13px;
  color: #999999;
  margin-left: 18px;
  line-height: 22px;
}

#consulation .cslt-detail a {
  color: #999999;
}

#consulation .pointer {
  cursor: pointer;
}


#consulation #wechat-detail {
  text-align: center;
  height: 318px;
  width: 202px;
  position: relative;
  top: -311px;
  background: rgba(0, 0, 0, 0.40);
  padding-top: 78px;
}

#consulation #program-detail {
  text-align: center;
  height: 318px;
  width: 202px;
  position: relative;
  top: -311px;
  background: rgba(0, 0, 0, 0.40);
  padding-top: 78px;
}

#consulation #wechat-detail img {
  width: 141px;
}

#consulation #program-detail img {
  width: 141px;
}

#suggestionModal .group {
  padding: 10px 0px;
  height: 60px;
}

#suggestionModal textarea {
  border-radius: 3px !important;
  height: 120px;
  border: 1px solid #D8d8d8;
}

#suggestionModal textarea:hover {
  border-color: #128BED;
}

#suggestionModal p {
  font-size: 12px;
  color: #999999;
  letter-spacing: 0;
  text-align: justify;
  line-height: 18px;
}

#suggestionModal label {
  font-size: 14px;
  color: #666666;
  padding-top: 10px;
  text-align: justify;
}

.modal-header {
  padding: 20px 20px 10px 20px;
}

.modal-new .modal-content {
  background: #FFFFFF;
  box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.20);
  border-radius: 3px;
  width: 600px;
}

.modal-new .modal-header {
  background: #F5F5F5;
  border-radius: 3px 3px 0 0;
  font-size: 16px;
  color: #999999;
  text-align: justify;
}

.modal-new .modal-header .close {
  margin-top: 6px !important;
  width: 12px;
  height: 12px;
}

.modal-new .modal-body input[type='text'] {
  border: 1px solid #D8d8d8;
  border-radius: 3px !important;
  font-size: 14px;
  color: #666666;
  height: 40px;
}

.modal-new .modal-body input[type='text']:hover {
  border-color: #128BED;
}

.modal-new .modal-body {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

.modal-new .modal-footer {
  border-top: none;
}

.el-message {
  z-index: 100001 !important;
}


/*---------返回顶部----------------*/
#backToTop {
  position: fixed;
  right: 0;
  bottom: 110px;
  cursor: pointer;
  display: none;
  z-index: 99;
  box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.15);
}

#backToTop:hover .icon_back2top {
  display: none;
}

#backToTop:hover .icon_back2top_text {
  display: inline-block;
}

#backToTop .icon_back2top_text {
  display: none;
}
