.embed-responsive,
.modal,
.modal-open,
.progress {
  padding-right: 0px !important;
}

img {
  vertical-align: middle;
}

[v-cloak] {
  display: none;
}

hr,
img {
  border: 0;
}

a {
  text-decoration: none !important;
}

a:hover,
a:focus {
  text-decoration: none !important;
}

.top-header.blackTheme {
  border-color: rgba(48, 49, 51, 0.1);
  background: white;
}

.top-header {
  min-width: 1300px;
  position: fixed;
  z-index: 10;
  left: 0;
  top: 0;
  height: 49px;
  line-height: 50px;
  box-sizing: content-box;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  font-size: 14px;
}

.top-header.blackTheme .logo-wrapper {
  border-color: rgba(48, 49, 51, 0.1);
}

.top-header .logo-wrapper {
  display: inline-block;
  width: auto;
  padding: 0px 15px 0px 30px;
  box-sizing: border-box;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.top-header.blackTheme .header-operate-wrapper a {
  color: #333333 !important;
}

.top-header .header-operate-wrapper {
  display: inline-block;
  width: auto;
  box-sizing: border-box;
  color: white !important;
  font-size: 0;
}

.top-header .header-operate-wrapper>* {
  font-size: 14px;
  display: inline-block;
  margin-right: 1px !important;
  margin-left: 0 !important;
}

.top-header .header-operate-wrapper>a:first-child {
  margin-left: 0;
}

.top-header.blackTheme .header-operate-wrapper .item.actived {
  color: white;
}

.top-header.blackTheme .header-operate-wrapper .item:hover {
  color: white;
}

.top-header .header-operate-wrapper .item.actived {
  background: #128bed;
}

.top-header .header-operate-wrapper .item:hover {
  background: #128bed;
}

.top-header .header-operate-wrapper .item {
  display: inline-block;
  height: 100%;
  padding: 0px 15px 0px 15px;
  cursor: pointer;
}

.top-header .header-operate-wrapper .item {
  display: inline-block;
  height: 100%;
  padding: 0 15px;
  cursor: pointer;
}

.top-header.blackTheme .loginbtn-wrapper {
  border-color: rgba(48, 49, 51, 0.1);
}

.top-header .loginbtn-wrapper {
  right: 0;
  top: 0;
  display: inline-block;
  width: auto;
  padding: 0 30px;
  box-sizing: border-box;

  display: flex;
  align-items: center;
}

.top-header .loginbtn-wrapper .__qfk-login-btn {
  max-height: 50px;
}


.top-header .loginbtn-wrapper .button {
  height: 30px;
  width: 84px;
}

.el-button--small {
  font-size: 14px !important;
  border-radius: 2px !important;
}

.el-button--primary,
.el-button--primaryTwo {
  color: #fff !important;
  background-color: #409eff !important;
  border-color: #409eff !important;
}

.el-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #666666;
  -webkit-appearance: none;
  text-align: center;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  -webkit-transition: 0.1s;
  transition: 0.1s;
  font-weight: 500;
  padding: 12px 20px;
  font-size: 14px;
  border-radius: 4px;
}

.el-button--primary:hover {
  background: #66b1ff !important;
  border-color: #66b1ff !important;
  color: #fff;
}

.top-header .loginbtn-wrapper .button.login {
  color: #128bed;
}

.el-button+.el-button {
  margin-left: 10px;
}

.top-header .loginbtn-wrapper .button.login:hover {
  color: #fff;
  background-color: #128bed;
  border-color: #128bed;
}

.freeUserBtn {
  background: #128BED !important;
  color: #ffffff !important;
}

.freeUserBtn:hover {
  background: #0069BF !important;
  color: #ffffff !important;
}

.imageClass {
  background-image: url(/img/zxba.png);
  display: inline-block;
  background-size: 138px 36px;
  background-position: 0px -1px;
  width: 138px;
  height: 35px;
  margin-top: 8px;
  cursor: pointer;
}

.btn-buy-container {
  display: flex;
}

.btn-buy {
  margin-top: -2px;
  height: 50px;
  padding-left: 15px;
  padding-right: 15px;
  background-color: transparent;
  border: 0px;
  color: #d9b171;
}

.btn-buy2 {
  height: 50px;
  padding-left: 15px;
  padding-right: 15px;
  background-color: transparent;
  border: 0px;
  color: #ffffff;
}

.blackTheme.btn-buy2 {
  color: #333333;
}

.blackTheme.btn-buy2.iconfont {
  color: #128bed !important;
}

.btn-foucs {
  outline: none;
}

.btn-foucs:focus {
  outline: none;
}

.btn-buy:hover {
  background-color: #128bed;
  color: #fff;
  outline: none;
}

.btn-buy2:hover {
  color: #128bed !important;
  outline: none;
}

.btn-pop-wx {
  height: 275px;
  width: 240px;
  //padding: 20px 30px;
  padding-top: 10px;
  text-align: center;
}

.btn-pop-wx-title {
  color: #666666;
  font-size: 14px;
  margin-bottom: 10px;
}

.btn-pop-wx-content {
  border: 1px;
}

.btn-pop-wx-img {
  height: 200px;
  width: 200px;
}

.btn-pop-wx-footer {
  color: #999999;
  font-size: 12px;
  margin-top: 10px;
}

.el-carousel__arrow {
  border-radius: 50% !important;
  background-color: #ffffff !important;
  color: #000000 !important;
  font-size: 25px !important;
  opacity: 0.5 !important;
}

.el-icon-arrow-left:before {
  content: "\e792" !important;
  //content: url('/img/arrow-left.png') !important;
  opacity: 0.3;
  font-size: 30px;
  position: absolute;
  top: 4px;
  left: 2px;
}

.el-icon-arrow-right:before {
  content: "\e791" !important;
  //content: url('/img/arrow-right.png') !important;
  opacity: 0.3;
  font-size: 30px;
  position: absolute;
  top: 3px;
  left: 4px;
}

.carousel-container-par {
  height: 450px;
}

.carousel-container {
  padding-top: 110px;
  // width: 1200px;
  // margin: 0 auto;
  text-align: left;
  padding-left: 15%;
}

.carousel-container2 {
  padding-top: 110px;
  width: 1000px;
  margin: 0 auto;
  text-align: left;
}

.carousel-title {
  font-size: 36px;
  color: #ffffff;
}

.carousel-title-2 {
  font-size: 42px;
  line-height: 55px;
  color: #ffffff;
  margin-top: 10px;
}

.carousel-title-2:first-child {
  margin-top: 0;
}

.carousel-desc {
  width: 660px;
  line-height: 30px;
  font-size: 16px;
  color: #FFFFFF;
  margin-top: 40px;
}


.carousel-content {
  margin-top: 15px;
  font-size: 16px;
  color: #ffffff;
  max-width: 620px;
}

.carousel-btn-c {
  margin-top: 30px;
}

.carousel-btn {
  font-size: 16px;
  color: #ffffff;
  background-color: #128bed;
  border-radius: 3px;
  width: 124px;
  height: 40px;
  outline: none;
  border: none;
  margin-right: 12px;
}

.carousel-btn:hover {
  background-color: #0069BF !important;
}

.carousel-btn2 {
  font-size: 16px;
  color: #128bed;
  border-color: #128BED;
  background-color: #ffffff;
  border-radius: 3px;
  width: 124px;
  height: 40px;
  outline: none;
  border: none;
}

.carousel-btn2:hover {
  color: #ffffff;
  background-color: #128bed;
}

.el-carousel__arrow {
  z-index: 4;
}

.link {
  color: #128BED !important;
  cursor: pointer !important;
  text-decoration: none;
}

.link:hover {
  color: #0069BF !important;
  text-decoration: none;
}

.feedback {
  width: 400px !important;
  top: 10vh;
}

.dataRequire {
  width: 800px !important;
  top: 10vh;
}

.dataRequire .modal-content {
  width: 800px !important;
}

.feedback .modal-content {
  width: 450px !important;
}

.dataRequire .modal-content .modal-header .close {
  margin-top: 0px !important;
}

.feedback .modal-content .modal-header .close {
  margin-top: 0px !important;
}

.buy-title {
  font-size: 14px;
  margin-bottom: 20px;
}

.btn-h40 {
  padding: 0 14px;
  height: 40px;
  line-height: 40px;
}

.concat-content {
  display: inline-block;
  margin-left: 15px;
  padding-top: 20px;
  vertical-align: top;
}

.person-name {
  font-size: 24px;
  font-weight: bold;
}

.concat-title {
  margin-top: 5px;
  line-height: 16px;
  font-size: 12px;
  color: #999999;
  margin-bottom: 20px;
}

.concat-phone,
.concat-email {
  font-size: 14px;
  margin-bottom: 10px;
  line-height: 19px;
}


.feedback .modal-content .modal-footer {
  padding-bottom: 20px;
  text-align: center !important;
  padding-top: 0px !important;
}

.feedback .modal-content .modal-footer button {
  color: #ffffff !important;
  background-color: #128bed !important;
  border-color: #128bed !important;
}


.modal-new .modal-header .close {
  height: auto !important;
  width: auto !important;
}

.top-header .header-operate-wrapper .more-menu {
  position: relative;
}

.top-header .header-operate-wrapper .more-menu:hover .item {
  background: #128bed;
}

.top-header .header-operate-wrapper .more-menu:hover .item .iconfont.icon-xiala {
  transform: rotate(-180deg);
  transition: transform 0.1s linear;
  top: -15px;
}

.top-header .header-operate-wrapper .more-menu:hover .drop-menus {
  display: block;
}

.top-header .header-operate-wrapper .more-menu .item {
  padding-right: 32px;
  margin-left: -3px;
}

.top-header.blackTheme .header-operate-wrapper .more-menu .item:not(.actived) .iconfont.icon-xiala {
  color: #333333;
}

.top-header.blackTheme .header-operate-wrapper .more-menu:hover .item,
.top-header.blackTheme .header-operate-wrapper .more-menu:hover .item .iconfont.icon-xiala {
  color: white;
}

.top-header .header-operate-wrapper .more-menu .item .iconfont.icon-xiala {
  font-size: 12px;
  position: absolute;
  right: 13px;
  top: -14px;
}

.top-header .header-operate-wrapper .more-menu .drop-menus {
  display: none;
  position: absolute;
  left: -3px;
  top: 34px;
  width: calc(100% - 1px);
  background: white;
  padding: 0;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  -webkit-box-shadow: rgba(0, 0, 0, 0.3) 0px 5px 12px;
  box-shadow: rgba(0, 0, 0, 0.3) 0px 5px 12px;
}

.top-header .header-operate-wrapper .more-menu .drop-menus li {
  list-style-type: none;
  padding: 10px 15px;
  padding-right: 0;
  width: 100%;
  margin: 0;
  line-height: 19px;
}

.top-header .header-operate-wrapper .more-menu .drop-menus li:last-child:hover {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.top-header .header-operate-wrapper .more-menu .drop-menus li:hover {
  background: #F7F7FA;
}

.top-header .header-operate-wrapper .more-menu .drop-menus li a {
  color: #333333;
}

.top-header .header-operate-wrapper .more-menu .drop-menus li a.c-actived {
  color: #128BED;
}

.el-button--normal,
.el-button--normal:focus {
  color: #333333;
  border-color: #D8d8d8;
  background-color: #fff;
}

.el-button--normal:hover,
.el-button--normal:focus:hover {
  border-color: #128bed;
  background-color: #fff;
  color: #128bed;
}
.qcc-result-company-item em {
  vertical-align: top;
  margin: 0;
}
