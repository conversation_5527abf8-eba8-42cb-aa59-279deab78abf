.content-width {
  width: 1180px;
}

.header-content-logo {
  height: 50px;

}

.header-content-height {
  height: 480px;
}

.margin-center {
  margin: 0 auto;
}

.margin-top-5 {
  margin-top: 5px;
}

.active-color {
  color: #128bed;
}


.dis-active {
  color: #128bed;
}



.odd-body-bg {
  background-color: #F7F7F7;
}


.header-bg {
  background: url('/img/acc-bg.jpg');
  background-position: top;
  background-size: cover;

  height: 550px;
}


.header {
  // background-color: black;
}

.logo-c {
  // background-color: black;

}

.border-bottom {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.scroll-header {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 0px 0px rgba(192, 196, 204, 0.3);
  position: fixed;
  z-index: 100;
  top: 0;
  width: 100%;
  border: none;
}

.logo {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-img {
  height: 36px;
  position: relative;
  top: -2px;
}

.logo-btn {
  height: 36px;
}

.logo-login {
  margin-left: 10px;
  width: 84px;
  height: 30px;
  font-size: 14px;
}

.logo-all-btn-try {
  cursor: pointer;
  color: white;
  background: #128bed;
  outline-style: none;
  border-radius: 15px;
  border: 1px solid #128bed;
}

.logo-all-btn {
  cursor: pointer;
  color: white;
  background: transparent;
  outline-style: none;
  border-radius: 15px;
  border: 1px solid #ffffff;
}

.scroll-header .logo-all-btn {
  color: #333333 !important;
  background: transparent;
  border: 1px solid #D8d8d8 !important;
}

.scroll-header .item {
  color: #333333 !important;
  background: transparent;
  // border: 1px solid #D8d8d8 !important;
}


.scroll-header .top-header-1 .header-operate-wrapper .item.actived {
  color: #fff !important;
}


.scroll-header .item:hover {
  color: white !important;
}

.logo-all-btn:hover {
  background: #128bed;
  border: 1px solid #128bed;
}


.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  flex-grow: 2;
}

.header-content-title {
  font-size: 42px;
  color: white;
  text-shadow: 0px 6px 12px rgba(0, 0, 0, 0.5);
}

.header-content-title-m {
  width: 30px;
  height: 5px;
  background-color: #128BED;
  margin-top: 30px;
  margin-bottom: 20px;
}

.header-content-title-2 {
  font-size: 16px;
  color: white;
  width: 760px;
  text-align: center;
  line-height: 32px;
  text-shadow: 0px 3px 6px rgba(0, 0, 0, 0.5);
}

.header-2 {
  background: transparent;
  opacity: 100%;
  display: flex;
}

.header-2-child {

  color: white;
  width: 20%;
  height: 125px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  color: white;
  padding: 0px 30px;
}

.child-boder-r {
  border-right: 1px solid #383838;
}

.header-2-child-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 10px;
}

.header-2-child-subTitle {
  font-size: 12px;
  line-height: 18px;
}







.body-c {
  padding: 80px 0px;
}

.body-title {
  padding-bottom: 30px;
  color: #333333;
  font-size: 28px;
  /*font-weight: bold;*/
  text-align: center;
}



.footer {
  height: 48px;
  background-color: black;
  text-align: center;
  color: #999999;
  line-height: 48px;
  font-size: 12px;
}

.footer-white-a {
  color: #999999;
  text-decoration: none;
}

.service-title {
  border-bottom: 1px solid #eeeeee;
  display: flex;
  justify-content: center;
  color: #333333;
}

.rno-2-tabs-list {
  border-bottom: #E5E8ED 2px solid;
  position: relative;
  list-style: none;
  padding: 0px;
  margin: 0px;
}

.rno-2-tabs-item {
  padding: 0px 59px;
  display: inline-block;
  text-align: center;
  position: relative;
  cursor: pointer;
  padding-bottom: 12px;
  overflow: hidden;
  vertical-align: bottom;
  margin-bottom: -2px !important;
  font-size: 16px;
}

.font-size-40 {
  font-size: 40px;
}

.rno-2-tabs-item-con {
  cursor: pointer;
}

.rno-2-tabs-item-con:before {
  content: '';
  position: absolute;
  left: 50%;
  margin-left: -4px;
  bottom: -4px;
  width: 8px;
  height: 8px;
  border-top: 2px solid #128bed;
  border-left: 2px solid #128bed;
  background-color: #fff;
  z-index: 10;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.rno-2-tabs-item-con:after {
  display: inline-block;
  content: "";
  height: 2px;
  width: 100%;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: #128bed;
  z-index: 1;
}

.rno-content {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  height: 320px;
}

.rno-content-item {
  width: 33.3%;
}

.rno-content-item-child-c {
  padding-left: 5px;
  padding-bottom: 10px;
}

.rno-content-item-child {
  border: 1px solid #eeeeee;
  display: flex;
  padding: 0px 30px;
  align-items: center;
}

.rno-serive-data {
  text-align: left;
  margin-left: 20px;
  display: flex;
  flex-direction: column;
}

.hangye {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hangye-content {
  margin-left: 30px;
  max-width: 585px;
}

.hangye-content-title {
  font-weight: bold;
  color: #333333;
  font-size: 18px;
}

.hangye-content-sub {
  margin-top: 20px;
  color: #666666;
  font-size: 14px;
  line-height: 21px;
}

.jjfa-f {
  display: flex;
}

.jjfa-margin-l {
  margin-left: 30px;
}

.jjfa-margin-t {
  margin-top: 30px;
}

.jjfa-f-sub {
  display: flex;
  height: 320px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.1);
  max-width: 860px;
}

.jjfa-f-sub-c {
  min-width: 430px;
  padding: 0px 30px;
  padding-top: 70px;
}

.jjfa-f-sub-title {
  font-weight: bold;
  font-size: 18px;
  color: #333333;
}

.jjfa-f-sub-sub {
  margin-top: 30px;
  font-size: 14px;
  color: #666666;
  line-height: 21px;
  max-width: 370px;
}


.service-firstTitle {
  font-size: 18px !important;
  font-weight: bold !important;
}

.service-firstsub {
  font-size: 14px !important;
  line-height: 21px !important;
}

.rno-serive-f-n {
  font-size: 16px;
  color: #333333;
}

.rno-serive-s-n {
  max-width: 258px;
  font-size: 12px;
  color: #666666;
  margin-top: 10px;
  line-height: 21px;
  height: 63px;
}


.my-youshi {
  background: url('/img/myyoushi.jpg');
  background-position: top;
  background-size: cover;
}

.youshi-margin {
  padding: 80px 0px;
}

.youshi-title {
  font-size: 24px;
  color: #ffffff;
  text-align: center;
  margin-bottom: 30px;
  font-weight: bold;
}

.youshi-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.youshi-content-item {
  width: 33%;

}

.youshi-m-r {
  padding-right: 30px;
}

.youshi-m-b {
  border-left: 1px solid #3E97DF;
  border-right: 1px solid #3E97DF;
}

.youshi-m-l10 {
  margin-left: 10px;
}

.youshi-m-l {
  padding-left: 30px;
}



.youshi-item-name {
  color: #FFFFFF;
  font-weight: bold;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.youshi-item-sec {
  margin-top: 20px;
  font-size: 12px;
  color: #FFFFFF;
  line-height: 18px;
  max-width: 360px;
}


.el-carousel__item--card {
  min-width: 860px !important;
}

a {
  text-decoration: none !important;
}


#index-banner .swiper-slide {
  width: auto;
}

.swiper-button-next,
.swiper-button-prev {
  left: 25%;
}


.swiper-button-prev {
  background-image: url("/img/left.png") !important;
  background-size: 60px !important;
  width: 70px !important;
  height: 70px !important;
  position: static !important;
}

.swiper-button-next {
  background-image: url("/img/right.png") !important;
  background-size: 60px !important;
  width: 70px !important;
  height: 70px !important;
  position: static !important;
}

/*// .swiper-pagination-bullet {*/
/*//   background-color: #BBBBBB;*/
/*// }*/

/*// .swiper-pagination-bullets {*/
/*//   bottom: -30px !important;*/
/*// }*/


.swiper-pagination {
  width: 100%;
  padding-top: 20px;
}

.swiper-pagination .swiper-pagination-bullet {
  background-color: #BBBBBB !important;
  margin-left: 12px;
}

.swiper-pagination-bullet-active {
  width: 28px !important;
  border-radius: 4px !important;
}


.swiper-button-cc {
  position: absolute;
  top: 50%;
  width: 100%;
  height: 70px;
  display: flex;
}

.swiper-button-cc2 {
  height: 70px;
  width: 1080px;
  z-index: 100;
  text-align: center;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
}

.top-header-1 {
  position: relative;
}


.top-header-1.blackTheme .header-operate-wrapper a {
  color: #333333 !important;
}

.top-header-1 .header-operate-wrapper {
  display: inline-block;
  width: auto;
  box-sizing: border-box;
  color: white !important;
  height: 50px;
  line-height: 50px;
  left: 150px;
}

.top-header-1 .header-operate-wrapper>a {
  margin-left: -3px;
}

.top-header-1 .header-operate-wrapper>a:first-child {
  margin-left: 0;
}

.top-header-1.blackTheme .header-operate-wrapper .item.actived {
  color: white;
}

.top-header-1.blackTheme .header-operate-wrapper .item:hover {
  color: white;
}

.top-header-1 .header-operate-wrapper .item.actived {
  background: #128bed !important;
}

.top-header-1 .header-operate-wrapper .item:hover {
  background: #128bed;
}

.top-header-1 .header-operate-wrapper .item {
  display: inline-block;
  height: 100%;
  padding: 0px 15px 0px 15px;
  cursor: pointer;
}

.top-header-1 .header-operate-wrapper .item {
  display: inline-block;
  height: 100%;
  padding: 0 15px;
  cursor: pointer;
}

#phonebtn {
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.scroll-header #phonebtn {
  color: #333333;
  border-right: 1px solid rgba(48, 49, 51, 0.1);

}
