body,
h1,
h2,
h3,
h4,
h5,
div,
span,
ul,
li,
p {
  margin: 0;
  padding: 0;
  font-family: "微软雅黑",
    "Microsoft YaHei",
    "PingFang SC",
    "Helvetica Neue",
    Arial,
    Helvetica;
}

*,
body {
  font-family: "微软雅黑", "Microsoft YaHei", "PingFang SC", "Helvetica Neue",
    Arial, Helvetica;
}

li {
  list-style: none;
}

.qfkHome.header-bg {
  background: url(/img/qfkImages/qfk-banner.jpg);
  background-position: top;
  background-size: cover;
  height: 650px;
}

.margin-top-3 {
  margin-top: 3px;
}

.qfkHome .hangye {
  position: relative;
}

.part3 .swiper-pagination {
  width: 1200px !important;
  position: absolute;
  bottom: 20px;
  left: 50%;
  margin-left: -600px;
  z-index: 11;
}

.header-content {
  margin-top: -14px;
}

.header-content-title {
  font-size: 54px;
}

.header-content-title-m {
  width: 40px;
  height: 3px;
  background: #FFFFFF;
  margin-bottom: 30px;
}

.header-content-title-2 {
  line-height: 24px;
  font-size: 18px;
}

.partItem .content-width-12x {
  width: 1200px !important;
}

.top-header-1 .header-operate-wrapper .item.actived {
  font-weight: bold;
  background: none !important;
  border-bottom: 2px solid #128bed !important;
  color: #128bed;
}

.top-header-1 .header-operate-wrapper .item:hover {
  color: #128bed !important;
  background: none;
}

.scroll-header .top-header-1 .header-operate-wrapper .item.actived {
  color: #128bed !important
}

.qfkHome .header-operate-wrapper {
  position: absolute;
  right: 264px;
  text-align: right;
  top: 0;
}

.logo-all-btn {
  background: #FFFFFF;
  color: #333333;
}

.logo-all-btn:hover {
  background: #FFFFFF;
  border: 1px solid #128bed !important;
  color: #128bed !important;
}

.border5R {
  border-radius: 5px;
}

.qfkHome .login-wrap {
  margin-right: 30px;
  margin-bottom: 9px;
  height: 30px;
  margin-top: 9px;
}

.fixedW4x {
  width: 400px;
  height: 510px;
  padding: 0 30px;
  background: #F8F8F8;
}

.margin-r-200x {
  margin-right: 200px;
}

.fixedW4x .fontSize28 {
  margin-top: 40px;
  font-size: 28px;
  color: #333333;
  line-height: 44px;
}

.fixedW4x .content {
  font-size: 14px;
  color: #666666;
  line-height: 28px;
}

.floatLeft {
  float: left;
}

.part2 {
  padding-top: 70px;
}

.part2 .content {
  margin-top: 20px;
}

.part2-rightC {
  width: 600px;
  height: auto;
  margin-top: 40px;
  text-align: center;
  height: 470px;
}

.part2-rightC .fontSize28 {
  font-size: 28px;
  color: #666666;
  line-height: 37px;
  margin-bottom: 10px;
  text-align: left;
}

.part2-rightC .gary {
  width: 100%;
  font-size: 14px;
  color: #999999;
  line-height: 19px;
  text-align: left;
}

.part2-rightC .item-content,
.part2-rightC .item-content ul {
  width: 100%;
  height: auto;
}

.part2-rightC .item-content ul li {
  float: left;
  width: 180px;
  margin-right: 30px;
  text-align: left;
  margin-top: 50px;
}

.part2-rightC .item-content ul li:nth-child(3n) {
  margin-right: 0px;
}

.part2-rightC ul li .smallImg {
  width: 100%;
  float: left;
  margin-bottom: 15px;

}

.part2-rightC ul li .fontsize16 {
  font-size: 16px;
  color: #333333;
  line-height: 21px;
  margin-bottom: 10px;
}

.part2-rightC ul li .desc {
  font-size: 12px;
  color: #999999;
  line-height: 18px;
}

.partItem .imgBg {
  width: 100%;
  height: 100%;
}

.partItem .imgBg {
  position: relative;
  width: 100%;
  height: 100%;
}

.swiper-container {
  position: relative;
  width: 100%;
  overflow: inherit;
  margin-top: 140px
}

.swiper-wrapper .jjfa-f-sub-c {
  width: 460px;
  margin-left: 70px;
  height: 510px;
  padding: 0px;
  padding-top: 70px;
}

.part3 {
  background: url(/img/qfkImages/qfk-part3-bg.jpg);
  background-position: top;
  background-size: cover;
  height: 720px;
  position: relative;
}

.part3-img {
  position: absolute;
  right: -60px;
  bottom: 0px;
}

.jjfa-f-sub {
  height: auto;
  background: none;
  box-shadow: none;
}

.jjfa-f-sub-title {
  font-size: 28px;
  color: #333333;
  line-height: 37px;
  font-weight: normal;
}

.jjfa-f-sub-sub {
  font-size: 14px;
  color: #666666;
  line-height: 22px;
  width: 460px;
  max-width: 460px;
}

.jjfa-f-sub-c ul {
  margin-top: 20px;
  margin-bottom: 30px;
}

.jjfa-f-sub-c ul li {
  font-size: 14px;
  color: #333333;
  line-height: 22px;
}

.part4 {
  padding-top: 70px;
}

.part4 .body-title {
  font-size: 28px;
  color: #333333;
  line-height: 37px;
  padding-bottom: 20px;
  font-weight: normal;
}

.part4 .margin-center .des {
  font-size: 14px;
  color: #666666;
  line-height: 19px;
  margin-bottom: 50px;
}

.part5 {
  margin-top: 70px;
  min-height: 650px;
  background: url(/img/qfkImages/qfk-part5.jpg);
  background-position: top;
  background-size: cover;
  position: relative;
}

.part5 .content-width-12x {
  height: 100%;
  position: relative;
}

.part5 .leftContent {
  float: left;
  width: 500px;
  height: 650px;
  background: #128bed;
}

.part5 .leftContent ul {
  margin-top: 30px;
  margin-left: 70px;
}

.part5 .leftContent ul li {
  width: 100%;
  display: block;
  margin-top: 70px;
  height: 60px;
  float: left;
  color: #fff;
}

.part5 .leftContent ul li .l {
  margin-right: 30px;
  float: left;
}

.part5 .leftContent ul li .l .iconfont {
  font-size: 40px;
}

.part5 .leftContent ul li .r {
  float: left;
  font-size: 14px;
}

.part5 .leftContent ul li .r p {
  margin: 0;
  margin-bottom: 5px;
}

.part5 .leftContent ul li .r .font-size24 {
  font-size: 24px;
  color: #FFFFFF;
  line-height: 31px;
  margin-right: 5px;
}

.part5 .leftContent ul li .r .font-size16 {
  font-size: 16px;
  color: #FFFFFF;
  line-height: 21px;
}

.part5 .rightContent {
  position: absolute;
  width: 800px;
  left: 400px;
  margin-top: 70px;
  z-index: 2;
  height: 510px;
  background: #FFFFFF;
}

.part5 .rc-wrap {
  margin-top: 60px;
  padding: 0 47px;
}

.part5 .rightContent .item {
  margin-bottom: 30px;
}

.part5 .rightContent .item h5 {
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  line-height: 21px;
}

.part5 .rightContent .item p {
  font-size: 14px;
  color: #666666;
  line-height: 22px;
}

.part6 {
  padding-top: 70px;
}

.part6 .body-title {
  padding-bottom: 20px;
  font-weight: normal;
}

.part6 .desc {
  width: 760px;
  margin: auto;
  font-size: 14px;
  color: #666666;
  line-height: 19px;
  text-align: center;
}

.margin-t-5 {
  margin-top: 50px;
}

.item-wrap .item {
  width: 300px;
  height: 295px;
  float: left;
  color: #fff;
}

.item1 {
  background: url('/img/qfkImages/qfk-part5-1.png') 100%;
  background-size: cover;
}

.item2 {
  background: url('/img/qfkImages/qfk-part5-2.png') 100%;
  background-size: cover;
}

.item3 {
  background: url('/img/qfkImages/qfk-part5-3.png') 100%;
  background-size: cover;
}

.item4 {
  background: url('/img/qfkImages/qfk-part5-4.png') 100%;
  background-size: cover;
}

.item-wrap {
  min-height: 295px;
  margin-top: 50px;
}

.item-wrap .item-title {
  width: 240px;
  font-size: 18px;
  margin: 0 auto;
  margin-top: 104px;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 24px;
  text-align: center;
}

.item-wrap p {
  width: 240px;
  margin: 0 auto;
  margin-top: 15px;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 24px;
  text-align: center;
}

.partItem7 {
  margin-top: 70px;
  width: 100%;
  height: 720px;
  background: url('/img/qfkImages/qfk-part6-bg.jpg');
  background-size: cover;
  background-position: center;
}

.partItem7 .leftContent {
  float: left;
  width: 600px;
  margin-top: 140px;
}

.leftContent .apply {
  margin-left: 70px;
  margin-top: 50px;
  color: #fff;
}

.leftContent .apply h4 {
  font-size: 28px;
  margin-top: 50px;
  margin-bottom: 30px;
}

.leftContent .el-form {
  width: 366px;
}

.leftContent .el-form .el-form-item {
  width: 360px;
  margin-bottom: 15px;
}

.leftContent .el-upload-dragger .el-icon-upload {
  color: #fff;
  font-size: 36px;
  margin: 0px;
}

.el-upload-dragger .el-icon-upload {
  margin-top: 10px;
  margin-bottom: 5px;
  line-height: 30px;
}

.apply-btn {
  width: 360px;
  height: 40px;
  background: #128BED;
  border-radius: 3px;
  font-size: 14px;
  color: #FFFFFF;
}

.leftContent .el-form .customer-upload {
  width: 360px;
  height: 114px;
  overflow: hidden;
}

.small-border {
  float: left;
  width: 36px;
  height: 4px;
  background: #FFFFFF;
  margin-top: 0px;
}

#slideValite.nc-container .scale_text.scale_text.slidetounlock span[data-nc-lang="_startTEXT"] {
  -webkit-text-fill-color: #fff !important;
  font-size: 14px;
}

.el-upload__text .top {
  font-size: 14px;
  margin-bottom: 3px;
  line-height: 19px;
}

.customer-upload .el-upload-dragger {
  height: 114px;
  border-radius: 3px;
  background: none;
}

.qfkDialog .el-input--medium .el-input__inner {
  line-height: 34px;
}

.leftContent .el-form .el-form-item .el-input__inner {
  height: 36px;
  line-height: 34px;
  border-radius: 3px;
  border: 1px solid #666666;
  background: none;
  color: #fff;
}

.leftContent .buy-code-get {
  font-size: 14px;
  color: #128BED;
  line-height: 19px;
  cursor: pointer;
  padding-right: 8px;
}

.partItem7 .rightContent {
  position: relative;
  width: 500px;
  float: left;
  height: 720px;
  margin-left: 100px;
}

.partItem7 .rightContent .footer {
  position: absolute;
  bottom: 15px;
  height: 40px;
  font-size: 12px;
  color: #999999;
  line-height: 20px;
  text-align: left;
  background: none;
}

.rightContent .top {
  margin-top: 140px;
  color: #fff;
}

.rightContent .top ul li {
  float: left;
  width: 100%;
  margin-bottom: 50px;
}

.leftIcon {
  float: left;
  font-size: 36px;
  margin-right: 20px;
  margin-top: 2px;
}

.qfkDialog .el-dialog__header {
  height: 50px;
  line-height: 50px;
  position: relative;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 20px;
  font-size: 16px;
  background: #F5F5F5;
  color: #999999;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}

.qfkDialog .el-dialog__headerbtn {
  position: absolute;
  height: 40px;
  padding: 10px;
  right: 0px;
  top: 3px;
  color: #999999;
  cursor: pointer;
  top: 6px;
  right: 6px;
}

.qfkDialog .buy-py-c-form .customer-margin-18 {
  margin-bottom: 18px !important;
}

.qfkDialog .buy-py-c-form .margin-38x {
  margin-bottom: 38px !important;
}

.qfkDialog .el-dialog__headerbtn .el-icon-close {
  float: left;
}

.qfkDialog .el-dialog__body {
  padding: 20px;
}

.qfkDialog .footerLeft {
  position: absolute;
  bottom: 20px;
  display: flex;
  -webkit-box-align: center;
  -moz-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0 0px 5px;
  min-height: 20px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.qfkDialog .el-dialog {
  width: 600px;
  margin: 0 auto;
}

.qfkDialog .el-upload {
  height: 80px;
  overflow: hidden;
}

.qfkDialog .el-upload-dragger {
  width: 430px;
  height: 80px;
}

.qfkDialog .el-icon-upload {
  color: #999999;
  display: block;
}

.qfkDialog ._footer-right {
  position: absolute;
  right: 20px;
  bottom: 20px;
}

.qfkDialog .buy-code-get {
  display: inline-block;
  color: #118BED;
  margin: 0px 15px;
  cursor: pointer;
}

.rightC .small {
  font-size: 14px;
  color: #FFFFFF;
  line-height: 19px;
  margin-bottom: 5px;
}

.rightC .font24 {
  font-size: 24px;
  color: #FFFFFF;
  line-height: 31px;
}

#slideValite .nc_wrapper,
#slideValite .errloading {
  width: 100% !important;
}

.nc-container .nc_wrapper {
  width: auto !important;
  height: 34px !important;
  background: #e8e8e8;
  position: relative;
  margin: 0;
  padding: 0;
  border-radius: 3px !important;
}

.leftContent .nc-container .nc_wrapper {
  background: none !important;
  border: 1px solid #666666 !important;
}

#slideValite.nc-container .nc_scale .nc_bg {
  width: 0px;
  height: 32px !important;
}

#slideValiteDialog.nc-container .nc_scale .nc_bg {
  width: 0px;
}

#slideValite .nc_scale {
  background: none;
}

#slideValite.nc-container .nc_scale span {
  height: 32px;
}

#slideValiteDialog.nc-container .nc_scale span {
  height: 34px;
}

#slideValiteDialog.nc-container .nc_scale .btn_slide {
  height: 34px !important;
}

#slideValite.nc-container .nc_scale .btn_slide {
  height: 32px !important;
  background: #128bed !important;
  border: none !important;
  color: #fff !important;
}

#slideValiteDialog ._nc .stage1 .button {
  border: 1px solid #ccc;
}

#slideValite ._nc .stage1 .label {
  color: #fff !important;
}

#slideValite ._nc .stage1 .button {
  text-align: center;
  width: 40px;
  height: 32px !important;
  line-height: 32px !important;
  border: none;
  position: absolute;
  left: 0;
  cursor: move;
  background: #128bed !important;
  color: #fff;
  z-index: 2;
}

.customer-margin-18 {
  position: relative;
}

.clear {
  clear: both;
}

.videoPop {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100vh;
  text-align: center;
  background: rgba(0, 0, 0, 0.6);
}

.videoPop .videoWrapper {
  width: 700px;
  height: 394px;
  margin-top: calc((100vh - 394px) / 2);
  margin-left: calc((100% - 700px) / 2);
  position: relative;
}

.videoPop .videoWrapper .trailer_screen {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  object-fit: fill;
}

.videoPop .videoWrapper .trailer_close {
  width: 32px;
  height: 32px;
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 100;
  cursor: pointer;
}

.el-upload-list {
  margin-top: -75px;
}

.el-dialog__body .el-upload-list {
  margin-top: -15px;
}

.leftContent input::-webkit-input-placeholder {
  /* WebKit browsers */
  color: #fff;
}

.leftContent input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #fff;
}

.leftContent input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #fff;
}

.leftContent input:-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: #fff;
}

.qfkDialog input::-webkit-input-placeholder {
  /* WebKit browsers */
  color: #999999;
}

.qfkDialog input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #999999;
}

.qfkDialog input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #999999;
}

.qfkDialog input:-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: #999999;
}

.el-form-item__label {
  font-weight: normal;
  padding-right: 2px !important;
  margin-bottom: 0;
}

.qfkDialog .el-dialog__header .el-dialog__title {
  font-size: 16px;
  color: #999999;
}

.logo-all-btn-try:hover {
  background: #0069BF
}

.freeBtn {
  width: 180px;
  height: 50px;
  background: #128BED;
  border-radius: 3px;
  font-size: 16px;
}

.header-content-height {
  height: 600px;
}

.swiper-pagination-clickable .swiper-pagination-bullet-active {
  background: #128bed !important;
}

.fixedLogo .logo-img {
  position: fixed;
  left: 30px;
  top: 9px;
  height: auto;
}

.leftContent .el-upload-list__item-name,
.leftContent .el-icon-document:before {
  color: #fff;
}

.fixedWidthBtn {
  width: 112px;
  height: 40px;
  background: #128BED;
  border-radius: 3px;
  font-size: 14px;
  color: #FFFFFF;
}

.youshi-content-item {
  width: auto;
  flex: 1;
}

.iconfont.font12 {
  font-size: 12px;
}

.el-message--error .el-message__content,
.el-message .el-icon-error {
  color: #F04040;
}

.el-message--error {
  background-color: #faecef;
  border-color: #f6d8de;
}

.blurTips .el-message-box__status.el-icon-info {
  color: #6bb8f0;
}

.pc-alert-theme .el-message-box__title {
  color: #a6a9ad !important;
  font-size: 16px;
}

.pc-alert-theme .el-message-box__header {
  color: #a6a9ad;
  font-size: 14px;
  background: #f5f5f5;
  line-height: 16px;
}

.buy-scan-success {
  width: 70px;
  margin: 30px auto;
}

.successTxt {
  margin-bottom: 20px;
  font-weight: bold;
  margin-top: 10px;
  font-size: 16px;
}

.pc-alert-theme {
  padding-bottom: 0;
}

.pc-alert-theme .el-icon-success {
  display: none;
}

.pc-alert-theme .buy-scan-success .el-icon-success {
  display: block;
}

.el-upload-list__item {
  transition: none !important;
}

.logo-login {
  float: left;
  margin-left: 15px;
}

.el-message-box.blurTips {
  width: 400px !important;
}

.el-message-box.blurTips .el-message-box__header {
  padding-top: 0;
}

.el-message-box.blurTips .el-message-box__message {
  padding-right: 0 !important;
}

.buy-code-get-bef {
  margin-right: 5px;
}

#slideValite.nc-container .nc_scale .scale_text2 b {
  font-size: 14px;
}

.pc-alert-theme .el-message-box__status+.el-message-box__message {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.alicode .el-form-item__label {
  line-height: 34px !important;
}

.alicode .el-form-item__label {
  line-height: 34px !important;
}
