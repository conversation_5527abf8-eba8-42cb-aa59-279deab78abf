.carousel-container-par {
  height: 740px;

}

.carousel-container-par .trial-content {
  width: 445px;
  height: 560px;
  background-color: #ffffff;
  padding: 30px 30px;
  position: relative;
  top: 130px;
  left: 60%;
}

.carousel-container-par .trial-content .form-free-text {
  color: #333333;
  text-align: center;
  font-size: 24px;
  margin-bottom: 20px;
}

.qfkwebsite .describitionContent {
  height: 100vh;
  max-height: 800px;
  min-height: 600px;
}

.yqgg-img {
  position: absolute;
  left: 17%;
  top: 190px
}

.app-swiper {
  height: calc(100% - 60px);
}

.app-swiper .el-carousel.el-carousel--horizontal {
  height: 100%;
  overflow: hidden;
}

.describitionTitle .iconfont.icon-shangbiao {
  position: absolute;
  top: 21vh;
}

.yqgg-remain-tip {
  position: absolute;
  left: 17%;
  top: 520px;
}

.yqgg-remain-tip img {
  height: 33px;
  vertical-align: bottom;
}

.yqgg-remain-tip .day {
  font-size: 48px;
  line-height: 64px;
  margin: 0 10px;
  color: #FFFD76;
}


@media screen and (max-width: 1700px) {

  .yqgg-remain-tip,
  .yqgg-img {
    left: 11%;
  }
}


/*小屏适配*/
@media screen and (max-height: 600px) {
  .carousel-container-par {
    height: 540px;
  }
}

/*@media screen and (min-height: 760px) {*/
/*  .qfkwebsite .describitionContent .newsWrapper {*/
/*    bottom: 110px;*/
/*  }*/
/*}*/

@media screen and (min-height: 800px) {
  .qfkwebsite .describitionContent .newsWrapper {
    bottom: auto;
  }
}

@media screen and (min-height: 800px) and (min-width: 1366px) {
  .qfkwebsite .describitionContent .newsWrapper {
    bottom: 0px;
  }
}

/*小屏适配*/
@media screen and (max-width: 1366px) {
  .yqgg-img {
    width: 450px !important;
  }

  .yqgg-remain-tip {
    top: 405px;
  }

  .yqgg-remain-tip img {
    height: 30px;
  }

  .yqgg-remain-tip .day {
    font-size: 40px
  }

  .carousel-container-par {
    height: 600px;
  }


  .carousel-container-par .trial-content {
    width: 430px;
    height: auto;
    padding-top: 10px;
    padding-bottom: 10px;
    top: 11vh;
  }

  .describitionTitle .iconfont.icon-shangbiao {
    top: 19vh;
  }

  .qfkwebsite .describitionContent .describitionTitle {
    padding-top: 18vh;
  }

  .carousel-container-par .trial-content .form-free-text {
    margin-bottom: 10px;
    font-size: 18px;
  }

  .carousel-container-par .trial-content .el-form-item__label {
    font-size: 12px;
    line-height: 36px;
    width: 115px !important;
  }

  .carousel-container-par .trial-content .el-form-item__content {
    margin-left: 115px !important;
  }

  .customer-margin-18 {
    margin-bottom: 10px !important;
  }

  .el-input--medium .el-input__inner {
    line-height: 34px;
    height: 34px;
  }

  .yqgg-img {
    top: calc(11vh + 50px);
  }
}
