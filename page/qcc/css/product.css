body {
  font-family: "微软雅黑", "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, Helvetica;
  font-size: 14px;
  line-height: 1.42857;
  color: #333;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  min-width: 1024px;
}

body {
  margin: 0;
}

.qfkwebsite .bannerWrapper {
  height: 450px;
  width: 100%;
  text-align: center;
  color: white;
  background-repeat: no-repeat;
}


.qfkwebsite .contentWrapper {
  width: 100%;
  text-align: center;
  background: white;
  margin-top: 80px;
  margin-bottom: 80px;
}

.qfkwebsite .contentWrapper .modelListWrapper {
  width: 1200px;
  margin: 0 auto;
  display: inline-block;
  background: #ffffff;
}

.qfkwebsite .contentWrapper .modelListWrapper .itemWrapper {
  position: relative;
  height: 350px;
  width: 100%;
  text-align: center;
}

.qfkwebsite .contentWrapper .modelListWrapper .itemWrapper .imgWrapper {
  background: #F5F5F5;
  height: 100%;
  width: 50%;
  position: absolute;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qfkwebsite .contentWrapper .modelListWrapper .itemWrapper .right.textWrapper {
  text-align: left;
}

.qfkwebsite .contentWrapper .modelListWrapper .itemWrapper .right {
  right: 0;
}

.qfkwebsite .contentWrapper .modelListWrapper .itemWrapper .textWrapper {
  height: 100%;
  width: 50%;
  position: absolute;
  top: 0;
}

.qfkwebsite .contentWrapper .modelListWrapper .itemWrapper .textWrapper .title {
  font-size: 24px;
  color: #333333;
  margin-top: 108px;
  display: inline-block;
  line-height: 31px;
}

.qfkwebsite .contentWrapper .modelListWrapper .itemWrapper .textWrapper .describition {
  margin-top: 10px;
  font-size: 14px;
  color: #666666;
  line-height: 1.8;
  display: inline-block;
  text-align: left;
}

.qfkwebsite .contentWrapper .modelListWrapper .itemWrapper .left.textWrapper {
  text-align: right;
}

.qfkwebsite .contentWrapper .modelListWrapper .itemWrapper .left {
  left: 0;
}
