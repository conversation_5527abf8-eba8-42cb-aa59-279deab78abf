body {
  font-family: "微软雅黑", "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, Helvetica;
  font-size: 14px;
  line-height: 1.42857;
  color: #333;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  min-width: 1024px;
}

body {
  margin: 0;
}

.qfkwebsite .bannerWrapper {
  height: 450px;
  width: 100%;
  text-align: center;
  color: white;
  background-repeat: no-repeat;
}

.qfkwebsite .bannerWrapper .title {
  font-size: 45px;
  display: inline-block;
  color: white;
  padding-top: 115px;
}

.qfkwebsite .bannerWrapper .line {
  display: inline-block;
  width: 50px;
  height: 2px;
  background: white;
  margin-bottom: 30px;
  margin-top: 39px;
  border-radius: 3px;
}

hr {
  border-top: none;
}

.qfkwebsite .bannerWrapper .describition {
  display: inline-block;
  line-height: 2.33em;
  font-size: 18px;
  width: 860px;
  margin: 2px 0px 0px 0px;
}

.container-fluid {
  position: relative;
}

.container,
.container-fluid {
  padding-left: 15px;
  padding-right: 15px;
}

.container,
.container-fluid {
  margin-right: auto;
  margin-left: auto;
}

.container {
  width: 100%;
}

.industry-nav {
  margin: 0px 100px 30px 65px;
}

.col-flex {
  flex: 1;
}

.nav-option {
  text-align: center;
}

.nav-option img {
  border: 1px solid;
  border-color: #D8d8d8;
  border-radius: 50%;
  padding: 5px;
}

.nav-active img,
.nav-active a {
  border-color: #118BED !important;
  color: #118BED !important;
  text-decoration: none;
}

.nav-option p a {
  padding: 10px 0px 8px 0px;
  display: inline-block;
  border-bottom: 3px solid;
  border-color: transparent;
  font-size: 18px;
  color: #3c4144;
  text-decoration: none;
}

.industry-tit {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 0px;
}

.industry-img {
  margin: 72px 0px;
  text-align: center;
}

.industry-img>img {
  width: 100%;
  max-width: 1200px;
}

.industry-describe {
  line-height: 1.8;
  margin-top: 20px;
}
