body {
  font-family: "微软雅黑", "Microsoft YaHei", "PingFang SC", "Helvetica Neue",
    Arial, Helvetica;
  font-size: 14px;
  line-height: 1.8;
  color: #333333 !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.footer-content .footer-item p {
  line-height: unset;
}

p {
  line-height: 1.8;
}

body {
  min-width: 1024px;
}

body {
  margin: 0;
}

.container-fluid {
  position: relative;
}

.container {
  width: 100%;
}

.row-flex {
  display: flex;
}

li {
  list-style: none;
}

.menu {
  width: 162px;
  display: inline-block;
}

.menu ul {
  list-style: none;
  width: 100%;
  padding: 0;
  border: 1px solid #EEEEEE;
  font-size: 14px;
  background: #ffffff;
}

.menu ul li {
  height: 40px;
  line-height: 40px;
  padding-left: 15px;
  cursor: pointer;
}

.active {
  background: #128BED;
  color: #FFFFFF !important;
}

.nav-menu {
  color: #333333;
}

.nav-menu-open {
  color: #333333;
}

.subMenu,
.subMenu-2 {
  color: #666666;
}

.isclose {
  display: none;
}

.content {
  width: 1020px;
  border: 1px solid #EEEEEE;
  display: inline-block;
  margin-left: 11px;
  vertical-align: top;
  background: #ffffff;
}

.content-disappear {
  display: none;
}

.content-tit {
  border-bottom: 1px solid #eeeeee;
  height: 45px;
  padding: 13px 0px 13px 10px;
  background: #fcfcfc;
}

.content-tit h3 {
  font-size: 14px;
  margin: 0px;
  font-weight: 500;
  color: #333333;
  display: inline-block;
}

.content-tit span {
  display: inline-block;
  width: 232px;
  height: 19px;
  margin-right: 10px;
  float: right;
  margin-top: -3px;
}

.content-tit span img {
  width: 100%;
}

.linkQiChaCha {
  color: #128bed !important;
  cursor: pointer;
}

.linkQiChaCha:hover {
  color: #0069BF !important;
  text-decoration: none;
}

.content-body {
  padding: 15px 10px 0px 10px;
}

.about-ui {
  width: 100%;
  height: 100%;
  padding: 0px;
}

.about-li {
  width: 100%;
  font-size: 14px;
  color: #333333;
  margin-top: 10px;
}

.circle {
  width: 6px;
  height: 6px;
  border-radius: 6px;
  background: #BBBBBB;
  margin-top: 6px;
  vertical-align: top;
}

.about-title {
  font-weight: bold;
  ;
}

.left-title {
  border-left: 3px solid #128bed;
  padding-left: 5px;
  color: #666666;
  font-size: 16px;
}

.about-tabel {
  border: 1px solid #eeeeee;
  width: 100%;
  font-size: 13px !important;
}

.data-tabel {
  border: 1px solid #eeeeee;
  width: 100%;
  font-size: 12px !important;
}

.about-tabel th,
.data-tabel th {
  border: 1px solid #eeeeee;
}

.about-tabel td,
.data-tabel td {
  border: 1px solid #eeeeee;
}

.header-tr {
  height: 32px;
  background-color: #F7F7FA;
}

.header-tr th {
  color: #333333;
  padding-left: 11px;
}

.origin-tr {
  min-height: 32px;
  color: #666666;
}

.origin-tr td {
  color: #666666;
  padding: 5px 0px 5px 11px;
}

.contact {
  padding: 20px 10px;
  display: flex;
}

.contact-way {
  display: inline-block;
  margin-left: 20px;
  color: #333333 !important;
}

.contact-way p {
  margin: 0;
}

.show {
  display: block !important;
}

.newListWrapper {
  text-align: center;
}

.newsItem {
  display: block;
  cursor: pointer;
  text-align: left;
  color: #333333;
  padding: 15px 10px;
  border-bottom: 1px solid #eeeeee;
}

a {
  text-decoration: none;
}

.newsItem:focus,
.newsItem:hover,
.newsItem:hover .newsDate {
  color: #128bed;
  text-decoration: none;
}

.question {
  padding: 15px 10px;
}

.question a {
  cursor: pointer;
  color: #333333;
  text-decoration: none;
  font-size: 14px !important;
}

.question a:hover {
  color: #128BED !important;
  text-decoration: none !important;
}

.question a:focus {
  color: #333333 !important;
  text-decoration: none !important;
}

.answer {
  font-size: 14px;
  color: #999999;
  text-align: justify;
  line-height: 22px;
  padding-left: 20px;
  padding-bottom: 15px;
}

.panel {
  border-bottom: 1px solid #EEEEEE !important;
  box-shadow: none !important;
}

.panel-group .panel+.panel {
  margin-top: 0px !important;
}

.benefit-body {
  width: calc((100% - 50px) / 3);
  height: 250px;
  display: inline-block;
  padding: 20px 20px 0px 20px;
  vertical-align: top;
}

.benefit-body.half {
  width: calc((100% - 50px) / 2);
  height: 185px;
}

.benefit-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
}

.benefit-item {
  margin-top: 10px;
}

.benefit-func {
  width: calc((100% - 60px) / 5);
  height: 210px;
  background: #F3F9FE;
  display: inline-block;
  vertical-align: top;
}

.noFrist {
  margin-left: 10px;
}

.func-title {
  padding: 15px 0px 15px 10px;
  background: #DCEDFC;
}

.func-content {
  padding: 15px 5px 0px 10px;
}

.videoPop {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100vh;
  text-align: center;
  background: rgba(0, 0, 0, 0.6);
}

.videoPop .videoWrapper {
  width: 700px;
  height: 394px;
  margin-top: calc((100vh - 394px) / 2);
  margin-left: calc((100% - 700px) / 2);
  position: relative;
}

.videoPop .videoWrapper .trailer_screen {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  object-fit: fill;
}

video {
  display: inline-block;
  vertical-align: baseline;
}

.videoPop .videoWrapper .trailer_close {
  width: 32px;
  height: 32px;
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 100;
}

.data-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10px;
}

.content-disappear-235 {
  font-size: 14px;
  padding: 15px 10px;
}

.content-title-fontsize {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.content-title-fontsize2 {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10px;
}

.content-source {
  font-size: 14px;
  color: #128bed;
}

.margin-top-l-16 {
  display: inline-block;
  margin-left: 16px;
  margin-top: -20px;
}

@media screen and (max-width: 1366px) {
  .menu ul {
    font-size: 12px;
  }

  .content {
    font-size: 12px;
  }

  .question a {
    font-size: 12px !important;
  }

  .content-disappear-235 {
    font-size: 12px;
  }

  .content-title-fontsize,
  .content-title-fontsize2 {
    font-size: 14px;
  }

  .about-li {
    font-size: 12px;
  }

  .left-title {
    font-size: 14px;
  }

  .el-button {
    font-size: 12px;
    padding: 6px 10px;
  }

  .content-source {
    font-size: 12px;
    color: #128bed;
  }

  .about-tabel {
    font-size: 12px !important;
  }

  .margin-top-l-16 {
    display: inline-block;
    margin-left: 5px;
    margin-top: -20px;
  }

  .answer {
    font-size: 12px;
  }
}
