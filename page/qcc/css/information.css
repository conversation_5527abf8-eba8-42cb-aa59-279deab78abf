body {
  font-family: "微软雅黑", "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, Helvetica;
  font-size: 14px;
  line-height: 1.8;
  color: #333;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  min-width: 1024px;
}

body {
  margin: 0;
}

.qfkwebsite {
  position: relative;
  width: 100%;
  font-size: 12px;
}

img {
  vertical-align: middle;
}

a {
  color: #337ab7;
  text-decoration: none;
  background-color: transparent;
}

.top-header.blackTheme {
  border-color: rgba(48, 49, 51, 0.1);
  background: white;
}

.top-header {
  position: fixed;
  z-index: 10;
  left: 0;
  top: 0;
  height: 50px;
  line-height: 50px;
  box-sizing: content-box;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  font-size: 14px;
}

.top-header.blackTheme .logo-wrapper {
  border-color: rgba(48, 49, 51, 0.1);
}

.top-header .logo-wrapper {
  display: inline-block;
  width: auto;
  padding: 0px 15px 0px 30px;
  box-sizing: border-box;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.top-header.blackTheme .header-operate-wrapper .item.actived {
  color: white;
}

.top-header.blackTheme .header-operate-wrapper .item:hover {
  color: white;
}

.top-header .header-operate-wrapper .item:hover {
  background: #219CFF;
}

.top-header.blackTheme .header-operate-wrapper {
  color: #333333;
}

.top-header .header-operate-wrapper {
  display: inline-block;
  width: auto;
  margin-left: -4px;
  box-sizing: border-box;
  color: white;
}

.top-header .header-operate-wrapper .item.actived {
  background: #128BED;
}

.top-header .header-operate-wrapper .item {
  display: inline-block;
  height: 100%;
  padding: 0px 11px 0px 15px;
  cursor: pointer;
}

.top-header.blackTheme .loginbtn-wrapper {
  border-color: rgba(48, 49, 51, 0.1);
}

.top-header .loginbtn-wrapper {
  position: absolute;
  right: 0;
  top: 0;
  display: inline-block;
  width: auto;
  padding: 0px 30px 0px 30px;
  box-sizing: border-box;

  display: flex;
  align-items: center;
}

.top-header .loginbtn-wrapper .__qfk-login-btn {
  max-height: 50px;
}


.top-header .loginbtn-wrapper .button {
  height: 30px;
  width: 84px;
}

.el-button+.el-button {
  margin-left: 10px;
}

.el-button--mini,
.el-button--small {
  font-size: 12px;
  border-radius: 3px;
}

.el-button--primary:focus,
.el-button--primary:hover {
  background: #66b1ff;
  border-color: #66b1ff;
  color: #fff;
}

.top-header .loginbtn-wrapper .button.login {
  color: #128bed;
}

.top-header .loginbtn-wrapper .button.login:hover {
  color: #fff;
  background-color: #128bed;
  border-color: #128bed;
}

.el-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dcdfe6;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  transition: .1s;
  padding: 12px 20px;
  font-weight: 500;
  border-radius: 4px;
}

.el-button--primary {
  color: #fff;
  background-color: #409EFF;
  border-color: #409EFF;
}

.el-button--mini,
.el-button--small {
  font-size: 12px;
  border-radius: 3px;
}

/*-------------------------头部结束-----------------------------*/

.qfkwebsite .contentWrapperAuto {
  padding: 80px 0;
  background: white;
}


b,
optgroup,
strong {
  font-weight: 700;
}

.pageClass button:hover {
  color: #409EFF;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}

a {
  cursor: pointer;
}

a {
  color: #3c4144;
  text-decoration: none;
}

a:hover {
  color: #181a1c;
  text-decoration: none;
  outline: none;
}

h2 {
  font-size: 30px;
}

h2 {
  margin-top: 20px;
  margin-bottom: 10px;
}

h2 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: inherit;
}

table {
  background-color: transparent;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

/*-----------------尾部--------------------------*/

.footer-nav {
  background: #333333 !important;
  padding: 0 120px;
}

.container_fluid.footer-nav .footer-bd {
  max-width: 1250px;
}

.footer-bd {
  margin: 0 auto;
  color: #aaa;
  font-size: 14px;
}

.row-flex {
  display: flex;
}

.row-flex {
  display: flex;
}

.footer-bd-t {
  padding: 34px 0 !important;
  padding-bottom: 20px !important;
}

.col-flex {
  flex: 1;
}

.footer-nav-l {
  padding-right: 70px;
}

.footer-h3 {
  color: #fff;
  font-size: 18px;
  line-height: 24px;
  margin-bottom: 20px;
  font-weight: 500;
}

.container_fluid.footer-nav .footer-nav-l .footer-h3 .title {
  color: white;
  text-decoration: none;
}

.footer-nav a:first-child {
  margin-left: 0;
}

.footer-bd .footer-nav-l .firstP {
  margin-top: 30px;
}

.footer-nav-l>p {
  line-height: 24px;
}

.footer-bd .footer-nav-l p {
  margin-top: 18px;
}

.footer-bd-b {
  border-top: 1px solid #2D3138;
  font-size: 12px;
  color: #aaa;
  text-align: center;
  padding: 20px 0;
}

.footer-bd-b {
  border-top: 1.1px solid #666666 !important;
  padding: 24px 0;
  max-width: 1250px;
  margin: 0 auto;
}

#friendListId {
  text-align: left;
  line-height: 18px;
  font-size: 12px;
}

.fotter-bg-content {
  display: inline-block;
  width: calc(100% - 65px);
  text-align: left;
  line-height: 18px;
  font-size: 12px;
  padding-left: 0px;
}

.fotter-bg-title {
  display: inline-block;
  width: 60px;
  line-height: 18px;
  font-size: 12px;
}

.footer-nav a:first-child {
  margin-left: 0;
}

.fotter-bg-content .notLink {
  cursor: default;
}

.footer-nav a {
  color: #999999;
}

.fotter-bg-content a {
  // text-underline: none;
  text-decoration: none;
  cursor: pointer;
  margin-left: 5px;
}

.fotter-bg-content em {
  border-right: 1px solid #999999;
  height: 12px;
  margin-left: 5px;
  display: inline-block;
  vertical-align: middle;
}

.footer-bottom {
  height: 48px !important;
  background: #212121;
  font-size: 12px;
  color: #999999;
  text-align: center;
  line-height: 18px;
}

.footer-bottom span {
  line-height: 48px !important;
}

.footer-bottom a {
  color: #999999;
  text-decoration: none;
  cursor: pointer;
  margin-left: 5px;
}

.footer-bottom a:hover {
  color: white;
  text-decoration: none;
}

.footer-bottom a:focus {
  color: #999999;
  text-decoration: none;
}

.container_fluid.footer-nav .footer-nav-l p>a:hover {
  color: white;
  text-decoration: none;
}

.newsItem {
  color: #3c4144 !important;
}

.friendsItem:hover,
.notLink:hover {
  color: white;
  text-decoration: none;
}
