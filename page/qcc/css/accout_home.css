 *,
 body,
 h1,
 h2,
 h3,
 h4,
 h5,
 div,
 span,
 ul,
 li,
 p {
   margin: 0;
   padding: 0;
 }

 *,
 body {
   font-family: "微软雅黑",
     "Microsoft YaHei",
     "PingFang SC",
     "Helvetica Neue",
     Arial,
     Helvetica;
 }

 .content-width {
   width: 1200px !important;
 }

 .newAccount .header-operate-wrapper {
   position: absolute;
   right: 266px;
   text-align: right;
   top: 0;
 }

 .newAccount .logo-login {
   border-radius: 5px;
   margin-left: 15px;
   float: left;
 }

 .newAccount .logo-img {
   height: auto;
 }

 .rno-content {
   margin-top: 0;
   height: auto;
 }

 .top-header-1 .header-operate-wrapper .item.actived {
   border-bottom: 2px solid #128bed;
   background: none !important;
   color: #128bed;
 }

 .scroll-header .top-header-1 .header-operate-wrapper .item.actived {
   color: #333333 !important;
 }

 .top-header-1 .header-operate-wrapper .item:hover {
   color: #128bed !important;
   background: none;
 }

 .logo-all-btn {
   background: #FFFFFF;
   color: #333333;
 }

 .logo-all-btn:hover {
   background: none;
   background: #FFFFFF;
   border: 1px solid #128bed !important;
   color: #128bed !important;
 }

 .accountDialog .el-dialog__header {
   height: 50px;
   line-height: 50px;
   position: relative;
   -webkit-box-sizing: border-box;
   box-sizing: border-box;
   padding: 0 20px;
   font-size: 16px;
   background: #F5F5F5;
   color: #999999;
   border-top-left-radius: 3px;
   border-top-right-radius: 3px;
 }

 .accountDialog .el-dialog__header .el-dialog__title {
   font-size: 16px;
   color: #999999;
 }

 .accountDialog .el-dialog__headerbtn {
   position: absolute;
   height: 40px;
   padding: 10px;
   right: 0px;
   top: 3px;
   color: #999999;
   cursor: pointer;
   top: 6px;
   right: 6px;
 }

 .accountDialog .buy-py-c-form .customer-margin-18 {
   margin-bottom: 18px !important;
 }

 .accountDialog .buy-py-c-form .margin-38x {
   margin-bottom: 38px !important;
 }

 .accountDialog .el-dialog__headerbtn .el-icon-close {
   float: left;
 }

 .kfDialog .el-dialog__headerbtn {
   top: 15px;
 }

 .accountDialog .el-dialog__body {
   padding: 20px;
 }

 .accountDialog .footerLeft {
   position: absolute;
   bottom: 20px;
   display: flex;
   -webkit-box-align: center;
   -moz-align-items: center;
   -ms-flex-align: center;
   align-items: center;
   padding: 0 0px 5px;
   min-height: 20px;
   -webkit-box-sizing: border-box;
   box-sizing: border-box;
 }

 .accountDialog .el-dialog {
   width: 600px;
   margin: 0 auto;
 }

 .accountDialog .el-upload {
   height: 80px;
   overflow: hidden;
 }

 .accountDialog .el-upload-dragger {
   width: 430px;
   height: 80px;
 }

 .accountDialog .el-icon-upload {
   height: 50px;
   color: #999999;
   display: block;
 }

 .accountDialog ._footer-right {
   position: absolute;
   right: 20px;
   bottom: 20px;
 }

 .accountDialog .buy-code-get {
   display: inline-block;
   color: #118BED;
   margin: 0px 15px;
   cursor: pointer;
 }

 #slideValite .nc-container.nc_wrapper,
 #slideValite .nc-container.errloading {
   width: 100% !important;
 }

 .nc-container #nc_1_wrapper {
   width: auto !important;
   height: 34px !important;
   background: #e8e8e8;
   position: relative;
   margin: 0;
   padding: 0;
 }

 .nc-container .nc_scale .nc_bg {
   position: absolute;
   height: 100%;
   _height: 34px;
   left: 0;
   width: 0px;
 }

 .nc-container .nc_scale span {
   height: 34px !important;
   line-height: 34px !important;
 }

 #slideValite.nc-container .nc_scale span {
   height: 32px;
 }

 .nc-container .nc_scale .scale_text {
   width: 100%;
   height: 100%;
   text-align: center;
   position: absolute;
   z-index: 1;
   background: transparent;
   color: #9c9c9c;
   line-height: 34px;
   font-size: 12px;
   cursor: pointer;
 }

 #slideValite.nc-container .nc_scale span {
   height: 34px;
 }

 .nc-container .nc_scale .clickCaptcha {
   position: absolute;
   left: 0;
   top: 35px;
   height: 270px;
   background: #fff;
   display: none;
 }

 .el-upload-list {
   margin-top: -15px;
 }

 .header-content-title {
   margin-top: -60px;
   font-weight: bold;
 }

 .swiper-pagination {
   padding-top: 30px;
 }

 .odd-body-bg {
   padding-bottom: 118px !important;
 }

 .top-header-1 .header-operate-wrapper .item.actived {
   font-weight: bold;
   background: none !important;
   border-bottom: 2px solid #128bed !important;
 }

 .scroll-header .top-header-1 .header-operate-wrapper .item.actived {
   color: #128bed !important
 }

 .el-form-item__label {
   font-weight: normal;
   padding-right: 2px !important;
   margin-bottom: 0;
 }

 .rno-serive-s-n {
   width: 253px;
 }

 .accountDialog input::-webkit-input-placeholder {
   /* WebKit browsers */
   color: #999999;
 }

 .accountDialog input:-moz-placeholder {
   /* Mozilla Firefox 4 to 18 */
   color: #999999;
 }

 .accountDialog input::-moz-placeholder {
   /* Mozilla Firefox 19+ */
   color: #999999;
 }

 .accountDialog input:-ms-input-placeholder {
   /* Internet Explorer 10+ */
   color: #999999;
 }

 .logo-all-btn-try:hover {
   background: #0069BF
 }

 .header-content-title-2 {
   width: 800px;
 }

 .fixedLogo .logo-img {
   position: fixed;
   left: 30px;
   top: 10px;
 }

 .youshi-item {
   flex: 1;
 }

 .top-header-1 {
   position: relative;
   width: 100%;
 }

 .logo-btn-wrap {
   margin-right: 30px;
   margin-top: 9px;
   margin-right: 30px;
   margin-bottom: 9px;
   height: 30px;
 }

 .iconfont.font12 {
   font-size: 12px;
 }

 .el-message--error .el-message__content,
 .el-message .el-icon-error {
   color: #F04040;
 }

 .el-message--error {
   background-color: #faecef;
   border-color: #f6d8de;
 }

 .blurTips .el-message-box__status.el-icon-info {
   color: #6bb8f0;
 }

 .el-message-box.blurTips {
   width: 400px !important;
   padding-bottom: 20px;
 }

 .el-message-box.blurTips .el-message-box__btns {
   padding: 10px 20px 0;
 }

 .el-message-box.blurTips .el-message-box__header {
   padding-top: 0;
 }

 .el-message-box.blurTips .el-message-box__message {
   padding-right: 0 !important;
 }

 .pc-alert-theme .el-message-box__header {
   color: #a6a9ad;
   font-size: 14px;
   background: #f5f5f5;
   line-height: 16px;
 }

 .buy-scan-success {
   width: 70px;
   margin: 30px auto;
 }

 .buy-scan-success {
   width: 70px;
   margin: 40px auto;
 }

 .successTxt {
   margin-bottom: 20px;
   font-weight: bold;
   margin-top: 10px;
   font-size: 16px;
 }

 .pc-alert-theme {
   padding-bottom: 0;
 }

 .pc-alert-theme .el-icon-success {
   display: none;
 }

 .pc-alert-theme .buy-scan-success .el-icon-success {
   display: block;
 }

 .el-upload-list__item {
   transition: none !important;
 }

 #consulation {
   position: fixed;
   bottom: 60px;
   right: -2px;
   height: 313px;
   line-height: normal;
 }

 #consulation .nav {
   float: right;
   height: auto;
   width: 33px;
   line-height: 20px;
   margin-top: 94px;
   padding: 15px 0px 11px 10px;
   background: #128BED;
   color: #FFFFFF;
   font-size: 14px;
   border-radius: 8px 0 0 8px;
   cursor: pointer;
   vertical-align: middle;
   box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.1);
 }

 .kfDialog .el-dialog {
   width: 450px;
   height: 350px;
   background: #FFFFFF;
   box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.2);
   border-radius: 3px;
 }

 .kfDialog .el-dialog__header {
   width: 450px;
   height: 50px;
   background: #F5F5F5;
   border-radius: 3px 3px 0px 0px;
   padding: 15px 20px 15px;
 }

 .kfDialog .el-dialog__body {
   padding: 0 20px;
   padding-top: 19px;
 }

 .kfDialog .el-dialog__header .el-dialog__title {
   font-size: 16px;
   color: #999999;
   line-height: 21px;
 }

 .kfDialog .buy-title {
   height: 21px;
   font-size: 14px;
   color: #333333;
   line-height: 21px;
   text-align: left;
   margin-bottom: 20px;
 }

 .kfDialog .concat-content {
   display: inline-block;
   margin-left: 15px;
   padding-top: 20px;
   vertical-align: top;
 }

 .person-name {
   font-size: 24px;
   font-weight: bold;
   color: #333333;
 }

 .concat-title {
   margin-top: 5px;
   line-height: 16px;
   font-size: 12px;
   color: #999999;
   margin-bottom: 20px;
 }

 .concat-phone,
 .concat-email {
   font-size: 14px;
   margin-bottom: 10px;
   line-height: 19px;
   color: #333333;
 }

 .kf-footer {
   padding-bottom: 20px;
   text-align: center !important;
   padding-top: 20px !important;
 }

 .kf-footer button {
   color: #ffffff !important;
   background-color: #128bed !important;
   border-color: #128bed !important;
 }

 .kf-footer button {
   color: #ffffff !important;
   background-color: #128bed !important;
   border-color: #128bed !important;
 }

 .nc-container .nc_wrapper {
   width: 100% !important;
 }

 .rno-content-item {
   width: 393px;
   margin-right: 10px;
   margin-left: 0;
   float: left;
   height: 155px;
   margin-bottom: 10px;
 }

 .rno-content-item:nth-child(3n) {
   margin-right: 0px;
 }

 .rno-content-item-child-c {
   padding-bottom: 0px;
   padding-left: 0px;
 }

 .swiper-button-cc2 {
   width: 1090px;
 }

 .swiper-button-prev,
 .swiper-button-next {
   background-size: 84px 84px !important;
 }

 .pc-alert-theme .el-message-box__status+.el-message-box__message {
   padding-left: 0px !important;
   padding-right: 0px !important;

 }

 .footer-bottom {
   position: absolute;
   background: #242424;
   bottom: -1px;
 }

 button,
 button:focus,
 .btn {
   outline: none !important;
 }

 #appHome {
   position: relative;
 }

 .alicode .el-form-item__label {
   line-height: 34px !important;
 }
