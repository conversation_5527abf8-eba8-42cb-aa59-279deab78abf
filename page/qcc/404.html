<!DOCTYPE html>
<html >
<head>
  <style>
    html,
    body {
      height: 100%;
      margin: 0;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      font-family: "微软雅黑", "Microsoft YaHei", "PingFangSC-Regular", "PingFangSC", "HiraginoSansGB-W3", "HiraginoSansGB", "HelveticaNeue", "Helvetica", "Arial", "ArialMT";
    }

    .result-page {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -ms-flex-direction: column;
      flex-direction: column;
      width: 100%;
      height: 100%;
      background: #fff;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
    }

    .result-page__image {
      margin-top: 20vh;
      margin-bottom: 20px;
      width: 240px;
      height: 180px;
    }

    .result-page__image img {
      width: 100%;
      height: 100%;
    }

    .result-page__title {
      color: #999999;
      font-size: 14px;
      line-height: 22px;
    }

    .result-page__extra {
      margin-top: 30px;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
    }

    .result-page__extra .el-button {
      border-radius: 2px;
      width: 94px;
      height: 32px;
      padding: 0 12px;
    }

    .el-button {
      display: inline-block;
      line-height: 1;
      white-space: nowrap;
      color: #ffffff;
      background-color: #128bed;

      cursor: pointer;
      border: 1px solid #128bed;
      -webkit-appearance: none;
      text-align: center;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      outline: none;
      margin: 0;
      -webkit-transition: 0.1s;
      -o-transition: 0.1s;
      transition: 0.1s;
      font-weight: 500;
      -moz-user-select: none;
      -webkit-user-select: none;
      -ms-user-select: none;
      padding: 12px 20px;
      font-size: 14px;
      border-radius: 4px;
    }

    .el-button:hover,
    .el-button:focus {
      background: #41a2f1;
      border-color: #41a2f1;
      color: #ffffff;
    }

    .el-button--primary {
      color: #FFFFFF;
      background-color: #128bed;
      border-color: #128bed;
    }

    .el-button--primary:hover {
      background: #0069BF;
      border-color: #0069BF;
    }

    .el-button--normal, .el-button--normal:focus {
      color: #333333;
      border-color: #D8d8d8;
      background-color: white;
    }

    .el-button--normal:hover, .el-button--normal:focus:hover {
      border-color: #128bed;
      background-color: white;
      color: #128bed;
    }

    .el-button + .el-button {
      margin-left: 10px;
    }
  </style>
</head>

<body>
<div class="result-page result-page-404">
  <div class="result-page__image">
    <img src="data:image/png;base64,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" alt="404">
  </div>
  <div class="result-page__title">
    您所访问的页面不存在
  </div>
  <div class="result-page__extra">
    <button class="el-button el-button--primary" onclick="back()">返回上一页</button>
    <button class="el-button el-button--normal" onclick="home()">返回首页</button>
  </div>
</div>
<script>
  function home() {
    window.location.href = "/";
  }

  function back() {
    if (
      navigator.userAgent.indexOf("MSIE") >= 0 &&
      navigator.userAgent.indexOf("Opera") < 0
    ) {
      // IE
      if (history.length > 0) {
        window.history.go(-1);
      } else {
        window.location.href = "/";
      }
    } else {
      //非IE浏览器
      if (
        navigator.userAgent.indexOf("Firefox") >= 0 ||
        navigator.userAgent.indexOf("Opera") >= 0 ||
        navigator.userAgent.indexOf("Safari") >= 0 ||
        navigator.userAgent.indexOf("Chrome") >= 0 ||
        navigator.userAgent.indexOf("WebKit") >= 0
      ) {
        if (window.history.length > 2) {
          window.history.go(-1);
        } else {
          window.location.href = "/";
        }
      } else {
        //未知的浏览器
        window.history.go(-1);
      }
    }
  }
</script>
</body>
</html>
