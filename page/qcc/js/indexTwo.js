function hideFooter() {
  $('#footerFloatWrapperId').hide()
}

$(document).ready(function() {
  $(window).scroll(function() {
    var scrollHight = $(window).scrollTop()
    if (scrollHight > 50) {
      $('#backToTop').show()
    } else {
      $('#backToTop').hide()
    }
  })

  var t
  $('#cslt-nav').hover(function() {
    $('#cslt-main').animate({
      width: '202px'
    }, 600)
    $('#consulation').css({
      'z-index': 1000010
    })
    clearTimeout(t)
  }, function() {
    t = setTimeout(function(e) {
      $('#cslt-main').animate({
        width: '0px',
        'z-index': 999999
      }, 600, function() {
        $('#consulation').css({
          'z-index': 999999
        })
      })
      $('#wechat-detail').hide()
      $('#program-detail').hide()
    }, 1000)
  })

  $('#cslt-main').hover(function() {
    $('#cslt-main').animate({
      width: '202px',
      'z-index': 1000010
    }, 600)
    $('#consulation').css({
      'z-index': 1000010
    })
    clearTimeout(t)
  }, function() {
    t = setTimeout(function(e) {
      $('#cslt-main').animate({
        width: '0px',
        'z-index': 999999
      }, 600, function() {
        $('#consulation').css({
          'z-index': 999999
        })
      })
      $('#wechat-detail').hide()
      $('#program-detail').hide()
    }, 1000)
  })

  $('#wechat-info').click(function() {
    $('#wechat-detail').show()
  })

  $('#wechat-detail').click(function() {
    $('#wechat-detail').hide()
  })

  $('#wechat-program').click(function() {
    $('#program-detail').show()
  })

  $('#program-detail').click(function() {
    $('#program-detail').hide()
  })
})

function scrollToTop() {
  window.scrollTo(0, 0)
}

function isBlank(val) {
  return val === null || val === ''
}

function addSuggestion() {
  const name = $('#name').val()
  const contact = $('#mobile').val()
  const content = $('#content').val()

  if (isBlank(contact)) {
    new Vue().$message.warning('请输入联系方式')
    return
  }

  if (isBlank(content)) {
    new Vue().$message.warning('请输入反馈内容')
    return
  }

  const regexs = {
    email: /^[a-zA-Z0-9]+([-_.][A-Za-z0-9]+)*@([a-zA-Z0-9]+[-.])+[A-Za-z0-9]{2,5}$/, // eslint-disable-line
    mobile: /^1[2|3|4|5|6|7|8|9]\d{9}$/
  }

  if (!isBlank(contact) && !regexs.email.test(contact) && !regexs.mobile.test(contact)) {
    new Vue().$message.warning('联系方式的格式不正确，请输入手机号码或者邮箱')
    return
  }

  postVue('/webapi/open/feedback/save', {
    dataEntity: {
      name: name,
      mobile: contact,
      content: content
    }
  }, function(response) {
    if (response.status === '200') {
      $('#suggestionModal').modal('hide')
      new Vue().$message.success('反馈提交成功')
    } else {
      new Vue().$message.error(response.msg)
    }
  })
}
