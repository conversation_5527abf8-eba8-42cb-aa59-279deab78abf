Vue.component('app-phone', {
  template: `<span class="app-phone" @mouseenter="phoneMover" @mouseleave="phoneLeave">
  <span class="iconfont icon-shouji2"></span>
  <div class="phone-qrcode" v-if="isShowPhone">
    <img src="/img/ipone-code.png?v=3" alt="">
  </div>
</span>`,
  data: function() {
    return {
      isShowPhone: false
    }
  },
  methods: {
    phoneMover() {
      this.isShowPhone = true
    },
    phoneLeave() {
      this.isShowPhone = false
    }
  }
})
