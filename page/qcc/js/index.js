function scroll(currentY, targetY) {
  // 计算需要移动的距离
  var needScrollTop = targetY - currentY
  var _currentY = currentY
  setTimeout(function() {
    // 一次调用滑动帧数，每次调用会不一样
    var dist = Math.ceil(needScrollTop / 10)
    _currentY += dist
    window.scrollTo(_currentY, currentY)
    // 如果移动幅度小于十个像素，直接移动，否则递归调用，实现动画效果
    if (needScrollTop > 10 || needScrollTop < -10) {
      scroll(_currentY, targetY)
    } else {
      window.scrollTo(_currentY, targetY)
    }
  }, 1)
}

function hideFooter() {
  $('#footerFloatWrapperId').hide()
}
var logoUrl1 = '/img/logo_pc.png?v=3'
var logoUrl2 = '/img/logo_pc2.png?v=3'

function scrollA() {
  var scrollHight = $(window).scrollTop()
  if (scrollHight > 50) {
    $('.top-header').addClass('blackTheme')
    $('.btn-buy2').css('color', '#333333')
    $('.logo-wrapper img').attr('src', logoUrl2)
    $('#backToTop').show()
  } else {
    $('.top-header').removeClass('blackTheme')
    $('.btn-buy2').css('color', '#ffffff')
    $('.logo-wrapper img').attr('src', logoUrl1)
    $('.isDetail img').attr('src', logoUrl2)
    $('#backToTop').hide()
  }
}

$(document).ready(function() {
  scrollA()
  $(window).scroll(function() {
    scrollA()
  })

  var t
  $('#cslt-nav').hover(function() {
    $('#cslt-main').animate({
      width: '202px'
    }, 600)
    $('#consulation').css({
      'z-index': 1000010
    })
    clearTimeout(t)
  }, function() {
    t = setTimeout(function(e) {
      $('#cslt-main').animate({
        width: '0px',
        'z-index': 999999
      }, 600, function() {
        $('#consulation').css({
          'z-index': 999999
        })
      })
      $('#wechat-detail').hide()
      $('#program-detail').hide()
    }, 1000)
  })
  $('#cslt-main').hover(function() {
    $('#cslt-main').animate({
      width: '202px',
      'z-index': 1000010
    }, 600)
    $('#consulation').css({
      'z-index': 1000010
    })
    clearTimeout(t)
  }, function() {
    t = setTimeout(function(e) {
      $('#cslt-main').animate({
        width: '0px',
        'z-index': 999999
      }, 600, function() {
        $('#consulation').css({
          'z-index': 999999
        })
      })
      $('#wechat-detail').hide()
      $('#program-detail').hide()
    }, 1000)
  })

  $('#wechat-info').click(function() {
    $('#wechat-detail').show()
  })

  $('#wechat-detail').click(function() {
    $('#wechat-detail').hide()
  })

  $('#wechat-program').click(function() {
    $('#program-detail').show()
  })

  $('#program-detail').click(function() {
    $('#program-detail').hide()
  })
})

function scrollToTop() {
  window.scrollTo(0, 0)
}

function isBlank(val) {
  return val === null || val === ''
}

function addSuggestion() {
  const name = $('#name').val()
  const contact = $('#mobile').val()
  const content = $('#content').val()

  if (isBlank(contact)) {
    new Vue().$message.warning('请输入联系方式')
    return
  }

  if (isBlank(content)) {
    new Vue().$message.warning('请输入反馈内容')
    return
  }

  const regexs = {
    email: /^[a-zA-Z0-9]+([-_.][A-Za-z0-9]+)*@([a-zA-Z0-9]+[-.])+[A-Za-z0-9]{2,5}$/, // eslint-disable-line
    mobile: /^1[2|3|4|5|6|7|8|9]\d{9}$/
  }

  if (!isBlank(contact) && !regexs.email.test(contact) && !regexs.mobile.test(contact)) {
    new Vue().$message.warning('联系方式的格式不正确，请输入手机号码或者邮箱')
    return
  }

  postVue('/webapi/open/feedback/save', {
    dataEntity: {
      name: name,
      mobile: contact,
      content: content
    }
  }, function(response) {
    if (response.status === '200') {
      $('#suggestionModal').modal('hide')
      new Vue().$message.success('反馈提交成功')
    } else {
      new Vue().$message.error(response.msg)
    }
  })
}

function setLocalStorge(catorgate, val) {
  localStorage.setItem(catorgate, val)
}

function getLocalStorge(catorgate) {
  localStorage.getItem(catorgate)
}
