var TIME_COUNT = 60
var regexs = {
  email: /^[a-zA-Z0-9]+([-_.][A-Za-z0-9]+)*@([a-zA-Z0-9]+[-.])+[A-Za-z0-9]{2,5}$/, // eslint-disable-line
  mobile: /^1[2|3|4|5|6|7|8|9]\d{9}$/
}

var applygmixins = {

  data: function() {
    return {
      mySwiper: null,
      isFixed: false,
      serviceTab: 0,
      drawer: false,
      detailDialog: false,
      getCodeStatus: true,
      kfDialog: false,
      getCodeSecond: 0,
      myFileList: [],
      imgUrl: false,
      infoDataF: { // 弹框内申请
        name: '',
        phone: '',
        email: '',
        companyName: '',
        phoneCode: '',
        uploadimage: '',
        businessLicenseImg: '',
        csessionid: '',
        sig: '',
        token: '',
        scene: 'register',
        address: ''
      },
      fileSize1: '',
      fileSize: '',
      timer: null,
      submitDisabled: false,
      rules: {
        companyName: [{
          required: true,
          validator: function(rule, value, callback) {
            if (value === undefined || value === null || value === '') {
              return callback(new Error('公司名称不能全为空'))
            }
            var emptyVal = value.replace(/ /g, '')
            if (emptyVal === '') {
              return callback(new Error('公司名称不能全为空'))
            }

            if (value.length > 100) {
              return callback(new Error('公司名称不能大于100个字符'))
            }
            callback()
          },
          trigger: 'blur'
        }],
        name: [
          {
            required: true,
            validator: function(rule, value, callback) {
              if (value === undefined || value === null || value === '') {
                return callback(new Error('姓名不能为空'))
              }
              var emptyVal = value.replace(/ /g, '')
              if (!emptyVal) {
                return callback(new Error('姓名不能为空'))
              }
              callback()
            },
            trigger: 'blur'
          }
        ],
        phone: [
          {
            required: true,
            validator: function(rule, value, callback) {
              // const phoneReg = /^1[34578]\d{9}$$/
              const phoneReg = /^([0-9_-])+/
              if (!value) {
                return callback(new Error('电话号码不能为空'))
              }
              var emptyVal = value.replace(/ /g, '')
              if (!emptyVal) {
                return callback(new Error('电话号码不能为空'))
              }
              setTimeout(function() {
                if (phoneReg.test(value)) {
                  callback()
                } else {
                  callback(new Error('请输入数字值'))
                }
              }, 100)
            },
            trigger: 'blur'
          }
        ],
        phoneCode: [{ required: true, message: '验证码不能为空', trigger: 'blur' },
          { min: 6, max: 6, message: '验证码长度为6个字符', trigger: 'blur' }],
        csessionid: [{
          required: true,
          validator: function(rule, value, callback) {
            if (value === undefined || value === null || value === '') {
              return callback(new Error('请完成滑动验证'))
            }
            callback()
          },
          trigger: 'blur'
        }]
      },
      isApplyFreeBookStatus: false
    }
  },
  methods: {
    trialN: function () {
      this.autoPlay = false
      var that = this
      this.openRegisterPop({
        onClose: function () {
          that.autoPlay = true
        }
      })
    },
    applyFreeBook: function() {
      this.isApplyFreeBookStatus = true
      this.autoPlay = false
      this.trial()
    },
    doLogin: function() {
      this.openLoginPop({})
    },
    openKfDialog: function() {
      this.kfDialog = true
    },
    closeSelf: function() {
      this.kfDialog = false
    },
    // 阿里云滑块验证码初始化
    getAliCaptcha: function(type, id) {
      var that = this
      var appkey = 'FFFF0N2N0000000084A3' // 应用标识,不可更改
      var scene = 'register' // 场景,不可更改
      var token = [appkey, (new Date()).getTime(), Math.random()].join(':')
      var nc = new noCaptcha() // eslint-disable-line
      this.ncRef = nc
      var option = {
        renderTo: id,
        appkey: appkey,
        scene: scene,
        token: token,
        callback: function(data) {
          that.infoDataF.csessionid = data.csessionid
          that.infoDataF.sig = data.sig
          that.infoDataF.token = data.token
        }
      }
      nc.init(option)
      nc.upLang('cn', {
        _startTEXT: '请按住滑块，拖动到最右边',
        _yesTEXT: '验证通过',
        _error300: '哎呀，出错了，点击<a href="javascript:__nc.reset()">刷新</a>再来一次',
        _errorNetwork: '网络不给力，请<a href="javascript:__nc.reset()">点击刷新</a>'
      })
    },
    login: function() {
      window.location.href = '/login'
    },
    handleRedirectDetail: function(page) {
      window.location.href = '/' + page + '.html'
    },
    // 导航申请按钮点击
    trial: function() {
      if (this.closeTimerOpenConsultPop) {
        this.closeTimerOpenConsultPop()
      }
      // zhugeTrackTwoLevel('免费试用','导航栏')
      var that = this
      this.resetForm()
      that.detailDialog = true
      this.myFileList = []
      that.$nextTick(function() {
        that.getAliCaptcha(1, '#slideValiteDialog')
      })
    },
    resetForm: function() {
      if (this.$refs.upload) {
        this.$refs.upload.clearFiles()
      }
      this.infoDataF = {
        name: '',
        phone: '',
        email: '',
        companyName: '',
        phoneCode: '',
        uploadimage: '',
        businessLicenseImg: '',
        csessionid: '',
        sig: '',
        token: '',
        scene: 'register',
        address: ''
      }
    },
    // dialog-upload
    handleAvatarSuccess: function(url, file) {
      if (url) {
        this.myFileList = [{
          name: file.name,
          url: file.response
        }]
      }
      this.infoDataF.businessLicenseImg = url
      this.infoDataF.uploadimage = window.URL.createObjectURL(file.raw)
      this.$refs.infoDataRef.validateField('businessLicenseImg', function(valid) {})
    },
    error: function() {
      this.$message.error('文件上传失败，请稍后重试')
    },
    handleRemove: function() {
      this.infoDataF.businessLicenseImg = ''
      this.infoDataF.uploadimage = ''
    },
    // 上传图片验证
    beforeAvatarUpload: function(file) {
      const isIMG =
        file.type === 'image/jpg' ||
        file.type === 'image/jpeg' ||
        file.type === 'image/png'
      const isLt = file.size / 1024 / 1024 / 5 <= 1
      if (!isIMG) { this.$message.error('图片只支持jpg、jpeg、png格式!') }
      if (!isLt) { this.$message.error('上传图片大小不能超过5MB!') }
      return isLt && isIMG
    },
    // 检索输入框
    checkInputAll: function(type) {
      this.$message.closeAll()
      if (!this.infoDataF.companyName) {
        this.$message.warning('请先填写您的公司名称')
        return false
      } else if (this.infoDataF.uploadimage && this.fileSize > 5) {
        this.$message.warning('上传图片大小不能超过5M')
        return false
      } else if (!this.infoDataF.name) {
        this.$message.warning('请输入姓名')
        return false
      } else if (!regexs.mobile.test(this.infoDataF.phone)) {
        this.$message.warning('请输入正确的手机号码')
        return false
      } else if (!this.infoDataF.csessionid || !this.infoDataF.sig || !this.infoDataF.token) {
        this.$message.warning('请先完成滑动验证码')
        return false
      } else if (!this.infoDataF.phoneCode) {
        this.$message.warning('请输入验证码！')
        return false
      } else {
        return true
      }
    },
    // 点击发送验证码验证前提
    checkInputCode: function(type) {
      this.$message.closeAll()
      if (!this.infoDataF.companyName) {
        this.$message.warning('请先填写您的公司名称')
        return false
      } else if (!this.infoDataF.name) {
        this.$message.warning('请先填写您的姓名')
        return false
      } else if (!regexs.mobile.test(this.infoDataF.phone)) {
        this.$message.warning('请输入正确的手机号码')
        return false
      } else {
        return true
      }
    },
    // 获取验证码
    clickToGetCode: function(type) {
      var that = this
      if (!this.checkInputCode(type)) {
        return
      }
      var param = {
        trialPhone: that.infoDataF.phone
      }
      var url = '/webapi/open/saas/' + (!this.isApplyFreeBookStatus ? 'sendTrialMobileCode' : 'sendTrialMobileCodeForMarketing') + '?showError'
      postVue(url, param, function(response) {
        if (response.status === '200') {
          that.getCodeSecond = 60
          that.$message({
            message: '验证码发送成功，请注意查收！',
            type: 'success'
          })
          var num = TIME_COUNT
          that.timer = window.setInterval(function() {
            that.getCodeSecond--
            if (that.getCodeSecond <= 0) {
              that.getCodeSecond = 0
              window.clearInterval(that.timer)
            }
          }, 1000)
        } else if (response.status === '201') {
          that.infoDataF.csessionid = ''
          that.$alert('当前手机号已开通，' + (that.currentIsMobile() ? '请使用电脑登录。' : '现在登录。'), '提示', {
            confirmButtonText: '确定',
            type: 'warning',
            callback: function() {
              if (!that.currentIsMobile()) {
                window.location.href = '/'
              }
            }
          })
          that.ncRef.reset()
        } else {
          that.ncRef.reset()
          that.infoDataF.csessionid = ''
          that.$message({
            message: response.msg,
            type: 'error'
          })
        }
      }, function() {
        console.log('请求失败处理')
      })
    },
    currentIsMobile: function() {
      if (/Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)) {
        return true
      }
      return false
    },
    // 立即申请
    freeUseClick: function() {
      var that = this
      this.$refs.infoDataRef.validate(function(valid) {
        if (valid) {
          that.doCommitData(that.infoDataF)
        }
      })
    },
    // 提交申请
    doCommitData: function(data) {
      var that = this
      this.submitDisabled = true
      if (!this.checkInputAll()) {
        return
      }
      var params = {
        contactName: data.name,
        mobile: data.phone,
        verifyCode: data.phoneCode,
        companyName: data.companyName,
        packageCode: '',
        channel: this.isApplyFreeBookStatus ? 'websiteBanner' : (this.source || (sessionStorage.getItem('sourceFrom') || '')),
        term: this.isApplyFreeBookStatus ? '尽调实务指南书籍' : (sessionStorage.getItem('sourceFrom_term') || ''),
        imageUrl: data.businessLicenseImg,
        medium: sessionStorage.getItem('sourceFrom_medium') || '',
        source: this.isApplyFreeBookStatus ? 'contentMarketing' : sessionStorage.getItem('sourceFrom_channel') || '',
        csessionid: data.csessionid,
        sig: data.sig,
        token: data.token,
        scene: 'register',
        address: data.address || '',
        multiFlag: this.isApplyFreeBookStatus ? 'cddbook' : ''
      }
      postVue('/webapi/open/online/order/epidemicCommonWeal', params, function(response) {
        // 拿到回调后，按钮可点击
        that.submitDisabled = false
        if (response.status === '200') {
          that.detailDialog = false
          var str = '<div class="buy-scan-success" style="width: 100%;text-align: center;"><div style="font-size:60px;text-align: center;"><i class="el-icon-success" style="color:#67C23A"></i></div>' +
            '<div class="successTxt" style="margin-top: 18px;margin-bottom: 10px;line-height: 21px;font-weight: normal;color: #333333;">感谢您的申领</div>' +
            '<div style="font-size: 14px;color: #999999;text-align: center;line-height: 19px;">我们的客户经理和服务人员会尽快与您联系!</div></div>'
          that.setShowSuccess(true, that.isApplyFreeBookStatus ? str : undefined, that.isApplyFreeBookStatus ? '申领指南' : '提示')
        } else if (response.status === '203') {
          that.detailDialog = false
          that.$alert(response.msg, {
            confirmButtonText: '确定',
            type: 'info',
            showClose: false,
            customClass: 'blurTips',
            callback: function(action) {
              that.ncRef.reset()
              that.infoDataF.csessionid = ''
              that.detailDialog = false
              that.resetForm()
            }
          })
        } else {
          that.ncRef.reset()
          that.infoDataF.csessionid = ''
          that.$message.error(response.msg)
        }
      }, function() {
        that.submitDisabled = false
        that.ncRef.reset()
        that.infoDataF.csessionid = ''
      })
    },
    setShowSuccess: function(is, msg, title) {
      var that = this
      var html = '<div class=\'buy-scan-success\'><div style=\'font-size:60px;\'><i class=\'el-icon-success\' style=\'color:#67C23A\'></i></div><div class=\'successTxt\'>申请成功!</div></div>'
      this.$alert(msg || (is ? html : '您已申请，请勿重复申请。'), title || '提示', {
        confirmButtonText: '确定',
        type: is ? '' : 'warning',
        dangerouslyUseHTMLString: true,
        showClose: true,
        showConfirmButton: false,
        customClass: 'pc-alert-theme',
        callback: function() {
          that.detailDialog = false
          that.infoDataF.name = ''
          that.infoDataF.companyName = ''
          that.infoDataF.phone = ''
          that.infoDataF.phoneCode = ''
          that.infoDataF.uploadimage = ''
          that.infoDataF.businessLicenseImg = ''
          that.infoDataF.address = ''
          that.getCodeSecond = '获取验证码'
          that.timer = ''
          if (that.$refs.upload) {
            that.$refs.upload.clearFiles()
          }
        }
      })
    },
    changeFile: function(file, fileList) {
      this.myFileList = fileList.slice(-1)
    },
    // 关闭弹框
    closeDialog: function() {
      var that = this
      that.detailDialog = false
      that.resetForm()
      that.$refs.infoDataRef.resetFields()
      setTimeout(function () {
        that.autoPlay = true
        that.isApplyFreeBookStatus = false
      }, 500)
    }
  },
  mounted: function () {
    if (this.initTimerOpenConsultPop) {
      this.initTimerOpenConsultPop()
    }
  }
}
