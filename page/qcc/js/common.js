function postVue(url, params, successListener, failedListener, headers, isNotAsync) {
  var p = {
    url: url,
    params: params
  }
  headers = headers || {}
  $.ajax({
    type: 'POST',
    url: '/proapi/common/post',
    data: JSON.stringify(p),
    dataType: 'json',
    cache: false,
    headers: headers,
    contentType: 'application/json; charset=utf-8',
    async: isNotAsync === undefined ? true : isNotAsync,
    success: successListener,
    error: failedListener
  })
}

function saveSourceFrom() {
  var res = {}
  var source = ''
  var url = window.location.href
  var arr1 = url.split('?')
  if (arr1 && arr1.length > 1) {
    var tmp = arr1[1]
    if (tmp) { // 解析url携带的参数
      var arr2 = tmp.split('&')
      if (arr2 && arr2.length > 0) {
        for (var i = 0; i < arr2.length; i++) {
          var params = arr2[i].split('=')
          if (params && params.length == 2) {
            if (params[0] == 'source') {
              sessionStorage.setItem('sourceFrom', decodeURI(params[1], 'utf-8'))
              source = params[1]
            }
          }
        }
        if (!source) {
          source = '&'
        }
        for (var i = 0; i < arr2.length; i++) {
          var params = arr2[i].split('=')
          if (params && params.length == 2) {
            if (params[0] == 'medium') {
              sessionStorage.setItem('sourceFrom_medium', decodeURI(params[1], 'utf-8'))
              source = source + '&medium=' + params[1]
            } else if (params[0] == 'term') {
              sessionStorage.setItem('sourceFrom_term', decodeURI(params[1], 'utf-8'))
              source = source + '&term=' + decodeURI(params[1], 'utf-8')
            } else if (params[0] == 'ch') {
              sessionStorage.setItem('sourceFrom_channel', decodeURI(params[1], 'utf-8'))
              source = source + '&term=' + decodeURI(params[1], 'utf-8')
            } else if (params[0] == 'urlfrom') {
              sessionStorage.setItem('sourceFrom_urlfrom', decodeURI(params[1], 'utf-8'))
              source = source + '&urlfrom=' + decodeURI(params[1], 'utf-8')
            }
          }
        }
      }
    }
  }
  res.source = source
  return res
}

function getSourceFrom() {
  var res = sessionStorage.getItem('sourceFrom')
  return res || ''
}

function IS_MOBILE() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Opera Mobi|Windows CE|Symbian|Windows Phone|POLARIS|lgtelecom|nokia|SonyEricsson|LG|SAMSUNG|Samsung/i.test(navigator.userAgent)
}

/**
 * 诸葛
 */

function zhugeTrackOneLevel(btnName) {
  return exeZhugeTrack(btnName)
}
function zhugeTrackTwoLevel(btnName, pageName) {
  var obj = {}
  if (pageName) {
    obj.page_name = pageName
  }
  if (btnName) {
    obj.button_name = btnName
  }
  return exeZhugeTrack(pageName, obj)
}

function zhugeTrackThreeoLevel(menuName, btnName, pageName) {
  var obj = {}
  if (pageName) {
    obj.page_name = pageName
  }
  if (menuName) {
    obj.button_detail = menuName
  }
  if (btnName) {
    obj.button_name = btnName
  }
  return exeZhugeTrack(pageName, obj)
}
function exeZhugeTrack(pageName, obj) {
  if (!pageName || pageName === 'undefined') {
    return true
  }
  try {
    obj = obj || {}
    obj.application_name = obj.application_name || '企查查专业版'
    window.qccZhuge && window.qccZhuge.track(pageName, obj)
  } catch (e) {
  }
  return true
}

if (window.location.href.indexOf('pro.qichacha.com') > -1) {
  window.location.replace(window.location.href.replace(new RegExp('pro.qichacha.com', 'g'), 'pro.qcc.com'))
}

