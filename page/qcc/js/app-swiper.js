Vue.component('app-swiper-arrow', {
  template: '<div class="app-swiper-arrow" @click="clickProgress">' +
    '<span class="inner-line" :style="{width: getWidth}"></span>' +
    '</div>',
  props: {
    delayTime: { default: 5000 },
    processTime: { default: 200 },
    pause: { default: false },
    controlModel: {},
    currentIsTiming: { default: true }
  },
  model: {
    prop: 'controlModel',
    event: 'changeModelEvent'
  },
  data: function() {
    return {
      currentTime: 0,
      taskId: ''
    }
  },
  computed: {
    getWidth: function () {
      return this.currentTime * 100.0 / this.delayTime + '%'
    }
  },
  watch: {
    pause: function (val) {
      // if (!val && !this.controlModel) {
      //   this.currentTime = 0
      // }
      if (!val && this.currentIsTiming && this.controlModel && this.currentTime < this.delayTime) {
        this.updateState()
      }
    },
    controlModel: function (val) {
      if (val && this.currentIsTiming && this.currentTime === 0) {
        this.updateState()
      } else if (!val && this.currentTime !== 0) {
        if (this.taskId) {
          clearTimeout(this.taskId)
        }
        this.currentTime = 0
      }
    }
  },
  methods: {
    clickProgress: function() {
      if (!this.controlModel) { // 如果当前不是点击，清除，并将状态改为当前
        this.$emit('timeProcessClick')
      }
    },
    updateState: function () {
      if (this.controlModel) {
        if (this.currentTime >= this.delayTime) { // 倒计时结束咯
          this.currentTime = this.$emit('endTiming') ? 0 : this.currentTime
        } else {
          if (!this.pause) {
            var that = this
            this.taskId = setTimeout(function () {
              that.currentTime += that.processTime
              that.updateState()
            }, this.processTime)
          }
        }
      } else {
        if (this.taskId) {
          clearTimeout(this.taskId)
        }
        this.currentTime = 0
      }
    }
  },
  mounted: function () {
    if (this.controlModel) {
      this.updateState()
    }
  }
})

Vue.component('app-swiper', {
  template: '<div class="app-swiper"><el-carousel arrow="always" :height="height" :autoplay="false" ref="carouselRef" indicator-position="none">' +
    '<slot></slot>' +
    '</el-carousel>' +
    '<div class="arrow-wrapper">' +
    '<app-swiper-arrow v-if="arrowModelList&&arrowModelList.length>1" v-for="(item,index) in arrowModelList" :key="index" v-model="item.model" :current-is-timing="index === currentIndex" ' +
    '@endTiming="endTiming(item,index)" @timeProcessClick="timeProcessClick(item,index)" :pause="pause"/>' +
    '</div>' +
    '</div>',
  props: {
    height: { default: '740px' },
    count: { default: 2 },
    pause: { default: false }
  },
  data: function() {
    return {
      currentIndex: 0,
      arrowModelList: [],
      lastClickTime: 0
    }
  },
  methods: {
    timeProcessClick: function (item, index) {
      this.arrowModelList.forEach(function (row) {
        row.model = false
      })
      this.$refs.carouselRef.setActiveItem(index)
      this.currentIndex = index
      item.model = true
      this.$emit('throttledarrowclick')
    },
    endTiming: function (item, index) {
      item.model = false
      this.arrowModelList.forEach(function (row) {
        row.model = false
      })
      if (index + 1 < this.arrowModelList.length) {
        this.$refs.carouselRef.setActiveItem(index + 1)
        this.arrowModelList[index + 1].model = true
        this.currentIndex = index + 1
      } else {
        this.$refs.carouselRef.setActiveItem(0)
        this.arrowModelList[0].model = true
        this.currentIndex = 0
      }
    }
  },
  created: function () {
    for (var i = 0; i < this.count; i++) {
      this.arrowModelList.push({
        model: false,
        index: i
      })
    }
    this.arrowModelList[0].model = true
  },
  mounted: function () {
    var that = this
    this.$refs.carouselRef.throttledArrowClick = function (index) {
      var currentTime = new Date().getTime()
      var dTime = currentTime - that.lastClickTime

      if (dTime < 500) {
        return
      }
      that.lastClickTime = currentTime
      // console.log(index)
      that.arrowModelList.forEach(function (t) {
        t.model = false
      })
      if (that.currentIndex < index) { // 下一页
        that.endTiming(that.arrowModelList[that.currentIndex], that.currentIndex)
      } else { // 上一页
        if (index < 0) {
          that.$refs.carouselRef.setActiveItem(0)
          that.arrowModelList[0].model = true
          that.currentIndex = 0
        } else {
          that.$refs.carouselRef.setActiveItem(index)
          that.arrowModelList[index].model = true
          that.currentIndex = index
        }
      }
      that.$emit('throttledarrowclick')
    }
  }
})
