<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <link rel="icon" href="/favicon78.png?v=2">
  <title>反洗钱 · 受益所有人识别</title>
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no">
  <meta name="Keywords" content="企查查专业版 | 企查查pro - 尽职调查_风险监控_反洗钱_反欺诈_风险管理系统_风险为本_空壳_套牌皮包公司 | 企查查高级版">
  <meta name="baidu-site-verification" content="zcEscL3TK0" />
  <link rel="stylesheet" href="/css/header.css?v=2023090801" />
  <link rel="stylesheet" href="/css/footer.css?v=2023072001" />
  <link rel="stylesheet" href="/css/iconfont/iconfont.css?v=2025022801" />
  <link rel="stylesheet" href="/css/product.css?v=2023011901" />
  <link rel="stylesheet" href="/css/consulation.css?v=2022012601" />
  <link rel="stylesheet" href="/css/footerFloat.css" />
  <link rel="stylesheet" href="/js/element-ui/index.css" />
   <link rel="stylesheet" href="/css/index.css?v=2023011901" />
</head>
<style>
  .qfkwebsite .bannerWrapper {
    height: 100vh;
    position: relative;
    padding-top: 130px;
    box-sizing: border-box;
  }
  .describitionContent .loginWrapper {
    width: 400px;
    margin: auto;
  }
  .describitionContent .loginWrapper .text {
    font-size: 20px;
    color: #FFFFFF;
    text-align: center;
  }
  .describitionContent .loginWrapper .text span {
    line-height: 26px;
    font-weight: bold;
  }
  .describitionContent .loginWrapper .loginTitle {
    font-size: 18px;
    line-height: 18px;
    color: #333333;
    margin-bottom: 30px;
    text-align: center;
  }
  .describitionContent .loginWrapper .loginTitle.topOffset {
    margin-top: 45px;
  }
  .describitionContent .loginWrapper .loginPannel {
    margin-top: 50px;
    border-radius: 8px;
    background: white;
    padding: 30px;
    position: relative;
    text-align: left;
  }
  .describitionContent .loginWrapper .loginPannel .loginpannelTips {
    position: absolute;
    left: 0;
    top: 0;
    height: 45px;
    line-height: 45px;
    width: 100%;
    box-sizing: border-box;
    background: #F8F8F8;
    border-radius: 8px 8px 0px 0px;
    padding-left: 15px;
    font-size: 12px;
    text-align: left;
    color: #666666;
  }
  .describitionContent .loginWrapper .loginPannel .loginpannelTips .iconfont {
    font-size: 14px;
  }
  .describitionContent .loginWrapper .loginPannel .errorTips {
    background: #FAEBEE;
    border: 1px solid #F3CFD6;
    font-size: 14px;
    color: #F04040;
    padding-left: 15px;
    box-sizing: border-box;
  }
  .describitionContent .loginWrapper .loginPannel .errorTips.timeout {
    background: #F3F4F4;
    border: 1px solid #E3E4E5;
    color: #333333;
  }
  .describitionContent .loginWrapper .loginPannel .errorTips.timeout a {
    color: #128BED;
    font-size: 14px;
    text-decoration: none;
  }
  .describitionContent .loginWrapper .el-form-item {
    margin-bottom: 20px;
  }
  .describitionContent .loginWrapper .el-form-item:last-child {
    margin-bottom: 0;
  }
  .describitionContent .loginWrapper .el-form-item .el-input-group__prepend {
    color: #666666;
    background: white;
  }
  .describitionContent .loginWrapper .el-form-item input::-webkit-input-placeholder {
    color: #999999 !important;
    opacity: 1;
  }
  .describitionContent .loginWrapper .el-form-item input:-moz-placeholder,
  .describitionContent .loginWrapper .el-form-item input::-moz-placeholder {
    /* Firefox 19+ */
    color: #999999 !important;
    opacity: 1;
  }
  .describitionContent .loginWrapper .el-form-item input:-ms-input-placeholder {
    color: #999999 !important;
    opacity: 1;
  }
  .describitionContent .loginWrapper .loginBtn {
    width: 100%;
    background: #128BED;
  }
  .describitionContent .loginWrapper .loginBtn:hover {
    opacity: 0.88;
  }
  .el-input-group__append,
  .el-input-group__prepend {
    padding: 0 0;
    text-align: center;
    width: 46px;
  }
  .el-input__icon {
    cursor: pointer;
  }
  .el-input__inner {
    color: #333333;
  }
  ::-ms-clear,::-ms-reveal{display:none;}
  .pwdEye{
    position: absolute;
    right: 5px;
    top: 0px;
    cursor: pointer;
    color: #999999;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }

</style>

<body>
<div id="websiteWrapperId" class="qfkwebsite">
  <div class="top-header">
    <div class="logo-wrapper" style="height: 49px;padding-right: 30px">
      <div style="float: left;">
        <a title="反洗钱 · 受益所有人识别" href="home_bank.html">
          <img alt="反洗钱 · 受益所有人识别" src="/img/icon_bank_logo.png" width="221">
        </a>
      </div>
    </div>
    <div class="header-operate-wrapper" style="position: absolute;">
      <a style="color: white">
        <span key="首页" class="item actived">首页</span>
      </a>
      <a href="b_benefit.html" style="margin-left: -5px; color: white">
        <span key="受益所有人" class="item">受益所有人</span>
      </a>
    </div>
    <div class="loginbtn-wrapper">
      <!--      <button type="button" class="el-button button login el-button&#45;&#45;default el-button&#45;&#45;small" onclick="location.href='/login'" style="padding: 0px 15px;">-->
      <!--        <span>登录</span>-->
      <!--      </button>-->
    </div>
  </div>
  <div class="bannerWrapper describitionContent" style="background: url(/img/websiteblank_bg.jpg) center top no-repeat;">
    <div class="loginWrapper">
      <div class="text">欢迎使用 <span>反洗钱 · 受益所有人识别</span></div>
      <transition name="el-zoom-in-center">
        <el-form class="loginPannel" :model="loginInfo" ref="formLogin" label-width="0" v-if="!isLoginSuccess" v-cloak>
          <div class="loginTitle">账号登录</div>
          <el-form-item label=" " class="errorTips" v-if="loginInfo.errorShow">
            {{loginInfo.errMsg ? loginInfo.errMsg : '账号密码输入错误，请确认后再填写'}}
          </el-form-item>
          <el-form-item label=" " prop="pass">
            <el-input v-model="loginInfo.name" autocomplete="off" placeholder="请输入您的用户名">
              <span slot="prepend" class="iconfont icon-xingming"></span>
            </el-input>
          </el-form-item>
          <el-form-item label=" " prop="checkPass">
            <el-input ref="inputPwdId" :type="pwdVisiable ? 'text' : 'password'" v-model="loginInfo.pwd" autocomplete="off" placeholder="请输入登录密码">
              <span slot="prepend" class="iconfont icon-mima"></span>
            </el-input>
            <i class="pwdEye iconfont" :class="pwdVisiable ? 'icon-mimakejian' : 'icon-mimabukejian'" @click="pwdVisiable = !pwdVisiable"></i>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="firstLogin" class="loginBtn" :loading.sync="loginInfo.loading">会员登录</el-button>
          </el-form-item>
        </el-form>
      </transition>
      <transition name="el-zoom-in-center">
        <el-form class="loginPannel" :model="loginCheckInfo" ref="formLogin" label-width="0" v-if="isLoginSuccess && loginIsHide" v-cloak>
          <div class="loginpannelTips" style="text-align: left"><span class="iconfont icon-wenxintishi" style="margin-right: 5px"></span>温馨提示：请至少输入{{loginCheckInfo.minVerifyNum}}个短信验证码</div>
          <div class="loginTitle topOffset">验证码登录</div>
          <el-form-item label=" " class="errorTips" v-if="loginCheckInfo.errorShow && !loginCheckInfo.timeOut">
            {{loginCheckInfo.errMsg ? loginCheckInfo.errMsg : '验证码输入错误，请确认后再填写'}}
          </el-form-item>
          <el-form-item label=" " class="errorTips timeout" v-if="loginCheckInfo.timeOut">
            {{loginCheckInfo.errorMsg}}请 <a href="/home_bank.html">重新登录</a>
          </el-form-item>
          <el-form-item v-for="(tmp,index) in loginCheckList" :key="index" label=" " :prop="tmp.model">
            <el-input v-model="tmp.model" autocomplete="off" :placeholder="'请输入短信验证码' + (index + 1)" type="number">
              <span slot="prepend" class="iconfont icon-yanzhengma1"></span>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="doVerify" :disabled="loginCheckInfo.disabled" class="loginBtn" :loading="loginCheckInfo.loading">会员登录</el-button>
          </el-form-item>
        </el-form>
      </transition>
    </div>
  </div>

<!--  <div id="backToTop" onclick="scrollToTop()">-->
<!--    <img src="/img/icon_back2top.png" width="30" class="icon_back2top">-->
<!--    <img src="/img/icon_back2top_text.png" width="30" class="icon_back2top_text">-->
<!--  </div>-->
</div>
<script type="text/javascript" src="/js/vue.min.js"></script>
<script type="text/javascript" src="/js/element-ui/index.js"></script>
<script type="text/javascript" src="/js/jquery-3.4.1.min.js"></script>
<script type="text/javascript" src="/js/index.js?v=**********"></script>
<script type="text/javascript" src="/js/common.js?v=**********"></script>
<script>
  logoUrl1 = '/img/icon_bank_logo.png'
  logoUrl2 = '/img/icon_bank_logodeep.png'

  function generateUUID() {
    var s = []
    var hexDigits = '0123456789abcdef'
    for (var i = 0; i < 32; i++) {
      s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
    }

    s[12] = '4' // bits 12-15 of the time_hi_and_version field to 0010
    s[16] = hexDigits.substr((s[19] & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01

    var uuid = s.join('')
    return uuid
  }
  var appVue = new Vue({
    el: "#websiteWrapperId",
    data:function () {
      return {
        loginInfo: {
          name: '',//***********
          pwd: '',//Pass7899
          errorShow: false,
          errMsg: '',
          loading: false,
        },
        loginCheckInfo: {
          minVerifyNum: 1,
          errorShow: false,
          timeOut: false,
          loading: false,
          disabled: false,
          errorMsg: ''
        },
        loginCheckList: [],
        pwdVisiable: false,
        isLoginSuccess: false,
        loginIsHide: false,
        phoneCodeTimeOutId: '',
        sessionId: ''
      }
    },
    computed: {
    },
    methods: {
      getCompCorpFunctionList: function(resData, call){
        postVue('/webapi/saas/corp/search/getCompCorpFunctionList', {}, function (res) {
          var list = res.result.compCorpFunctionList
          window.localStorage.setItem('compCorpFunctionStorage', JSON.stringify(list))
          call()
        }, function (err) {

        },{
          'pro-access-secret-key': resData.accessSecretKey,
          'pro-access-token': resData.accessToken,
          'pro-client-id': resData.uuid
        })
      },
      doVerify: function(codes){
        var paramArr = codes && typeof codes === 'string' ? codes : []
        this.loginCheckList.forEach(function (item) {
          if (item.model && item.model != ''){
            paramArr.push(item.model);
          }
        })
        if (!paramArr || paramArr.length < this.loginCheckInfo.minVerifyNum){
          this.$message.warning("请至少输入 " + this.loginCheckInfo.minVerifyNum + " 个验证码")
          return
        }

        this.loginCheckInfo.loading = true;
        var uuid = generateUUID()
        var that = this;
        postVue("/webapi/open/scr/doLoginBank",{
          verifyCodes: paramArr.toString(),
          loginName: this.loginInfo.name.trim(),
          clientId: uuid,
          sessionId: this.sessionId
        }, function (data) {
          if (data.status == '200') {
            data.result.uuid = uuid
            window.localStorage.setItem('userStorage', JSON.stringify(data.result))
            that.getCompCorpFunctionList(data.result, function () {
              window.location = '/welcome'
            })
          } else {
            if (data.status == '2001') {
              that.loginCheckInfo.timeOut = true;
              that.loginCheckInfo.disabled = true;
              that.loginCheckInfo.errorMsg = data.msg;
              that.$alert(data.msg + "请重新登录", '提示', {
                confirmButtonText: '确定',
                type: 'warning',
                callback: function () {
                  window.location.href = "/home_bank.html"
                }
              })
            }else{
              // that.$alert(data.msg, '提示', {
              //     confirmButtonText: '确定',
              //     type: 'warning',
              //     callback:undefined
              // })
              that.loginCheckInfo.errMsg = data.msg;
              that.loginCheckInfo.errorShow = true;
            }
          }
          that.loginCheckInfo.loading = false;
        },function () {
          that.loginCheckInfo.loading = false;
        })

      },
      firstLogin: function(){
        if (this.loginInfo.name.trim() == ''){
          this.$message.warning("请输入账号")
          return
        }
        if (this.loginInfo.pwd.trim() == ''){
          this.$message.warning("请输入密码")
          return
        }
        var that = this;
        this.loginInfo.loading = true;
        postVue("/webapi/open/scr/prelogin",{
          "loginName": this.loginInfo.name.trim(),
          "password": this.loginInfo.pwd
        },function (data) {
          if (data.status == '200') {
            that.sessionId = data.result.sessionId
            if (data.result.minVerifyNum === 0){
              that.doVerify(that.loginInfo.pwd)
              return
            }

            that.loginCheckInfo.minVerifyNum = data.result.minVerifyNum;
            for(var i=0;i<data.result.verifyNum;i++) {
              that.loginCheckList.push({model:''})
            }
            that.firstLoginSuccess(data.result.expiryMin);
          } else {
            that.loginInfo.errorShow = true;
            if (data.msg){
              that.loginInfo.errMsg = data.msg;
            }
          }
          that.loginInfo.loading = false;
        },function (data) {
          that.$message.error('请求失败')
          that.loginInfo.loading = false;
        })

      },
      firstLoginSuccess: function(timeOutMin){
        this.isLoginSuccess = true;
        this.loginInfo.errorShow = false;
        this.loginCheckInfo.errorShow = false;
        var that = this;
        setTimeout(function () {
          that.loginIsHide = true;
        },300)
        if (this.phoneCodeTimeOutId) {
          window.clearTimeout(this.phoneCodeTimeOutId)
          this.phoneCodeTimeOutId = ''
        }
        this.phoneCodeTimeOutId = setTimeout(function () {
          that.loginCheckInfo.timeOut = true;
          that.loginCheckInfo.disabled = true;
          that.loginCheckInfo.errMsg = '验证码超时，'
        }, timeOutMin *60 * 1000)
      }

    },
    mounted:function () {
      this.activedTitle = '首页';
      var suffixList = this.$refs.inputPwdId.$vnode.elm.getElementsByClassName('el-input__suffix')
      console.log(suffixList[0])
      if (suffixList && suffixList.length > 0) {
        var that = this;
        suffixList[0].onclick = function () {
          that.pwdVisiable = !that.pwdVisiable;
        }
      }
    }
  })

</script>
</body>

</html>
