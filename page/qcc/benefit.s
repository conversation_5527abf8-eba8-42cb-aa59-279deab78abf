<!DOCTYPE html>
<html>

<head>
  <script type="text/javascript" src="/js/common.js?v=2022060801"></script>
  <script>
    var source = saveSourceFrom()
    if (IS_MOBILE()) {
      window.location.href = "/benefit?source=" + (source.source || '');
    }else{
      window.location.href = "/buy/buy-combo?source=" + (source.source || '');
    }
  </script>
  <meta charset="UTF-8">
  <link rel="icon" href="/favicon78.png">
  <script type="text/javascript" src="/js/hm.statistics.js"></script>
  <title>企查查专业版 | 企查查pro - 尽职调查_风险监控_反洗钱_反欺诈_风险管理系统_风险为本_空壳_套牌皮包公司 | 企查查高级版</title>
  <meta name="description" content="企查查专业版是企查查旗下的企业风险监控管理平台,为银行、证券、保险等客户在企业开户、审批、反洗钱，为企业供应商提供风险管理、信用报告舆情监测招标采购一站式服务">
  <meta name="Keywords" content="企查查专业版 | 企查查pro - 尽职调查_风险监控_反洗钱_反欺诈_风险管理系统_风险为本_空壳_套牌皮包公司 | 企查查高级版">
  <script type="text/javascript" src="./zhuge.js?v=2022042701"></script>
  <link rel="stylesheet" href="/css/header.css?v=2023090801" />
  <link rel="stylesheet" href="/css/footer.css?v=2023072001" />
  <link rel="stylesheet" href="/css/index.css?v=2023011901" />
  <link rel="stylesheet" href="/css/iconfont/iconfont.css?v=2025022801" />
  <link rel="stylesheet" href="/css/benefit.css?v=2023011901" />
  <link rel="stylesheet" href="/css/consulation.css?v=2022012601" />
  <link rel="stylesheet" href="/css/footerFloat.css?v=2022012601" />
  <link rel="stylesheet" href="/js/element-ui/index.css" />
  <link rel="stylesheet" href="/css/bootstrap.min.css" />

</head>

<body>
  <div id="websiteWrapperId" class="qfkwebsite">
    <div class="top-header">
      <div class="logo-wrapper" style="height: 49px;">
        <div style="float: left; width: 162px;">
          <a title="企查查专业版" href="https://pro.qcc.com/">
            <img alt="企查查专业版" src="/img/logo_pc.png" width="160">
          </a>
        </div>
        <div style="display: inline-block;margin-left:15px;">
          <span v-popover:popover5 class="imageClass"></span>
          <div></div>

        </div>
      </div>
      <div class="header-operate-wrapper" style="position: absolute;">
        <a onclick="zhugeTrackTwoLevel('首页','导航栏');" href="/home" title="首页" style="color:white">
          <span key="首页" class="item">首页</span>
        </a>
        <a onclick="zhugeTrackTwoLevel('产品体系','导航栏');" href="product" title="产品体系" style="margin-left: -5px; color: white">
          <span key="产品体系" class="item">产品体系</span>
        </a>
        <a onclick="zhugeTrackTwoLevel('行业方案','导航栏');" href="industry.html" title="行业方案" style="margin-left: -5px; color: white">
          <span key="行业方案" class="item">行业方案</span>
        </a>
        <a onclick="zhugeTrackTwoLevel('受益所有人识别','导航栏');" href="" title="受益所有人识别" style="margin-left: -5px; color: white">
          <span key="受益所有人识别" class="item actived" style="margin-left: -5px; color: white">受益所有人识别</span>
        </a>
        <a onclick="zhugeTrackTwoLevel('关于我们','导航栏');" href="about_us" title="关于我们" style="margin-left: -5px; color: white">
          <span key="关于我们" class="item">关于我们</span>
        </a>
      </div>
      <el-popover ref="popover5" placement="bottom-start" trigger="hover" v-cloak>
        <img alt="企业征信机构备案" src="/img/zxbaxq.png?v=2019110104" style="width: 552px;margin: -10px;" v-cloak />
      </el-popover>
      <div style="position: absolute; right: 0;top: 0; display: inline-block; width: auto;display: flex;flex-direction: row;">
        <div>
          <button class='btn-foucs' type="button" onclick="zhugeTrackTwoLevel('产品套餐','导航栏');location.href='/buy/buy-combo'" style="margin-right:25px;background-color: transparent;border: 0px; color: #D9B171;">
            <img src="/img/cptc.png" style="margin-bottom: 3px;">&nbsp;<span>产品套餐</span>
          </button>
        </div>
        <div class="loginbtn-wrapper" style='position: unset;'>
          <button type="button" class="el-button button login el-button--default el-button--small" onclick="zhugeTrackTwoLevel('登录','导航栏');location.href='/login'" style="padding: 0px 15px;">
            <span>登录</span>
          </button>
          <button type="button" class="el-button button el-button--primary el-button--small" onclick="zhugeTrackTwoLevel('免费试用','导航栏');location.href='/buy/buy-combo'" style="padding: 0px 15px 0px 13px;">
            <span>免费试用</span>
          </button>
        </div>
      </div>
    </div>
    <div class="bannerWrapper" style="background: url(/img/websitebg_benefit.jpg) center top no-repeat;">
      <p class="title">受益所有人身份识别</p>
      <br>
      <hr class="line">
      <br>
      <p class="describition" style="line-height: 28px;">通过大数据技术，深度挖掘企业复杂股权结构和对外投资，逐层深入穿透，股权出资占比加权求总计算，识别公司最终受益人及公司投资图谱</p>
      <p onclick="zhugeTrackTwoLevel('观看视频介绍','受益所有人身份识别');showVideo()" class="playVideo flex-def flex-cCenter flex-zCenter">
        <img src="/img/trailer_watch.png" class="trailer_play">
        <span>观看视频介绍</span>
      </p>
      <button type="button" style="background: #128bed !important;margin-top:20px;" class="el-button shengqingBtn el-button--primary el-button--large" onclick="zhugeTrackTwoLevel('免费试用企查查专业版','受益所有人身份识别');location.href='/trial/index_trial'" style="margin-top: 15px;font-size: 14px;">
        <span>免费试用企查查专业版</span>
      </button>
    </div>
  </div>
  <div class="container-fluid" style="background: white;">
    <div class="container home-box maxLimit">
      <h1 class="fontColor1">央行235号文</h1>
      <p class="assistant fontColor2">
        <a onclick="zhugeTrackTwoLevel('235号文','受益所有人身份识别')" href="about_us" style="text-decoration: underline;color: #3c4144;display: inline-block;margin-top: 20px;" title="《中国人民银行关于加强反洗钱客户身份识别有关工作的通知》">《中国人民银行关于加强反洗钱客户身份识别有关工作的通知》</a>
      </p>
      <p class="assistant fontColor2" style="line-height: 1.8;">落实国务院关于完善<a href='/'>反洗钱</a>、反恐怖融资、反逃税监管体制机制的意见，防范违法犯罪分子利用复杂的股权、控制权等关系掩饰、隐瞒真实身份、资金性质或者交易目的、性质，提高受益所有人信息透明度，规范反洗钱义务机构开展非自然人客户的受益所有人身份识别工作</p>
      <div class="row-flex" style="margin-top:50px; margin-bottom: 95px;">
        <div class="col-flex notice" style="background-image: url(/img/benefit_bg2_1.png)">
          <h3 class="tc">要求</h3>
          <p style="line-height: 1.8;"><a href='/'>反洗钱</a>义务机构进一步提高<a href='/'>反洗钱</a>客户身份识别工作的有效性</p>
          <p style="line-height: 1.8;">提高受益所有人信息透明度，加强风险评估和分类管理，防范复杂股权或者控制权结构导致的洗钱和恐怖融资风险</p>
        </div>
        <div class="col-flex notice" style="margin-left:51px; margin-right:51px; background-image: url(/img/benefit_bg2_2.png)">
          <h3 class="tc">方法</h3>
          <p style="line-height: 1.8;">采取合理措施了解非自然人客户的业务性质与股权或者控制权结构，了解相关的受益所有人信息</p>
          <p style="line-height: 1.8;">从可靠途径、以可靠方式获取的相关信息或者数据，识别非自然人客户的受益所有人</p>
          <p style="line-height: 1.8;">逐层深入并最终明确为掌握控制权或者获取收益的自然人</p>
        </div>
        <div class="col-flex notice" style="background-image: url(/img/benefit_bg2_3.png)">
          <h3 class="tc">时间</h3>
          <p style="line-height: 1.8;">在建立或者维持业务关系时，了解相关的受益所有人信息</p>
          <p style="line-height: 1.8;">在业务关系存续期间，持续关注受益所有人信息变更情况</p>
          <p style="line-height: 1.8;">对存量客户组织排查，于2018年6月30日前完成存量客户的身份识别工作</p>
        </div>
      </div>
    </div>
  </div>
  <div class="container-fluid" style="height:450px; background-image: url(/img/benefit_bg1.png)">
    <div class="container home-box">
      <h1 class="fontColor1" style="margin-top: 35px; color: white">受益所有人穿透识别算法说明</h1>
      <p class="assistant fontColor2" style="color: white;line-height: 1.8;margin-top: 20px;">对目标企业进行股权关系层层遍历，逐层深入并判定受益所有人，穿透边界为自然人、政府机构、外国公司；<br>
        将公司股权层级及各层级实际占有的股权出资占比依次加权求总计算，筛选&gt;=25%的自然人股东；<br>
        如果穿透识别结果没有持股25%以上的自然人股东，依据《公司法》筛选企业的关键管理人员或法定代表人。
      </p>
    </div>
  </div>
  <div class="container-fluid" style="background: white;">
    <div class="container home-box maxLimit">
      <h1 class="fontColor1">一站式企业风险管控的平台</h1>
      <p class="assistant fontColor2" style="margin-top: 20px;">让客户快速做对决策</p>
      <div class="row-flex" style="margin-top:50px; margin-bottom: 95px;">
        <div class="col-flex box ">
          <div class="row-flex aic box-header">
            <h4 class="fontColor1 col-flex tc">存量客户受益所有人识别</h4>
          </div>
          <p class="fontColor2 box-body">非自然人客户进行批量导入</p>
          <p class="fontColor2 box-body">最终受益所有人穿透识别</p>
          <p class="fontColor2 box-body">在线查看穿透识别结果</p>
          <p class="fontColor2 box-body">图表展示,下载报告</p>
        </div>
        <div class="col-flex box box-margin">
          <div class="row-flex aic box-header">
            <h4 class="fontColor1 col-flex tc">新增客户受益所有人识别</h4>
          </div>
          <p class="fontColor2 box-body">实时查询其最终受益所有人</p>
          <p class="fontColor2 box-body">接口对接服务</p>
        </div>
        <div class="col-flex box box-margin">
          <div class="row-flex aic box-header">
            <h4 class="fontColor1 col-flex tc">自然人客户的身份识别</h4>
          </div>
          <p class="fontColor2 box-body">对外投资层层穿透识别</p>
          <p class="fontColor2 box-body">对外任职揭示</p>
        </div>
        <div class="col-flex box box-margin">
          <div class="row-flex aic box-header">
            <h4 class="fontColor1 col-flex tc">业务关系中客户的身份识别</h4>
          </div>
          <p class="fontColor2 box-body">受益所有人股权关系揭示</p>
          <p class="fontColor2 box-body">投资关系揭示</p>
          <p class="fontColor2 box-body">黑名单关联关系揭示</p>
        </div>
        <div class="col-flex box box-margin">
          <div class="row-flex aic box-header">
            <h4 class="fontColor1 col-flex" style="text-align: center">变更监控</h4>
          </div>
          <p class="fontColor2 box-body">受益所有人信息的变更监控</p>
          <p class="fontColor2 box-body">工商变更、司法涉诉、经营风险等各维度的风险监控</p>
        </div>
      </div>
    </div>
  </div>
  <div class="container-fluid" style="background: #F7FAFC;padding-bottom: 100px;">
    <div class="container home-box maxLimit">
      <h1 class="fontColor1"><a href='/'>企查查专业版</a>穿透识别受益所有人常见问题及解答</h1>
      <div class="row-flex question-answer">
        <div class="col-flex">
          <div class="row-flex question">1. 为什么要加强非自然人客户身份识别？</div>
          <p class="answer">答：根据《中国人民银行关于加强<a href='/'>反洗钱</a>客户身份识别有关工作的通知》（银发[2017]235号），简称235号文），金融机构需对非自然人客户受益所有人进行身份识别并留存相关信息及资料，识别和了解受益所有人是客户身份识别核心内容，也是金融行动特别工作组（FATF）和各国监管关注重点。通过有效开展非自然人客户的身份识别，提高受益所有人信息透明度，加强风险评估和分类管理，以防范复杂股权或者控制权结构导致的洗钱、恐怖融资、逃税风险。</p>
        </div>
      </div>
      <div class="row-flex question-answer">
        <div class="col-flex">
          <div class="row-flex question">2. 在哪些业务阶段需了解关注受益所有人信息？</div>
          <p class="answer">答：在建立业务或者维持业务关系时，需采取合理措施了解非自然人客户的业务性质与股权或者控制权结构，了解相关的受益所有人信息。在业务关系存续期间，应持续关注受益所有人信息变更情况。</p>
        </div>
      </div>
      <div class="row-flex question-answer">
        <div class="col-flex">
          <div class="row-flex question">3. 怎么判定非自然人客户受益所有人？</div>
          <p class="answer">
            答：对非自然人客户受益所有人的追溯，应当逐层深入并最终明确为掌握控制权或者获取收益的自然人。<br>
            1.公司的受益所有人应当按照以下标准依次判定：直接或者间接拥有超过25%公司股权或者表决权的自然人；通过人事、财务等其他方式对公司进行控制的自然人；公司的高级管理人员。<br>
            特别提示：如果非自然人客户的大股东是公司，则需对该公司进一步追溯，逐层深入最终明确掌握控制权或者获取收益的自然人。<br>
            2.合伙企业的受益所有人是指拥有超过25%合伙权益的自然人。<br>
            3.信托的受益所有人是指信托的委托人、受托人、受益人以及其他对信托实施最终有效控制的自然人。<br>
            4.基金的受益所有人是指拥有超过25%权益份额或者其他对基金进行控制的自然人。<br>
            对风险较高的非自然人客户，义务机构应当采取更严格的标准判定其受益所有人。
          </p>
        </div>
      </div>
      <div class="row-flex question-answer">
        <div class="col-flex">
          <div class="row-flex question">4. 哪些非自然人客户可以将其法定代表人或者实际控制人视同为受益所有人？</div>
          <p class="answer">
            答：在充分评估下述非自然人客户风险状况基础上，可以将其法定代表人或者实际控制人视同为受益所有人。<br>
            1.个体工商户、个人独资企业，不具备法人资格的专业服务机构。<br>
            2.经营农林渔牧产业的非公司制农民专业合作组织。<br>
            对于受政府控制的企业事业单位，参照上述标准执行。<br>
          </p>
        </div>
      </div>
      <div class="row-flex" style="margin-top: 30px;">
        <div class="row-flex" style="margin: 0 auto">
          <button class="more-questions" onclick="javascript:window.location.href='about_us'">查看更多解答</button>
        </div>
      </div>
    </div>
  </div>
  <div class="container_fluid footer-nav">
    <div class="footer-bd">
      <div class="row-flex footer-bd-t">
        <div class="row-flex col-flex">
          <div class="footer-nav-l">
            <h3 class="footer-h3">
              <a href="product" rel="nofollow" title="产品体系" class="title">产品体系</a>
            </h3>
            <p class="firstP">
              <a href="product" rel="nofollow" title="精准拓客">精准拓客</a>
            </p>
            <p>
              <a href="product" rel="nofollow" title="尽职调查 · 受益所有人">尽职调查 · 受益所有人</a>
            </p>
            <p>
              <a href="product" rel="nofollow" title="风险监控" onclick="zhugeTrackTwoLevel('风险监控', window.currentPagename)">风险监控</a>
            </p>
            <p>
              <a href="product" rel="nofollow" title="尽职调查" onclick="zhugeTrackTwoLevel('尽职调查', window.currentPagename)">尽职调查</a>
            </p>
            <p>
              <a href="product" rel="nofollow" title="企云图">企云图</a>
            </p>
          </div>
          <div class="footer-nav-l">
            <h3 class="footer-h3">
              <a href="industry.html" rel="nofollow" title="行业方案" class="title">行业方案</a>
            </h3>
            <p class="firstP">
            <a onclick='localStorage.setItem("catorgate_industry", "1")' href="industry.html" rel="nofollow" title="金融业">金融业</a>
          </p>
          <p>
            <a  onclick='localStorage.setItem("catorgate_industry", "2")' href="industry.html" rel="nofollow" title="制造业">制造业</a>
          </p>
          <p>
            <a  onclick='localStorage.setItem("catorgate_industry", "3")' href="industry.html" rel="nofollow" title="投资机构">投资机构</a>
          </p>
          <p>
            <a  onclick='localStorage.setItem("catorgate_industry", "4")' href="industry.html" rel="nofollow" title="政府">政府</a>
          </p>
          <p>
            <a  onclick='localStorage.setItem("catorgate_industry", "5")' href="industry.html" rel="nofollow" title="互联网">互联网</a>
          </p>
          </div>
          <div class="footer-nav-l">
            <div class="footer-title">
            <a onclick='localStorage.setItem("catorgate_about", "1")' href="about_us" rel="nofollow" title="关于我们" class="title">关于我们</a>
          </div>
          <p class="subtitle">
            <a onclick='localStorage.setItem("catorgate_about", "1")' href="about_us" rel="nofollow" title="关于我们">关于我们</a>
          </p>
          <p>
            <a onclick='localStorage.setItem("catorgate_about", "5")' href="about_us" title="受益所有人">受益所有人</a>
          </p>
          <p>
            <a onclick='localStorage.setItem("catorgate_about", "4")' href="about_us" rel="nofollow" title="服务条款">服务条款</a>
          </p>
              <p>
                <a href="/" title="企查查专业版">企查查专业版</a>
              </p>
          </div>
          <div class="footer-nav-l">
            <h3 class="footer-h3">
              <a href="about_us" rel="nofollow" title="联系我们" class="title">联系我们</a>
            </h3>
            <p class="firstP">电话咨询：400-088-8275</p>
            <p>商务合作电话：400-088-8275<br /><span style="display: inline-block;padding-left: 60px;">400-088-8275</span></p>
            <p>技术支持QQ：
              <a target="_blank" rel="nofollow" href="https://wpa.qq.com/msgrd?v=3&amp;uin=3347686608&amp;site=qq&amp;menu=yes">3347686608</a>
            </p>
            <p>合作邮箱：
              <a rel="nofollow" href="mailto:<EMAIL>"><EMAIL></a>
            </p>
          </div>
        </div>
        <div style="margin-right: 20px;">
          <h3 class="footer-h3" style="height: 24px;"></h3>
          <div class="row-flex">
            <div>
              <img alt="企查查专业版手机端应用" src="/img/wechat_pub.png" width="120" class="mt10">
              <p class="tc mt10">扫一扫，来互动</p>
            </div>
            <div style="margin-left: 20px;">
              <img alt="微信公众号" src="/img/mini_program_code.png" width="120" class="mt10">
              <p class="tc mt10">小程序</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="footer-bd-b">

      <div style="margin-top:-5px;margin-bottom: -10px;">
        <span class="fotter-bg-title">数据来源：</span>
        <ul id="infoResourceId" class="fotter-bg-content">
          <a rel="nofollow" class="notLink">国家企业信用信息公示系统</a>
          <em></em>
          <a rel="nofollow" class="notLink">中国裁判文书网</a>
          <em></em>
          <a rel="nofollow" class="notLink">中国执行信息公开网</a>
          <em></em>
          <a rel="nofollow" class="notLink">国家知识产权局</a>
          <em></em>
          <a rel="nofollow" class="notLink">商标局</a>
          <em></em>
          <a rel="nofollow" class="notLink">版权局</a>
        </ul>
      </div>
    </div>
  </div>
  <div class="footer-bottom">
    <span>© 2014-2025
      <a rel="nofollow" href="https://beian.miit.gov.cn/" title="苏ICP备15042526号" target="_blank">苏ICP备15042526号</a> 版权所有 企查查科技股份有限公司 增值电信业务经营许可证：苏ICP证B2-20180251
    </span>
  </div>
  <div id="footerFloatWrapperId" class="footerFloatWrapper" style="background-image:url('/img/website_footer_promote.png');
					background-size:cover;background-position:center;background-repeat:no-repeat">
    <span class="el-icon-close" onclick="hideFooter()"></span>
  </div>
  <div id="consulation">
    <div class="main" id="cslt-main">
      <div style="width: 202px; overflow-x:hidden">
        <div class="info-item">
          <a target="_blank" rel="nofollow" href="https://szld.kf5.com/kchat/23210?from=%E5%9C%A8%E7%BA%BF%E6%94%AF%E6%8C%81&group=29332" title="在线客服">
            <img src="/img/customer-service.png" alt="在线客服" style="width: 14px;" />
            <span class="cslt-title">在线客服</span>
          </a>
        </div>
        <div class="info-item">
          <a target="_blank" rel="nofollow" href="https://wpa.qq.com/msgrd?v=3&amp;uin=3347686608&amp;site=qq&amp;menu=yes" title="技术支持">
            <img src="/img/qq.png" alt="技术支持" style="width: 14px;" />
            <span class="cslt-title">技术支持</span>
          </a>
        </div>
        <div class="info-item">
          <img src="/img/cooperation.png" alt="商务合作" />
          <span class="cslt-title">商务合作电话</span>
          <span class="cslt-detail">400-088-8275</span>
        </div>
        <div class="info-item">
          <img src="/img/email.png" alt="合作邮箱" />
          <span class="cslt-title">合作邮箱</span>
          <span class="cslt-detail"><a rel="nofollow" href="mailto:<EMAIL>" title="合作邮箱"><EMAIL></a></span>
        </div>
        <div class="info-item" id="wechat-info">
          <img src="/img/wechart.png" alt="微信公众号" />
          <span class="cslt-title pointer">微信公众号</span>
        </div>
        <div class="info-item" style="border-bottom: none" data-toggle="modal" data-target="#suggestionModal" id="suggestion-info">
          <img src="/img/suggestion.png" alt="意见反馈" />
          <span class="cslt-title pointer">意见反馈</span>
        </div>
        <div id="wechat-detail" style="display: none">
          <img src="/img/wechat_pub.png" />
        </div>
      </div>
    </div>
    <div class="nav" id="cslt-nav">
      咨询 &nbsp;· <br>反馈
    </div>
  </div>
<!--  <div id="backToTop" onclick="scrollToTop()">-->
<!--    <img src="/img/icon_back2top.png" width="30" class="icon_back2top">-->
<!--    <img src="/img/icon_back2top_text.png" width="30" class="icon_back2top_text">-->
<!--  </div>-->

  <div id="videoPop" class="videoPop" style="z-index: 999; display: none;">
    <div class="videoWrapper">
      <video id="playerVideo" class="trailer_screen" controls="true" preload="none" controlslist="nodownload" src="https://pro-files.qichacha.com/open/video/pro_website_beneficial_owner.mp4">
        <source src="https://pro-files.qichacha.com/open/video/pro_website_beneficial_owner.mp4" type="video/mp4">
      </video>
      <img id="closeVideo" class="trailer_close" src="/img/trailer_close.png">
    </div>
  </div>

  <div class="modal fade modal-new" id="suggestionModal" tabindex="-1" role="dialog" aria-labelledby="suggestionModalLabel" aria-hidden="true" style="z-index: 999999!important">
    <div class="modal-dialog" style="width:450px; margin-top: 100px; margin-left: calc(50% - 270px);">
      <div class="modal-content" style="padding-bottom: 15px">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
          <h4 class="modal-title" id="selectCorpsModalLabel">意见反馈</h4>
        </div>
        <div class="modal-body" style="padding-top:15px; height: 300px;">
          <div class="group">
            <label class="col-sm-3 control-label" style="padding-left:68px">姓名：</label>
            <div class="col-sm-9">
              <input id="name" type="text" name="name" class="required form-control" placeholder="请输入您的姓名">
            </div>
          </div>
          <div class="group">
            <label class="col-sm-3 control-label" style="padding-left:40px"><span class="marks"> * </span>联系方式：</label>
            <div class="col-sm-9">
              <input id="mobile" name="mobile" type="text" class="required mobile form-control" placeholder="请输入手机号码或者邮箱">
            </div>
          </div>
          <div class="group">
            <label class="col-sm-3 control-label" style="padding-left:30px"><span class="marks"> * </span>反馈内容：</label>
            <div class="col-sm-9">
              <textarea id="content" name="content" type="text" class="required form-control" style="resize:vertical; max-height: 350px;" placeholder="请在这里直接填写您遇到的问题或意见建议，您的意见是我们前进的动力"></textarea>
              <p>非常感谢您对企查查专业版的关注，欢迎提出宝贵的意见和建议，我们将积极采纳，及时为您解答问题，更好的改善我们的服务，为您提供更贴心的服务。</p>
            </div>
          </div>
        </div>
        <div class="modal-footer" style="margin-top: 15px;">
          <button type="button" id="saveSuggestion" class="btn btn-primary pointer" onclick="addSuggestion()" style="margin-right: 15px;">提交反馈</button>
        </div>
      </div>
    </div>
  </div>

  <script type="text/javascript" src="/js/video.dev.js?v=1000000000"></script>
  <script type="text/javascript" src="/js/vue.min.js?v=1000000000"></script>
  <script type="text/javascript" src="/js/element-ui/index.js?v=1000000000"></script>
  <script type="text/javascript" src="/js/jquery-3.4.1.min.js?v=1000000000"></script>
  <script type="text/javascript" src="/js/bootstrap.min.js?v=1000000000"></script>
  <script type="text/javascript" src="/js/index.js?v=2020022301"></script>
  <script>
    function showVideo() {
      $("#videoPop").css("display", "block");
      var video = document.getElementById("playerVideo");
      video.play();
    }

    $(document).ready(function() {
      $("#closeVideo").click(function(event) {
        event.stopPropagation;
        $("#videoPop").css("display", "none")
        var video = document.getElementById("playerVideo");
        video.pause();
      });
    })

  </script>
  <script>
    var appVue = new Vue({
      el: "#websiteWrapperId"
    })

  </script>
  <script>
    (function() {
      var bp = document.createElement('script');
      var curProtocol = window.location.protocol.split(':')[0];
      if (curProtocol === 'https') {
        bp.src = 'https://zz.bdstatic.com/linksubmit/push.js';
      }
      else {
        bp.src = 'http://push.zhanzhang.baidu.com/push.js';
      }
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(bp, s);
    })();

  </script>
</body>

</html>
